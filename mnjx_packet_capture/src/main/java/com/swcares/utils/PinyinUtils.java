package com.swcares.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
public class PinyinUtils extends PinyinUtil {
    /**
     * 将汉字转换为全拼<br/>
     * 输入项可能是混合的<br/>
     * 所以就需要一个个的去取字符进行转换，最后输入所有的
     *
     * @param hybridInput 混合输入
     * @param allPinYin   true：返回所有的拼音（多音字）；false：只返回第一个拼音
     * @return 汉字拼音
     */
    public static String getPinYin(String hybridInput, boolean allPinYin) {
        List<String> allPinYins = PinyinUtils.getAllPinYins(hybridInput);
        String chinesePinYin = StrUtil.EMPTY;
        if (CollUtil.isNotEmpty(allPinYins)) {
            if (allPinYin) {
                for (int i = 0, j = allPinYins.size(); i < j; i++) {
                    chinesePinYin = StrUtil.format("{}{}", chinesePinYin, allPinYins.get(i));
                    if (i < (j - 1)) {
                        chinesePinYin = StrUtil.format("{}{}", chinesePinYin, StrUtil.COMMA);
                    }
                }
            } else {
                chinesePinYin = allPinYins.get(0);
            }
        }
        return chinesePinYin;
    }

    /**
     * 获得所有拼音
     *
     * @param hybridInput 混合输入项
     * @return 所有拼音组合后的拼音
     */
    public static List<String> getAllPinYins(String hybridInput) {
        // 分组后的拼音数据
        List<List<String>> groupPinYins = groupPinYins(hybridInput);
        return groupPinYins.stream().reduce((group1, group2) -> group1.stream().flatMap((Function<String, Stream<String>>) s1 -> group2.stream().map(s2 -> StrUtil.format("{}{}", s1, s2))).collect(Collectors.toList())).orElse(null);
    }

    /**
     * 输入的数据分组，因为输入项中拼音和中文可能一起存在，必须分组，用户后面的组装
     *
     * @param hybridInput 混合输入项
     * @return 分组后的数据
     */
    public static List<List<String>> groupPinYins(String hybridInput) {
        List<List<String>> allPinYins = new ArrayList<>();
        // 转换为字符数组
        char[] chineseCharacters = hybridInput.toCharArray();
        // 创建
        String tmpStr = StrUtil.EMPTY;
        for (char chineseCharacter : chineseCharacters) {
            // 判断能否为汉字字符
            if (PinyinUtil.isChinese(chineseCharacter)) {
                // 字符串不为空，那这个字符串要先生成一个空间加入
                if (StrUtil.isNotBlank(tmpStr)) {
                    allPinYins.add(Collections.singletonList(tmpStr));
                    tmpStr = StrUtil.EMPTY;
                }
                // 获取这个汉字的所有拼音
                List<String> pinYins = PinyinUtils.getAllPinYins(chineseCharacter);
                allPinYins.add(pinYins);
            }
            // 不为中文，那就往临时字符上面拼接
            else {
                tmpStr = StrUtil.format("{}{}", tmpStr, chineseCharacter);
            }
        }
        return allPinYins;
    }

    /**
     * 获取一个中文的所有拼音
     *
     * @param chinese 中文
     * @return 返回所有的拼音
     */
    public static List<String> getAllPinYins(char chinese) {
        // 设置汉字拼音输出的格式
        HanyuPinyinOutputFormat hanyuPinyinOutputFormat = new HanyuPinyinOutputFormat();
        // 该选项指示汉语拼音以小写字母的形式输出
        hanyuPinyinOutputFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        // 该选项表示输出的汉语拼音不带音号或音标
        hanyuPinyinOutputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        // 该选项指示'ü'的输出是"u:"(航信的ü是发的U所以我们要特殊处理)
//        hanyuPinyinOutputFormat.setVCharType(HanyuPinyinVCharType.WITH_U_AND_COLON);
        hanyuPinyinOutputFormat.setVCharType(HanyuPinyinVCharType.WITH_V);
        List<String> allPinYins = null;
        if (PinyinUtil.isChinese(chinese)) {
            try {
                String[] pinYins = PinyinHelper.toHanyuPinyinStringArray(chinese, hanyuPinyinOutputFormat);
                //
                allPinYins = Arrays.stream(pinYins).distinct().collect(Collectors.toList());
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        }
        return allPinYins;
    }
}
