package com.swcares;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.swcares.core.CaptureConfiguration;
import com.swcares.core.EtermCodeUtil;
import com.swcares.core.utils.DateUtils;
import jpcap.JpcapCaptor;
import jpcap.NetworkInterface;
import jpcap.PacketReceiver;
import jpcap.packet.Packet;
import jpcap.packet.TCPPacket;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/30 16:18
 */
@Slf4j
public class PacketCapture {

    private static final Pattern FIRST_LINE_PATTERN = Pattern.compile("((\\w{2}\\d{4}/\\d{2}[A-Z]{3}\\d{2})[A-Z*][A-Z]{3}(\\d+)?)(.+)*");

    /**
     * 白屏发送指令格式
     * BC:CA1234/03AUG22NCTU,BN002,R;BN003,R
     * 默认航班格式
     * BC:*,BN001,R;BN002,R
     */
    private static final Pattern BC_PARAM_PATTERN1 = Pattern.compile("(((\\w{5,6})/(\\d{2}[A-Z]{3}\\d{2})([A-Z])([A-Z]{3}))|([*])),((BN\\d+,R)(;BN\\d+,R)*)");

    /**
     * BC:1,R
     * BC:1-3,R
     * BC:1PD,R
     * BC:1PD-3PD,R
     * BC:1-3PD,R
     */
    private static final Pattern BC_PARAM_PATTERN2 = Pattern.compile("(\\d+)(PD)?(-(\\d+)(PD)?)?,R");

    /**
     * BC:1,R
     * BC:1,R;3,R
     * BC:1PD,R
     * BC:1PD,R;3PD,R
     */
    private static final Pattern BC_PARAM_PATTERN3 = Pattern.compile("(\\d+)(PD)?,R(;\\d+(PD)?,R)*");

    /**
     * 1,R;2,R;3,R
     */
    private static final Pattern NO_BAG_NO_PATTERN = Pattern.compile("\\d+,R(;\\d+,R)*");

    /**
     * 1,R000111/R222111    (\d+),(R\d{6}?(/\d{6})*)
     */
    private static final Pattern BAG_NO_PATTERN = Pattern.compile("(\\d+),(R\\d{6}?(/\\d{6})*)");

    private static final Pattern BAG_PATTERN = Pattern.compile("(AVIH)?\\d+/\\d+");
    private static final Pattern BN_PATTERN = Pattern.compile("[\\s,;]+BN\\d{3}[\\s,]+");

    private static final Pattern NREC_PATTERN = Pattern.compile("[\\w.\\s]+-NOREC PSGR.*");

    private static Map<String, Object> keyMap = new HashMap<>();

    private static final String ip;
    private static final String etermPort;
    private static final String webPort;
    private static final String mac;

    static {
        CaptureConfiguration configuration = getConfiguration();
        etermPort = configuration.getEtermPort();
        webPort = configuration.getWebPort();
        ip = configuration.getIp();
        mac = configuration.getMac().toLowerCase();
    }

    public static void main(String[] args) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("===================");
            log.info("配置IP：{}", ip);
            log.info("配置eterm端口：{}", etermPort);
            log.info("配置web后端端口：{}", webPort);
            log.info("配置MAC地址：{}", mac);
            log.info("===================");
            try {
                log.info("开始获取本机mac列表");
                int deviceNo = -1;
                // 获取网络接口列表
                NetworkInterface[] devices = JpcapCaptor.getDeviceList();
                log.info("获取到网络设备数量：{}", devices.length);
                int k = -1;
                // 显示所有网络设备的mac
                for (NetworkInterface n : devices) {
                    k++;
                    String macString = getMacString(n.mac_address);
                    log.info("序号 " + k + "   " + macString);
                    log.info("------------------------------------------------");
                    if (macString.equals(mac)) {
                        deviceNo = k;
                        log.info("找到该mac地址的网卡，序号为{}", k);
                        break;
                    }
                }
                if (deviceNo == -1) {
                    log.info("未找到该mac地址的网卡");
                    return;
                }

                // 选择一个网络接口
                NetworkInterface device = devices[deviceNo];

                // 打开网络接口进行抓包
                JpcapCaptor captor = JpcapCaptor.openDevice(device, 65535, true, 50);

                // 设置过滤器
                captor.setFilter("port " + etermPort, true);

                // 创建一个数据包接收器，用于处理抓到的数据包
                int portNum = Integer.parseInt(etermPort);

                // 开始抓包，直到用户停止
                captor.loopPacket(-1, new PacketReceive(portNum));

                // 停止
                log.info("停止抓包");
                // 可能出现的异常
                log.error(captor.getErrorMessage());
                // 关闭抓包器
                captor.close();
                log.info("程序退出");
            } catch (Exception e) {
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }));
    }

    /**
     * Title: getBoardingPrintData
     * Description: 调用web接口获取登机牌打印数据<br>
     *
     * @param key
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/11/1 16:46
     */
    private static List<String> getBoardingPrintData(String key) {
        List<String> valueList = new ArrayList<>();
        // 调web接口通过key查询打印数据
        String url = StrUtil.format("http://{}:{}/mnjx/print/getBoardingPrintData?key={}", ip, webPort, key);
        String result = HttpUtil.get(url);
        if (StrUtil.isNotEmpty(result)) {
            Map<String, Object> map = JSONUtil.toBean(result, Map.class);
            if ((int) map.get("code") == 200) {
                JSONArray jsonArray = new JSONArray(map.get("data"));
                valueList = new ArrayList<>(jsonArray.toList(String.class));
            }
        }
        return valueList;
    }

    /**
     * Title: getLuggagePrintData
     * Description: 调用web接口获取行李打印数据<br>
     *
     * @param key
     * @param bagNo
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/11/1 16:46
     */
    private static List<String> getLuggagePrintData(String key, String bagNo) {
        List<String> valueList = new ArrayList<>();
        // 调web接口通过key查询打印数据
        String url = StrUtil.format("http://{}:{}/mnjx/print/getLuggagePrintData?key={}&bagNo={}", ip, webPort, key, bagNo);
        String result = HttpUtil.get(url);
        if (StrUtil.isNotEmpty(result)) {
            Map<String, Object> map = JSONUtil.toBean(result, Map.class);
            if ((int) map.get("code") == 200) {
                JSONArray jsonArray = new JSONArray(map.get("data"));
                valueList = new ArrayList<>(jsonArray.toList(String.class));
            }
        }
        return valueList;
    }

    /**
     * Title: printBoarding
     * Description: 调用本地打印程序打印登机牌<br>
     *
     * @param value
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 16:45
     */
    private static void printBoarding(String value) {
        if (StrUtil.isEmpty(value)) {
            log.info("登机牌数据获取为空，不调用打印");
            return;
        }
        log.info("开始打印登机牌");
        String url = "http://127.0.0.1:18350/printer/boarding/print";
        try {
            HttpUtil.post(url, value, 10000);
        } catch (Exception e) {
            log.error("登机牌打印接口连接失败");
            log.error(e.getMessage());
        }
        log.info("结束打印登机牌");
        keyMap.remove("bcKey");
    }

    /**
     * Title: printLuggage
     * Description: 调用本地打印程序打印行李牌<br>
     *
     * @param value
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 16:46
     */
    private static void printLuggage(String value) {
        if (StrUtil.isEmpty(value)) {
            log.info("行李牌数据获取为空，不调用打印");
            return;
        }
        log.info("开始打印行李牌");
        String url = "http://127.0.0.1:18350/printer/luggage/print";
        try {
            HttpUtil.post(url, value, 10000);
        } catch (Exception e) {
            log.error("行李牌打印接口连接失败");
            log.error(e.getMessage());
        }
        log.info("结束打印行李牌");
        keyMap.remove("bagNoList");
    }

    /**
     * Title: getMacString
     * Description: 转换获取mac地址<br>
     *
     * @param macBytes
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/11/1 11:58
     */
    private static String getMacString(byte[] macBytes) {
        StringBuilder builder = new StringBuilder();
        for (byte macByte : macBytes) {
            builder.append('-').append(Integer.toHexString(0xFF & macByte).toLowerCase());
        }
        return builder.substring(1);
    }

    /**
     * Title: parsingReceiveData
     * Description: 解析接收的数据包：
     * 1.当接收指令为HBPA、PA时，判断指令行是否有#（无#调用登机牌打印），是否有1/10行李参数格式（有行李调用行李牌打印）
     * 2.当接收指令为HBPU时，判断指令行是否有#（有#不调用任何打印），是否有1/10行李参数格式（有行李调用行李牌打印）
     * 3.当接收内容为BOARDING PASS ISSUED时，获取上次发送包保存的数据查询旅客及航班信息，调用登机牌打印，清除发送时保存的BC信息
     * 4.当接收内容为ACCEPTED时，获取上次发送包保存的PR数据，查询旅客、航班及行李信息，调用行李牌打印，清除发送时保存的PR和BAG信息
     *
     * @param data
     * @return void
     * <AUTHOR>
     * @date 2023/10/31 11:18
     */
    public static void parsingReceiveData(byte[] data) {
        // 筛选指令的回显
        if (ObjectUtil.isNotEmpty(data) && data.length >= 26) {
            // 数据转换为回显内容
            byte[] resultBytes = new byte[data.length - 26];
            System.arraycopy(data, 19, resultBytes, 0, resultBytes.length);
            String result;
            try {
                result = new String(resultBytes, CharsetUtil.GBK);
                log.info(result);
            } catch (UnsupportedEncodingException e) {
                log.error("字节转换异常");
                log.error(e.getMessage());
                return;
            }
            // 去除指令头部开始符
            String cmdHead = "\u0010";
            result = result.replace(cmdHead, "");
            // 按行分割
            String[] split = result.split("\r\n");
            // 只处理接收、添加行李、重打的指令
            String firstLine = split[0];
            String cmd = firstLine.split(":")[0].trim();
            // 记录上个指令是否被改动过，BAG BC指令如果报错的话，需要将上个指令改回BAG BC执行之前的指令，不能记录成当前的BAG BC指令
            boolean changeLastCmd = false;
            switch (cmd) {
                case "PA":
                    parsingPa(split, firstLine);
                    parsingReceivePcmd(firstLine, "PA", "paKey");
                    break;
                case "HBPA":
                    parsingPa(split, firstLine);
                    parsingReceivePcmd(firstLine, "HBPA", "hbpaKey");
                    break;
                case "JC":
                    parsingPa(split, firstLine);
                    parsingReceivePcmd(firstLine, "JC", "jcKey");
                    break;
                case "HBJC":
                    parsingPa(split, firstLine);
                    parsingReceivePcmd(firstLine, "HBJC", "hbjcKey");
                    break;
                case "PU":
                    parsingPu(split, firstLine);
                    parsingReceivePcmd(firstLine, "PU", "puKey");
                    break;
                case "HBPU":
                    parsingPu(split, firstLine);
                    parsingReceivePcmd(firstLine, "HBPU", "hbpuKey");
                    break;
                case "PR":
                    parsingReceivePcmd(firstLine, "PR", "prKey");
                    break;
                case "HBPR":
                    parsingReceivePcmd(firstLine, "HBPR", "hbprKey");
                    break;
                case "PW":
                    parsingReceivePcmd(firstLine, "PW", "pwKey");
                    break;
                case "HBPW":
                    parsingReceivePcmd(firstLine, "HBPW", "hbpwKey");
                    break;
                case "ACCEPTED":
                    log.info("接收到{}指令结果AECCEPTED", cmd);
                    log.info("ACCPETED KEYMAP:{}", keyMap.toString());
                    log.info("条件：{}", MapUtil.isNotEmpty(keyMap) && keyMap.get("cmd").toString().startsWith("BAG") && keyMap.containsKey("bagNoList") && keyMap.containsKey("bagKey"));
                    if (MapUtil.isNotEmpty(keyMap) && keyMap.get("cmd").toString().startsWith("BAG") && keyMap.containsKey("bagNoList") && keyMap.containsKey("bagKey")) {
                        parsingReceiveBag();
                    }
                    break;
                case "BOARDING PASS ISSUED":
                    if (MapUtil.isNotEmpty(keyMap) && keyMap.containsKey("bcKey")) {
                        parsingReceiveBc();
                    }
                    break;
                case "FORMAT":
                case "FORMAT ERROR":
                case "NUMBER ERROR":
                case "ENTRY NBR":
                case "NO RECORD":
                case "NO DISPLAY":
                    if (MapUtil.isNotEmpty(keyMap) && keyMap.get("cmd").toString().startsWith("BAG") && keyMap.containsKey("bagNoList") && keyMap.containsKey("bagKey")) {
                        keyMap.put("cmd", keyMap.get("lastCmd").toString());
                        changeLastCmd = true;
                    }
                    if (MapUtil.isNotEmpty(keyMap) && keyMap.containsKey("bcKey")) {
                        keyMap.put("cmd", keyMap.get("lastCmd").toString());
                        changeLastCmd = true;
                    }
                    break;
                default:
                    if (MapUtil.isNotEmpty(keyMap) && keyMap.containsKey("ftKey") && keyMap.get("cmd").toString().startsWith("FT")) {
                        keyMap.remove("ftKey");
                    }
                    break;
            }
            if (!changeLastCmd && !StrUtil.startWithAny(keyMap.get("cmd").toString(),"PN", "PB", "PF", "PL", "PG")) {
                keyMap.put("lastCmd", keyMap.get("cmd").toString());
            }
        }
    }

    /**
     * Title: parsingSendData
     * Description: 针对BC和BAG的返回信息没有包含航班及旅客信息，需要对发送包进行解析：
     * 1.解析发送的数据包，转换为指令，当指令为PR时，对指令内容进行记录，下一次指令是BAG且返回包解析为ACCEPTED则需要使用记录的PR去查询旅客及行李信息调用行李牌打印
     * 2.如果发送的指令是BC，直接解析指令内容存储，返回包解析为BOARDING PASS ISSUED使用存储的指令内容查询旅客信息调用登机牌打印
     *
     * @param data
     * @return void
     * <AUTHOR>
     * @date 2023/10/31 11:13
     */
    public static void parsingSendData(byte[] data) {
        String cmdHead = "\u0010";
        String sendCmd = parseSendCmd(data).toUpperCase().trim().replace(cmdHead, "");
        String[] split = sendCmd.split("\r\n");
        keyMap.put("cmd", sendCmd);
        if (sendCmd.startsWith("BAG")) {
            parsingSendBag(split[0]);
        } else if (sendCmd.startsWith("BC")) {
            parsingSendBc(split[0]);
        } else if (sendCmd.startsWith("FT")) {
            parsingSendFt(split[0]);
        } else if (sendCmd.startsWith("PD")) {
            parsingReceivePcmd(split[0], "PD", "pdKey");
        } else if (sendCmd.startsWith("SO")) {
            keyMap.clear();
        }
    }

    /**
     * Title: parseSendCmd
     * Description: 解析抓包获取发送的数据包，转换为指令字符串<br>
     *
     * @param rcv
     * @return {@link String}
     * <AUTHOR>
     * @date 2023/11/1 16:45
     */
    private static String parseSendCmd(byte[] rcv) {
        // 解析出输入的字符串，中间要处理包含中文的情况(第19位是黑色3角符号,第20位开始才是输入的指令),这个是实际的输入值
        byte[] actual = ArrayUtil.sub(rcv, 18, rcv.length - 1);
        // 标准化处理，里面可能包含中文，则必须对字节码进行处理
        actual = EtermCodeUtil.chineseEtermDecode(actual);
        // 处理中文的 拼音、开始分隔符、结束分隔符
        return EtermCodeUtil.removePinYins(actual);
    }

    /**
     * Title: parsingPa
     * Description: 解析PA的返回抓包数据，获取打印数据进行打印<br>
     *
     * @param split
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 15:18
     */
    private static void parsingPa(String[] split, String firstLine) {
        String paParam = firstLine.split(":")[1].trim().split(" ")[0];
        String[] paramSplit = paParam.split(",");
        // 预接收和候补和XBP参数不会调用打印
        if (paParam.contains("#")) {
            return;
        }
        boolean haveXbp = false;
        boolean haveXbt = false;
        for (String param : paramSplit) {
            if (StrUtil.equalsAny(param, "XBP")) {
                haveXbp = true;
            } else if (StrUtil.equalsAny(param, "XBT")) {
                haveXbt = true;
            }
        }
        if (ReUtil.isMatch(FIRST_LINE_PATTERN, paParam)) {
            List<String> keyList = new ArrayList<>();
            // 获取存储的key
            List<String> allGroups = ReUtil.getAllGroups(FIRST_LINE_PATTERN, paParam);
            boolean isNrec = false;
            for (String line : split) {
                Matcher matcher = BN_PATTERN.matcher(line);
                while (matcher.find()) {
                    String group = matcher.group();
                    String key = StrUtil.format("{}/{}", allGroups.get(2), group.replace(",", "").trim());
                    keyList.add(key);
                }
                Matcher nrecMatcher = NREC_PATTERN.matcher(line);
                while (nrecMatcher.find()) {
                    isNrec = true;
                }
            }
            if (isNrec) {
                return;
            }
            if (CollUtil.isNotEmpty(keyList)) {
                for (String key : keyList) {
                    if (!haveXbp) {
                        log.info("HBPA打印登机牌：{}", key);
                        // 获取登机牌打印数据
                        List<String> boardingPrintDataList = getBoardingPrintData(key);
                        if (CollUtil.isEmpty(boardingPrintDataList)) {
                            return;
                        }
                        for (String boardingPrintData : boardingPrintDataList) {
                            printBoarding(boardingPrintData);
                        }
                    }
                    if (!haveXbt) {
                        // 获取行李牌打印数据
                        List<String> luggagePrintData = new ArrayList<>();
                        List<String> collect = Arrays.stream(paramSplit)
                                .filter(s -> ReUtil.isMatch(BAG_PATTERN, s))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(collect)) {
                            luggagePrintData = getLuggagePrintData(key, "");
                        }
                        // 调用打印
                        if (CollUtil.isNotEmpty(luggagePrintData)) {
                            log.info("HBPA打印行李牌：{}", key);
                            luggagePrintData.forEach(PacketCapture::printLuggage);
                        }
                    }
                }
            }
        }
    }

    /**
     * Title: parsingPu
     * Description: 解析PU的返回抓包数据，获取行李牌打印数据进行打印<br>
     *
     * @param split
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 15:19
     */
    private static void parsingPu(String[] split, String firstLine) {
        String puParam = firstLine.split(":")[1].trim().split(" ")[0];
        // 预修改的不会调用打印
        if (StrUtil.containsAny(puParam, "#", "URES", "NREC", "XBT")) {
            return;
        }
        if (ReUtil.isMatch(FIRST_LINE_PATTERN, puParam)) {
            String key = "";
            // 获取存储的key
            List<String> allGroups = ReUtil.getAllGroups(FIRST_LINE_PATTERN, puParam);
            for (String line : split) {
                Matcher matcher = BN_PATTERN.matcher(line);
                while (matcher.find()) {
                    String group = matcher.group();
                    key = StrUtil.format("{}/{}", allGroups.get(2), group.replace(",", "").trim());
                }
                if (StrUtil.isNotEmpty(key)) {
                    break;
                }
            }
            if (StrUtil.isNotEmpty(key)) {
                // 获取行李牌打印数据
                String[] paramSplit = puParam.split(",");
                List<String> collect = Arrays.stream(paramSplit)
                        .filter(s -> ReUtil.isMatch(BAG_PATTERN, s))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    List<String> luggagePrintData = getLuggagePrintData(key, "");
                    if (CollUtil.isNotEmpty(luggagePrintData)) {
                        luggagePrintData.forEach(PacketCapture::printLuggage);
                    }
                }
            }
        }
    }

    /**
     * Title: parsingReceiveBag
     * Description: 解析重打行李条返回数据，通过PR发送数据解析的key查询行李牌打印数据进行打印<br>
     *
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 15:19
     */
    private static void parsingReceiveBag() {
        List<String> bagNoList = (List<String>) keyMap.get("bagNoList");
        List<String> bagKeyList = (List<String>) keyMap.get("bagKey");
        for (String key : bagKeyList) {
            // 有行李号，打印指定行李
            if (CollUtil.isNotEmpty(bagNoList)) {
                for (String bagNo : bagNoList) {
                    log.info("BAG打印行李牌：{}-{}", key, bagNo);
                    List<String> luggagePrintData = getLuggagePrintData(key, bagNo);
                    luggagePrintData.forEach(PacketCapture::printLuggage);
                }
            }
            // 无行李号，打印该旅客所有行李
            else {
                log.info("BAG打印所有行李牌：{}-R", key);
                List<String> luggagePrintData = getLuggagePrintData(key, "R");
                luggagePrintData.forEach(PacketCapture::printLuggage);
            }
        }
    }

    /**
     * Title: parsingReceiveBc
     * Description: 解析重打登机牌返回数据，通过BC发送数据解析的key查询登机牌打印数据进行打印<br>
     *
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 15:20
     */
    private static void parsingReceiveBc() {
        List<String> keyList = (List<String>) keyMap.get("bcKey");
        for (String key : keyList) {
            log.info("BC打印登机牌：{}", key);
            List<String> valueList = getBoardingPrintData(key);
            for (String value : valueList) {
                printBoarding(value);
            }
        }
    }

    /**
     * Title: parsingSendBag
     * Description: 解析BAG发送的数据<br>
     *
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 16:44
     */
    private static void parsingSendBag(String firstLine) {
        List<String> bagNoList = new ArrayList<>();
        String param = firstLine.replace("BAG:", "").replace("BAG ", "").trim();
        if (param.equals(firstLine)) {
            param = param.substring(3).trim();
        }
        if (ReUtil.isMatch(BAG_NO_PATTERN, param)) {
            String[] split = param.replace("R", "").split(",")[1].split("/");
            bagNoList = CollUtil.toList(split);
            keyMap.put("bagNoList", bagNoList);
            String index = param.split(",")[0];
            List<String> indexList = CollUtil.toList(index);
            String lastCmd = keyMap.get("lastCmd").toString();
            if (lastCmd.startsWith("PD")) {
                putBagKey("pdKey", "pd", indexList);
            } else if (lastCmd.startsWith("PA")) {
                putBagKey("paKey", "pa", indexList);
            } else if (lastCmd.startsWith("PU")) {
                putBagKey("puKey", "pu", indexList);
            } else if (lastCmd.startsWith("PR")) {
                putBagKey("prKey", "pr", indexList);
            } else if (lastCmd.startsWith("PW")) {
                putBagKey("pwKey", "pw", indexList);
            } else if (lastCmd.startsWith("JC")) {
                putBagKey("jcKey", "jc", indexList);
            } else if (lastCmd.startsWith("HBPA")) {
                putBagKey("hbpaKey", "hbpa", indexList);
            } else if (lastCmd.startsWith("HBPU")) {
                putBagKey("hbpuKey", "hbpu", indexList);
            } else if (lastCmd.startsWith("HBPR")) {
                putBagKey("hbprKey", "hbpr", indexList);
            } else if (lastCmd.startsWith("HBPW")) {
                putBagKey("hbpwKey", "hbpw", indexList);
            } else if (lastCmd.startsWith("HBJC")) {
                putBagKey("hbjcKey", "hbjc", indexList);
            }
        }
        // 不输入行李号的BAG指令，直接放一个空list
        else if (ReUtil.isMatch(NO_BAG_NO_PATTERN, param)) {
            String[] split = param.replace(",R", "").split(";");
            List<String> indexList = CollUtil.toList(split);
            keyMap.put("bagNoList", bagNoList);
            String lastCmd = keyMap.get("lastCmd").toString();
            if (lastCmd.startsWith("PD")) {
                putBagKey("pdKey", "pd", indexList);
            } else if (lastCmd.startsWith("PA")) {
                putBagKey("paKey", "pa", indexList);
            } else if (lastCmd.startsWith("PU")) {
                putBagKey("puKey", "pu", indexList);
            } else if (lastCmd.startsWith("PR")) {
                putBagKey("prKey", "pr", indexList);
            } else if (lastCmd.startsWith("PW")) {
                putBagKey("pwKey", "pw", indexList);
            } else if (lastCmd.startsWith("JC")) {
                putBagKey("jcKey", "jc", indexList);
            } else if (lastCmd.startsWith("HBPA")) {
                putBagKey("hbpaKey", "hbpa", indexList);
            } else if (lastCmd.startsWith("HBPU")) {
                putBagKey("hbpuKey", "hbpu", indexList);
            } else if (lastCmd.startsWith("HBPR")) {
                putBagKey("hbprKey", "hbpr", indexList);
            } else if (lastCmd.startsWith("HBPW")) {
                putBagKey("hbpwKey", "hbpw", indexList);
            } else if (lastCmd.startsWith("HBJC")) {
                putBagKey("hbjcKey", "hbjc", indexList);
            }
        }
    }

    /**
     * Title: parsingSendBc
     * Description: 解析BC发送的数据<br>
     *
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2023/11/1 16:45
     */
    private static void parsingSendBc(String firstLine) {
        String bcParam = firstLine.replace("BC:", "").replace("BC ", "");
        if (bcParam.startsWith("BC")) {
            bcParam = bcParam.substring(2);
        }
        if (ReUtil.isMatch(BC_PARAM_PATTERN1, bcParam)) {
            List<String> allGroups = ReUtil.getAllGroups(BC_PARAM_PATTERN1, bcParam);
            String flightNoAndDate = allGroups.get(1);
            if ("*".equals(flightNoAndDate)) {
                if (!keyMap.containsKey("ftKey")) {
                    log.error("没有默认航班");
                    return;
                }
                String ft = keyMap.get("ftKey").toString();
                String[] ftSplit = ft.split("/");
                String date = ftSplit[1];
                if (date.matches("[.+-]")) {
                    date = DateUtils.ymd2Com(DateUtils.com2ymd(date));
                    ft = StrUtil.format("{}/{}", ftSplit[0], date);
                }
                flightNoAndDate = ft;
            } else {
                flightNoAndDate = flightNoAndDate.substring(0, flightNoAndDate.length() - 4);
            }
            Matcher matcher = BN_PATTERN.matcher(bcParam);
            List<String> keyList = new ArrayList<>();
            while (matcher.find()) {
                String group = matcher.group();
                String key = StrUtil.format("{}/{}", flightNoAndDate, group.replace(",", "").replace(";", "").trim());
                keyList.add(key);
            }
            keyMap.put("bcKey", keyList);
        } else if (ReUtil.isMatch(BC_PARAM_PATTERN2, bcParam)) {
            List<String> allGroups = ReUtil.getAllGroups(BC_PARAM_PATTERN2, bcParam);
            int start = Integer.parseInt(allGroups.get(1));
            int end = StrUtil.isNotEmpty(allGroups.get(4)) ? Integer.parseInt(allGroups.get(4)) : start;
            List<String> indexList = new ArrayList<>();
            for (int i = start; i <= end; i++) {
                indexList.add(StrUtil.toString(i));
            }
            // PD参数判断
            boolean fromPd = bcParam.contains("PD");
            if (fromPd) {
                putBcKey("pdKey", "pd", indexList);
            } else {
                String lastCmd = keyMap.get("lastCmd").toString();
                if (lastCmd.startsWith("PD")) {
                    putBcKey("pdKey", "pd", indexList);
                } else if (lastCmd.startsWith("PA")) {
                    putBcKey("paKey", "pa", indexList);
                } else if (lastCmd.startsWith("PU")) {
                    putBcKey("puKey", "pu", indexList);
                } else if (lastCmd.startsWith("PR")) {
                    putBcKey("prKey", "pr", indexList);
                } else if (lastCmd.startsWith("PW")) {
                    putBcKey("pwKey", "pw", indexList);
                } else if (lastCmd.startsWith("JC")) {
                    putBcKey("jcKey", "jc", indexList);
                } else if (lastCmd.startsWith("HBPA")) {
                    putBcKey("hbpaKey", "hbpa", indexList);
                } else if (lastCmd.startsWith("HBPU")) {
                    putBcKey("hbpuKey", "hbpu", indexList);
                } else if (lastCmd.startsWith("HBPR")) {
                    putBcKey("hbprKey", "hbpr", indexList);
                } else if (lastCmd.startsWith("HBPW")) {
                    putBcKey("hbpwKey", "hbpw", indexList);
                } else if (lastCmd.startsWith("HBJC")) {
                    putBcKey("hbjcKey", "hbjc", indexList);
                }
            }
        } else if (ReUtil.isMatch(BC_PARAM_PATTERN3, bcParam)) {
            // PD参数判断
            boolean fromPd = bcParam.contains("PD");
            String psgNumStr = bcParam.replace("PD", "").replace(",R", "");
            String[] psgNumStrSplit = psgNumStr.split(";");
            List<String> indexList = CollUtil.toList(psgNumStrSplit);
            if (fromPd) {
                putBcKey("pdKey", "pd", indexList);
            } else {
                String lastCmd = keyMap.get("lastCmd").toString();
                if (lastCmd.startsWith("PD")) {
                    putBcKey("pdKey", "pd", indexList);
                } else if (lastCmd.startsWith("PA")) {
                    putBcKey("paKey", "pa", indexList);
                } else if (lastCmd.startsWith("PU")) {
                    putBcKey("puKey", "pu", indexList);
                } else if (lastCmd.startsWith("PR")) {
                    putBcKey("prKey", "pr", indexList);
                } else if (lastCmd.startsWith("PW")) {
                    putBcKey("pwKey", "pw", indexList);
                } else if (lastCmd.startsWith("JC")) {
                    putBcKey("jcKey", "jc", indexList);
                } else if (lastCmd.startsWith("HBPA")) {
                    putBcKey("hbpaKey", "hbpa", indexList);
                } else if (lastCmd.startsWith("HBPU")) {
                    putBcKey("hbpuKey", "hbpu", indexList);
                } else if (lastCmd.startsWith("HBPR")) {
                    putBcKey("hbprKey", "hbpr", indexList);
                } else if (lastCmd.startsWith("HBPW")) {
                    putBcKey("hbpwKey", "hbpw", indexList);
                } else if (lastCmd.startsWith("HBJC")) {
                    putBcKey("hbjcKey", "hbjc", indexList);
                }
            }
        }
    }

    private static void putBcKey(String cmdKey, String cmd, List<String> indexList) {
        if (keyMap.containsKey(cmdKey)) {
            String value = keyMap.get(cmdKey).toString();
            Map<String, String> map = JSONUtil.toBean(value, Map.class);
            String flightNoAndDate = keyMap.get(cmd).toString();
            List<String> keyList = new ArrayList<>();
            for (String s : indexList) {
                String key = StrUtil.format("{}/{}", flightNoAndDate, map.get(s));
                keyList.add(key);
            }
            keyMap.put("bcKey", keyList);
        }
    }

    private static void putBagKey(String cmdKey, String cmd, List<String> indexList) {
        if (keyMap.containsKey(cmdKey)) {
            String value = keyMap.get(cmdKey).toString();
            Map<String, String> map = JSONUtil.toBean(value, Map.class);
            String flightNoAndDate = keyMap.get(cmd).toString();
            List<String> keyList = new ArrayList<>();
            for (String s : indexList) {
                String key = StrUtil.format("{}/{}", flightNoAndDate, map.get(s));
                keyList.add(key);
            }
            keyMap.put("bagKey", keyList);
        }
    }

    /**
     * Title: parsingSendFt
     * Description: 记录FT默认航班号日期<br>
     *
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2024/5/20 11:49
     */
    private static void parsingSendFt(String firstLine) {
        String ft = firstLine.replace("FT:", "").replace("FT ", "");
        if (ft.equals(firstLine)) {
            ft = ft.substring(2);
        }
        keyMap.put("ftKey", ft);
    }

    /**
     * Title: parsingSendPcmd
     * Description: 记录PA\PD\PR\PU\PW结果<br>
     *
     * @param firstLine
     * @return void
     * <AUTHOR>
     * @date 2024/5/20 13:32
     */
    private static void parsingReceivePcmd(String firstLine, String cmd, String cmdKey) {
        String param = firstLine.replace(cmd + ":", "").replace(cmd + " ", "").trim();
        if (param.equals(firstLine)) {
            param = param.substring(2).trim();
        }
        String urlKey;
        if (param.startsWith("*")) {
            if (keyMap.containsKey("ftKey")) {
                urlKey = keyMap.get("ftKey").toString();
                String[] split = urlKey.split("/");
                String date = DateUtils.ymd2Com(DateUtils.com2ymd(split[1]));
                urlKey = StrUtil.format("{}/{}", split[0], date);
            } else {
                log.error("没有默认航班");
                return;
            }
        } else {
            String[] split = param.split("/");
            String date = DateUtils.ymd2Com(DateUtils.com2ymd(split[1].length() >= 5 ? split[1].substring(0, 5) : split[1]));
            urlKey = StrUtil.format("{}/{}", split[0], date);
        }
        String url = StrUtil.format("http://{}:{}/mnjx/print/getSavedForPrintDataList?key={}", ip, webPort, StrUtil.format("{}/{}", urlKey, cmd));
        log.info("请求url：{}", url);
        try {
            String result = HttpUtil.get(url, 10000);
            if (StrUtil.isNotEmpty(result)) {
                Map<String, Object> map = JSONUtil.toBean(result, Map.class);
                if ((int) map.get("code") == 200) {
                    log.info("获取{}结果", cmd);
                    String value = StrUtil.toString(map.get("data"));
                    keyMap.put(cmdKey, value);
                    keyMap.put(cmd.toLowerCase(), urlKey);
                    log.info("keyMap：{}", keyMap.toString());
                }
            } else {
                log.info("未查到数据");
            }
        } catch (Exception e) {
            log.error("web接口调用失败");
            log.error(e.getMessage());
        }
    }

    /**
     * Title: getConfiguration
     * Description: 获取配置文件环境变量<br>
     *
     * @param
     * @return {@link CaptureConfiguration}
     * <AUTHOR>
     * @date 2023/11/1 11:58
     */
    private static CaptureConfiguration getConfiguration() {
        InputStream inputStream = PacketCapture.class.getClassLoader().getResourceAsStream("application.yml");
        Yaml yaml = new Yaml();
        return yaml.loadAs(inputStream, CaptureConfiguration.class);
    }
}

class PacketReceive implements PacketReceiver {

    private final int portNum;

    public PacketReceive(int portNum) {
        this.portNum = portNum;
    }

    @Override
    public void receivePacket(Packet packet) {
        byte[] data = packet.data;
        // 解析接收的数据包
        if (((TCPPacket) packet).src_port == portNum && ObjectUtil.isNotEmpty(data) && data.length != 0) {
            PacketCapture.parsingReceiveData(data);
        }
        // 解析发送的数据包
        else if (((TCPPacket) packet).dst_port == portNum && ObjectUtil.isNotEmpty(data) && data.length != 0) {
            PacketCapture.parsingSendData(data);
        }
    }
}