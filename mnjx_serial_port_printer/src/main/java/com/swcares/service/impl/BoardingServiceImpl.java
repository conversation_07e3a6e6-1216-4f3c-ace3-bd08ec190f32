package com.swcares.service.impl;

import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.core.config.PrinterConfiguration;
import com.swcares.core.config.SerialPortManager;
import com.swcares.core.protocol.atp.AtpDataHeader;
import com.swcares.core.protocol.atp.AtpDataPayload;
import com.swcares.core.protocol.atp.AtpDataPayloadItem;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.service.IBoardingService;
import com.swcares.vo.BoardingVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/14 18:41
 */
@Slf4j
@Service
public class BoardingServiceImpl implements IBoardingService {

    @Resource
    private PrinterConfiguration printerConfiguration;

    private String generatePrintData(BoardingVo vo) {
        // 数据流的头部
        String atpDataHeader = AtpDataHeader.toStr();
        // 数据流的主体
        String atpDataPayload = this.getAtpDataPayloadShanghai(vo);

        // 构建打印数据
        // 海联学校标签打印机的格式
        if ("hailian".equalsIgnoreCase(printerConfiguration.getDataHeadType())) {
            return StrFormatter.format("\u0002{}#{}\u0003", atpDataHeader, atpDataPayload);
        } else {
            // 霍尼韦尔、IER标签打印机的格式
            return StrFormatter.format("{}#{}", atpDataHeader, atpDataPayload);
        }
    }

    @Override
    public void print(BoardingVo vo) throws UnifiedResultException {
        // 生成打印数据字符串
        String printData = this.generatePrintData(vo);
        log.info("登机牌数据流：{}", printData);
        this.transmitData(printData);
    }

    /**
     * 串口打印数据
     *
     * @param tData 要打印的数据
     */
    public void transmitData(String tData) {
        try {
            // 1、构建打印数据
            byte[] data = ArrayUtil.addAll(new byte[]{0x2}, tData.getBytes(CharsetUtil.GBK), new byte[]{0x3});
//            byte[] bytes = ConvertHexStrAndStrUtils.hexStrToBytes(tData);
            // 2、打印数据
            this.printDataByComPort(data);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取可用串口
     * 一般的客户机上的串口数量都是有限的，除特别需要外，一般都是一个，
     * 所以我们这里获取可以用的串口
     *
     * @return 打印串口
     */
    private void printDataByComPort(byte[] data) {
        SerialPortManager.connectSerialPort(printerConfiguration.getCpCom());
        SerialPortManager.sendSerialPortData(data);
        SerialPortManager.closeSerialPort();
    }

    /**
     * 获得负载的数据
     *
     * @param vo 登机牌数据
     * @return 登机牌对应需要打印的字符串
     * 要打印的数据：CP#1C01#01V#02禽溶#03qinrong#04CA#058541#06SHA#07上海#08TFU#09成都#1028FEB24#11018#12K#13#1425#150710#1623D#179991101322660#18ETKT#19018      #
     * 登机牌数据流：CP#1C01#01V#05QIUXINYU#07邱新雨#08HU#10HU7602#152025#1828FEB24#2028A#2125#298801101363376#32001#33K#35北京#36PKX#61001      #
     */
    private String getAtpDataPayload(BoardingVo vo) {
        List<AtpDataPayloadItem> atpDataPayloadItems = new ArrayList<>();
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("07").content(vo.getNameZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("05").content(PinyinUtil.getPinyin(vo.getNameZh(), StrUtil.EMPTY).toUpperCase()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("08").content(vo.getFlightNo().substring(0, 2)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("10").content(vo.getFlightNo()).build());
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("06").content(vo.getFrom()).build());
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("07").content(vo.getFromZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("36").content(vo.getTo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("35").content(vo.getToZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("18").content(vo.getFlightDate()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("32").content(vo.getBoardingNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("33").content(vo.getCabin()).build());
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("13").content(vo.getFrequentNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("21").content(vo.getGate()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("15").content(Optional.ofNullable(vo.getBoardingTime()).orElse(StrUtil.EMPTY)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("20").content(vo.getSeatNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("29").content(vo.getTicketNo()).build());
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("18").content(vo.getTicketPre()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("61").content(vo.getBarcode()).build());
        return AtpDataPayload.builder().atpDataPayloadItems(atpDataPayloadItems).build().toStr();
    }

    /**
     * 上航职-霍尼韦尔打印机登机牌数据流模板
     * @param vo
     * @return
     */
    private String getAtpDataPayloadShanghai(BoardingVo vo) {
        List<AtpDataPayloadItem> atpDataPayloadItems = new ArrayList<>();
        //中文姓名
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("02").content(vo.getNameZh()).build());
        //英文姓名
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("03").content(PinyinUtil.getPinyin(vo.getNameZh(), StrUtil.EMPTY).toUpperCase()).build());
        //航司
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("04").content(vo.getFlightNo().substring(0, 2)).build());
        //航班号 去掉航司
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("05").content(vo.getFlightNo().substring(2)).build());
        //始发站
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("06").content(vo.getFrom()).build());
       //始发站中文
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("07").content(vo.getFromZh()).build());
        //目的站
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("08").content(vo.getTo()).build());
        //目的站中文
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("09").content(vo.getToZh()).build());
        //航班日期
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("10").content(vo.getFlightDate()).build());
        //登机序号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("11").content(vo.getBoardingNo()).build());
        //舱位
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("12").content(vo.getCabin()).build());
        //常旅客卡号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("13").content(vo.getFrequentNo()).build());
        //登机口
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("14").content(vo.getGate()).build());
        //登机时间
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("15").content(Optional.ofNullable(vo.getBoardingTime()).orElse(StrUtil.EMPTY)).build());
        //座位号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("16").content(vo.getSeatNo()).build());
        //票号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("17").content(vo.getTicketNo()).build());
        //票号前缀
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("18").content(vo.getTicketPre()).build());
        //条形码信息
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("19").content(vo.getBarcode()).build());
        return AtpDataPayload.builder().atpDataPayloadItems(atpDataPayloadItems).build().toStr();
    }
}
