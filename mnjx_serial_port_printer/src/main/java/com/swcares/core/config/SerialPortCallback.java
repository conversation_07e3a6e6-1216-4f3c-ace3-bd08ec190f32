package com.swcares.core.config;

import com.swcares.core.utils.ConvertHexStrAndStrUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/14 18:39
 */
@Slf4j
public class SerialPortCallback {
    public void dataAvailable() {
        try {
            //当前监听器监听到的串口返回数据 back
            byte[] back = SerialPortManager.readSerialPortData();
            log.info("back-" + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())) + "--" + ConvertHexStrAndStrUtils.bytesToHexStr(back));
            String s = ConvertHexStrAndStrUtils.bytesToHexStr(back);
            log.info("rev--data:" + s);
        } catch (Exception e) {
            log.info(e.toString());
        }
    }
}
