package com.swcares.core.config;

import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import com.fazecast.jSerialComm.SerialPortEvent;

/**
 * <AUTHOR>
 * @date 2023/11/14 18:38
 */
public class SerialPortListener implements SerialPortDataListener {

    private final SerialPortCallback serialPortCallback;

    public SerialPortListener(SerialPortCallback serialPortCallback) {
        this.serialPortCallback = serialPortCallback;
    }

    @Override
    public int getListeningEvents() { //必须是return这个才会开启串口工具的监听
        return SerialPort.LISTENING_EVENT_DATA_AVAILABLE;
    }

    @Override
    public void serialEvent(SerialPortEvent serialPortEvent) {
        if (serialPortCallback != null) {
            serialPortCallback.dataAvailable();
        }
    }
}
