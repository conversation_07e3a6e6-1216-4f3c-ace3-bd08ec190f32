package com.swcares.controller;

import com.swcares.core.config.PrinterConfiguration;
import com.swcares.core.config.SerialPortManager;
import com.swcares.core.utils.ConvertHexStrAndStrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14 18:37
 */
@Slf4j
@Controller
@RequestMapping("/serialPort")
public class SerialPortController {

    @Resource
    private PrinterConfiguration printerConfiguration;

    @GetMapping("/list")
    @ResponseBody
    public List<String> listPorts() {
        log.info("获取到的配置文件串口：登机牌：{}，行李牌：{}", printerConfiguration.getCpCom(), printerConfiguration.getBtpCom());
        List<String> portList = SerialPortManager.getSerialPortList();
        if (!portList.isEmpty()) {
            return portList;
        }
        return Collections.emptyList();
    }

    @PostMapping("/send/{hexData}")
    @ResponseBody
    public String sendPorts(@PathVariable("hexData") String hexData) {
        if (SerialPortManager.SERIAL_PORT_STATE) {
            SerialPortManager.sendSerialPortData(ConvertHexStrAndStrUtils.hexStrToBytes(hexData));
            return "success";
        }
        return "fail";
    }
}
