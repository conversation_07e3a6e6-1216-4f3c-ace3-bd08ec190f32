echo "STS 开始构建"

#切换分支，否则后面使用release插件会报错
git checkout dev_netty
##################################################
# 对切换的分支进行 release prepare修改当前工程版本号
# 第一步对当前的版本进行release
# 第二步对当前版本+1
##################################################
mvn -DignoreSnapshots=true release:clean release:prepare

#取出当前release版本号
###################################################
# 获取所有的tag，并且每行输出都放入{}中，后面的指令对每行输出都进行操作
# 删除本地的tag 因为本地与远程的不一直，所以需要先删除
git tag | xargs -I {} git tag -d {}
# 重新获取远程的TAG
git pull origin dev_netty --rebase --tag
# 获取最新的tag
releaseVersion=$(git tag | sed  "s/[a-zA-Z]*-//g" | sort -nr | head -1)

# 要发布的版本
echo "release版本 ${releaseVersion}"

#发包
mvn release:perform

#将release版本号传递给STS根的pom
echo version=${releaseVersion} > releaseInfo.txt
echo "STS 构建结束"