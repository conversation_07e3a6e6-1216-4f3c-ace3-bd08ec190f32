#!/bin/bash
# 清理原有的yum源
sudo rm -rf /etc/yum.repos.d/*
# 使用阿里的yum源
sudo curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo
# 卸载旧版本或残留文件
sudo yum remove docker docker-common docker-selinux docker-engine
# 安装需要的软件包， yum-util 提供yum-config-manager功能，另外两个是devicemapper驱动依赖的
sudo yum install -y yum-utils device-mapper-persistent-data lvm2

# 设置yum源
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
# 将docker的官方源替换为清华源
sudo sed -i 's|download.docker.com|mirror.tuna.tsinghua.edu.cn/docker-ce|g' /etc/yum.repos.d/docker-ce.repo
# 清理元数据
sudo yum clean all
# 安装docker
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 当前用户添加到docker用户组
sudo usermod -aG docker $USER
# 更新用户组
newgrp docker

# 启动docker
sudo systemctl start docker
# 开机启动docker
sudo systemctl enable docker
# 输出docker详细信息
sudo docker info