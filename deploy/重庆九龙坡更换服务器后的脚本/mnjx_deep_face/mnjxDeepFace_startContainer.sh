basepath=$(cd `dirname $0`; pwd)
echo "重新部署mnjx_deep_face系统容器"
echo "删除mnjx_deep_face系统容器"
containerId=$(docker ps -a | grep -i mnjx_deep_face | awk '{print $12}')
if [ ! "${containerId}" ]; then
  echo "mnjx_deep_face container is null"
else
  docker rm -f mnjx_deep_face
  echo "mnjx_deep_face系统容器删除成功"
fi
echo "删除本地mnjx_deep_face镜像"
imageId=$(docker images | grep -i mnjx_deep_face | awk '{print $3}')
if [ ! "${imageId}" ]; then
  echo "mnjx_deep_face image is null"
else
  docker rmi -f "${imageId}"
  echo "mnjx_deep_face镜像删除成功"
fi
echo "因为镜像为私有的，所以必须要登录：登录镜像仓库"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "重新构建容器所有容器对象"
# -d 后端运行容器对象，并且打印容器名
# -f 指定编排的文件
docker-compose -f $basepath/mnjxDeepFace_docker-compose.yml up -d
