basepath=$(cd `dirname $0`; pwd)
echo "重新部署mnjx_self_check_in_front系统容器"
echo "删除mnjx_self_check_in_front系统容器"
docker rm -f $(docker ps -qa -f name="mnjx_self_check_in_front$")
echo "删除本地mnjx_self_check_in_front镜像"
docker rmi -f $(docker images "*/*/mnjx_self_check_in_front" -aq)
echo "因为镜像为私有的，所以必须要登录：登录镜像仓库"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "重新构建容器所有容器对象"
# -d 后端运行容器对象，并且打印容器名
# -f 指定编排的文件
docker-compose -f $basepath/mnjxSelfCheckInFront_docker-compose.yml up -d
