package com.swcares;

import cn.hutool.core.date.DateUtil;
import com.swcares.handler.PrinterWebSocketServerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.lang.Nullable;

import java.util.Optional;

/**
 * Hello world!
 */
@Slf4j
@EnableCaching
@SpringBootApplication
public class PrinterWsStarter implements ApplicationRunner, ApplicationListener<ContextClosedEvent> {

    private Channel webServerChannel;

    public static void main(String[] args) {
        SpringApplication.run(PrinterWsStarter.class, args);
    }

    /**
     * 这个是容器在启动的时候，当前的bean对象实例化了，则这个方法运行完毕
     *
     * @param args 启动参数
     */
    @Override
    public void run(ApplicationArguments args) {
        log.info("启动程序启动：{}", DateUtil.now());
        this.startMonitor();
    }

    public void startMonitor() {
        //创建2个线程组
        EventLoopGroup bossGroup = new NioEventLoopGroup(4);
        EventLoopGroup workerGroup = new NioEventLoopGroup(16);

        //创建服务器监听
        ServerBootstrap serverBootstrap = new ServerBootstrap();

        //添加前置参数
        serverBootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .handler(new LoggingHandler(LogLevel.DEBUG))
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ChannelPipeline pipeline = ch.pipeline();
                        //因为基于http协议，使用http的编码和解码器
                        pipeline.addLast(new HttpServerCodec());
                        // 粘包问题处理
                        pipeline.addLast(new ChunkedWriteHandler());
                        //是以块方式写，添加ChunkedWriteHandler处理器
                        pipeline.addLast(new HttpObjectAggregator(1024 * 1024 * 1024));

                        pipeline.addLast(new WebSocketServerProtocolHandler("/print"));

                        pipeline.addLast(new PrinterWebSocketServerHandler());
                    }
                });
        ChannelFuture channelFuture = serverBootstrap.bind(18351);
        // 启动成功后的回显
        channelFuture.addListener((ChannelFutureListener) cf -> {
            if (cf.isDone()) {
                log.info("*****************************************");
                log.info("* PRINTER WS 监听启动,正在监听 18351 端口   *");
                log.info("*****************************************");
                log.info("正在等待用户连接...");
            } else {
                log.error("服务器启动失败......");
            }
        });

        webServerChannel = channelFuture.channel();
        ChannelFuture closeChannelFuture = webServerChannel.closeFuture();
        // 资源回收
        closeChannelFuture.addListener((ChannelFutureListener) cf -> {
            if (cf.isDone()) {
                bossGroup.shutdownGracefully();
                workerGroup.shutdownGracefully();
                log.info("资源清理成功......");
            } else {
                log.error("资源清理失败......");
            }
        });
    }

    @Override
    public void onApplicationEvent(@Nullable ContextClosedEvent event) {
        Optional.ofNullable(event).ifPresent(contextClosedEvent -> {
            log.info("服务器发生关闭事件。事件内类：{}；关闭时间：{}", event.getSource(), event.getTimestamp());
            Optional.ofNullable(webServerChannel).ifPresent(ChannelOutboundInvoker::close);
        });
    }
}
