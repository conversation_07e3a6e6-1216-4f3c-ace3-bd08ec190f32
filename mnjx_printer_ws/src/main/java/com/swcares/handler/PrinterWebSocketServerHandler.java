package com.swcares.handler;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/18 14:45
 */
@Slf4j
public class PrinterWebSocketServerHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {

    private final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame msg) {
        Map<String, Object> result = new HashMap<>();
        log.info("接受消息：{}", msg.text());
        log.info("开始打印登机牌");
        String url = "http://127.0.0.1:18350/printer/boarding/print";
        JSONObject param = JSONUtil.parseObj(msg.text());
        result.put("seatNo", param.getStr("seatNo"));
        try {
            HttpResponse response = HttpUtil.createPost(url)
                    .body(param.toString())
                    .execute();
            String body = response.body();
            JSONObject bodyJson = JSONUtil.parseObj(body);
            int code = Integer.parseInt(bodyJson.get("code").toString());
            if (code == 200) {
                result.put("code", 200);
            } else {
                log.error(bodyJson.get("message").toString());
                result.put("code", 400);
            }
        } catch (Exception e) {
            log.error("登机牌打印接口连接失败");
            e.printStackTrace();
            log.error(e.getMessage());
            result.put("code", 400);
        }
        ctx.channel().writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(result)));
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        log.info("handlerAdded被调用：{}", ctx.channel().id().asLongText());
        log.info("handlerAdded被调用：{}", ctx.channel().id().asShortText());
        log.info("{}  用户已连接！", SDF.format(new Date()) );
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) {
        log.info("handlerRemoved被调用：{}", ctx.channel().id().asLongText());
        log.info("{}  用户已断开连接", SDF.format(new Date()));
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("发生异常：{}", cause.getMessage());
        ctx.close();
    }
}
