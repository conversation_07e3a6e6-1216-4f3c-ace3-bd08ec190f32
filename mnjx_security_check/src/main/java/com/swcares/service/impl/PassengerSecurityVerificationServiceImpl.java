package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.mapper.PassengerSecurityVerificationMapper;
import com.swcares.obj.dto.CertificateInfoDto;
import com.swcares.obj.dto.CheckInDto;
import com.swcares.obj.dto.PassengerFlightMsgDto;
import com.swcares.obj.dto.PassengerSecurityVerificationDto;
import com.swcares.obj.vo.PassengerCheckInVo;
import com.swcares.obj.vo.PassengerFlightMsgVo;
import com.swcares.obj.vo.PassengerSecurityVerificationVo;
import com.swcares.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PassengerSecurityVerificationServiceImpl implements IPassengerSecurityVerificationService {

    /**
     * 证件号正则
     */
    private static final Pattern REG_FOID = Pattern.compile("SSR\\sFOID\\s[A-Z0-9]{2}\\s\\w{3}\\s(\\w+)/\\w{2,}");

    @Resource
    private PassengerSecurityVerificationMapper passengerSecurityVerificationMapper;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxVerifyInfoService iMnjxVerifyInfoService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    @Resource
    private IMnjxUnpackInfoService iMnjxUnpackInfoService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    /**
     * 特殊旅客
     */
    private final List<String> specialPassengers = Arrays.asList("GMJC", "BLND", "DEAF", "DEPA", "DEPU", "STCR", "TWOV", "BSCT", "AVIH", "PETC", "SLPR",
            "WCBD", "WCBW", "WCHC", "WCHR", "WCHS", "WCMP", "WCOB", "WEAP");

    @Override
    public UnifiedResult passengerSecurityVerification(PassengerSecurityVerificationDto passengerSecurityVerification) throws UnifiedResultException {
        // 安检验证信息 初始化
        PassengerSecurityVerificationVo passengerSecurityVerificationVo = new PassengerSecurityVerificationVo();
        Map<String, Object> checkInMap = new HashMap<>(1024);
        checkInMap.put("status", false);
        checkInMap.put("msg", "未值机");
        passengerSecurityVerificationVo.setCheckIn(checkInMap);
        Map<String, Object> passengerLuggageMap = new HashMap<>(1024);
        passengerLuggageMap.put("status", true);
        passengerLuggageMap.put("msg", "旅客无交运行李");
        passengerSecurityVerificationVo.setPassengerLuggage(passengerLuggageMap);
        Map<String, Object> isolationZoneMap = new HashMap<>(1024);
        isolationZoneMap.put("status", true);
        isolationZoneMap.put("msg", "隔离区正常");
        passengerSecurityVerificationVo.setIsolationZone(isolationZoneMap);
        Map<String, Object> executeControlMap = new HashMap<>(1024);
        executeControlMap.put("status", true);
        executeControlMap.put("msg", "非异常布控，正常");
        passengerSecurityVerificationVo.setExecuteControl(executeControlMap);
        Map<String, Object> certificateMap = new HashMap<>(1024);
        certificateMap.put("status", false);
        certificateMap.put("msg", "证件信息异常，非本人");
        passengerSecurityVerificationVo.setCertificate(certificateMap);
        Map<String, Object> flightStatusMap = new HashMap<>(1024);
        flightStatusMap.put("status", true);
        flightStatusMap.put("msg", "航班正常");
        passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
        Map<String, Object> specialPassengersMap = new HashMap<>(1024);
        specialPassengersMap.put("status", true);
        specialPassengersMap.put("msg", "非特殊旅客，正常");
        passengerSecurityVerificationVo.setSpecialPassengers(specialPassengersMap);
        Map<String, Object> numberOfSecurityCheckMap = new HashMap<>(1024);
        numberOfSecurityCheckMap.put("status", true);
        numberOfSecurityCheckMap.put("msg", "初次安检");
        passengerSecurityVerificationVo.setNumberOfSecurityChecks(numberOfSecurityCheckMap);

        // 值机信息
        CheckInDto checkInInfo = passengerSecurityVerification.getCheckInInfo();
        // 证件信息
        CertificateInfoDto certificateInfo = passengerSecurityVerification.getCertificateInfo();

        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, checkInInfo.getFlightNo())
                .one();
        if (ObjectUtil.isEmpty(mnjxFlight)) {
            throw new UnifiedResultException("航班数据异常");
        }

        // 航班状态
        MnjxPlanFlight planFlight = passengerSecurityVerificationMapper.retrievePlanFlight(checkInInfo.getFlightNo(), checkInInfo.getFlightDate());
        // 查询共享航班
        if (ObjectUtil.isEmpty(planFlight)) {
            planFlight = passengerSecurityVerificationMapper.retrievePlanFlight(mnjxFlight.getCarrierFlight(), checkInInfo.getFlightDate());
        }
        if (ObjectUtil.isEmpty(planFlight)) {
            throw new UnifiedResultException("航班数据异常");
        }
        String flightStatus = planFlight.getFlightStatus();
        if (flightStatus.equalsIgnoreCase(Constant.FLIGHT_STATUS_CANCEL)) {
            flightStatusMap.put("status", false);
            flightStatusMap.put("msg", "航班异常，当前航班已取消");
            passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
        } else if (flightStatus.equalsIgnoreCase(Constant.FLIGHT_STATUS_DELETE)) {
            flightStatusMap.put("status", false);
            flightStatusMap.put("msg", "航班异常，当前航班已删除");
            passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
        } else {
            flightStatusMap.put("status", false);
            flightStatusMap.put("msg", "航班异常，未开始安检");
            passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
        }

        // 值机信息
        // 验证值机信息
        PassengerCheckInVo passengerCheckInVo = passengerSecurityVerificationMapper.retrievePassengerCheckIn(checkInInfo);
        if (ObjectUtil.isEmpty(passengerCheckInVo)) {
            // 如果找不到查询共享航班信息
            checkInInfo.setFlightNo(mnjxFlight.getFlightNo());
            passengerCheckInVo = passengerSecurityVerificationMapper.retrievePassengerCheckIn(checkInInfo);
        }
        if (ObjectUtil.isNotEmpty(passengerCheckInVo)) {
            // 航班起飞时间
            DateTime fltDateTime = DateUtil.parse(StrUtil.format("{} {}", passengerCheckInVo.getFlightDate(), DateUtils.comHm2hmis(passengerCheckInVo.getEstimateOff())));
            // 当前时间
            DateTime nowDateTime = DateUtil.parse(DateUtil.now());
            // 当前日期大于航班起飞日期
            if (fltDateTime.before(nowDateTime)) {
                flightStatusMap.put("status", false);
                flightStatusMap.put("msg", "航班异常，当前航班已起飞");
                passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(fltDateTime, -2)) >= 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -30)) <= 0) {
                flightStatusMap.put("status", true);
                flightStatusMap.put("msg", "航班正常");
                passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(fltDateTime, -2)) > 0) {
                flightStatusMap.put("status", false);
                flightStatusMap.put("msg", "航班异常，未开始安检");
                passengerSecurityVerificationVo.setFlightStatus(flightStatusMap);
            }

            // 旅客状态
            String ckiStatus = passengerCheckInVo.getCkiStatus();
            if (ckiStatus.equalsIgnoreCase(Constant.ACC)) {
                checkInMap.put("status", true);
                checkInMap.put("msg", "值机信息正常");
                passengerSecurityVerificationVo.setCheckIn(checkInMap);
            } else if (ckiStatus.equalsIgnoreCase(Constant.GT)) {
                checkInMap.put("msg", "已登机");
                passengerSecurityVerificationVo.setCheckIn(checkInMap);
            }
            // 电子客票状态
            MnjxPnrNmTicket pnrNmTicket = passengerSecurityVerificationMapper.retrieveTicketStatus(passengerCheckInVo.getPnrNmId());
            if (ObjectUtil.isNotEmpty(pnrNmTicket)) {
                // 处理电子客票状态
                String pnrSegId = passengerCheckInVo.getPnrSegId();
                if (pnrNmTicket.getS1Id().equals(pnrSegId)) {
                    String ticketStatus = pnrNmTicket.getTicketStatus1();
                    if (ticketStatus.equals(Constant.TICKET_STATUS_REFOUND)) {
                        checkInMap.put("msg", "已退票");
                        passengerSecurityVerificationVo.setCheckIn(checkInMap);
                    }
                } else if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSegId)) {
                    String ticketStatus = pnrNmTicket.getTicketStatus2();
                    if (ticketStatus.equals(Constant.TICKET_STATUS_REFOUND)) {
                        checkInMap.put("msg", "已退票");
                        passengerSecurityVerificationVo.setCheckIn(checkInMap);
                    }
                }
            }
            // 提取SSR中的证件号
            String idNo = ReUtil.get(REG_FOID, passengerCheckInVo.getIdNo(), 1);
            passengerCheckInVo.setIdNo(idNo.substring(2));

            // 记录VIP、特殊旅客
            List<String> specialSsrType = new ArrayList<>();
            // OSI VIP
            List<MnjxNmOsi> nmOsis = iMnjxNmOsiService.lambdaQuery()
                    .eq(MnjxNmOsi::getPnrNmId, passengerCheckInVo.getPnrNmId())
                    .eq(MnjxNmOsi::getPnrOsiType, Constant.VIP_TYPE)
                    .list();
            if (CollUtil.isNotEmpty(nmOsis)) {
                specialSsrType.add(Constant.VIP_TYPE);
            }
            // 特殊旅客
            List<MnjxNmSsr> nmSsrs = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passengerCheckInVo.getPnrNmId())
                    .in(MnjxNmSsr::getSsrType, specialPassengers)
                    .list();
            specialSsrType.addAll(nmSsrs.stream().map(MnjxNmSsr::getSsrType).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(specialSsrType)) {
                String specialSsrTypeStr = String.join(",", specialSsrType);
                specialPassengersMap.put("status", false);
                specialPassengersMap.put("msg", StrUtil.format("特殊旅客,{}", specialSsrTypeStr));
                passengerSecurityVerificationVo.setSpecialPassengers(specialPassengersMap);
            }

            // 安检次数
            List<MnjxVerifyInfo> verifyInfos = iMnjxVerifyInfoService.lambdaQuery()
                    .eq(MnjxVerifyInfo::getPsgCkiId, passengerCheckInVo.getPsgCkiId())
                    .list();
            if (CollUtil.isNotEmpty(verifyInfos)) {
                numberOfSecurityCheckMap.put("status", false);
                numberOfSecurityCheckMap.put("msg", StrUtil.format("{}次安检", verifyInfos.size() + 1));
                passengerSecurityVerificationVo.setNumberOfSecurityChecks(numberOfSecurityCheckMap);
            }

            // 交运行李
            List<MnjxLuggage> luggages = iMnjxLuggageService.lambdaQuery()
                    .eq(MnjxLuggage::getPnrNmId, passengerCheckInVo.getPnrNmId())
                    .eq(MnjxLuggage::getLuggageType, Constant.STR_ONE)
                    .isNull(MnjxLuggage::getIsDel)
                    .list();
            if (CollUtil.isNotEmpty(luggages)) {
                List<String> luggageIds = luggages.stream()
                        .map(MnjxLuggage::getLuggageId)
                        .collect(Collectors.toList());
                List<MnjxUnpackInfo> unpackInfos = iMnjxUnpackInfoService.lambdaQuery()
                        .in(MnjxUnpackInfo::getLuggageId, luggageIds)
                        .list();
                if (CollUtil.isNotEmpty(unpackInfos)) {
                    passengerLuggageMap.put("status", false);
                    passengerLuggageMap.put("msg", "交运行李异常，需开包检查处理");
                    passengerSecurityVerificationVo.setPassengerLuggage(passengerLuggageMap);
                } else {
                    passengerLuggageMap.put("msg", StrUtil.format("交运行李正常，共{}件", luggages.size()));
                    passengerSecurityVerificationVo.setPassengerLuggage(passengerLuggageMap);
                }
            }
            // 证件信息
            // 从读到的证件号和订座记录中的证件号对比
            String certificateNo = certificateInfo.getCertificateNo();
            String certificateType = certificateInfo.getCertificateType();
            // 姓名
            String name = certificateInfo.getName();
            MnjxNmSsr foidSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passengerCheckInVo.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_FOID)
                    .one();
            String foidInfo = StrUtil.format("{}{}", certificateType, certificateNo);
            switch (certificateType) {
                case "NI":
                    if (certificateNo.equalsIgnoreCase(passengerCheckInVo.getIdNo()) && name.equalsIgnoreCase(passengerCheckInVo.getName())) {
                        certificateMap.put("status", true);
                        certificateMap.put("msg", "证件信息正常");
                        passengerSecurityVerificationVo.setCertificate(certificateMap);
                    }
                    break;
                case "ID":
                case "PP":
                    if (foidSsr.getInputValue().contains(foidInfo)) {
                        certificateMap.put("status", true);
                        certificateMap.put("msg", "证件信息正常");
                        passengerSecurityVerificationVo.setCertificate(certificateMap);
                    }
                    break;
                default:
                    break;
            }
        } else {
            checkInMap.put("status", false);
            checkInMap.put("msg", "值机信息异常");
            passengerSecurityVerificationVo.setCheckIn(checkInMap);
        }
        return UnifiedResult.ok("成功", passengerSecurityVerificationVo);
    }

    @Override
    public UnifiedResult retrieveDetail(PassengerFlightMsgDto passengerFlightMsgDto) {
        PassengerFlightMsgVo passengerFlightMsgVo = passengerSecurityVerificationMapper.retrieveDetail(passengerFlightMsgDto);
        return UnifiedResult.ok("成功", passengerFlightMsgVo);
    }
}
