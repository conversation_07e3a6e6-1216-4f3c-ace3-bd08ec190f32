package com.swcares.core.unified;

import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
/**
 * 处理统一返回结果转换到string的问题
 *
 * <AUTHOR>
 */
@Configuration
public class UnifiedConfig {
    @Bean
    public HttpMessageConverters httpMessageConverter() {
        return new HttpMessageConverters(new FastJsonHttpMessageConverter());
    }
}
