package com.swcares.obj.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Api(tags = "证件信息")
@Data
public class CertificateInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("姓名")
    @NotNull(message = "证件姓名不能为空")
    private String name;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("证件类型")
    private String certificateType;

    @ApiModelProperty("证件号")
    @NotNull(message = "证件号不能为空")
    private String certificateNo;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("生日")
    private String birthday;

    @ApiModelProperty("签发机关")
    private String signingIssuingOrganization;

    @ApiModelProperty("有效期限")
    private String validityPeriod;

    @ApiModelProperty("住址")
    private String address;
}
