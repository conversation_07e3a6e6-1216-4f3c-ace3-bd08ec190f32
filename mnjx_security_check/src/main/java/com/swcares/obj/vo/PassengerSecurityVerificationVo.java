package com.swcares.obj.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PassengerSecurityVerificationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 执行信息
     */
    private Map<String, Object> checkIn;

    /**
     * 旅客无交运行李
     */
    private Map<String, Object> passengerLuggage;

    /**
     * 航班状态
     */
    private Map<String, Object> flightStatus;

    /**
     * 证件信息是否正常
     */
    private Map<String, Object> certificate;

    /**
     * 布控
     */
    private Map<String, Object> executeControl;

    /**
     * 特殊旅客
     */
    private Map<String, Object> specialPassengers;

    /**
     * 安检次数
     */
    private Map<String, Object> numberOfSecurityChecks;

    /**
     * 隔离区
     */
    private Map<String, Object> isolationZone;
}
