spring:
  cache:
    # 现在改为none,使用j2cache 去配置
    type: none
# j2cache配置路径指定
j2cache:
  # j2cache 配置文件
  config-location: classpath:j2cache.properties
  # 关闭二级缓存 (net.oschina.j2cache.autoconfigure.J2CacheSpringRedisAutoConfiguration)
  l2-cache-open: false
  # 开启cacheManager (net.oschina.j2cache.autoconfigure.J2CacheSpringCacheAutoConfiguration)
  open-spring-cache: true
  #
  cache-clean-mode: active