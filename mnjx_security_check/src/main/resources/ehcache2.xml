<?xml version="1.0" encoding="UTF-8"?>
<!-- for ehcache 2.x -->
<ehcache updateCheck="false" dynamicConfig="false"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd">
    <!--
        磁盘存储:将缓存中暂时不使用的对象,转移到硬盘,类似于Windows系统的虚拟内存
        path:指定在硬盘上存储对象的路径
        path可以配置的目录有：
            user.home（用户的家目录）
            user.dir（用户当前的工作目录）
            java.io.tmpdir（默认的临时目录）
            ehcache.disk.store.dir（ehcache的配置目录）
            绝对路径（如：d:\\ehcache）
        操作系统缓存的临时目录，不同操作系统的缓存临时目录不一样。
        在Windows的缓存目录为：C:\Users\<USER>\AppData\Local\Temp\
        在Linux的缓存目录为：/tmp
    -->
    <diskStore path="java.io.tmpdir"/>

    <!-- 默认配置 -->
    <defaultCache maxElementsInMemory="10000"
                  overflowToDisk="true"
                  diskPersistent="false"
                  timeToIdleSeconds="3600"
                  timeToLiveSeconds="3600"
                  memoryStoreEvictionPolicy="FIFO"/>

    <!-- rear缓存空间 -->
    <cache name="cacheWormRear"
           maxElementsInMemory="10000"
           overflowToDisk="true"
           timeToIdleSeconds="32400"
           timeToLiveSeconds="32400"/>

    <!--
        name:缓存名称。
        maxElementsInMemory：缓存最大个数。
        eternal:对象是否永久有效，一但设置了，timeout将不起作用。
        timeToIdleSeconds：设置对象在失效前的允许闲置时间（单位：秒）。仅当eternal=false对象不是永久有效时使用，可选属性，默认值是0，也就是可闲置时间无穷大。
        timeToLiveSeconds：设置对象在失效前允许存活时间（单位：秒）。最大时间介于创建时间和失效时间之间。仅当eternal=false对象不是永久有效时使用，默认是0.，也就是对象存活时间无穷大。
        overflowToDisk：当内存中对象数量达到maxElementsInMemory时，Ehcache将会对象写到磁盘中。
        diskSpoolBufferSizeMB：这个参数设置DiskStore（磁盘缓存）的缓存区大小。默认是30MB。每个Cache都应该有自己的一个缓冲区。
        maxElementsOnDisk：硬盘最大缓存个数。
        diskPersistent：是否缓存虚拟机重启期数据 Whether the disk store persists between restarts of the Virtual Machine. The default value is false.
        diskExpiryThreadIntervalSeconds：磁盘失效线程运行时间间隔，默认是120秒。
        memoryStoreEvictionPolicy：当达到maxElementsInMemory限制时，Ehcache将会根据指定的策略去清理内存。默认策略是LRU（最近最少使用）。你可以设置为FIFO（先进先出）或是LFU（较少使用）。
        clearOnFlush：内存数量最大时是否清除。
    -->
</ehcache>
