<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>STS</artifactId>
        <groupId>com.swcares</groupId>
        <version>4.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mnjx_eterm</artifactId>
    <packaging>jar</packaging>

    <!--依赖管理 -->
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jfinal</groupId>
            <artifactId>enjoy</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>

        <!-- j2cache -->
        <dependency>
            <groupId>net.oschina.j2cache</groupId>
            <artifactId>j2cache-spring-boot2-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>net.oschina.j2cache</groupId>
            <artifactId>j2cache-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>mnjx_business</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!--参考资料: https://blog.csdn.net/w727655308/article/details/121586559 -->
        <!--多个resource可以理解为按顺序对多个resource进行收集资源-->
        <resources>
            <!-- maven项目中默认资源文件为src/main/resources和src/test/resources目录 -->
            <!-- 为什么要这样做，因为不保证xls和xls文件种刚好没的{}的占位符，所以这样打包出来的文件会引起文件损坏 -->
            <!-- 总的说明：对所有的文件做替换操作，并且把xls和xlsx文件排除打包 -->
            <resource>
                <!-- 标签<directory>指定资源文件目录-->
                <directory>src/main/resources</directory>
                <!--标签<filtering>是一个bool值，默认值为false。指定打包时的配置文件中是否进行变量替换-->
                <filtering>false</filtering>
                <!--标签 <includes>指定资源文件目录中，仅包含哪些文件被打包-->
                <includes>
                    <include>**/*</include>
                </includes>
                <!--标签<excludes>指定资源文件目录中，仅哪些文件不被打包-->
                <excludes>
                    <exclude>**/*.yml*</exclude>
                </excludes>
            </resource>

            <!-- 对xls和xlsx文件打包，但是内部不替换 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml*</include>
                </includes>
            </resource>
        </resources>


        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.swcares.EtermStarter</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
