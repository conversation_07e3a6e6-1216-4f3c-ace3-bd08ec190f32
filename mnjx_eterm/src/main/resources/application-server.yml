#======================服务器配置===============================#
eterm:
  socket:
    # socket服务端口
    port: 351
    # websocket服务端口
    webPort: 352
    # 绑定的网卡
    ip: 0.0.0.0
    # 连接监听线程
    bossNum: 4
    # 工作线程大小，如果值越大，对资源的消耗越大
    workNum: 16
#======================spring配置===============================#
spring:
  # 项目名称
  application:
    name: mnjx_eterm
  # 个人理解为禁用了开发属性(默认为true，生产我们设置为false)
  devtools:
    add-properties: true
  aop:
    proxy-target-class: true
