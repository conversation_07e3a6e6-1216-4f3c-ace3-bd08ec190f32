<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.cmn.mapper.AuthMapper">
    <select id="retrieveAuth" resultType="java.util.Map">
        select mlo.*
        from mnjx_level_order mlo
                 left join mnjx_order mo on mlo.order_id = mo.order_id
                 left join mnjx_level ml on mlo.level_code = ml.level_code
        where mo.order_name = #{instruction}
          and mlo.level_code = #{level}
          and ml.level_status = 1
    </select>
</mapper>
