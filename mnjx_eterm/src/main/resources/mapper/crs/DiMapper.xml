<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.DiMapper">

    <select id="retrieveTicket" resultType="com.swcares.eterm.crs.obj.vo.DiVo">
        select
            *
        from
            mnjx_printer mp
        join mnjx_office mo on
            mp.office_id = mo.office_id
        where
            1 = 1
            and mp.printer_no = #{printNo}
            and mo.office_no = #{officeNo}
    </select>
</mapper>
