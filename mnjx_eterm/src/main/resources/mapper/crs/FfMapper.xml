<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.FfMapper">

    <select id="retrievePlanFltIsValid" resultType="java.util.Map">
        SELECT *
        FROM mnjx_flight mf
                 JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
                 JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
        WHERE mf.flight_no = #{fltNo}
          AND mf.flight_status != 'DELETE'
          AND mpf.flight_date = #{fltDate}
          AND mpf.flight_date <![CDATA[>=]]> mt.start_date
          AND mpf.flight_date <![CDATA[<=]]> mt.end_date
    </select>

    <select id="retrieveTcardSection" resultType="com.swcares.eterm.crs.obj.dto.FfDto">
        SELECT ma.airport_code,
               mts.dep_time,
               mts.arr_time,
               mt.eqt type
        FROM mnjx_tcard_section mts
                 JOIN mnjx_tcard mt ON mts.tcard_id = mt.tcard_id
                 JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
                 JOIN mnjx_flight mf ON mt.flight_id = mf.flight_id
                 JOIN mnjx_airport ma ON mts.airport_id = ma.airport_id
        WHERE mf.flight_no = #{fltNo}
          AND mpf.flight_date = #{fltDate}
    </select>

</mapper>
