<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.fdc.mapper.FsdMapper">

    <select id="retrieveFsdFlightInfoList" resultType="com.swcares.eterm.dcs.fdc.dto.FsdFlight">
        SELECT DISTINCT
            mf.flight_no,
            mpf.ck_status as `status`,
            mps.actual_off as dept,
            CONCAT( mpm.plane_model_type, '/', mpm.plane_model_version ) AS equip,
            mps.plan_section_id,
            mc.layout
        FROM
            mnjx_flight mf
            LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
            LEFT JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
            LEFT JOIN mnjx_cnd mc ON mpf.cnd_no = mc.cnd_no
            LEFT JOIN mnjx_plane mpl ON mc.cnd_id = mpl.cnd_id
            LEFT JOIN mnjx_plane_model mpm ON mpl.plane_model_id = mpm.plane_model_id
            LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
        WHERE
            mf.flight_no LIKE concat(#{fsdDto.airlineCode},'%')
            AND mps.actual_off BETWEEN #{fsdDto.startTime} AND #{fsdDto.endTime}
            AND mpf.flight_date = #{fsdDto.date}
            AND mps.dep_apt_id IN (
                SELECT
                    airport_id
                FROM
                    mnjx_airport
                WHERE
                    city_id = ( SELECT city_id FROM mnjx_city WHERE city_code = #{fsdDto.orgCityCode} ))
        ORDER BY mps.actual_off
    </select>
</mapper>
