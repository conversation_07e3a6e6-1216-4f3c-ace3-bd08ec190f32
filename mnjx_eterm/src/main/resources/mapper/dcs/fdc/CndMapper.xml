<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.fdc.mapper.CndMapper">
    <select id="retrieveCndAndPlaneModel" resultType="com.swcares.eterm.dcs.fdc.dto.CndDto">
        SELECT DISTINCT mc.cnd_no,
        mpm.plane_model_type,
        mpm.plane_model_version,
        ifnull(mc.first_cabin_class,'') as firstCabinClass,
        ifnull(mc.first_sell_cabin,'') as firstSellCabin,
        ifnull(mc.first_seats,'') as firstSeats,
        ifnull( mc.second_cabin_class,'') as secondCabinClass,
        ifnull(mc.second_sell_cabin,'') as secondSellCabin,
        ifnull(mc.second_seats,'') as secondSeats,
        ifnull(mc.third_cabin_class,'') as thirdCabinClass,
        ifnull(mc.third_sell_cabin,'') as thirdSellCabin,
        ifnull(mc.third_seats,'') as thirdSeats,
        ifnull(mc.fourth_cabin_class,'') as fourthCabinClass,
        ifnull(mc.fourth_sell_cabin,'') as fourthSellCabin,
        ifnull(mc.fourth_seats,'') as fourthSeats,
        ifnull(mc.fifth_cabin_class,'') as fifthCabinClass,
        ifnull(mc.fifth_sell_cabin,'') as fifthSellCabin,
        ifNULL(mc.fifth_seats,'') as fifthSeats
        FROM mnjx_plane mp
        LEFT JOIN mnjx_cnd mc ON mc.cnd_id = mp.`cnd_id`
        LEFT JOIN mnjx_plane_model mpm ON mc.plane_model_id = mpm.plane_model_id
        WHERE mc.cnd_no <![CDATA[ <> ]]>''
        and mp.is_use = #{planeStatus}
        and mp.airline_id = #{airlineId}
        <if test="planeModelType!=null and planeModelType!=''">
            AND mpm.plane_model_type = #{planeModelType}
        </if>
        <if test="planeModelVersion!=null and planeModelVersion!=''">
            AND mpm.plane_model_version = #{planeModelVersion}
        </if>
        ORDER BY mc.cnd_no
    </select>
</mapper>
