<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.SbMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.eterm.dcs.cki.obj.dto.SbFlightDto">
        <id column="flight_id" property="flightId" />
        <result column="flight_no" property="flightNo" />
        <result column="tcard_id" property="tcardId" />
        <result column="plan_flight_id" property="planFlightId" />
        <result column="is_flight_initial" property="isFlightInitial" />
        <result column="cnd_no" property="cndNo" />
        <collection property="sbAirportDtos" column="excellence_id" ofType="com.swcares.eterm.dcs.cki.obj.dto.SbAirportDto">
            <id column="plan_section_id" property="planSectionId" />
            <result column="dep_airport_code" property="depAirportCode" />
            <result column="arr_airport_code" property="arrAirportCode" />
        </collection>
    </resultMap>

    <resultMap id="BaseResultPnrNmMap" type="com.swcares.eterm.dcs.cki.obj.dto.SbHbPnrNmDto">
        <id column="pnr_seg_id" property="pnrSegId" />
        <result column="flight_date" property="flightDate" />
        <result column="flight_no" property="flightNo" />
        <result column="sell_cabin" property="sellCabin" />
        <result column="pnr_seg_no" property="pnrSegNo" />
        <result column="dst" property="dst" />
        <result column="pnr_nm_id" property="pnrNmId" />
        <result column="query_name" property="queryName" />
        <result column="psg_cki_id" property="psgCkiId" />
        <result column="hb_no" property="hbNo" />
        <result column="is_hb" property="isHb" />
        <result column="aboard_time" property="aboardTime" />
    </resultMap>

    <select id="searchFlight" resultMap="BaseResultMap">
        SELECT
            mf.flight_id,
            mf.flight_no,
            mt.tcard_id,
            mpf.plan_flight_id,
            mpf.is_flight_initial,
            mpf.cnd_no,
            mps.plan_section_id,
            ma1.airport_code dep_airport_code,
            ma2.airport_code arr_airport_code
        FROM
            mnjx_flight mf
            LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
            LEFT JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
	        LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
            LEFT JOIN mnjx_airport ma1 ON mps.dep_apt_id = ma1.airport_id
            LEFT JOIN mnjx_airport ma2 on mps.arr_apt_id = ma2.airport_id
        <where>
            <if test="query.fltNo != null and query.fltNo != ''">
                and mf.flight_no = #{query.fltNo}
            </if>
            <if test="query.fltDate != null and query.fltDate != ''">
                and mpf.flight_date = #{query.fltDate}
            </if>
        </where>
    </select>

    <select id="selectNm" resultType="com.swcares.eterm.dcs.cki.obj.dto.SbHbPnrNmDto">
        SELECT
            mps.pnr_seg_id,
            mps.flight_date,
            mps.flight_no,
            mps.sell_cabin,
            mps.pnr_seg_no,
            mps.carrier_flight,
            mps.dst,
            mpn.pnr_nm_id,
            mpn.query_name,
            mpc.psg_cki_id,
            mpc.cki_status,
            mpc.hb_no,
            mpc.is_hb,
            mpc.aboard_time
        FROM
            mnjx_pnr_seg mps
                LEFT JOIN mnjx_pnr_nm mpn ON mps.pnr_id = mpn.pnr_id
                LEFT JOIN mnjx_psg_cki mpc ON mpn.pnr_nm_id = mpc.pnr_nm_id
        <where>
            <if test="query.fltNo != null and query.fltNo != ''">
                and (mps.flight_no = #{query.fltNo} or mps.carrier_flight = #{query.fltNo})
            </if>
            <if test="query.fltDate != null and query.fltDate != ''">
                and mps.flight_date = #{query.fltDate}
            </if>
            <if test="query.isHb != null and query.isHb != ''">
                and mpc.is_hb = #{query.isHb}
            </if>
            <if test="query.passCkiStatus != null and query.passCkiStatus != ''">
                and mpc.cki_status != #{query.passCkiStatus}
            </if>
            <if test="query.sellCabin != null and query.sellCabin != ''">
                and mps.sell_cabin = #{query.sellCabin}
            </if>
        </where>
    </select>

    <select id="retrieveFlightByDate" resultType="com.swcares.entity.MnjxPlanFlight">
        select
            mpf.*
        from
            mnjx_flight mf
                left join mnjx_tcard mt on
                mf.flight_id = mt.flight_id
                left join mnjx_plan_flight mpf on
                mpf.tcard_id = mt.tcard_id
        where
            mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
    </select>


</mapper>
