<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.BsMapper">
    <update id="updateSeatStatusX">
    UPDATE mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        LEFT JOIN mnjx_airport ma ON ma.airport_id = mps.dep_apt_id
        LEFT JOIN mnjx_airport mat ON mat.airport_id = mps.arr_apt_id
        LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
        LEFT JOIN mnjx_seat ms ON ms.open_cabin_id = moc.open_cabin_id
        SET ms.seat_status = 'X'
    WHERE
	mf.flight_no = #{bsDto.flightNo}
	AND mpf.flight_date = #{bsDto.flightDate}
	AND ma.airport_code = #{bsDto.dep}
	AND mat.airport_code = #{bsDto.arr}
        <if test="bsDto.seatNo != null and bsDto.seatNo != ''">
            AND ms.seat_no = #{bsDto.seatNo}
        </if>
        <if test="bsDto.seatRow != null and bsDto.seatRow != ''">
            AND ms.seat_row = #{bsDto.seatRow}
        </if>
    </update>
    <update id="updateSeatStatusC">
        UPDATE mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        LEFT JOIN mnjx_airport ma ON ma.airport_id = mps.dep_apt_id
        LEFT JOIN mnjx_airport mat ON mat.airport_id = mps.arr_apt_id
        LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
        LEFT JOIN mnjx_seat ms ON ms.open_cabin_id = moc.open_cabin_id
        SET ms.seat_status = 'C'
    WHERE
	mf.flight_no = #{bsDto.flightNo}
	AND mpf.flight_date = #{bsDto.flightDate}
	AND ma.airport_code = #{bsDto.dep}
	AND mat.airport_code = #{bsDto.arr}
	<if test="bsDto.seatNo != null and bsDto.seatNo != ''">
        AND ms.seat_no = #{bsDto.seatNo}
    </if>
    <if test="bsDto.seatRow != null and bsDto.seatRow != ''">
        AND ms.seat_row = #{bsDto.seatRow}
    </if>
    </update>
    <update id="updateSeatRowsStatusX">
        UPDATE mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        LEFT JOIN mnjx_airport ma ON ma.airport_id = mps.dep_apt_id
        LEFT JOIN mnjx_airport mat ON mat.airport_id = mps.arr_apt_id
        LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
        LEFT JOIN mnjx_seat ms ON ms.open_cabin_id = moc.open_cabin_id
        SET ms.seat_status = 'X'
    WHERE
	mf.flight_no = #{bsDto.flightNo}
	AND mpf.flight_date = #{bsDto.flightDate}
	AND ma.airport_code = #{bsDto.dep}
	AND mat.airport_code = #{bsDto.arr}
	AND ms.seat_row BETWEEN #{end} AND #{start}
    </update>
    <update id="updateSeatRowsStatusC">
        UPDATE mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        LEFT JOIN mnjx_airport ma ON ma.airport_id = mps.dep_apt_id
        LEFT JOIN mnjx_airport mat ON mat.airport_id = mps.arr_apt_id
        LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
        LEFT JOIN mnjx_seat ms ON ms.open_cabin_id = moc.open_cabin_id
        SET ms.seat_status = 'C'
    WHERE
	mf.flight_no = #{bsDto.flightNo}
	AND mpf.flight_date = #{bsDto.flightDate}
	AND ma.airport_code = #{bsDto.dep}
	AND mat.airport_code = #{bsDto.arr}
	AND ms.seat_row BETWEEN #{end} AND #{start}
    </update>

    <select id="retrieveSeat" resultType="com.swcares.eterm.dcs.cki.obj.dto.BsDto">
        select
        distinct  ms.seat_id ,
        (
        select ma.airport_code from mnjx_airport ma where ma.airport_id = mps.dep_apt_id
        ) dst,
        (
        select ma.airport_code from mnjx_airport ma where ma.airport_id = mps.arr_apt_id
        ) arr,
        moc.cabin_class,
        ms.seat_no ,
        ms.seat_row,
        ms.seat_status,
        ms.seat_status_old
        from
        mnjx_flight mf
        left join mnjx_tcard mt on
        mf.flight_id = mt.flight_id
        left join mnjx_plan_flight mpf on
        mt.tcard_id = mpf.tcard_id
        left join mnjx_tcard_section mts on
        mts.tcard_id = mt.tcard_id
        left join mnjx_plan_section mps on
        mps.plan_flight_id = mpf.plan_flight_id
        left join mnjx_open_cabin moc on
        moc.plan_section_id = mps.plan_section_id
        left join mnjx_seat ms on
        ms.open_cabin_id = moc.open_cabin_id
        where
        mf.flight_no = #{bsDto.flightNo}
        and mpf.flight_date = #{bsDto.flightDate}
    </select>
</mapper>
