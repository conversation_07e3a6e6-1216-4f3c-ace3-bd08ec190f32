<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.FtMapper">
    <select id="retrievePlanFlt" resultType="com.swcares.eterm.dcs.cki.obj.dto.FtDto">
        SELECT mpf.flight_status, mpf.is_flight_initial, mpf.ck_status
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt
                           ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        WHERE mf.flight_no = #{fltNo}
          AND mpf.flight_date = #{fltDate}
          AND mpf.flight_status NOT IN ('DELETE', 'CANCEL')
    </select>
</mapper>
