<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.GsMapper">

    <select id="retrievePlanFlt" resultType="java.lang.String">
        SELECT mpf.plan_flight_id
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        WHERE mf.flight_no = #{fltNo}
          AND mpf.flight_date = #{fltDate}
    </select>

    <select id="retrieveCabin" resultType="java.lang.String">
        SELECT CASE
                   WHEN INSTR(mc.first_cabin_class, #{cabin})
                       THEN first_cabin_class
                   WHEN INSTR(mc.second_cabin_class, #{cabin})
                       THEN second_cabin_class
                   WHEN INSTR(mc.third_cabin_class, #{cabin})
                       THEN third_cabin_class
                   WHEN INSTR(mc.fourth_cabin_class, #{cabin})
                       THEN fourth_cabin_class
                   ELSE fifth_cabin_class
                   END AS cabinClass
        FROM mnjx_cnd mc
        WHERE mc.`cnd_no` = #{cndNo}
    </select>
</mapper>
