{"properties": [{"name": "eterm.socket.port", "type": "java.lang.String", "description": "Description for eterm.socket.port."}, {"name": "eterm.socket.ip", "type": "java.lang.String", "description": "Description for eterm.socket.ip."}, {"name": "eterm.socket.accountNum", "type": "java.lang.String", "description": "Description for eterm.socket.accountNum."}, {"name": "eterm.socket.classNum", "type": "java.lang.String", "description": "Description for eterm.socket.classNum."}, {"name": "spring.enjoy.template-loader-path", "type": "java.lang.String", "description": "Description for spring.enjoy.template-loader-path."}]}