#(inst)
#(results[0].order):#(results[0].flightNo)/#(results[0].date)#if(results[0].cabin != null)#end/#(results[0].cabin)#if(results[0].airportCode != null)#end/#(results[0].airportCode)                              #(results[0].ckStatus)/NAM #(wrap())
#(results[0].planeType) #(results[0].gtd) POS/GATE BDT#(results[0].boarding) SD#(results[0].estimateOff) #if(results[0].actualOff != null)ED#(results[0].actualOff)#end SA#(results[0].estimateArr) FT#(results[0].timeDiff) #(wrap())
AV #(results[0].av)   PAD #(results[0].pad) #(wrap())
    *SHA* **********WELCOME TO SHANGHAI AIRPORT********** #(wrap())
STANDBY #(wrap())
#if(results[0].nms == null || results[0].nms.size() == 0)
NIL #(wrap())
#else
#for (i=0;i<results[0].nms.size();i++)
#(i+1). #(results[0].nms[i].queryNameShow) #if(results[0].nms[i].ckiStatus == 'DL')DELETED     #else#(results[0].nms[i].hbNoShow) #end URES  #(results[0].nms[i].sellCabinShow)#(results[0].nms[i].dstShow)P0 #(wrap())
#end
#end