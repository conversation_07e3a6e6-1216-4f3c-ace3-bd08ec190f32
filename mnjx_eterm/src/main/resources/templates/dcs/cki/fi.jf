#set(fltPlanDto=results[0])
#(action):#(fltPlanDto.flightNo)/#(fltPlanDto.flightDate)#((fltPlanDto.paramCity??)?"/"+fltPlanDto.paramCity:"")#(wrap())
#(fillAfter(fltPlanDto.flightNo,cn.hutool.core.util.CharUtil::SPACE,8)) #if(fltPlanDto.isFuUpdate==true)
#(fltPlanDto.flightDate)          --#(fltPlanDto.mpfStatus??)
#else
#(fltPlanDto.startDate)-#(fltPlanDto.endDate) #(fltPlanDto.cycle??) --#(fltPlanDto.mfStatus)
#end
-- FLTTYP #(fltPlanDto.type) CONTROL #(fltPlanDto.officeNo)#(wrap())
#if(fltPlanDto.city??)#(fillBefore(fltPlanDto.city,cn.hutool.core.util.CharUtil::SPACE,27))#(wrap())#end
#for(x:fltPlanDto.planSectionList)
#(x.airportCode)    #((x.actualArr??)?x.actualArr:(x.estimateArr??"    "))#((x.actualArrChange??&&x.actualArr??)?x.actualArrChange:((x.estimateArrChange??&&x.estimateArr??)?x.estimateArrChange:" "))        #((x.actualOff??)?x.actualOff:(x.estimateOff??"     "))   #(x.gate??"   ")   #(x.cndNo??) #(x.eqt??)  #(x.vers??)  #(x.planeNo??)#(wrap())
#end