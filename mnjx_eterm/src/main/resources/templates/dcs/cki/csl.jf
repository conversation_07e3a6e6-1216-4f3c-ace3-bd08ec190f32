#set(csl = results[0], null)
#(args[0])#(wrap())
    HOST        SHARED#(wrap())
  AC  FLT #   AC  FLT #      DATE RANGE       S X M D G    WEEK    SEGMENT#(wrap())
  --- -----   --- -----    ---------------    - - - - -    ----    -------#(wrap())
#for(shareFlightNo:csl.shareFlightNoList)
  #if(for.index==0)#(csl.carrierFlightNo.substring(0, 2))  #(fillAfter(csl.carrierFlightNo.substring(2),cn.hutool.core.util.CharUtil::SPACE,5))#else#('         ')#end#('   ')#(shareFlightNo.substring(0,2))  #(fillAfter(shareFlightNo.substring(2),cn.hutool.core.util.CharUtil::SPACE,5))    #(csl.dateStart)-#(csl.dateEnd)                 #(fillAfter(csl.cycle,cn.hutool.core.util.CharUtil::SPACE,4))    #(csl.dep) #(csl.arr)#(wrap())
#end