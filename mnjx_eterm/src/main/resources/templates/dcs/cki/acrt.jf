#(args[0])#(wrap())
PID NO  LOCATION  A/L  MODE SI TIME/DATE  AGENT#(wrap())
======  ========  ===  ==== ============  =====#(wrap())
#for(x:results)
#(fillAfter(x.siPid,cn.hutool.core.util.CharUtil::SPACE,6))   #(x.officeNo)   1E   12   #((x.signInDatetime??)?ymdhms2hm(x.signInDatetime):nowHm2ComHm())/#((x.signInDatetime??)?subPre(ymd2Com(ymdhms2YmdStr(x.signInDatetime)), 5):subPre(nowYmdToCom(), 5)) #(x.activeSign??" ")  #(x.siNo)#(wrap())
#end