#set(firstRes = results[0], endRes = results[results.length - 1], arg = args[0], null)
#if(firstRes.ures?? || firstRes.nrec??)
HBPA: #(arg) #if(results.length > 1)#(results.length)#end#(wrap())
AV #(firstRes.avLayout)    PAD #(firstRes.padLayout)#(wrap())
#if(firstRes.planeType??)#(firstRes.planeType)/#(firstRes.planeVersion) #end#[[GTD/]]##(firstRes.gate) POS/GATE#if(firstRes.bdt??) BDT#(firstRes.bdt)#end#if(firstRes.sd??) SD#(firstRes.sd)#end#if(firstRes.ed??) ED#(firstRes.ed)#end#if(firstRes.sa??)  SA#(firstRes.sa)#end#if(firstRes.ft??)  FT#(firstRes.ft)#end#(wrap())
#for(res:results)
#set(ssrListSize = res.otherSsrList.size(), oiSize = res.interlineResults.size(), null)
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.bnNo?? && res.ckiStatus == 'ACC')BN#(res.bnNo)  #elseif(res.hbNo??)SB#(res.hbNo) #else#('       ')#end#if(res.stcr??)#(res.stcr)   #elseif(res.jmp??)#(res.jmp)    #elseif(res.seatNo?? && !res.isDlOrNaccToSb())#(fillString(res.seatNo, 3, false))    #elseif(res.isCap())CAP    #elseif(res.hbNo??)URES   #else#('       ')#end#if(res.shareFlightNo??)SIP#(res.shareFlightNo.substring(0,2)) #end#(res.sellCabin) #(res.orgAirport)#(res.dstAirport) #if(res.nrs??) #(res.nrs)  #end#if(res.isAdv())ADV-PA/NP #else#if(res.weight??)#(res.weight) #elseif(!res.ff??)#(res.fba) #end#if(res.ures?? || res.exstType?? || res.nrec??)#if(res.ures??)URES #elseif(res.nrec??)NREC #end#if(!res.exstSeat?? && res.exstType??)#(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(' ')#end#end#if(res.infName??)INF1/10 #end#if(res.sex??)#(res.sex)1/0 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 2)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#end#(wrap())
#if(res.isAdv())                                        #if(res.weight??)#(res.weight) #elseif(!res.ff??)#(res.fba) #end#if(res.sex??)#(res.sex)1/0 #end#if(res.infName??)INF1/10 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 2)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#(wrap())#end
#if(res.bsctNum?? || res.petcNum?? || res.adsr??)                                        #if(res.bsctNum??)BSCT#(res.bsctNum)/#(res.bsctWeight) #end#if(res.petcNum??)PETC#(res.petcNum)/#(res.petcWeight) #end#if(res.adsr??)#(res.adsr)#if(res.adsrWeight??)#(res.adsrWeight)#end#(' ')#end#(wrap())#end
#if(ssrListSize > 1 && ssrListSize < 8)                                        #for(i = 1; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 7 && ssrListSize < 14)                                        #for(i = 7; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 13 && ssrListSize < 20)                                        #for(i = 13; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 19)                                        #for(i = 19; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(res.isCnin())                                        CNIN/#(res.name)#(wrap())#end
#if(res.isEt())#for(ticketNo:res.ticketNoList)                                        ET TKNE/#if(for.index == 1)INF#end#(ticketNo)/1#(wrap())#end#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
#if(res.exstSeat??)                                 #(fillString(res.exstSeat, 3, false))    #(res.exstType)#(res.exstWeight)#(wrap())#end
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) //#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)//#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) //#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end//#(res.chld)#(wrap())#end
#if(res.shareFlightNo??)MARKETING FLIGHT:#(res.shareFlightNo) FARE CLASS:#(res.cabinClass)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.orgAirport) #(res.airlineCode)-#if(res.orderDate??)#(res.orderDate)-#end#(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
#if(res.otherWc??)#(res.otherWc)-#(wrap())#end
#if(oiSize > 0)#for(oi:res.interlineResults)#(oi)#(wrap())#end#end
#end
#else
HBPA: #(arg) #if(results.length > 1)#(results.length)#end#(wrap())
#for(res:results)
#if(res.errorO??)
#(res.errorO)
#else
#if(for.index == 0 || res.isTypeO())
#if(res.isTypeO())
 TRANSFER  #(res.orgAirport) #(res.flightNo)/#(res.flightDate)#(res.sellCabin)#(res.dstAirport)
#end
AV #(res.avLayout)    PAD #(res.padLayout)#(wrap())
#(res.planeType)/#(res.planeVersion) GTD/#(res.gate) POS/GATE#if(res.bdt??) BDT#(res.bdt)#end#[[ SD]]##(res.sd)#if(res.ed??) ED#(res.ed)#end#if(res.sa??)  SA#(res.sa)#end#if(res.ft??)  FT#(res.ft)#end#(wrap())
#end
#set(ssrListSize = res.otherSsrList.size(), oiSize = res.interlineResults.size(), null)
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.bnNo?? && res.ckiStatus == 'ACC')BN#(res.bnNo)  #elseif(res.hbNo??)SB#(res.hbNo) #else#('       ')#end#if(res.stcr??)#(res.stcr)   #elseif(res.isCap())CAP    #elseif(res.jmp??)#(res.jmp)    #elseif(res.seatNo??)#(fillString(res.seatNo, 3, false))    #elseif(res.hbNo??)URES   #else#('       ')#end#if(res.shareFlightNo??)SIP#(res.shareFlightNo.substring(0,2)) #end#(res.sellCabin) #(res.orgAirport)#(res.dstAirport) #if(res.ures?? || res.exstType?? || res.nrec??)#if(res.ures??)URES #elseif(res.nrec??)NREC #end#if(!res.exstSeat?? && res.exstType??)#(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(' ')#end#end#if(res.nrec??)NREC #end#if(res.nrs??) #(res.nrs)  #end#if(res.isAdv())ADV-PA/NP #else#if(res.weight??)#(res.weight) #elseif(!res.ff??)#(res.fba) #end#if(res.sex??)#(res.sex)1/0 #end#if(res.infName??)INF1/10 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 2)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#end#(wrap())
#if(res.isAdv())                                        #if(res.weight??)#(res.weight) #elseif(!res.ff??)#(res.fba) #end#if(res.sex??)#(res.sex)1/0 #end#if(res.infName??)INF1/10 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 2)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#(wrap())#end
#if(res.bsctNum?? || res.petcNum?? || res.adsr??)                                        #if(res.bsctNum??)BSCT#(res.bsctNum)/#(res.bsctWeight) #end#if(res.petcNum??)PETC#(res.petcNum)/#(res.petcWeight) #end#if(res.adsr??)#(res.adsr)#if(res.adsrWeight??)#(res.adsrWeight)#end#(' ')#end#(wrap())#end
#if(ssrListSize > 1 && ssrListSize < 8)                                        #for(i = 1; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 7 && ssrListSize < 14)                                        #for(i = 7; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 13 && ssrListSize < 20)                                        #for(i = 13; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 19)                                        #for(i = 19; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(res.ff??)                                        #(res.ff)/#(res.airlineCode) #(res.ffNo)/#(res.ffLevel) #(res.ffWeight)#(wrap())#end
#if(res.isCnin())                                        CNIN/#(res.name)#(wrap())#end
#if(res.isEt())#for(ticketNo:res.ticketNoList)                                        ET TKNE/#if(for.index == 1)INF#end#(ticketNo)/1#(wrap())#end#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
#if(res.exstSeat??)                                 #(fillString(res.exstSeat, 3, false))    #(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(wrap())#end
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) //#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)//#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) //#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end//#(res.chld)#(wrap())#end
#if(res.shareFlightNo??)MARKETING FLIGHT:#(res.shareFlightNo) FARE CLASS:#(res.cabinClass)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.orgAirport) #(res.airlineCode)-#if(res.orderDate??)#(res.orderDate)-#end#(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
#if(res.otherWc??)#(res.otherWc)-#(wrap())#end
#if(res.meda??)#(res.meda)#(wrap())#end
#if(oiSize > 0)#for(oi:res.interlineResults)#(oi)#(wrap())#end#end
#end
#if(res.oList.size() > 0 || res.isTypeO())
  *#(res.dstAirport)* WELCOME TO #(res.cityName)#(wrap())
#end
#end
#if(endRes.oList.size() == 0 && !endRes.isTypeO())
  *#(endRes.dstAirport)* WELCOME TO #(endRes.cityName)#(wrap())
#end
#end