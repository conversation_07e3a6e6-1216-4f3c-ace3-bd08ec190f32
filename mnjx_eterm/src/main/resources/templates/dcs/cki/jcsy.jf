#set(cmd = args[0], option = args[1], res = results[0], null)
#(cmd)#(wrap())
#if("O" == option)
#if(res.maxCabinClassNum == 2)
FLT/DEST/GTD   DEPT   BKD     CHK(NTC)   CHK(TC)    UCK     BAG#(wrap())
#else
FLT/DEST/GTD   DEPT   BKD        CHK(NTC)      CHK(TC)       UCK        BAG
#end
#for(jcsy:res.jcsyVoList)
#(jcsy.flightNo)/#(jcsy.dest)/#(jcsy.gtd)#(jcsy.dept)   #(jcsy.bkd) #(jcsy.chkNtc) #(jcsy.chkTc) #(jcsy.uck) #(jcsy.bag)#if(jcsy.cabinClassNum == 2)#(wrap())#end
#end
##TOTAL##  /          #(res.totalBkd) #(res.totalChkNtc) #(res.totalChkTc) #(res.totalUck) #(res.totalBag)#(wrap())
#else
#if(res.maxCabinClassNum == 2)
FLT/ORIG   ARVL   BKD     CHK        UCK     NBRD       BAG#(wrap())
#else
FLT/ORIG   ARVL   BKD        CHK           UCK        NBRD          BAG
#end
#for(jcsy:res.jcsyVoList)
#(jcsy.flightNo)/#(jcsy.orig)#(jcsy.arvl)   #(jcsy.bkd) #(jcsy.chk) #(jcsy.uck) #(jcsy.nbrd) #(jcsy.bag)#if(jcsy.cabinClassNum == 2)#(wrap())#end
#end
##TOTAL##         #(res.totalBkd) #(res.totalChk) #(res.totalUck) #(res.totalNbrd) #(res.totalBag)#(wrap())
#end
