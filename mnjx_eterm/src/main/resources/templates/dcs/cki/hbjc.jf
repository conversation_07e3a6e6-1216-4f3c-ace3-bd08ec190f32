#set(firstRes = results[0], arg = args[0], null)
#if(firstRes.ures?? || firstRes.nrec??)
#(arg) #if(results.length > 1)#(results.length)#end#(wrap())
AV #(firstRes.avLayout)    PAD #(firstRes.padLayout)#(wrap())
#if(firstRes.planeType??)#(firstRes.planeType)/#(firstRes.planeVersion) #end#[[GTD/]]##(firstRes.gate) POS/GATE#if(firstRes.bdt??) BDT#(firstRes.bdt)#end#if(firstRes.sd??) SD#(firstRes.sd)#end#if(firstRes.ed??) ED#(firstRes.ed)#end#if(firstRes.sa??)  SA#(firstRes.sa)#end#if(firstRes.ft??)  FT#(firstRes.ft)#end#(wrap())
#for(res:results)
#set(ssrListSize = res.otherSsrList.size(), oiSize = res.interlineResults.size(), null)
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.bnNo?? && res.ckiStatus == 'ACC')BN#(res.bnNo)  #elseif(res.hbNo??)SB#(res.hbNo) #else#('       ')#end#if(res.stcr??)#(res.stcr)   #elseif(res.jmp??)#(res.jmp)    #elseif(res.seatNo?? && !res.isDlOrNaccToSb())#(fillString(res.seatNo, 3, false))    #elseif(res.hbNo??)URES   #else#('       ')#end#(res.sellCabin) #(res.orgAirport)#(res.dstAirport) #if(res.nrs??) #(res.nrs)  #end#if(res.isAdv())ADV-PA/NP #end#if(res.ures?? || res.exstType?? || res.nrec??)#if(res.ures??)URES #elseif(res.nrec??)NREC #end#if(!res.exstSeat?? && res.exstType??)#(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(' ')#end#end#if(res.infName??)INF1/10 #end#if(res.sex??)#(res.sex)1/0 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 6)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#(wrap())
#if(res.bsctNum?? || res.petcNum?? || res.adsr??)                                        #if(res.bsctNum??)BSCT#(res.bsctNum)/#(res.bsctWeight) #end#if(res.petcNum??)PETC#(res.petcNum)/#(res.petcWeight) #end#if(res.adsr??)#(res.adsr)#if(res.adsrWeight??)#(res.adsrWeight)#end#(' ')#end#(wrap())#end
#if(ssrListSize > 5 && ssrListSize < 12)                                        #for(i = 5; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 11 && ssrListSize < 18)                                        #for(i = 11; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 17 && ssrListSize < 24)                                        #for(i = 17; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 23)                                        #for(i = 23; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
                                        #if(res.isCnin())CNIN/#(res.name) #end#if(res.weight??)#(res.weight) #else#(res.fba) #end#(wrap())
#if(res.isEt())#for(ticketNo:res.ticketNoList)                                        ET TKNE/#if(for.index == 1)INF#end#(ticketNo)/1#(wrap())#end#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
#if(res.exstSeat??)                                 #(fillString(res.exstSeat, 3, false))    #(res.exstType)#(res.exstWeight)#(wrap())#end
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) //#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)//#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) //#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end//#(res.chld)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.orgAirport) #(res.airlineCode)-#if(res.orderDate??)#(res.orderDate)-#end#(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
#if(oiSize > 0)#for(oi:res.interlineResults)#(oi)#(wrap())#end#end
#end
#else
#(arg) #if(results.length > 1)#(results.length)#end#(wrap())
AV #(firstRes.avLayout)    PAD #(firstRes.padLayout)#(wrap())
#if(firstRes.planeType??)#(firstRes.planeType)/#(firstRes.planeVersion) #end#[[GTD/]]##(firstRes.gate) POS/GATE#if(firstRes.bdt??) BDT#(firstRes.bdt)#end#if(firstRes.sd??) SD#(firstRes.sd)#end#if(firstRes.ed??) ED#(firstRes.ed)#end#if(firstRes.sa??)  SA#(firstRes.sa)#end#if(firstRes.ft??)  FT#(firstRes.ft)#end#(wrap())
#for(res:results)
#set(ssrListSize = res.otherSsrList.size(), oiSize = res.interlineResults.size(), null)
#(fillIndex((for.index + 1), 3, true)). #(res.queryName)#(res.groupName)#if(res.bnNo?? && res.ckiStatus == 'ACC')BN#(res.bnNo)  #elseif(res.hbNo??)SB#(res.hbNo) #else#('       ')#end#if(res.stcr??)#(res.stcr)   #elseif(res.isCap() && res.ckiStatus != 'ACC')CAP    #elseif(res.jmp??)#(res.jmp)    #elseif(res.seatNo??)#(fillString(res.seatNo, 3, false))    #elseif(res.hbNo??)URES   #else#('       ')#end#(res.sellCabin) #(res.orgAirport)#(res.dstAirport) #if(res.ures?? || res.exstType?? || res.nrec??)#if(res.ures??)URES #elseif(res.nrec??)NREC #end#if(!res.exstSeat?? && res.exstType??)#(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(' ')#end#end#if(res.nrec??)NREC #end#if(res.nrs??) #(res.nrs)  #end#if(res.isAdv())ADV-PA/NP #end#if(res.sex??)#(res.sex)1/0 #end#if(res.infName??)INF1/10 #end#if(res.inputSeatNo?? && res.isNormalSeat())R#(res.inputSeatNo) #end#if(ssrListSize > 0 && ssrListSize < 6)#for(i = 0; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#end#(wrap())
#if(res.bsctNum?? || res.petcNum?? || res.adsr??)                                        #if(res.bsctNum??)BSCT#(res.bsctNum)/#(res.bsctWeight) #end#if(res.petcNum??)PETC#(res.petcNum)/#(res.petcWeight) #end#if(res.adsr??)#(res.adsr)#if(res.adsrWeight??)#(res.adsrWeight)#end#(' ')#end#(wrap())#end
#if(ssrListSize > 5 && ssrListSize < 12)                                        #for(i = 5; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 11 && ssrListSize < 18)                                        #for(i = 11; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 17 && ssrListSize < 24)                                        #for(i = 17; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(ssrListSize > 23)                                        #for(i = 23; i < ssrListSize; i++)#(res.otherSsrList.get(i)) #end#(wrap())#end
#if(res.ff??)                                        #(res.ff)/#(res.airlineCode) #(res.ffNo)/#(res.ffLevel) #(res.ffWeight)#(wrap())#end
                                        #if(res.isCnin())CNIN/#(res.name) #end#if(res.weight??)#(res.weight) #else#(res.fba) #end#(wrap())
#if(res.isEt())#for(ticketNo:res.ticketNoList)                                        ET TKNE/#if(for.index == 1)INF#end#(ticketNo)/1#(wrap())#end#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
#if(res.exstSeat??)                                 #(fillString(res.exstSeat, 3, false))    #(res.exstType)#if(res.exstWeight??)#(res.exstWeight)#end#(wrap())#end
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) //#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)//#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) //#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end//#(res.chld)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.orgAirport) #(res.airlineCode)-#if(res.orderDate??)#(res.orderDate)-#end#(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
#if(oiSize > 0)#for(oi:res.interlineResults)#(oi)#(wrap())#end#end
#end
#end
  *#(firstRes.dstAirport)* **WELCOME TO #(firstRes.airportInfo)**#(wrap())