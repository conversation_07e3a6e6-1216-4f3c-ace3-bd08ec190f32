#(args[0])#(wrap())
#for(x:results)
#(fillAfter(x.cnd<PERSON>o,cn.hutool.core.util.CharUtil::SPACE, 6)) #(x.planeModelType) #(x.planeModelVersion)  #((x.firstClass?? && x.firstSeats!=0)?x.firstClass+"/"+x.firstSeats:"") #((x.secondClass?? && x.secondSeats!=0)?x.secondClass+"/"+x.secondSeats:"") #((x.thirdClass?? && x.thirdSeats!=0)?x.thirdClass+"/"+x.thirdSeats:"") #((x.thirdClass?? && x.thirdSeats!=0)?x.thirdClass+"/"+x.thirdSeats:"") #((x.fourthClass?? && x.fourthSeats!=0)?x.fourthClass+"/"+x.fourthSeats:"") #((x.fifthClass?? && x.fifthSeats!=0)?x.fifthClass+"/"+x.fifthSeats:"")#(wrap())
#end
