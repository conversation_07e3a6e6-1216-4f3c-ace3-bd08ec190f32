#for(seg:segs)
#if(seg.isTransit())
TRANSIT R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
#if(seg.chldNo??)                                     CHD#(seg.chldNo)#(wrap())#end
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
#else
*#(seg.cityPair) R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
        SB#(seg.sbNo)  B#(seg.sbBagNo)         #if(seg.avihNo??)AVIH#(seg.avihNo) #end#if(seg.chldNo??)CHD#(seg.chldNo)#end#(wrap())
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        EDII#(seg.ediiNo)              EDIO#(seg.edioNo)#(wrap())
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
#end
#end
#if(segs.size() > 1)
TOTALS* R#(total.rNo)   C#(total.cNo)  B#(total.bNo)  #if(total.umNo??)UM#(total.umNo)  #end#if(total.wchNo??)WCH#(total.wchNo)  #end#if(total.inftNo??)I#(total.inftNo)#end#(wrap())
        SB#(total.sbNo)  B#(total.sbBagNo)         #if(total.avihNo??)AVIH#(total.avihNo) #end#if(total.chldNo??)CHD#(total.chldNo)#end#(wrap())
        SA#(total.saNo)                EXST#(total.exstNo) XCR#(total.xcrNo) ZCR#(total.zcrNo)#(wrap())
        EDII#(total.ediiNo)  EDIO#(total.edioNo)#(wrap())
        RET#(total.retNo)               CET#(total.cetNo)#(wrap())
#end