#for(seg:segs)
#set(sellCabinSize = seg.sellCabinList.size(), loopCount = (sellCabinSize - 1) / 12, null)
#if(seg.isTransit())
TRANSIT R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
#if(seg.chldNo??)                                     CHD#(seg.chldNo)#(wrap())#end
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
#else
*#(seg.cityPair) R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
        SB#(seg.sbNo)  B#(seg.sbBagNo)         #if(seg.avihNo??)AVIH#(seg.avihNo) #end#if(seg.chldNo??)CHD#(seg.chldNo)#end#(wrap())
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        #if(sellCabinSize < 12)
            #for(i = 0; i < sellCabinSize; i++) #(seg.sellCabinList.get(i))  #end#(wrap())
        RES #for(i = 0; i < sellCabinSize; i++)#(seg.resList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        CKI #for(i = 0; i < sellCabinSize; i++)#(seg.ckiList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        CET #for(i = 0; i < sellCabinSize; i++)#(seg.cetList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        #else
        #for(j = 0; j < loopCount + 1; j++)
        #if(j == loopCount)
            #for(i = j * 12; i < sellCabinSize; i++) #(seg.sellCabinList.get(i))  #end#(wrap())
        RES #for(i = j * 12; i < sellCabinSize; i++)#(seg.resList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        CKI #for(i = j * 12; i < sellCabinSize; i++)#(seg.ckiList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        CET #for(i = j * 12; i < sellCabinSize; i++)#(seg.cetList.get(i))#if(i < sellCabinSize - 1)/#end#end#(wrap())
        #else
            #for(i = j * 12; i < (j + 1) * 12; i++) #(seg.sellCabinList.get(i))  #end#(wrap())
        RES #for(i = j * 12; i < (j + 1) * 12; i++)#(seg.resList.get(i))#if(i < (j + 1) * 12 - 1 && i != sellCabinSize)/#end#end#(wrap())
        CKI #for(i = j * 12; i < (j + 1) * 12; i++)#(seg.ckiList.get(i))#if(i < (j + 1) * 12 - 1 && i != sellCabinSize)/#end#end#(wrap())
        CET #for(i = j * 12; i < (j + 1) * 12; i++)#(seg.cetList.get(i))#if(i < (j + 1) * 12 - 1 && i != sellCabinSize)/#end#end#(wrap())
        #end
        #end
        #end
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
#end
#end
#if(segs.size() > 1)
#set(totalSize = total.sellCabinList.size(), totalLoopCount = totalSize / 12, null)
TOTALS* R#(total.rNo)   C#(total.cNo)  B#(total.bNo)  #if(total.umNo??)UM#(total.umNo)  #end#if(total.wchNo??)WCH#(total.wchNo)  #end#if(total.inftNo??)I#(total.inftNo)#end#(wrap())
        SB#(total.sbNo)  B#(total.sbBagNo)         #if(total.avihNo??)AVIH#(total.avihNo) #end#if(total.chldNo??)CHD#(total.chldNo)#end#(wrap())
        SA#(total.saNo)                EXST#(total.exstNo) XCR#(total.xcrNo) ZCR#(total.zcrNo)#(wrap())
        #for(j = 0; j < totalLoopCount + 1; j++)
            #for(i = j * 12; i < (j + 1) * 12; i++) #(total.sellCabinList.get(i))  #end#(wrap())
        RES #for(i = j * 12; i < (j + 1) * 12; i++)#(total.resList.get(i))#if(i < (j + 1) * 12 - 1 && i != totalSize)/#end#end#(wrap())
        CKI #for(i = j * 12; i < (j + 1) * 12; i++)#(total.ckiList.get(i))#if(i < (j + 1) * 12 - 1 && i != totalSize)/#end#end#(wrap())
        CET #for(i = j * 12; i < (j + 1) * 12; i++)#(total.cetList.get(i))#if(i < (j + 1) * 12 - 1 && i != totalSize)/#end#end#(wrap())
        #end
        RET#(total.retNo)               CET#(total.cetNo)#(wrap())
#end