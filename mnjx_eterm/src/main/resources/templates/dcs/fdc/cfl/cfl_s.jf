#for(i:resDto[0])
#(i.flightNo)  ARRIVES DEPARTS BOARDS STAT POS  GATES  ARN   SSEL    EQUIP     TYPE#(wrap())
#for(j:i.sectionDtos)
 #(j.city)    #((j.arrTime??)?fillAfter(j.arrTime,cn.hutool.core.util.CharUtil::SPACE,6):"     ")   #((j.offTime??)?fillAfter(j.offTime,cn.hutool.core.util.CharUtil::SPACE,6):"     ")  #((j.aboardTime??)?fillAfter(j.aboardTime,cn.hutool.core.util.CharUtil::SPACE,6):"     ") #(j.stat??"    ") #(j.pos??"    ") #(j.gates??"   ")   #(j.arn??"    ")  #((j.ssel??)?fillAfter(j.ssel,cn.hutool.core.util.CharUtil::SPACE,5):"     ")   #(j.equip??"       ") #(j.type??)#(wrap())
#end
#end