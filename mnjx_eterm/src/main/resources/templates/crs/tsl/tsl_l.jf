  TKT-NUMBER       ORIG-DEST   COLLECTION    TAXS    COMM     PNR   FOP   AGENT#(wrap())
--------------------------------------------------------------------------------
#for(t:resDto.ticketList)
#(fillAfter(t.tktNumber,cn.hutool.core.util.CharUtil::SPACE,16))   #(fillAfter(t.orgDst,cn.hutool.core.util.CharUtil::SPACE,9))   #((t.collection??)?fillBefore(t.collection,cn.hutool.core.util.CharUtil::SPACE,8):"        ")     #((t.taxs??)?fillAfter(t.taxs,cn.hutool.core.util.CharUtil::SPACE,6):"      ")   #((t.comm??)?fillAfter(t.comm,cn.hutool.core.util.CharUtil::SPACE,4):"    ")    #((t.crsPnr??)?fillAfter(t.crsPnr,cn.hutool.core.util.CharUtil::SPACE,6):"      ")  CAS  #(t.agentNo)#(wrap())
#end
*==============================================================================*
 TOTAL TICKETS:   #(resDto.totalTickets)   ( 0 TICKETS VOID /  #(resDto.ticketRefund) TICKETS REFUND ) #(wrap())
*==============================================================================*