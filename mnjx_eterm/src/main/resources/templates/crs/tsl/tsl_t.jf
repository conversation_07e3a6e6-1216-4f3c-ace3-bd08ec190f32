********************************************************************************
*                  CAAC  MIS  OPTAT  DAILY-SALES-REPORT                        *
*                                                                              *
*   OFFICE : #(resDto.office)    IATA NUMBER : #(resDto.iataNumber)    DEVICE : #(fillAfter(resDto.device,cn.hutool.core.util.CharUtil::SPACE,3))  #(resDto.pid)            *
*   DATE   : #(resDto.dateCom)                               AIRLINE:   #(resDto.airline)                *
--------------------------------------------------------------------------------
  TKT-NUMBER      ORIG-DEST  COLLECTION    TAXS    COMM     PNR   FOP   AGENT#(wrap())
--------------------------------------------------------------------------------
*==============================================================================*
     TOTAL TICKETS:       #(resDto.totalTickets) (      0 TICKETS VOID /      #(resDto.ticketRefund) TICKETS REFUND )#(wrap())
----------------NORMAL TICKETS--------------------------------------------------
   NORMAL  FARE-- AMOUNT :          #(fillBefore(resDto.normalFare,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
      CARRIERS -- AMOUNT :          #(fillBefore(resDto.carries,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
        COMMIT -- AMOUNT :          #(fillBefore(resDto.commit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
   NORMAL  TAX -- AMOUNT :          #(fillBefore(resDto.normalTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
----------------REFUND TICKETS--------------------------------------------------
    NET REFUND -- AMOUNT :          #(fillBefore(resDto.netRefund,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
     DEDUCTION -- AMOUNT :          #(fillBefore(resDto.deduction,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND    TAX -- AMOUNT :          #(fillBefore(resDto.refundTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND COMMIT -- AMOUNT :          #(fillBefore(resDto.refundCommit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
*==============================================================================*