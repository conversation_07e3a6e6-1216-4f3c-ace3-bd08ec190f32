#set(C = args[0])
#set(list = results)
#for(dsg:list)
#if(C == "C")
#set(sectionList = dsg.dsgSectionVoList)
#set(sectionSize = sectionList.size())
  #(dsg.flightNo)  #((dsg.sellCabin??)?dsg.sellCabin:" ") (#(dsg.week))#(dsg.flightDate)        #for(i = 0; i < sectionSize; i++)#if(i == 0)#(sectionList[i].airportCode)       #(sectionList[i].offTime)    #(sectionList[i].planeModel) #(sectionList[i].mealCode??) #(sectionList[i].stopPoint)  #(sectionList[i].remark??)#(wrap())
#else if(i == sectionSize - 1)
                       #(sectionList[i].arrTime)   #(sectionList[i].airportCode) ELAPSED TIME  #(sectionList[i].totalFlyTime) DIST #(sectionList[i].totalFlyDistance)M#(wrap())
#else
                       #(sectionList[i].arrTime)   #(sectionList[i].airportCode)  (#(sectionList[i].stopTime)) #(sectionList[i].offTime)    #(sectionList[i].planeModel)#(wrap())
#end
#end
#else
  #(dsg.flightNo)  #((dsg.sellCabin??)?dsg.sellCabin:" ") (#(dsg.week))#(dsg.flightDate)     #(dsg.airportPair) #(dsg.offTime)  #(dsg.arrTime)    #(dsg.planeModel) #(dsg.mealCode) #(dsg.stopPoint)  #(dsg.remark)#(wrap())
#end
#end
