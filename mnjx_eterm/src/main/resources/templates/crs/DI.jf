                   DEVICE INFORMATION DISPLAY  - DEVICE #(results[0].printerNo)#(wrap())
            DEVICE STATUS                      DEVICE DEFINITION#(wrap())
            -------------                      -----------------#(wrap())
      CONTROL PID: #(results[0].controlPid)        OFFICE: #(results[0].office)#(wrap())
    CONTROL AGENT: #(results[0].controlAgent)         PID: #(results[0].printerPid)#(wrap())
           STATUS: #(results[0].printerStatus)  ATTRIBUTE: #(results[0].printAttribute)#(wrap())
            INPUT: #(results[0].inputStatus)         MODE: #(results[0].printerMode)#(wrap())
           OUTPUT: #(results[0].outputStatus)        TYPE: #(results[0].printerType)#(wrap())
             NACK: #(results[0].isNormal)        CURRENCY: #(results[0].currencyType)#(wrap())
          TICKETS: 0#(wrap())
    BOARDING PASS: 0#(wrap())
          AMS PID:#(wrap())
#(wrap())
#if(results[0].startTicket ??)
          LAST TKT #   AIRLINE    TICKET NUMBER RANGE#(wrap())
          ----------   -------   ----------------------#(wrap())
          #(results[0].lastTicket)    #(results[0].airline)     #(results[0].startTicket) / #if(results[0].endTicket ??)#(results[0].endTicket)#(wrap())#end
#end