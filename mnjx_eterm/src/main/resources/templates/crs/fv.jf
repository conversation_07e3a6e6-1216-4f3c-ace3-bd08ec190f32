 #(args[0].flightDate)(#(args[0].week)) #(args[0].cityPair) VIA #(args[0].airlineCode??"") #(args[0].seatLevel??"")#(wrap())
#for(flight:results[0])
#set(sellCabinAndSeat = flight.sellCabinAndSeat, seatSize = sellCabinAndSeat.size(), null)
#(fillIndex((for.index + 1), 2, false)) #if(flight.carrierFlight??)#("*")#else#(" ")#end#(flight.flightNo)  #(flight.org)#(flight.dst) #(flight.estimateOff)#(flight.estimateOffChange) #(flight.estimateArr)#(flight.estimateArrChange) #(flight.planeModelType) #(flight.stopPoint)#(flight.allowAsr)#(flight.mealCode) E DS##for(x : sellCabinAndSeat)#if(for.index < 10)#(" ")#(subString(x,0,2))#else#break#end#end#(wrap())
#if(seatSize > 10)                 #for(i = 10; i < seatSize; i++)#(" ")#(subString(sellCabinAndSeat.get(i),0,2))#end#(wrap())#end
#end