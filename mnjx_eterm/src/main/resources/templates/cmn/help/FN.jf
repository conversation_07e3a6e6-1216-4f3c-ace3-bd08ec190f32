HELP INFORMATION FOR FN: TRANSACTION. APPLICATION GRP IS TKT
THIS FUNCTION IS ALLOWED UNDER THE FOLLOWING USER GROUPS
THE SECURITY GROUP IS : 1
REPRESENTING THE FOLLOWING USER GROUPS: 1-144
***************增加燃油附加费********
输入CN和YQ税:
FN:FCNY1000.00/SCNY1000.00/C3.00/TCNY50.00CN/TCNY40.00YQ

输入三种以上的税:YQ在FN中输入
FN:FCNY1000.00/SCNY1000.00/C3.00/TCNY50.00CN/TCNY40.00YQ/TCNY100.00XT
FC:/XT

输入三种以上的税:YQ在FC中输入
FN:FCNY1000.00/SCNY1000.00/C3.00/TCNY50.00CN/TCNY29.00XY/TCNY100.00XT
FC:/XT 40.00YQ 60.00US

免税:
FN:FCNY1000.00/SCNY1000.00/C3.00/TEXEMPTCN/TEXEMPTYQ
*************************************
****************指令内容详细介绍（此为模拟系统特有）*********************
F               初次开票标识，一般票面价记录在此。
                若为再次开票，应为 RCNYxxxx.xx
-----------------------------------------------------------------------------
CNY xxxx.xx     货币及数额 -- 用于填写 FARE 票价栏（必须输入的项）
                CNY人民币
                USD美元
-----------------------------------------------------------------------------
ECNYxxxx.xx     等值付款数额 -- 用于填写 EQUIV. FARE PD.实付等值栏
-----------------------------------------------------------------------------
SCNYxxxx.xx     现金收受数额 -- 用于填写 CASH COLLECTION 栏（必须输入的项）
-----------------------------------------------------------------------------
Cx.xx           代理费费率 -- 用于填写 COMM.RATE 栏（必须输入的项）
-----------------------------------------------------------------------------
OCNYxxx.xxUS    上次出票时已经支付的某种税款
-----------------------------------------------------------------------------
TCNYxxx.xxUS    本次出票需支付的某种税款 -- 用于填写 TAX 税栏
                若某一国家只有一种税,可以按照该格式输入,US 为国家两字代码
-----------------------------------------------------------------------------
TXYCNYxxxx.xxUS 某种税款 -- 用于填写 TAX 税栏
                a. 若某一国家有多种税,则应分别列出每一种税.
                如XY为税名称,US 为国家两字代码.
                当税项超过三项时,列出其中的两种,以及其他税项的总和.
                用XT表示,记录在FN中,其他税项的具体内容可在票价计算FC中加以说明.
                参见技巧 7;如 TXACNY17.00US/TXYCNY50.00US/TXTCNY240.00US
                b.若为免税,应输入 TEXEMPTAG AG 为国家名。
                XCNYxxxx.xx 税款总额 -- 用于填写 TAX AMOUNT 栏
                此项可省,通常在输完税款后,电脑在此给出税的总数
-----------------------------------------------------------------------------
ACNYxxxx.xx     对初次出票，该项是电脑自动给出的，它是票面价与税款的总和，将
                打印在总数栏；对再次出票，该项必须手工输入，是实收票价与补交
                税款的总和，将打印在总数栏。
-----------------------------------------------------------------------------
