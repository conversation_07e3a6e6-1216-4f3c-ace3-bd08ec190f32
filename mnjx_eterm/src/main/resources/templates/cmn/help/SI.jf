HELP INFORMATION FOR SI: TRANSACTION. APPLICATION GRP IS SYS
THIS FUNCTION IS ALLOWED UNDER THE FOLLOWING USER GROUPS
THE SECURITY GROUP IS : 1
REPRESENTING THE FOLLOWING USER GROUPS: 1-144
DESCRIPTION:
<PERSON><PERSON><PERSON> AN AGENT TO ACCESS THE FUNCTIONAL CAPABILITY OF THE SYSTEM
BY ESTABLISHING THE AGENT IN THE  CONTEXT OF A USER GROUP AND AN
AGENT AREA.
INPUT FORMAT:
      SI: MODE AGENT PERSONAL-ACCOUNT-NUMBER USER-GROUP
          AGENT-AREA CITY-OFFICE
EXAMPLES:
      SI:2003/38481H/41/PEK102