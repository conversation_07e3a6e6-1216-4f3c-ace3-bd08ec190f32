#set(firstRes = results[0])
#for(res:results)
#if(res.airportEname.length() > 22)
#(res.airportCode),#(fillString(res.airportEname, 50, false))#(res.cityCode)/#(firstRes.countryIso)#(wrap())
    #(fillString(res.airportCname, 50, false))#(res.latitude)/#(res.longitude)#(wrap())
#else
#(res.airportCode),#(fillString(res.airportEname, 22, false))#(fillString(res.airportCname, 22, false))#(res.cityCode)/#(firstRes.countryIso)#(wrap())
#(res.latitude)/#(res.longitude)#(wrap())
#end
#end