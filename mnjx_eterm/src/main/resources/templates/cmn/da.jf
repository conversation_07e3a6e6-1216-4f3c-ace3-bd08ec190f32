#(action)
#for (cabinet:results[0])
#set(mnjxSi=cabinet.mnjxSi)
#set(mnjxLevel=cabinet.mnjxLevel)
#set(mnjxOffice=results[2])
#(cabinet.label)#(cabinet.activity == 'Y'?'*':' ') #((mnjxSi??)?mnjxSi.siNo:"AVAIL")  #((mnjxSi??)?date2PreCom(mnjxSi.signInDatetime,5):"")  #((mnjxSi??)?date2hm(mnjxSi.signInDatetime):"")  #((mnjxLevel??)?mnjxLevel.levelCode:"")  #((mnjxSi?? && mnjxOffice??)?mnjxOffice.officeNo:"")
#end
#set(memoryDataCabinet= results[1])
PID  = #((memoryDataCabinet.mnjxSi??)?memoryDataCabinet.mnjxSi.siPid:"00000")   HARDCOPY  = 62124
TIME =  #(nowHm2ComHm())    DATE = #(subPre(ymd2Com(today()), 5))    HOST = LUCK
AIRLINE = 1E    SYSTEM =  MNJX  APPLICATION =  2
