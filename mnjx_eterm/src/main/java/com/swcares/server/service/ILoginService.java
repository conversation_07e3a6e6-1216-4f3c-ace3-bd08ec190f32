package com.swcares.server.service;

import com.swcares.core.unified.UnifiedResultException;

/**
 * <AUTHOR>
 */
public interface ILoginService {

    /**
     * 登陆认证
     * 使用OFFICE进行登陆认证，就是eterm客户端需要连接主机，客户端使用的账户数据
     *
     * @param msg 162长度的字节数据，具体每个字节代码什么意思还需要完善
     * @throws UnifiedResultException 统一异常
     */
    void login(byte[] msg) throws UnifiedResultException;

    /**
     * 登录的具体逻辑
     *
     * @param username 用户名
     * @param password 密码
     * @throws UnifiedResultException 统一异常
     */
    void login(String username, String password) throws UnifiedResultException;

    /**
     * si 工作号退出
     */
    void logout();

    /**
     * Title: restoreStatus
     * Description: 程序意外关闭（服务器重启、应用重启等），需要将某些状态恢复
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/6/15 11:41
     */
    void restoreStatus();
}
