package com.swcares.server.netty;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxConfig;
import com.swcares.eterm.base.service.IRestoreCkiService;
import com.swcares.server.service.IEtermService;
import com.swcares.service.IMnjxConfigService;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @date 2023/7/19 14:35
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class WebSocketServerHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {

    @Resource
    private IEtermService iEtermService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IRestoreCkiService iRestoreCkiService;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        // 每个channel一定绑定有自己的内存对象
        Optional.ofNullable(ctx.channel()).ifPresent(channel -> {
            // 每个连接都有自己的内存对象
            MemoryData memoryData = applicationContext.getBean(MemoryData.class);
            // 内存对象放入通道
            memoryData.setChannel(channel);
            // web请求标识
            memoryData.setRequestFromWebSocket(true);
            log.info("收到web用户{}的连接！", channel.remoteAddress());
            channel.attr(AttributeKey.newInstance(channel.id().asLongText())).set(memoryData);
        });
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame msg) {
        log.info("线程{}发生读取事件....远程地址：{}，本地地址：{}", Thread.currentThread().getName(), ctx.channel().remoteAddress(), ctx.channel().localAddress());
        Optional.ofNullable(ctx.channel()).ifPresent(channel -> {
            // 每次数据的读取都把通道设置到线程上面去，这样就可以通过当前线程绑定的通道获取到通道上面绑定的内存对象
            MemoryDataUtils.setChannel(channel);
            log.info("线程{}收到客户端的数据:{}", Thread.currentThread().getName(), msg);
            String text = msg.text();
            if (StrUtil.isEmpty(text)) {
                channel.writeAndFlush(new TextWebSocketFrame("请输入指令内容"));
                return;
            }
            byte[] bytes;
            try {
                bytes = text.getBytes(CharsetUtil.GBK);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            // 登录
            if (text.startsWith("login")) {
                String[] split = text.substring(6).trim().split(StrUtil.SPACE);
                String userName = split[0];
                if (userName.getBytes().length > 16) {
                    channel.writeAndFlush(new TextWebSocketFrame("用户名过长"));
                    return;
                }
                String password = split[1];
                if (password.getBytes().length > 32) {
                    channel.writeAndFlush(new TextWebSocketFrame("密码过长"));
                    return;
                }
                byte[] headBytes = new byte[]{1, 0, 0, -92};
                byte[] userNameBytes = new byte[16];
                System.arraycopy(userName.getBytes(), 0, userNameBytes, 0, userName.getBytes().length);
                byte[] passwordBytes = new byte[32];
                System.arraycopy(password.getBytes(), 0, passwordBytes, 0, password.getBytes().length);
                byte[] tailBytes = new byte[]{100, 54, 54, 100, 54, 100, 51, 98,
                        55, 98, 102, 100, 49, 57, 50, 46,
                        49, 54, 56, 46, 51, 49, 46, 50,
                        49, 55, 32, 51, 56, 52, 55, 48,
                        49, 48, 0, 48, 48, 48, 48, 48,
                        48, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0,
                        0, 0, 0, 0, 0, 0, 0, 0};
                byte[] tempBytes = new byte[headBytes.length + userNameBytes.length + passwordBytes.length + tailBytes.length];
                System.arraycopy(headBytes, 0, tempBytes, 0, headBytes.length);
                System.arraycopy(userNameBytes, 0, tempBytes, headBytes.length, userNameBytes.length);
                System.arraycopy(passwordBytes, 0, tempBytes, headBytes.length + userNameBytes.length, passwordBytes.length);
                System.arraycopy(tailBytes, 0, tempBytes, headBytes.length + userNameBytes.length + passwordBytes.length, tailBytes.length);
                bytes = tempBytes;
//                bytes = new byte[]{1, 0, 0, -92,
//                        83, 72, 65, 48, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
//                        54, 54, 54, 54, 54, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
//                        100, 54, 54, 100, 54, 100, 51, 98,
//                        55, 98, 102, 100, 49, 57, 50, 46,
//                        49, 54, 56, 46, 51, 49, 46, 50,
//                        49, 55, 32, 51, 56, 52, 55, 48,
//                        49, 48, 0, 48, 48, 48, 48, 48,
//                        48, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0,
//                        0, 0, 0, 0, 0, 0, 0, 0};
            }
            // 处理指令
            else {
                byte[] headBytes = new byte[]{1, 0, 0, 38, 0, 0, 0, 1, 57, 81, 112, 2, 27, 11, 32, 32, 0, 15, 30};
                byte[] tailBytes = new byte[]{32, 3};
                byte[] tempBytes = new byte[headBytes.length + bytes.length + tailBytes.length];
                System.arraycopy(headBytes, 0, tempBytes, 0, headBytes.length);
                System.arraycopy(bytes, 0, tempBytes, headBytes.length, bytes.length);
                System.arraycopy(tailBytes, 0, tempBytes, headBytes.length + bytes.length, tailBytes.length);
                bytes = tempBytes;
            }
            ConcurrentLinkedQueue<byte[]> queue = iEtermService.dealData(bytes);
            log.debug("这个默认的大小:{}", queue.size());
            // 保证顺序的执行
            while (!queue.isEmpty()) {
                byte[] poll = queue.poll();
                // 向web返回的指令执行结果数组需要去掉头尾
                if (ObjectUtil.isNotEmpty(poll)) {
                    // 登录成功
                    if (poll.length == 20) {
                        log.info("线程{}客户端channel{}登录office成功", Thread.currentThread().getName(), channel);
                        channel.writeAndFlush(new TextWebSocketFrame("Office登录成功"));
                    }
                    // 登录成功后返回的第二条信息
                    else if (poll.length == 18) {
                        log.info("线程{}客户端channel{}登录office成功", Thread.currentThread().getName(), channel);
                        channel.writeAndFlush(new TextWebSocketFrame(""));
                    }
                    // 心跳或其他信息
                    else {
//                    byte[] resultBytes = new byte[poll.length - 26];
//                    System.arraycopy(poll, 19, resultBytes, 0, resultBytes.length);
//                        String result;
//                        try {
//                            result = new String(poll, CharsetUtil.GBK);
//                        } catch (UnsupportedEncodingException e) {
//                            throw new RuntimeException(e);
//                        }
                        String result = new String(poll, StandardCharsets.UTF_8);
                        log.info("线程{}开始向客户端channel{}返回数据:{}", Thread.currentThread().getName(), channel, result);
                        channel.writeAndFlush(new TextWebSocketFrame(StrUtil.format(">{}\r\n{}\r\n", text, result)));
//                        log.info("线程{}开始向客户端channel{}返回数据:{}", Thread.currentThread().getName(), channel, result);
//                        channel.writeAndFlush(new TextWebSocketFrame(result));
                    }
                } else {
                    // IG第一次执行没有回显，做特殊处理
                    if (StrUtil.equalsAnyIgnoreCase(text, "ig", "i")) {
                        log.info("线程{}开始向客户端channel{}返回数据:{}", Thread.currentThread().getName(), channel, text);
                        channel.writeAndFlush(new TextWebSocketFrame(StrUtil.format(">{}\r\n", text)));
                    }
                }
            }
        });
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        log.info("将channel添加到集合中:{}", ctx.channel());
        Optional.ofNullable(ctx.channel()).ifPresent(MemoryDataUtils.CHANNEL_GROUP::add);
        super.handlerAdded(ctx);
    }

    /**
     * handler移除
     *
     * @param ctx 上下文
     * @throws Exception 异常
     */
    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        log.info("从集合中移除当前channel:{}", ctx.channel());
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESTORE")
                .one();
        if (ObjectUtil.isNotEmpty(config) && Constant.STR_ONE.equals(config.getAvailable())) {
            log.info("websocket channel移除执行还原操作");
            iRestoreCkiService.dealWithRestore();
        }
        MemoryDataUtils.CHANNEL_GROUP.removeIf(channel -> channel == ctx.channel());
        super.handlerRemoved(ctx);
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        log.debug("数据读取完成....远程地址：{}，本地地址：{}", ctx.channel().remoteAddress(), ctx.channel().localAddress());
        super.channelReadComplete(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        Channel channel = ctx.channel();
        log.info("客户端主动断开了连接....远程地址：{}，本地地址：{}", channel.remoteAddress(), channel.localAddress());
        channel.close();
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        cause.printStackTrace();
        Channel channel = ctx.channel();
        log.error("连接通信被迫中断....远程地址：{}，本地地址：{}", channel.remoteAddress(), channel.localAddress());
        log.error("连接通信中断,发生异常:{}", cause.getMessage());
        channel.close();
        super.exceptionCaught(ctx, cause);
    }
}
