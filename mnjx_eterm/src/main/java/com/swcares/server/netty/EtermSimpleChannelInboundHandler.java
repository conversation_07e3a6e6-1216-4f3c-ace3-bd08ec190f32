package com.swcares.server.netty;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxConfig;
import com.swcares.eterm.base.service.IRestoreCkiService;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.server.service.IEtermService;
import com.swcares.service.IMnjxConfigService;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class EtermSimpleChannelInboundHandler extends SimpleChannelInboundHandler<byte[]> {
    /**
     * 容器对象
     */
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private IEtermService iEtermService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IRestoreCkiService iRestoreCkiService;

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        // 每个channel一定绑定有自己的内存对象
        Optional.ofNullable(ctx.channel()).ifPresent(channel -> {
            // 每个连接都有自己的内存对象
            MemoryData memoryData = applicationContext.getBean(MemoryData.class);
            // 内存对象放入通道
            memoryData.setChannel(channel);
            log.info("收到Eterm用户{}的连接！", channel.remoteAddress());
            channel.attr(AttributeKey.newInstance(channel.id().asLongText())).set(memoryData);
            log.debug("压测测试用，连接建立，初始化MemoryData，ID：{}---{}", memoryData.getMemoryDataId(), channel);
        });
    }

    /**
     * channel 添加，后续使用
     *
     * @param ctx 上下文
     * @throws Exception 异常
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        log.info("将channel添加到集合中:{}", ctx.channel());
        Optional.ofNullable(ctx.channel()).ifPresent(MemoryDataUtils.CHANNEL_GROUP::add);
        super.handlerAdded(ctx);
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, byte[] msg) {
        log.info("线程{}发生读取事件....远程地址：{}，本地地址：{}", Thread.currentThread().getName(), ctx.channel().remoteAddress(), ctx.channel().localAddress());
        Optional.ofNullable(ctx.channel()).ifPresent(channel -> {
            // 每次数据的读取都把通道设置到线程上面去，这样就可以通过当前线程绑定的通道获取到通道上面绑定的内存对象
            MemoryDataUtils.setChannel(channel);
            log.info("线程{}收到客户端的数据:{}", Thread.currentThread().getName(), msg);
            ConcurrentLinkedQueue<byte[]> queue = iEtermService.dealData(msg);
            log.debug("这个默认的大小:{}", queue.size());
            // 保证顺序的执行
            while (!queue.isEmpty()) {
                byte[] poll = queue.poll();
                log.info("线程{}开始向客户端channel{}返回数据:{}", Thread.currentThread().getName(), channel, poll);
                channel.writeAndFlush(poll);
            }
        });
    }

    /**
     * handler移除
     *
     * @param ctx 上下文
     * @throws Exception 异常
     */
    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        log.info("从集合中移除当前channel:{}", ctx.channel());
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESTORE")
                .one();
        if (ObjectUtil.isNotEmpty(config) && Constant.STR_ONE.equals(config.getAvailable())) {
            log.info("channel移除执行还原操作");
            iRestoreCkiService.dealWithRestore();
        }
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        memoryData.getMemoryDataFt().clear();
        // 清除值机缓存
        iCkiCacheService.clearPuCache(memoryData);
        iCkiCacheService.clearHbpuCache(memoryData);

        iCkiCacheService.clearBfCache(memoryData);

        iCkiCacheService.clearSbCache(memoryData);

        iCkiCacheService.clearPaCache(memoryData);
        iCkiCacheService.clearHbpaCache(memoryData);

        iCkiCacheService.clearPrCache(memoryData);
        iCkiCacheService.clearHbprCache(memoryData);

        iCkiCacheService.clearPwCache(memoryData);
        iCkiCacheService.clearHbpwCache(memoryData);

        iCkiCacheService.clearPdAbcInfo(memoryData);
        iCkiCacheService.clearPdDefaultInfo(memoryData);
        iCkiCacheService.clearPdSbyInfo(memoryData);
        MemoryDataUtils.CHANNEL_GROUP.removeIf(channel -> channel == ctx.channel());
        super.handlerRemoved(ctx);
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        log.debug("数据读取完成....远程地址：{}，本地地址：{}", ctx.channel().remoteAddress(), ctx.channel().localAddress());
        super.channelReadComplete(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        Channel channel = ctx.channel();
        log.info("客户端主动断开了连接....远程地址：{}，本地地址：{}", channel.remoteAddress(), channel.localAddress());
        channel.close();
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        cause.printStackTrace();
        Channel channel = ctx.channel();
        log.error("连接通信被迫中断....远程地址：{}，本地地址：{}", channel.remoteAddress(), channel.localAddress());
        log.error("连接通信中断,发生异常:{}", cause.getMessage());
        channel.close();
        super.exceptionCaught(ctx, cause);
    }
}
