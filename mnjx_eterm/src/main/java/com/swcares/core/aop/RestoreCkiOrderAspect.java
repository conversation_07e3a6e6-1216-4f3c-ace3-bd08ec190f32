package com.swcares.core.aop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.type.RestoreAfter;
import com.swcares.core.type.RestoreBefore;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxConfig;
import com.swcares.eterm.base.service.IRestoreCkiService;
import com.swcares.service.IMnjxConfigService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Slf4j
@Aspect
@Component
public class RestoreCkiOrderAspect {

    @Resource
    private IRestoreCkiService iRestoreCkiService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    /**
     * Title: beforeRestore
     * Description:
     * 通过 @RestoreBefore 注解切入方法
     * 前置切入：原逻辑为修改、删除，存储需要还原的原数据（修改、插入操作）在修改、删除前执行
     * ！！前置切入（还原操作为update、insert）记录的json数据为完整的实体对象数据
     *
     * @param point
     * <AUTHOR>
     * @date 2021/12/28 14:00
     */
    @Before("@annotation(com.swcares.core.type.RestoreBefore)")
    public void beforeRestore(JoinPoint point) {
        log.info("========前置操作还原start=========");
        doAspectRestore(point);
        log.info("========前置操作还原end=========");
    }

    /**
     * Title: afterRestore
     * Description:
     * 通过 @RestoreAfter 注解切入方法
     * 后置切入：原逻辑为插入，存储需要还原插入的数据（删除操作）在插入后执行
     * ！！后置切入（还原操作为delete）的json数据只需要有id字段值的实体对象数据
     *
     * @param point
     * <AUTHOR>
     * @date 2021/12/28 14:00
     */
    @AfterReturning("@annotation(com.swcares.core.type.RestoreAfter)")
    public void afterRestore(JoinPoint point) {
        log.info("========后置操作还原start=========");
        doAspectRestore(point);
        log.info("========后置操作还原end=========");
    }

    /**
     * Title: doAspectRestore
     * Description: 切面操作还原，因为环绕增强不太适合本操作，所以写个公共方法，在前置增强和执行成功后置增强调用<br>
     *
     * @param point
     * @return
     * <AUTHOR>
     * @date 2021/12/29 9:56
     */
    private void doAspectRestore(JoinPoint point) {
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESTORE")
                .one();
        if (ObjectUtil.isEmpty(config) || Constant.STR_ZERO.equals(config.getAvailable())) {
            return;
        }

        Object[] parameters = point.getArgs();
        log.info("方法参数：{}", parameters);

        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        log.info("切面获取到方法：{}", method.getName());

        String entityName;
        String operateType;
        String methodName;
        RestoreBefore restoreBefore = method.getAnnotation(RestoreBefore.class);
        if (ObjectUtil.isNotEmpty(restoreBefore)) {
            entityName = restoreBefore.entityName();
            operateType = restoreBefore.operateType();
            methodName = restoreBefore.methodName();
        } else {
            RestoreAfter restoreAfter = method.getAnnotation(RestoreAfter.class);
            entityName = restoreAfter.entityName();
            operateType = restoreAfter.operateType();
            methodName = restoreAfter.methodName();
        }

        log.info("切面获取到方法的注解数据库实体名：{}", entityName);
        log.info("切面获取到方法的注解数据库操作方式：{}", operateType);
        log.info("切面获取到方法的注解获取json数据方法的名字：{}", methodName);

        Class<?> clazz = point.getTarget().getClass();
        List<Method> methods = Arrays.asList(clazz.getMethods());
        methods = methods.stream()
                .filter(m -> methodName.equals(m.getName()))
                .collect(Collectors.toList());
        for (Method thisMethod : methods) {
            List<String> jsonList;
            try {
                jsonList = (List<String>) thisMethod.invoke(point.getTarget(), parameters);
            } catch (Exception e) {
                log.error("进行还原数据存储出现错误，反射调用的方法参数异常（出现意料外的方法重载），跳过本次方法调用");
                e.printStackTrace();
                continue;
            }
            for (String json : jsonList) {
                iRestoreCkiService.restoreStatus(json, entityName, operateType, StrUtil.toString(parameters[parameters.length - 1]));
            }
        }
    }
}
