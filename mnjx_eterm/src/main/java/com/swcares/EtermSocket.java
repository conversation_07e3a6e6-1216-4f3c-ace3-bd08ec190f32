package com.swcares;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "eterm.socket")
@Data
@Component
public class EtermSocket {

    /**
     * 服务器ip
     */
    private String ip;

    /**
     * 服务器端口
     */
    private int port;
    /**
     * websocket服务端口
     */
    private int webPort;
    /**
     * 连接监听线程，就是主线程
     */
    private int bossNum;
    /**
     * 工作线程大小，如果值越大，对资源的消耗越大
     */
    private int workNum;
}
