package com.swcares.eterm.cmn.service;

import com.swcares.core.unified.UnifiedResultException;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IDateService {

    /**
     * generateFmt1
     *
     * @param cmd                cmd
     * @param defaultIntervalArr defaultIntervalArr
     * @param dateArr            dateArr
     * @return generateFmt1
     * @throws UnifiedResultException UnifiedResultException
     */
    String generateFmt1(String cmd, List<Integer> defaultIntervalArr, String[] dateArr) throws UnifiedResultException;

    /**
     * generateFmt2
     *
     * @param dateArr dateArr
     * @return generateFmt2
     */
    String generateFmt2(String[] dateArr);

    /**
     * generateFmt3
     *
     * @param defaultIntervalArr defaultIntervalArr
     * @param dateArr            dateArr
     * @return generateFmt3
     */
    String generateFmt3(List<Integer> defaultIntervalArr, String[] dateArr);
}
