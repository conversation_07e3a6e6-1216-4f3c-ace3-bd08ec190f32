package com.swcares.eterm.cmn.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.service.IAnService;
import com.swcares.eterm.dcs.cki.obj.dto.AnDto;

import javax.annotation.Resource;

/**
 * AN指令：旧密码/新密码 密码修改
 *
 * <AUTHOR>
 */
@OperateType(action = "AN")
public class AnHandler implements Handler {

    @Resource
    public IAnService iAnService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        AnDto anDto = iAnService.parse(cmd);
        return iAnService.handle(anDto);
    }
}
