package com.swcares.eterm.cmn.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.eterm.cmn.service.IPnService;
import com.swcares.eterm.crs.service.IAvService;
import com.swcares.obj.dto.AvDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PnServiceImpl implements IPnService {

    @Resource
    private IAvService iAvService;

    @Override
    public List<Integer> getPnFull(MemoryDataHandlerResult memoryDataHandlerResult) {
        // 当前页码情况
        List<Integer> currentPages = memoryDataHandlerResult.getCurrentPages();
        int totalPage = memoryDataHandlerResult.getTotalPage();
        switch (currentPages.size()) {
            // 一个页码
            case 1:
                // 当前页码
                Integer currentPage = currentPages.get(BigDecimal.ZERO.intValue());
                // 最多就2页的情况
                if (totalPage - Constant.THREE < 0) {
                    currentPages = ListUtil.toList(currentPage + 1);
                }
                // 翻页后是最后一页了
                else if (currentPage == totalPage - 1) {
                    currentPages = ListUtil.toList(totalPage - 1);
                } else {
                    currentPages = ListUtil.toList(currentPage + 1, currentPage + 2);
                }
                break;
            // 两个页码
            case 2:
                Integer fCurrentPage = currentPages.get(BigDecimal.ZERO.intValue());
                Integer lCurrentPage = currentPages.get(BigDecimal.ONE.intValue());
                int fNextPage = fCurrentPage + 2;
                int lNextPage = lCurrentPage + 2;
                // 当前最后一页
                if (lCurrentPage == totalPage - 1) {
                    currentPages = ListUtil.toList(fCurrentPage, lCurrentPage);
                }
                // 这次翻页后只有一页到最后一页
                else if (lCurrentPage == totalPage - Constant.TWO) {
                    currentPages = ListUtil.toList(totalPage - 1);
                } else {
                    currentPages = ListUtil.toList(fNextPage, lNextPage);
                }
                break;
            default:
                // 默认也什么都不做
                break;
        }
        return currentPages;
    }

    @Override
    public List<Integer> getPnHalf(MemoryDataHandlerResult memoryDataHandlerResult) {
        // 当前页码情况
        List<Integer> currentPages = memoryDataHandlerResult.getCurrentPages();
        switch (currentPages.size()) {
            case 1:
                // 当前页码
                Integer currentPage = currentPages.get(BigDecimal.ZERO.intValue());
                if (currentPage > memoryDataHandlerResult.getTotalPage() - Constant.TWO) {
                    int totalPage = memoryDataHandlerResult.getTotalPage();
                    currentPages = ListUtil.toList(totalPage - 1);
                } else {
                    // 获取下一个页码
                    currentPages = ListUtil.toList(currentPage + 1);
                }
                break;
            case 2:
                // 如果是两个页码，就取第一个页码
                currentPage = currentPages.get(BigDecimal.ZERO.intValue());
                if (currentPage > memoryDataHandlerResult.getTotalPage() - 1) {
                    int totalPage = memoryDataHandlerResult.getTotalPage();
                    currentPages = ListUtil.toList(totalPage);
                } else {
                    // 第一个页码数加1
                    currentPages = ListUtil.toList(currentPage + 1);
                }
                break;
            default:
                // 默认也什么都不做
                break;
        }
        return currentPages;
    }

    @Override
    public UnifiedResult handleAvPn() throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
        AvDto retrieveDto = iAvService.getAvDtoCache(memoryData.getMemoryDataPnr());
        UnifiedResult unifiedResult = iAvService.getAvUnifiedResultCache(memoryDataPnr);
        String flightDate = retrieveDto.getFlightDate();
        // 如果是当天查询后使用PN，则需要去除条件中的起飞时间限制
        DateTime oldFlightDate = DateUtil.parseDate(DateUtils.com2ymd(flightDate));
        DateTime now = DateTime.now();
        if (oldFlightDate.toDateStr().equals(now.toDateStr())) {
            retrieveDto.setOffTime(null);
        }
        // 航班日期+1
        DateTime dateTime = DateUtil.offsetDay(oldFlightDate, 1);
        flightDate = DateUtils.date2ymd(dateTime);
        retrieveDto.setFlightDate(flightDate);

        iAvService.handle(retrieveDto);
        return unifiedResult;
    }
}
