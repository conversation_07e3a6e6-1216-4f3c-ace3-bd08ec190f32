package com.swcares.eterm.cmn.handler;

import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.NumberUtils;
import com.swcares.core.util.ObjectUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.service.IPgService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Pattern;

/**
 * PB and PN ORDER分页处理
 *
 * <AUTHOR>
 */
@Slf4j
@OperateType(action = "PG", predicate = false, staging = false, shorthand = true)
public class PgHandler implements Handler {
    private static final Pattern PATTERN_REG_FMT = Pattern.compile("^[Pp][Gg]:?(1)?$");

    @Resource
    private IPgService iPgService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        if (!ReUtils.isMatch(PATTERN_REG_FMT, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        String arg = ReUtils.getGroup1(PATTERN_REG_FMT, cmd);

        MemoryDataHandlerResult memoryDataHandlerResult = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult();
        List<Integer> currentPages;
        if (ObjectUtils.isNotNull(arg) && NumberUtils.parseInt(arg) == BigDecimal.ONE.intValue()) {
            currentPages = iPgService.getPgFull(memoryDataHandlerResult);
        } else {
            currentPages = iPgService.getPgHalf(memoryDataHandlerResult);
        }
        memoryDataHandlerResult.setCurrentPages(currentPages);
        return memoryDataHandlerResult.getResult();
    }
}
