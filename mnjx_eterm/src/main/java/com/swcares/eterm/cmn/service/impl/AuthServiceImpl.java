package com.swcares.eterm.cmn.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.swcares.eterm.cmn.mapper.AuthMapper;
import com.swcares.eterm.cmn.service.IAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 处理级别权限  service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements IAuthService {

    @Resource
    private AuthMapper authMapper;

    /**
     * 检查用户级别 是否拥有该指令的 权限
     *
     * @param instruction 指令
     * @param level       级别
     * @return 检查用户级别 是否拥有该指令的 权限
     */
    @Override
    public boolean retrieveAuth(String instruction, String level) {
        List<Map<String, Object>> list = authMapper.retrieveAuth(instruction.trim(), level.trim());
        return CollUtil.isNotEmpty(list);
    }
}
