package com.swcares.eterm.cmn.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.regex.Pattern;

/**
 * $$OPEN指令
 *
 * <AUTHOR>
 */
@Slf4j
@OperateType(action = "$$OPEN", predicate = false)
public class OpenHandler implements Handler {

    private static final String FMT_OPEN_REG = "^\\${2}OPEN:(\\w+)";

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        StringBuilder sb = new StringBuilder();
        String goal = "";
        if (ReUtil.isMatch(Pattern.compile(FMT_OPEN_REG), cmd)) {
            List<String> args = ReUtil.getAllGroups(Pattern.compile(FMT_OPEN_REG), cmd);
            goal = args.get(1);
        }
        switch (goal) {
            case "TIPC3":
                sb.append("SESSION PATH OPEN TO:TIPC3\r");
                break;
            case "TIPJ":
                //离港生产系统
                sb.append("SESSION PATH OPEN TO:TIPJ\r");
                break;
            case "TIPD2":
                //离港测试系统
                sb.append("SESSION PATH OPEN TO:TIPD2\r");
                break;
            case "TIPB":
                //离港测试系统
                sb.append("SESSION PATH OPEN TO:TIPB\r");
                break;
            default:
                sb.append("SESSION PATH DOWN\r");
        }
        return sb.toString();
    }
}
