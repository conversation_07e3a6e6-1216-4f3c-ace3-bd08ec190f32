package com.swcares.eterm.cmn.mapper;

import com.swcares.eterm.cmn.dto.CntzResultDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/4 10:45
 */
public interface CntzMapper {

    /**
     * retrieveCityInfo
     *
     * @param cityName cityName
     * @return retrieveCityInfo
     */
    List<CntzResultDto> retrieveCityInfo(@Param("cityName") String cityName);

    /**
     * Title: retrieveAirlineInfoByName
     * Description: <br>
     *
     * @param airlineName
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/2/5 10:12
     */
    List<CntzResultDto> retrieveAirlineInfoByName(@Param("airlineName") String airlineName);

    /**
     * Title: retrieveAirportByCityCode
     * Description: <br>
     *
     * @param cityCode
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/2/19 13:35
     */
    List<CntzResultDto> retrieveAirportByCityCode(@Param("cityCode") String cityCode);

    /**
     * Title: retrieveAirlineInfoByAirlineCode
     * Description: <br>
     *
     * @param airlineCode
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/2/22 14:16
     */
    List<CntzResultDto> retrieveAirlineInfoByAirlineCode(@Param("airlineCode") String airlineCode);

    /**
     * Title: retrieveAirportByAirportCode
     * Description: <br>
     *
     * @param airportCode
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/2/22 14:42
     */
    List<CntzResultDto> retrieveAirportByAirportCode(@Param("airportCode") String airportCode);

    /**
     * Title: retrieveAirportByAirportName
     * Description: <br>
     *
     * @param airportName
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/2/22 14:52
     */
    List<CntzResultDto> retrieveAirportByAirportName(@Param("airportName") String airportName);

    /**
     * Title: retrieveCountryByCountryCode
     * Description: <br>
     *
     * @param countryCode
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/4/8 11:37
     */
    List<CntzResultDto> retrieveCountryByCountryCode(@Param("countryCode") String countryCode);

    /**
     * Title: retrieveCountryByCountryName
     * Description: <br>
     *
     * @param countryName
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/4/8 13:51
     */
    List<CntzResultDto> retrieveCountryByCountryName(@Param("countryName") String countryName);

    /**
     * Title: retrieveStateByStateName
     * Description: <br>
     *
     * @param stateName
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/4/8 14:27
     */
    List<CntzResultDto> retrieveStateByStateName(@Param("stateName") String stateName);

    /**
     * Title: retrieveStateByStateCode
     * Description: <br>
     *
     * @param stateCode
     * @return {@link List< CntzResultDto>}
     * <AUTHOR>
     * @date 2024/4/8 14:56
     */
    List<CntzResultDto> retrieveStateByStateCode(@Param("stateCode") String stateCode);
}
