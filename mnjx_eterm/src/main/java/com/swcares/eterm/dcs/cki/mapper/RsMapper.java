package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.obj.dto.RsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2022/8/26
 */
public interface RsMapper {

    /***
     * 查询航班状态
     * @param rsDto
     * @return
     */
    List<String> retrieveCkStatus(@Param("rsDto") RsDto rsDto);

    /***
     * 查询该航班所有座位的座位状态
     * @param rsDto
     * @return
     */
    List<MnjxSeat> retrieveSeatStatus(@Param("rsDto") RsDto rsDto);
}
