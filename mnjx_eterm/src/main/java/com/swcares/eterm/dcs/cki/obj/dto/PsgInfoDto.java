package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PsgInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String name;

    /**
     * 航班号
     */
    private String fltNo;

    /**
     * 航班日期
     */
    private String fltDate;

    /**
     * 舱位
     */
    private String cabin;

    /**
     * 主舱位
     */
    private String cabinClass;

    /**
     * 起飞机场
     */
    private String offAirPort;

    /**
     * 到达机场
     */
    private String arrAirPort;

    /**
     * 登机口
     */
    private String gate;

    /**
     * 登记时间
     */
    private String boardingTime;

    /**
     * 座位
     */
    private String seat;

    /**
     * 客票号
     */
    private String ticketNo;

    /**
     * 登机牌号
     */
    private String bcNo;

    /**
     * 起飞城市中文名
     */
    private String offCityCname;

    /**
     * 到达城市中文名
     */
    private String arrCityCname;

    private String bagWeight;

    private String bagNo;

    private String aboardNo;

    private String segNo;

    private String bagType;

    private boolean isGroup;
}
