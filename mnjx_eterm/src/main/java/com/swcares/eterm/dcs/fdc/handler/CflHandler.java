package com.swcares.eterm.dcs.fdc.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.dto.CflParamDto;
import com.swcares.eterm.dcs.fdc.dto.CflPlanFlightDto;
import com.swcares.eterm.dcs.fdc.service.ICflService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * CFL指令:航空公司二字码/日期/选项(O\C\CL\S\I\N)
 * 显示系统中某一天指定航空公司办理开始办理值机、已经停止办理值机、完全关闭的航班表
 * 显示系统中某一天本航空公司或者其他航空公司能够办理值机的航班表，只显示当日已经初始化了的航班
 *
 * <AUTHOR>
 */
@OperateType(action = "CFL", template = "/dcs/fdc/cfl.jf", fullScreen = true)
public class CflHandler implements Handler {

    @Resource
    private ICflService iCflService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        //参数解析
        CflParamDto cflParamDto = iCflService.parseCfl(cmd);
        List<CflPlanFlightDto> cflDtoList = iCflService.handle(cflParamDto);
        unifiedResult.setResults(ListUtils.toList(cflParamDto, Collections.singletonList(cflDtoList)).toArray());
        return unifiedResult;
    }
}
