package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.type.OperateType;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.FuRetrieveDto;
import com.swcares.eterm.dcs.cki.service.IFuService;

import javax.annotation.Resource;

/**
 * FU修改航班属性，一次只能修改一项 【航班号】/【日期】/【起飞城市】/【表示符】/【修改数据】
 * <p>
 * 实际着陆时间;实际起飞时间;预计登机时间;预计进港时间;预计离港时间;登机口;飞机停靠位置;飞机布局表号
 * <p>
 * ETD ：离港(起飞)时间   ETD/0830        实际起飞时间,最多不能提前 4 小时，推迟无限制
 * CTN：布局表号         CTN/300         先查找新的 CND 表号，并以新表号 300 更换飞机
 * ARN：飞机注册号       ARN/B2824       添加/修改飞机注册号
 * GATE：登机口         GATE/3          在登机牌上打登机口，只能在 FU 中修改
 * BDT：登机时间        BDT/0810        实际登机时间，默认提前半小时，只能在FU 中修改
 * ETA：到达时间        ETA/1900        修改到达时间，城市使用到达城市
 * <p>
 * 取消航班;恢复取消航班;取消一切修改
 * RTNS：取消一切修改   RTNS            恢复到 T-CARD 原始状态
 * ERS：取消单项修改   GATE/ERS  删除登机口   ARN/ERS  删除飞机号
 *
 * <AUTHOR>
 */
@OperateType(action = "FU",shorthand = true)
public class FuHandler implements Handler {

    @Resource
    private IFuService iFuService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        //参数解析
        FuRetrieveDto fuRetrieveDto = iFuService.parseFu(cmd);
        return iFuService.handle(fuRetrieveDto);
    }
}
