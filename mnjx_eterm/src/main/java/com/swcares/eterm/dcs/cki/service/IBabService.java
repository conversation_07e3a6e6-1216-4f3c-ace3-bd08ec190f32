package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxPnrNmTn;
import com.swcares.entity.MnjxPnrSeg;
import com.swcares.entity.MnjxPsgOperateRecord;
import com.swcares.eterm.dcs.cki.obj.dto.BabDto;
import com.swcares.eterm.dcs.cki.obj.vo.BabRenderVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBabService {

    /**
     * parseBab
     *
     * @param cmd cmd
     * @return parseBab
     * @throws UnifiedResultException 异常
     */
    BabDto parseBab(String cmd) throws UnifiedResultException;

    /**
     * handle
     *
     * @param babDto babDto
     * @return handle
     * @throws UnifiedResultException 异常
     */
    BabRenderVo handle(BabDto babDto) throws UnifiedResultException;

    /**
     * constructOperateRecord
     *
     * @param operateType operateType
     * @param pnrSegNo    pnrSegNo
     * @param pnrNmId     pnrNmId
     * @return constructOperateRecord
     */
    MnjxPsgOperateRecord constructOperateRecord(String operateType, int pnrSegNo, String pnrNmId);

    /**
     * updateTicketStatus
     *
     * @param nmTnList     nmTnList
     * @param pnrSeg       pnrSeg
     * @param ticketStatus ticketStatus
     */
    void updateTicketStatus(List<MnjxPnrNmTn> nmTnList, MnjxPnrSeg pnrSeg, String ticketStatus);
}
