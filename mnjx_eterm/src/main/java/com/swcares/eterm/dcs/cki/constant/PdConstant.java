/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.constant;

/**
 * <AUTHOR>
 */
public class PdConstant {
    /**
     * 已接收旅客
     */
    public final static String ACC = "ACC";
    /**
     * 换飞机后新座位号
     * 与原来不同的旅客
     */
    public final static String AECX = "AECX";
    /**
     * 换飞机后未确定座位的旅客
     */
    public final static String AEC = "AEC";
    /**
     * 所有有行李的旅客
     */
    public final static String BAG_ALL = "BAG/ALL";
    /**
     * 预留座位的要客(VIP)
     */
    public final static String ASR = "ASR";

    /**
     * 值机预留座位的旅客
     */
    public final static String CRS = "CRS";
    /**
     * 盲人旅客
     */
    public final static String BLND = "BLND";

    /**
     * 座位号
     */
    public final static String SN = "SN";


    /**
     * 耳聋旅客
     */
    public final static String DEAF = "DEAF";
    /**
     * 儿童
     */
    public final static String CHD = "CHD";
    /**
     * 额外占座旅客
     */
    public final static String EXST = "EXST";

    /**
     * 降舱旅客
     */
    public final static String DNG = "DNG";
    /**
     * 行李超重的旅客
     */
    public final static String EXBG = "EXBG";
    /**
     *  无行李旅客
     */
    public final static String NBAG = "NBAG";
    /**
     * 与其他主机有数据交换的旅客
     */
    public final static String EDI = "EDI";
    /**
     * 常旅客
     */
    public final static String FF = "FF";
    /**
     * 持电子客票的旅客
     */
    public final static String ET = "ET";

    /**
     * 团体
     */
    public final static String GRP = "GRP";
    /**
     * 进港(转港)旅客
     */
    public final static String I = "I";
    /**
     * 带婴儿的旅客
     */
    public final static String INF = "INF";
    /**
     * 不包括团名显示
     */
    public final static String NGRP = "NGRP";

    /**
     * 未接收旅客
     */
    public final static String NACC = "NACC";

    /**
     * 未接收旅客
     */
    public final static String DL = "DL";
    /**
     * 联程旅客
     */
    public final static String O = "O";
    /**
     * 有护照信息的旅客
     */
    public final static String PSPT = "PSPT";
    /**
     * 需特殊服务旅客
     */
    public final static String PSM = "PSM";

    /**
     * 候补旅客
     */
    public final static String SBY = "SBY";
    /**
     * 预留座位的团体旅客
     */
    public final static String RES = "RES";
    /**
     * 升舱旅客
     */
    public final static String UPG = "UPG";
    /**
     * 担架旅客
     */
    public final static String STCR = "STCR";
    /**
     * 无人陪伴儿童
     */
    public final static String UM = "UM";

    /**
     * 在订座、离港都无记录的旅客
     */
    public final static String URES = "URES";
    /**
     * 轮椅旅客
     */
    public final static String WCH = "WCH";
    /**
     * 要客
     */
    public final static String VIP = "VIP";
    /**
     * 转换航班旅客
     */
    public final static String Z = "Z";
    /**
     * 被 PW 删除的旅客
     */
    public final static String XRES = "XRES";

    /**
     * 旅客姓名查询
     */
    public final static String EN = "EN";

    // ==========================================

    public static final String FORMAT = "FORMAT";
    public static final String CITY = "CITY";
    public static final String FLT_NUMBER = "FLT NUMBER";
    public static final String NUMBER_ERROR = "NUMBER ERROR";
    public static final String DATE = "DATE";
    public static final String NO_RECORD = "NO RECORD";
    public static final String NO_FT = "NO FT";
    public static final String CABIN = "CLASS";
    public static final String NAME = "NAME";
}
