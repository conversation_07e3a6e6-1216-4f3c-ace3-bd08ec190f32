package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import com.swcares.eterm.dcs.cki.obj.dto.AcrtDto;
import com.swcares.eterm.dcs.cki.mapper.AcrtMapper;
import com.swcares.eterm.dcs.cki.service.IAcrtService;
import com.swcares.service.IMnjxOfficeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ACRT指令处理
 *
 * <AUTHOR>
 */
@Service
public class AcrtServiceImpl implements IAcrtService {

    @Resource
    private AcrtMapper acrtMapper;
    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    /**
     * ACRT指令业务处理
     *
     * @param unifiedResult 参数
     * @return 部门、si工作号等信息
     */
    @Override
    public List<AcrtDto> handle(UnifiedResult unifiedResult) throws UnifiedResultException {
        Object[] args = unifiedResult.getArgs();
        String officeNo = ObjectUtil.isNotEmpty(args[1]) ? StrUtils.toString(args[1]) : StrUtil.EMPTY;
        //查询部门号是否存在
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery().eq(MnjxOffice::getOfficeNo, officeNo).one();
        if (ObjectUtil.isEmpty(mnjxOffice)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        //业务查询
        List<AcrtDto> acrtDtoList = acrtMapper.retrieveOfficeAndSi(officeNo);
        if (CollUtil.isEmpty(acrtDtoList)) {
            throw new UnifiedResultException(Constant.NO_OFFICE_SI);
        }
        MnjxSi mnjxSi = MemoryDataUtils.getMemoryData().getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi();
        //返回的结果处理
        acrtDtoList.forEach(acrtDto -> {
            if (ObjectUtils.isNotEmpty(mnjxSi)) {
                if (acrtDto.getSiNo().equalsIgnoreCase(mnjxSi.getSiNo())) {
                    acrtDto.setActiveSign(Constant.AV_OPTION_A);
                }
            }
        });
        return acrtDtoList;
    }
}
