/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@SuppressWarnings("AlibabaClassMustHaveAuthor")
@Data
@ToString
public class PaObligateDto implements Serializable {

    private String fltNo;

    private String fltDate;

    private String preSeat;

    private String psgId;

    /**
     * 航节飞行计划号
     */
    private String sectionPlanId;

    /**
     * 航段号
     */
    private String segmentId;

    private String offAirport;

    /**
     * 命令参数，比如#，R12L等
     */
    private String cmd;

    /**
     * 常客号
     */
    private String ffca;

    /**
     * 婴儿名字
     */
    private String babyName;

    /**
     * 预留座位状态
     */
    private String seatStatus;

    /**
     * 舱位
     */
    private String cabinClass;

    /**
     * 完整指令
     */
    private String fullCmd;
}
