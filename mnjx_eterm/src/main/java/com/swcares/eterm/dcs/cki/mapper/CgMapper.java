package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPlaneModel;
import com.swcares.eterm.crs.obj.vo.CgVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * description：CgMapper
 *
 * <AUTHOR>
 * @date 2021/7/20
 */
public interface CgMapper {

    /**
     * retrieveFltJoinPlanSection
     *
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @param org     org
     * @return retrieveFltJoinPlanSection
     */
    List<MnjxOpenCabin> retrieveFltJoinPlanSection(@Param("flightNo") String fltNo, @Param("fltDate") String fltDate, @Param("org") String org);

    /**
     * retrieveFltJoinPlaneJoinPlaneType
     *
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @param org     org
     * @return retrieveFltJoinPlaneJoinPlaneType
     */
    List<MnjxPlaneModel> retrieveFltJoinPlaneJoinPlaneType(@Param("flightNo") String fltNo, @Param("fltDate") String fltDate, @Param("org") String org);

    /**
     * retrieveFltJoinSectionJoinOpenCabin
     *
     * @param airCode airCode
     * @return retrieveFltJoinSectionJoinOpenCabin
     */
    List<MnjxOpenCabin> retrieveFltJoinSectionJoinOpenCabin(String airCode);

    /**
     * retrieveFlightPlanSection
     *
     * @param flightNo flightNo
     * @param fltDate  fltDate
     * @return retrieveFlightPlanSection
     */
    List<CgVo> retrieveFlightPlanSection(@Param("flightNo") String flightNo, @Param("fltDate") String fltDate);
}
