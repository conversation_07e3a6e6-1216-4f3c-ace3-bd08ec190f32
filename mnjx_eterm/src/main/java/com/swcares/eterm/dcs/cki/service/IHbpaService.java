package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.HbpaCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;

import java.util.List;

/**
 * HBPA业务层
 * Created on 2018年6月29日<br>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface IHbpaService {

    /**
     * 指令解析
     *
     * @param cmd 指令
     * @return 指令解析
     * @throws UnifiedResultException 统一异常
     */
    List<HbpaCmdDto> parseCmd(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param memoryData     内存对象
     * @param hbpaCmdDtoList hbpaCmdDtoList
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    List<PaResultDto> handle(MemoryData memoryData, List<HbpaCmdDto> hbpaCmdDtoList) throws UnifiedResultException;

    /**
     * paResultDtoList
     *
     * @param paResultDtoList paResultDtoList
     */
    void print(List<PaResultDto> paResultDtoList);
}
