package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;
import com.swcares.eterm.dcs.cki.obj.dto.SbInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public interface ICkiCacheService {

    /**
     * 获取PA结果缓存
     *
     * @param memoryData 内存对象
     * @return 获取PA结果缓存
     */
    List<PaResultDto> getPaCache(MemoryData memoryData);

    /**
     * 清除PA结果缓存
     *
     * @param memoryData 内存对象
     */
    void clearPaCache(MemoryData memoryData);

    /**
     * 获取HBPA结果缓存
     *
     * @param memoryData 内存对象
     * @return 获取HBPA结果缓存
     */
    List<PaResultDto> getHbpaCache(MemoryData memoryData);

    /**
     * 清除HBPA结果缓存
     *
     * @param memoryData 内存对象
     */
    void clearHbpaCache(MemoryData memoryData);

    /**
     * 获取PW结果缓存
     *
     * @param memoryData 内存对象
     * @return 获取PW结果缓存
     */
    List<PwResultDto> getPwCache(MemoryData memoryData);

    /**
     * 清理PW结果缓存
     *
     * @param memoryData 内存对象
     */
    void clearPwCache(MemoryData memoryData);

    /**
     * 获取默认的PD缓存信息
     *
     * @param memoryData 内存对象
     * @return 获取默认的PD缓存信息
     */
    PdInfoDto getPdDefaultInfo(MemoryData memoryData);

    /**
     * 清除默认的PD缓存信息
     *
     * @param memoryData 内存对象
     */
    void clearPdDefaultInfo(MemoryData memoryData);

    /**
     * 获取SBY的PD缓存信息
     *
     * @param memoryData 内存对象
     * @return 获取SBY的PD缓存信息
     */
    PdInfoDto getPdSbyInfo(MemoryData memoryData);

    /**
     * 清除SBY的PD缓存信息
     *
     * @param memoryData 内存对象
     */
    void clearPdSbyInfo(MemoryData memoryData);

    /**
     * 获取ABC的PD缓存信息
     *
     * @param memoryData 内存对象
     * @return 获取ABC的PD缓存信息
     */
    PdInfoDto getPdAbcInfo(MemoryData memoryData);

    /**
     * 清除ABC的PD缓存信息
     *
     * @param memoryData 内存对象
     */
    void clearPdAbcInfo(MemoryData memoryData);

    /**
     * getPuCache
     *
     * @param memoryData 内存对象
     * @return getPuCache
     */
    List<PaResultDto> getPuCache(MemoryData memoryData);

    /**
     * clearPuCache
     *
     * @param memoryData 内存对象
     */
    void clearPuCache(MemoryData memoryData);

    /**
     * getHbpuCache
     *
     * @param memoryData 内存对象
     * @return getHbpuCache
     */
    List<PaResultDto> getHbpuCache(MemoryData memoryData);

    /**
     * clearHbpuCache
     *
     * @param memoryData 内存对象
     */
    void clearHbpuCache(MemoryData memoryData);

    /**
     * pr保存的缓存信息
     *
     * @param memoryData 内存对象
     * @return pr保存的缓存信息
     */
    PdInfoDto getPrCache(MemoryData memoryData);

    /**
     * pr保存的缓存信息
     *
     * @param memoryData 内存对象
     */
    void clearPrCache(MemoryData memoryData);

    /**
     * getHbprCache
     *
     * @param memoryData 内存对象
     * @return getHbprCache
     */
    PdInfoDto getHbprCache(MemoryData memoryData);

    /**
     * clearHbprCache
     *
     * @param memoryData 内存对象
     */
    void clearHbprCache(MemoryData memoryData);

    /**
     * 获取HBPW结果缓存
     *
     * @param memoryData 内存对象
     * @return 获取HBPW结果缓存
     */
    List<PwResultDto> getHbpwCache(MemoryData memoryData);

    /**
     * 清理HBPW结果缓存
     *
     * @param memoryData 内存对象
     */
    void clearHbpwCache(MemoryData memoryData);

    /**
     * 获取SB结果缓存
     *
     * @param memoryData 内存对象
     * @return 获取SB结果缓存
     */
    SbInfoDto getSbCache(MemoryData memoryData);

    /**
     * 清理SB结果缓存
     *
     * @param memoryData 内存对象
     */
    void clearSbCache(MemoryData memoryData);

    String extractedBfAndPutCache(MemoryData memoryData, List<String> analysisCmd, UnifiedResult unifiedResult) throws UnifiedResultException;

    /**
     * getBfCache
     *
     * @param memoryData 内存对象
     * @return getBfCache
     */
    String getBfCache(MemoryData memoryData);

    /**
     * clearBfCache
     *
     * @param memoryData 内存对象
     */
    void clearBfCache(MemoryData memoryData);
}
