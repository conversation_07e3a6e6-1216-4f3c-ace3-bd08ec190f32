package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.SbDto;
import com.swcares.eterm.dcs.cki.obj.dto.SbInfoDto;

/**
 * <AUTHOR>
 */
public interface ISbService {

    /**
     * 指令参数解析
     *
     * @param cmd 指令
     * @return 解析返回对象
     * @throws UnifiedResultException 异常
     */
    SbDto parse(String cmd) throws UnifiedResultException;

    /**
     * 处理逻辑
     *
     * @param sbDto      解析返回对象
     * @param memoryData 内存对象
     * @return 处理后对象
     * @throws UnifiedResultException 异常
     */
    SbInfoDto handlerSb(MemoryData memoryData, SbDto sbDto) throws UnifiedResultException;
}
