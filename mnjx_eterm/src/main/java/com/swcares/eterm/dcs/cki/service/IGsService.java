package com.swcares.eterm.dcs.cki.service;


import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.GsParamDto;

/**
 * <AUTHOR>
 */
public interface IGsService {
    /**
     * 参数解析
     *
     * @param cmd gs指令
     * @return 解析的参数对象
     * @throws UnifiedResultException 统一异常
     */
    GsParamDto parseGs(String cmd) throws UnifiedResultException;

    /**
     * GS限额处理
     *
     * @param gsParamDto 解析的参数对象
     * @return 最终的结果
     * @throws UnifiedResultException 统一异常
     */
    String handle(GsParamDto gsParamDto) throws UnifiedResultException;
}
