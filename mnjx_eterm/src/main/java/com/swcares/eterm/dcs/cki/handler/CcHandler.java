package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.core.type.OperateType;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.ICcService;
import javax.annotation.Resource;

/**
 * 航班中间关闭
 *
 * <AUTHOR>
 */
@OperateType(action = "CC", shorthand = true, template = "/dcs/cki/cc.jf")
public class CcHandler implements Handler {

    @Resource
    private ICcService iCcService;


    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        String result = iCcService.handle(cmd);
        unifiedResult.setResults(ListUtils.toList(result).toArray());
        return unifiedResult;
    }
}
