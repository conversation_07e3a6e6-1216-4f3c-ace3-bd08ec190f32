package com.swcares.eterm.dcs.cki.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.AcrtDto;
import com.swcares.eterm.dcs.cki.service.IAcrtService;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * ACRT 指令
 *
 * <AUTHOR>
 *  2022-05-13 10:21:38
 */
@OperateType(action = "ACRT", template = "/dcs/cki/acrt.jf", canChangePageSize = true)
public class AcrtHandler implements Handler {

    @Resource
    private IAcrtService iAcrtService;

    /**
     * ACRT:OFFICE号
     */
    private static final String ACRT_REGX = "ACRT:(\\w{1,8})";

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        // 验证格式
        if (!ReUtil.isMatch(ACRT_REGX, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 参数
        unifiedResult.setArgs(ReUtil.getAllGroups(Pattern.compile(ACRT_REGX), cmd, true).toArray());
        // 要返回的结果容器
        List<AcrtDto> acrtDtoList = iAcrtService.handle(unifiedResult);
        unifiedResult.setResults(acrtDtoList.toArray());
        return unifiedResult;
    }
}

