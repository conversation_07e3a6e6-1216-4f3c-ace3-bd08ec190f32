package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.BabDto;
import com.swcares.eterm.dcs.cki.obj.vo.BabRenderVo;

/**
 * <AUTHOR>
 */
public interface IBdbService {

    /**
     * 数据解析
     *
     * @param cmd cmd
     * @return 数据解析
     * @throws UnifiedResultException 统一异常
     */
    BabDto parseBab(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param babDto babDto
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    BabRenderVo handle(BabDto babDto) throws UnifiedResultException;
}
