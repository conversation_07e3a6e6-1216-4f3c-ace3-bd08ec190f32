package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataCabinet;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.eterm.crs.service.ISsrService;
import com.swcares.eterm.dcs.cki.mapper.PrMapper;
import com.swcares.eterm.dcs.cki.mapper.PuMapper;
import com.swcares.eterm.dcs.cki.obj.dto.*;
import com.swcares.eterm.dcs.cki.service.*;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 为接受旅客提供服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class PaServiceImpl implements IPaService {

    /**
     * 接收特殊餐食
     * SPML+自由文本，长度不超过60
     */
    private static final Pattern SPML_PATTERN = Pattern.compile("SPML\\s?([A-Z\\u4e00-\\u9fa5\\s\\d/]+)");

    /**
     * 接收指定座位
     * R11L
     * RWA W--窗口  I–走道   F–靠前 A–靠后   L-靠左   R–靠右
     */
    private static final Pattern SEAT_PATTERN = Pattern.compile("^R(([WAILFR]+)|(\\d+[A-Z])|((\\d+)(-(\\d+))?([A-Z]+(-([A-Z]))?)))$");

    /**
     * SNR分配座位
     * SNR11L
     */
    private static final Pattern SNR_PATTERN = Pattern.compile("SNR(\\d+[A-Z])");

    /**
     * 性别
     */
    private static final Pattern SEX_PATTERN = Pattern.compile("([MF])(\\d+)");

    /**
     * 接收旅客类型
     * CHD1
     * UM4
     * INF1
     */
    private static final Pattern PSG_TYPE_PATTERN = Pattern.compile("(CHD|INF|UM)(\\d*)([\\u4e00-\\u9fa5A-Z/]+)?");

    /**
     * 接收行李
     */
    private static final Pattern BAG_PATTERN = Pattern.compile("^(\\d+)/(\\d+)$");

    /**
     * 接收AVIH行李
     */
    private static final Pattern AVIH_PATTERN = Pattern.compile("^AVIH(\\d+)?/?(\\d+)?$");

    /**
     * 行李目的地 BTSHA
     */
    private static final Pattern BAG_DST = Pattern.compile("BT([A-Z]{3})");

    /**
     * 轮椅类型
     */
    private static final Pattern WC_PATTERN = Pattern.compile("WC(HR|HC|HS|BD|BW|MP|OB)");

    private static final Pattern PSM_PATTERN = Pattern.compile("^SSR\\sPSM\\s(.+)/P\\d+$");
    private static final Pattern MSG_PIL_PATTERN = Pattern.compile("^SSR\\s(MSG|PIL)\\s(.+)/P\\d+$");

    /**
     * 客舱宠物 PETC2/10
     */
    private static final Pattern PETC_PATTERN = Pattern.compile("^PETC(\\d+)/(\\d+)$");

    /**
     * SSR SPML CA NN1 CTUPEK CA8916 N01SEP/P1
     * SSR SPML CA NN1 CTUPEK CA8916 N01SEP FREETEXT/P1
     */
    private static final Pattern SPML_SSR_PATTERN = Pattern.compile("^SSR\\sSPML\\s[A-Z0-9]{2}\\s(NN|HK|RR)1\\s[A-Z]{6}\\s[A-Z0-9]{4,6}\\s[A-Z]\\d{2}[A-Z]{3}(\\d{2})?(\\s(.+))?/P\\d+$");

    /**
     * 手工行李 T/CA/123456/PEK
     */
    private static final Pattern EXCESS_LUGGAGE_PATTERN = Pattern.compile("T/([0-9A-Za-z]{2})/([0-9]{6})(-([0-9]{3}))?/([A-Za-z]{3})");

    /**
     * （预）升降舱（自愿、非自愿）
     * 降舱：
     * 自愿：DNG舱等V
     * 非自愿：DNG舱等
     * 升舱
     * 自愿：UPG舱等V
     * 非自愿：UPG舱等
     */
    private static final Pattern UPG_DNG_PATTERN = Pattern.compile("(DNG|UPG)([A-Z])?(V)?");

    /**
     * 摇篮 BSCT1/n
     */
    private static final Pattern BSCT_PATTERN = Pattern.compile("BSCT(\\d+)/(\\d+)");

    /**
     * 额外占座  EXST50
     */
    private static final Pattern EXST_PATTER = Pattern.compile("(EXST|CBBG|COUR|DIPL)(\\d*)");

    /**
     * 邻近占座  ADSR11
     */
    private static final Pattern ADSR_PATTER = Pattern.compile("ADSR(\\d+)?");

    /**
     * 多旅客同时分配座位 13AB 13-14A
     */
    private static final Pattern MULTI_SEAT_PATTER = Pattern.compile("((\\d+)(-(\\d+))?)(([A-Z])+(-([A-Z]))?)");


    private static final Pattern REG_XBP = Pattern.compile("XBP");

    /**
     * 常客 FF航司/常客卡号/等级
     */
    private static final Pattern FF_PATTERN = Pattern.compile("FF([A-Z0-9]{1,2})/([@\\w\\u4e00-\\u9fa5]+)(/([CV]))?");

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Resource
    private IAspectCkiService iAspectCkiService;

    @Resource
    private PuMapper puMapper;

    @Resource
    private PrMapper prMapper;

    @Resource
    private IPdService iPdService;

    @Resource
    private IPrService iPrService;

    @Resource
    private ISyService iSyService;

    @Resource
    private ISsrService iSsrService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxConfigLuggageService iMnjxConfigLuggageService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IBcService iBcService;

    @Resource
    private IBagService iBagService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxPsgCkiOptionService iMnjxPsgCkiOptionService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IMnjxSeatModelService iMnjxSeatModelService;

    @Resource
    private IMnjxFrequenterService iMnjxFrequenterService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxPrintDataService iMnjxPrintDataService;

    @Override
    @CachePut(key = "'paResult' + #memoryData.memoryDataId")
    public List<PaResultDto> handle(MemoryData memoryData, List<PaCmdDto> paCmdDtoList) throws UnifiedResultException {
        List<PaResultDto> paResultDtoList = new ArrayList<>();
        if (StrUtil.isNotEmpty(paCmdDtoList.get(0).getNrec())) {
            List<HbpaCmdDto> hbpaCmdDtoList = new ArrayList<>();
            paCmdDtoList.forEach(p -> {
                HbpaCmdDto hbpaCmdDto = new HbpaCmdDto();
                BeanUtil.copyProperties(p, hbpaCmdDto);
                hbpaCmdDtoList.add(hbpaCmdDto);
            });
            this.handleUres(hbpaCmdDtoList, paResultDtoList);
            return paResultDtoList;
        }
        MemoryDataHandlerResult memoryDataHandlerResult = memoryData.getMemoryDataHandlerResult();
        String lastAction = memoryDataHandlerResult.getLastAction();
        if (paCmdDtoList.stream().anyMatch(PaCmdDto::isFromPd)) {
            lastAction = "PD";
        }
        switch (lastAction) {
            case "PA":
            case "JC":
                List<PaResultDto> cachePaResultDtoList = iCkiCacheService.getPaCache(memoryData);
                if (CollUtil.isEmpty(cachePaResultDtoList)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                for (PaCmdDto paCmdDto : paCmdDtoList) {
                    if (paCmdDto.getPdIndex() > cachePaResultDtoList.size()) {
                        throw new UnifiedResultException(Constant.NO_RECORD);
                    }
                    this.constructPaResultDtoFromPa(paCmdDto, cachePaResultDtoList, paResultDtoList);
                }
                break;
            case "RL":
            case "RN":
            case "PD":
                PdInfoDto pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
                if (ObjectUtil.isEmpty(pdInfoDto)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                this.handleFromPd(paCmdDtoList, paResultDtoList, pdInfoDto);
                break;
            case "FSN":
            case "FB":
            case "PR":
                pdInfoDto = iCkiCacheService.getPrCache(memoryData);
                if (ObjectUtil.isEmpty(pdInfoDto)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                this.handleFromPr(paCmdDtoList, paResultDtoList, pdInfoDto);
                break;
            case "PU":
                List<PaResultDto> cachePuResultDtoList = iCkiCacheService.getPuCache(memoryData);
                if (CollUtil.isEmpty(cachePuResultDtoList)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                for (PaCmdDto paCmdDto : paCmdDtoList) {
                    if (paCmdDto.getPdIndex() > cachePuResultDtoList.size()) {
                        throw new UnifiedResultException(Constant.NO_RECORD);
                    }
                    this.constructPaResultDtoFromPa(paCmdDto, cachePuResultDtoList, paResultDtoList);
                }
                break;
            case "PW":
                List<PwResultDto> pwResultDtoList = iCkiCacheService.getPwCache(memoryData);
                if (CollUtil.isEmpty(pwResultDtoList)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                this.handleFromPw(paCmdDtoList, paResultDtoList, pwResultDtoList);
                break;
            case "SB":
                SbInfoDto sbInfoDto = iCkiCacheService.getSbCache(memoryData);
                if (ObjectUtil.isEmpty(sbInfoDto)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                this.handleFromSb(paCmdDtoList, paResultDtoList, sbInfoDto);
                break;
            default:
                throw new UnifiedResultException(Constant.ENTRY_NBR);
        }
        if (paResultDtoList.stream().anyMatch(PaResultDto::isFromPaNrec)) {
            this.handlePaNrec(paResultDtoList);
        } else {
            this.parseAndUpdateData(paResultDtoList);
        }
        return paResultDtoList;
    }

    private void handleFromSb(List<PaCmdDto> paCmdDtos, List<PaResultDto> paResultDtos, SbInfoDto sbInfoDto) throws UnifiedResultException {
        if (paCmdDtos.size() > 1) {
            throw new UnifiedResultException(Constant.NUMBER_ERROR);
        }
        PaCmdDto paCmdDto = paCmdDtos.get(0);
        PaResultDto paResultDto = new PaResultDto();
        this.constructPaResultDtoFromSb(paResultDto, sbInfoDto, paCmdDto.getPdIndex());
        paResultDto.setAdv(paCmdDto.isAdv());
        paResultDto.setOptionList(paCmdDto.getOptionList());
        paResultDto.setCmd(paCmdDto.getCmd());
        paResultDtos.add(paResultDto);
    }

    private void constructPaResultDtoFromSb(PaResultDto paResultDto, SbInfoDto sbInfoDto, int pdIndex) throws UnifiedResultException {
        if (pdIndex > sbInfoDto.getNms().size()) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        SbHbPnrNmDto sbHbPnrNmDto = sbInfoDto.getNms().get(pdIndex - 1);
        paResultDto.setInputIndex(pdIndex);
        // 构建航班信息
        paResultDto.setFlightNo(StrUtil.isNotEmpty(sbHbPnrNmDto.getCarrierFlight()) ? sbHbPnrNmDto.getCarrierFlight() : sbHbPnrNmDto.getFlightNo());
        String date = sbHbPnrNmDto.getFlightDate();
        if (!date.contains(StrUtil.DASHED)) {
            date = DateUtils.com2ymd(date);
        }
        paResultDto.setFlightDate(date);
        paResultDto.setOrgAirport(sbInfoDto.getAirportCode().substring(0, 3));
        paResultDto.setPlaneType(sbInfoDto.getPlaneType());
        paResultDto.setPlaneVersion("");
        paResultDto.setGate(StrUtil.isNotEmpty(sbInfoDto.getGtd()) ? "????" : sbInfoDto.getGtd());
        paResultDto.setBdt(sbInfoDto.getBoarding());
        paResultDto.setSd(sbInfoDto.getEstimateOff());
        paResultDto.setEd(sbInfoDto.getActualOff());
        paResultDto.setSa("");
        paResultDto.setFt("");
        paResultDto.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));
        // 构建旅客信息数据
        paResultDto.setQueryName(sbHbPnrNmDto.getQueryName().matches("\\d+.+") ? sbHbPnrNmDto.getQueryName().substring(1) : sbHbPnrNmDto.getQueryName());
        paResultDto.setPnrNmId(sbHbPnrNmDto.getPnrNmId());
        paResultDto.setName(sbHbPnrNmDto.getQueryName());
        paResultDto.setDstAirport(sbHbPnrNmDto.getDst());
        paResultDto.setSellCabin(sbHbPnrNmDto.getSellCabin());
        paResultDto.setShareFlightNo(sbHbPnrNmDto.getShareFlightNo());
    }

    /**
     * Title: handlePaNrec
     * Description: F12添加NREC第二次调用PA时的处理逻辑
     *
     * @param paResultDtoList paResultDtoList
     * <AUTHOR>
     * @date 2022/11/30 11:25
     */
    private void handlePaNrec(List<PaResultDto> paResultDtoList) {
        paResultDtoList.forEach(p -> {
            p.setFlightDate(p.getFlightDate().contains(StrUtil.DASHED) ? DateUtils.ymd2Com(p.getFlightDate()) : p.getFlightDate());
            MnjxPnrNm pnrNm = p.getPnrNm();
            if (ObjectUtil.isEmpty(pnrNm)) {
                pnrNm = iMnjxPnrNmService.getById(p.getPnrNmId());
            }
            String queryName = pnrNm.getQueryName().toUpperCase();
            if (StrUtil.isNotEmpty(pnrNm.getIsCnin())) {
                if (queryName.length() > Constant.SEVENTEEN) {
                    queryName = StrUtil.format("{}/", queryName.substring(0, 17));
                } else {
                    queryName = StrUtil.fill(queryName + StrUtil.SLASH, ' ', 18, false);
                }
            } else {
                if (queryName.length() > Constant.SEVENTEEN) {
                    queryName = StrUtil.format("{}+", queryName.substring(0, 17));
                } else {
                    queryName = StrUtil.fill(queryName, ' ', 18, false);
                }
            }
            p.setQueryName(queryName);
        });
    }

    private void constructPaResultDtoFromPa(PaCmdDto paCmdDto, List<PaResultDto> cachePaResultDtoList, List<PaResultDto> paResultDtoList) {
        PaResultDto newPa = new PaResultDto();
        PaResultDto paResultDto = cachePaResultDtoList.get(paCmdDto.getPdIndex() - 1);
        if (StrUtil.isNotEmpty(paResultDto.getNrec())) {
            BeanUtil.copyProperties(paResultDto, newPa);
            newPa.setFlightDate(paResultDto.getFlightDate().contains(StrUtil.DASHED) ? paResultDto.getFlightDate() : DateUtils.com2ymd(paResultDto.getFlightDate()));
            newPa.setQueryName(StrUtil.fill(paResultDto.getQueryName().trim(), ' ', 7, false));
            newPa.setFromPaNrec(true);
        } else {
            newPa.setFlightDate(paResultDto.getFlightDate().contains(StrUtil.DASHED) ? paResultDto.getFlightDate() : DateUtils.com2ymd(paResultDto.getFlightDate()));
            newPa.setQueryName(StrUtil.fill(paResultDto.getQueryName().trim(), ' ', 7, false));
            newPa.setFlightNo(paResultDto.getFlightNo());
            newPa.setOrgAirport(paResultDto.getOrgAirport());
            newPa.setPlaneType(paResultDto.getPlaneType());
            newPa.setPlaneVersion(paResultDto.getPlaneVersion());
            newPa.setGate(paResultDto.getGate());
            newPa.setBdt(paResultDto.getBdt());
            newPa.setSd(paResultDto.getSd());
            newPa.setEd(paResultDto.getEd());
            newPa.setSa(paResultDto.getSa());
            newPa.setFt(paResultDto.getFt());
            newPa.setAirlineCode(paResultDto.getAirlineCode());
            newPa.setPnrNmId(paResultDto.getPnrNmId());
            newPa.setCnin(paResultDto.isCnin());
            newPa.setName(paResultDto.getName());
            newPa.setOInterlink(paResultDto.getOInterlink());
            newPa.setDstAirport(paResultDto.getDstAirport());
            newPa.setSellCabin(paResultDto.getSellCabin());
            newPa.setCabinClass(paResultDto.getCabinClass());
            newPa.setShareFlightNo(paResultDto.getShareFlightNo());
        }
        newPa.setInputIndex(paCmdDto.getPdIndex());
        newPa.setAdv(paCmdDto.isAdv());
        newPa.setOptionList(paCmdDto.getOptionList());
        newPa.setCmd(paCmdDto.getCmd());
        paResultDtoList.add(newPa);
    }

    /**
     * Title: handleFromPw
     * Description: 从PW结果构建数据
     *
     * @param paCmdDtos    paCmdDtos
     * @param paResultDtos paResultDtos
     * @param pwResultDtos pwResultDtos
     * <AUTHOR>
     */
    private void handleFromPw(List<PaCmdDto> paCmdDtos, List<PaResultDto> paResultDtos, List<PwResultDto> pwResultDtos) throws UnifiedResultException {
        if (paCmdDtos.size() > 1) {
            throw new UnifiedResultException(Constant.NUMBER_ERROR);
        }
        PaCmdDto paCmdDto = paCmdDtos.get(0);
        PaResultDto paResultDto = new PaResultDto();
        this.constructPaResultDtoFromPw(paResultDto, pwResultDtos, paCmdDto.getPdIndex());
        paResultDto.setAdv(paCmdDto.isAdv());
        paResultDto.setOptionList(paCmdDto.getOptionList());
        paResultDto.setCmd(paCmdDto.getCmd());
        paResultDtos.add(paResultDto);
    }

    /**
     * Title: constructPaResultDtoFromPw
     * Description: 从PW结果构建数据
     *
     * @param paResultDto paResultDto
     * @param pwResultDts pwResultDts
     * @param pwIndex     pwIndex
     * <AUTHOR>
     */
    private void constructPaResultDtoFromPw(PaResultDto paResultDto, List<PwResultDto> pwResultDts, int pwIndex) throws UnifiedResultException {
        if (pwIndex > pwResultDts.size()) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        PwResultDto pwResultDto = pwResultDts.get(pwIndex - 1);
        paResultDto.setInputIndex(pwIndex);
        // 构建航班信息
        paResultDto.setFlightNo(pwResultDto.getFltNo());
        String date = pwResultDto.getFltDate();
        if (!date.contains(StrUtil.DASHED)) {
            date = DateUtils.com2ymd(date);
        }
        paResultDto.setFlightDate(date);
        paResultDto.setOrgAirport(pwResultDto.getOrg());
        paResultDto.setPlaneType(pwResultDto.getPlaneType());
        paResultDto.setPlaneVersion(pwResultDto.getPlaneVersion());
        paResultDto.setGate(StrUtil.isNotEmpty(pwResultDto.getGate()) ? "????" : pwResultDto.getGate());
        paResultDto.setBdt(pwResultDto.getEstimateBoarding());
        paResultDto.setSd(pwResultDto.getEstimateOff());
        paResultDto.setEd(pwResultDto.getActualOff());
        paResultDto.setSa("");
        paResultDto.setFt("");
        paResultDto.setAirlineCode(pwResultDto.getFltNo().substring(0, 2));
        // 构建旅客信息数据
        paResultDto.setQueryName(pwResultDto.getQueryName().matches("\\d+.+") ? pwResultDto.getQueryName().substring(1) : pwResultDto.getQueryName());
        paResultDto.setPnrNmId(pwResultDto.getPnrNmId());
        paResultDto.setCnin(StrUtil.isNotEmpty(pwResultDto.getIsCnin()));
        paResultDto.setName(pwResultDto.getName());
        paResultDto.setOInterlink(pwResultDto.getOInterlink());
        paResultDto.setDstAirport(pwResultDto.getDst());
        paResultDto.setSellCabin(pwResultDto.getSellCabin());
        paResultDto.setCabinClass(pwResultDto.getCabinClass());
        paResultDto.setShareFlightNo(pwResultDto.getShareFltNo());
    }

    /**
     * Title: handleFromPd
     * Description: 从PD结果集处理参数
     *
     * @param paCmdDtos    paCmdDtos
     * @param paResultDtos paResultDtos
     * @param pdInfoDto    pdInfoDto
     * <AUTHOR>
     */
    private void handleFromPd(List<PaCmdDto> paCmdDtos, List<PaResultDto> paResultDtos, PdInfoDto pdInfoDto) throws UnifiedResultException {
        for (PaCmdDto paCmdDto : paCmdDtos) {
            int pdIndex = paCmdDto.getPdIndex();
            if (pdIndex > pdInfoDto.getNms().size() || pdIndex < 1) {
                throw new UnifiedResultException(Constant.NO_RECORD);
            }
            PaResultDto paResultDto = new PaResultDto();
            paResultDto.setAdv(paCmdDto.isAdv());
            paResultDto.setOptionList(paCmdDto.getOptionList());
            paResultDto.setCmd(paCmdDto.getCmd());
            this.constructPaResultDtoFromPd(paResultDto, pdInfoDto, paCmdDto.getPdIndex());
            paResultDtos.add(paResultDto);
        }
    }

    /**
     * Title: validateFlightStatus
     * Description: 验证航班状态和航班起飞时间
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param orgCity    orgCity
     * <AUTHOR>
     */
    @Override
    public void validateFlightStatus(String flightNo, String flightDate, String orgCity, PaResultDto paResultDto) throws UnifiedResultException {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getFlightDate, flightDate)
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .one();
        // 出港联程航班如果未初始化，出港部分返回错误信息
        if (!Constant.Y.equals(planFlight.getIsFlightInitial()) && paResultDto.isTypeO()) {
            String hhmm = DateUtil.format(new Date(), "HHmm");
            paResultDto.setErrorO(StrUtil.format("{}\r\nCKI TIME {}", this.getErrorO(paResultDto), hhmm));
        }
        switch (planFlight.getCkStatus()) {
            case Constant.CK_STATUS_CI:
                throw new UnifiedResultException(Constant.FLT_INITIALLY_CLOSED);
            case Constant.CK_STATUS_CL:
                if (ObjectUtil.isNotEmpty(paResultDto) && paResultDto.isTypeO()) {
                    paResultDto.setErrorO(this.getErrorO(paResultDto));
                    break;
                } else {
                    throw new UnifiedResultException(Constant.FLIGHT_CLOSED_FOR_LDP);
                }
            case Constant.CK_STATUS_CC:
                throw new UnifiedResultException(Constant.FLT_CLOSED);
            case Constant.CK_STATUS_EC:
            case Constant.CK_STATUS_PC:
                throw new UnifiedResultException(Constant.PROT_COV);
            case Constant.CK_STATUS_XX:
                throw new UnifiedResultException(Constant.FLT_CANCELED);
            case Constant.CK_STATUS_OP:
                // OP的时候验证起飞时间
                MnjxAirport airport = iMnjxAirportService.lambdaQuery()
                        .eq(MnjxAirport::getAirportCode, orgCity)
                        .one();
                List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                        .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                        .eq(MnjxPlanSection::getDepAptId, airport.getAirportId())
                        .orderByAsc(MnjxPlanSection::getIsLastSection)
                        .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                        .orderByAsc(MnjxPlanSection::getEstimateOff)
                        .list();
                MnjxPlanSection firstPlanSection = planSectionList.get(0);
                String actualOff = firstPlanSection.getActualOff();
                String actualOffChange = firstPlanSection.getActualOffChange();
                String calculateDate;
                if (StrUtil.isEmpty(actualOff)) {
                    String estimateOff = firstPlanSection.getEstimateOff();
                    String estimateOffChange = firstPlanSection.getEstimateOffChange();
                    calculateDate = DateUtils.calculateDate(flightDate, estimateOff, estimateOffChange);
                } else {
                    calculateDate = DateUtils.calculateDate(flightDate, actualOff, actualOffChange);
                }
                int compare = DateUtil.compare(DateUtil.parseDateTime(DateUtil.now()), DateUtil.parseDateTime(calculateDate));
                if (compare > 0) {
                    throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
                }
                break;
            default:
                break;
        }
    }

    /**
     * Title: constructPaResultDtoFromPd
     * Description: 从PD结果集初步构建返回结果
     *
     * @param paResultDto paResultDto
     * @param pdInfoDto   pdInfoDto
     * <AUTHOR>
     */
    @Override
    public void constructPaResultDtoFromPd(PaResultDto paResultDto, PdInfoDto pdInfoDto, int pdIndex) throws UnifiedResultException {
        // 构建航班信息
        String date = pdInfoDto.getDate();
        if (!date.contains(StrUtil.DASHED)) {
            date = DateUtils.com2ymd(date);
        }
        paResultDto.setFlightDate(date);
        paResultDto.setOrgAirport(pdInfoDto.getCity().substring(0, 3));
        paResultDto.setPlaneType(pdInfoDto.getPlaneType().split(StrUtil.SLASH)[0]);
        paResultDto.setPlaneVersion(pdInfoDto.getPlaneType().split(StrUtil.SLASH)[1]);
        paResultDto.setGate(pdInfoDto.getGtd().split(StrUtil.SLASH)[1]);
        paResultDto.setBdt(pdInfoDto.getBoarding());
        paResultDto.setSd(pdInfoDto.getEstimateOff());
        paResultDto.setEd(pdInfoDto.getActualOff());
        paResultDto.setSa("");
        paResultDto.setFt("");
        // 构建旅客信息数据
        List<PdNmDto> pdNmDtoList = pdInfoDto.getNms();
        if (pdIndex > pdNmDtoList.size()) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        PdNmDto pdNmDto = pdNmDtoList.get(pdIndex - 1);
        paResultDto.setInputIndex(pdIndex);
        paResultDto.setQueryName(pdNmDto.getQueryName().matches("\\d+.+") ? pdNmDto.getQueryName().substring(1) : pdNmDto.getQueryName());
        paResultDto.setPnrNmId(pdNmDto.getPnrNmId());
        paResultDto.setCnin(StrUtil.isNotEmpty(pdNmDto.getIsCnin()));
        paResultDto.setName(pdNmDto.getName());
        paResultDto.setOInterlink(pdNmDto.getOInterlink());
        paResultDto.setDstAirport(pdNmDto.getDst());
        paResultDto.setSellCabin(pdNmDto.getSellCabin());
        paResultDto.setCabinClass(pdNmDto.getCabinClass());
        paResultDto.setShareFlightNo(pdNmDto.getShareFlight());
        paResultDto.setFlightNo(StrUtil.isEmpty(pdNmDto.getCarrierFlight()) ? pdInfoDto.getFlightNo() : pdNmDto.getCarrierFlight());
        paResultDto.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));
    }

    /**
     * Title: handleFromPr
     * Description: 从PR结果集处理参数
     *
     * @param paCmdDtos    paCmdDtos
     * @param paResultDtos paResultDtos
     * @param pdInfoDto    pdInfoDto
     * <AUTHOR>
     */
    private void handleFromPr(List<PaCmdDto> paCmdDtos, List<PaResultDto> paResultDtos, PdInfoDto pdInfoDto) throws UnifiedResultException {
        for (PaCmdDto paCmdDto : paCmdDtos) {
            int pdIndex = paCmdDto.getPdIndex();
            if (pdIndex > pdInfoDto.getNms().size() || pdIndex < 1) {
                throw new UnifiedResultException(Constant.NO_RECORD);
            }
            PaResultDto paResultDto = new PaResultDto();
            paResultDto.setAdv(paCmdDto.isAdv());
            paResultDto.setOptionList(paCmdDto.getOptionList());
            paResultDto.setCmd(paCmdDto.getCmd());
            this.constructPaResultDtoFromPd(paResultDto, pdInfoDto, paCmdDto.getPdIndex());
            paResultDtos.add(paResultDto);
        }
    }

    /**
     * Title: parseAndUpdateData
     * Description: 统一处理所有的返回数据，并更新业务修改的数据
     *
     * @param paResultDtos paResultDtos
     * <AUTHOR>
     */
    @Override
    public void parseAndUpdateData(List<PaResultDto> paResultDtos) throws UnifiedResultException {
        // 如果有出港联程参数O，则先把List<PaResultDto>重新处理，出港联程的部分构建成PaResultDto对象加进去，统一走后面逻辑，按多旅客处理
        List<PaResultDto> newPaResultDtos = new ArrayList<>();
        // 记录各航班号下接收旅客产生的下一个BN号
        if (paResultDtos.stream().anyMatch(p -> CollUtil.isNotEmpty(p.getOptionList()) && p.getOptionList().stream().anyMatch(Constant.O::equals))) {
            // HBJC JC不允许参数带O
            if (paResultDtos.stream().anyMatch(p -> p.getCmd().startsWith(Constant.CMD_HBJC) || p.getCmd().startsWith(Constant.CMD_JC))) {
                throw new UnifiedResultException(Constant.ACCEPTANCE_REJECTED);
            }
            // 存储第一个旅客的除开O之后的参数列表，当多个旅客接收时需要给其他旅客同样添加这些参数
            List<String> psgOptionList = new ArrayList<>();
            this.preHandleO(paResultDtos, newPaResultDtos, psgOptionList);
        } else if (paResultDtos.size() > 1) {
            // 如果同时接收多个旅客，参数没有O，当多个旅客有出港联程航班时，依然需要判断这些旅客的出港联程航班是不是一样
            List<String> firstFlightNoList = new ArrayList<>();
            for (int i = 0; i < paResultDtos.size(); i++) {
                PaResultDto paResultDto = paResultDtos.get(i);
                String pnrId = iMnjxPnrNmService.getById(paResultDto.getPnrNmId()).getPnrId();
                // 获取该pnr所有航段组及旅客信息
                List<PrDto> currentSegList = prMapper.retrieveByInterline(pnrId, null);
                // 获取当前航段及出港联程部分的航段组及旅客信息
                List<PrDto> oSegList = CollUtil.sub(currentSegList, currentSegList.indexOf(currentSegList.stream().filter(s -> paResultDto.getFlightNo().equals(StrUtil.isNotEmpty(s.getCarrierFlight()) ? s.getCarrierFlight() : s.getFlightNo())).collect(Collectors.toList()).get(0)), currentSegList.size());
                List<String> flightNoList = oSegList.stream()
                        .map(p -> StrUtil.isNotEmpty(p.getCarrierFlight()) ? p.getCarrierFlight() : p.getFlightNo())
                        .filter(StrUtil::isNotEmpty)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                if (i == 0) {
                    firstFlightNoList = flightNoList;
                } else if (!CollUtil.isEqualList(firstFlightNoList, flightNoList)) {
                    throw new UnifiedResultException(Constant.OUTBOUND_CONFLICT);
                }
            }
        }
        // 有O联程旅客，重新赋值
        if (CollUtil.isNotEmpty(newPaResultDtos)) {
            paResultDtos.clear();
            paResultDtos.addAll(newPaResultDtos);
        }

        List<MnjxPsgCki> psgCkiList = new ArrayList<>();
        // 记录各航班旅客的座位
        Map<String, List<String>> existRandomSeatListMap = new HashMap<>(1024);
        // 先做所有旅客的OPTION验证
        for (PaResultDto paResultDto : paResultDtos) {
            // 处理选项数据更新，并验证各个旅客数据
            this.handleOption(paResultDto, psgCkiList, existRandomSeatListMap, paResultDtos);
        }

        // 如果当前段不是SB状态且联程航段有SB状态的，报错
        if (paResultDtos.stream().anyMatch(p -> Constant.CKI_STATUS_SB.equals(p.getMnjxPsgCki().getCkiStatus()) && p.isTypeO())
                && paResultDtos.stream().anyMatch(p -> !Constant.CKI_STATUS_SB.equals(p.getMnjxPsgCki().getCkiStatus()) && !p.isTypeO())) {
            throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
        }
        // 如果当前段是SB状态，但是指令入口不是JC HBJC，报错
        if (paResultDtos.stream().anyMatch(p -> Constant.CKI_STATUS_SB.equals(p.getMnjxPsgCki().getCkiStatus()))
                && !paResultDtos.stream().anyMatch(p -> StrUtil.startWithAny(p.getCmd(), Constant.CMD_HBJC, Constant.CMD_JC))) {
            throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
        }
        // 如果不是SB状态，但是指令入口是HBJC JC，报错
        if (!paResultDtos.stream().allMatch(p -> Constant.CKI_STATUS_SB.equals(p.getMnjxPsgCki().getCkiStatus()))
                && paResultDtos.stream().anyMatch(p -> StrUtil.startWithAny(p.getCmd(), Constant.CMD_HBJC, Constant.CMD_JC))) {
            throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
        }

        // 座位不足引起候补
        if (paResultDtos.stream().anyMatch(PaResultDto::isCap)) {
            this.changeCap(paResultDtos);
        }

        // 进行统一验证
        this.validateCommon(paResultDtos, psgCkiList);

        // 接收前如果预接收了AVIH行李，则接收的时候必须输入AVIH参数
        if (paResultDtos.stream().anyMatch(p -> !p.isAdv())) {
            List<String> pnrNmIdList = paResultDtos.stream()
                    .map(PaResultDto::getPnrNmId)
                    .distinct()
                    .collect(Collectors.toList());
            List<MnjxLuggage> avihLuggageList = iMnjxLuggageService.lambdaQuery()
                    .in(MnjxLuggage::getPnrNmId, pnrNmIdList)
                    .in(MnjxLuggage::getLuggageType, Constant.STR_THREE, Constant.STR_FOUR)
                    .and(l -> l.isNull(MnjxLuggage::getIsDel).or().ne(MnjxLuggage::getIsDel, Constant.DELETE_TYPE))
                    .list();
            if (CollUtil.isNotEmpty(avihLuggageList) && paResultDtos.stream().flatMap(p -> p.getOptionList().stream()).noneMatch(o -> o.startsWith("AVIH"))) {
                throw new UnifiedResultException(Constant.AVIH_COUNT_CONFLICT);
            }
        }

        // 查询所有的SSR
        List<String> nmIdList = paResultDtos.stream()
                .map(PaResultDto::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxNmSsr> allSsrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, nmIdList)
                .list();
        // 对每个旅客设置自己的SSR列表
        for (PaResultDto paResultDto : paResultDtos) {
            MnjxPnrSeg pnrSeg = paResultDto.getMnjxPnrSeg();
            List<MnjxNmSsr> ssrList = allSsrList.stream()
                    .filter(s -> ObjectUtil.isEmpty(s.getPnrSegNo()) || pnrSeg.getPnrSegNo().equals(s.getPnrSegNo()))
                    .filter(s -> s.getPnrNmId().equals(paResultDto.getPnrNmId()))
                    .collect(Collectors.toList());
            paResultDto.setSsrList(ssrList);
            List<MnjxNmSsr> foidList = ssrList.stream()
                    .filter(s -> Constant.SSR_TYPE_FOID.equals(s.getSsrType()))
                    .collect(Collectors.toList());
            // 没有证件信息报错
            if (CollUtil.isEmpty(foidList)) {
                if (StrUtil.isEmpty(paResultDto.getFoidParam()) && !paResultDto.isAdv()) {
                    throw new UnifiedResultException(Constant.DATA_REQUIRED);
                }
            } else {
                MnjxNmSsr foidSsr = foidList.get(0);
                String foidInfo = foidSsr.getInputValue().split(" ")[4].split(StrUtil.SLASH)[0];
                String foidType = foidInfo.substring(0, 2);
                String foidNo = foidInfo.substring(2);
                // 查询该旅客是否已有当前航司的常客卡
                MnjxFrequenter frequenter = iMnjxFrequenterService.lambdaQuery()
                        .eq(MnjxFrequenter::getAirlineCode, paResultDto.getAirlineCode())
                        .eq(MnjxFrequenter::getFrequenterCertificateType, foidType)
                        .eq(MnjxFrequenter::getFrequenterCertificateNo, foidNo)
                        .one();
                // 旅客有常客卡
                if (ObjectUtil.isNotEmpty(frequenter)) {
                    // 如果输入了FF选项，卡号或者级别不匹配，报错
                    if (StrUtil.isNotEmpty(paResultDto.getFfNo()) && (!paResultDto.getFfNo().equals(frequenter.getFrequenterCard()) || !paResultDto.getFfLevel().equals(frequenter.getFrequenterLevel()))) {
                        throw new UnifiedResultException(Constant.INVALID_PROFILE_NUMBER);
                    }
                }
                // 如果输入的常客卡号在数据库中存在，验证卡号存在时与旅客的身份证信息匹配
                if (ObjectUtil.isNotEmpty(paResultDto.getDbFrequenter()) && !foidNo.equals(paResultDto.getDbFrequenter().getFrequenterCertificateNo())) {
                    throw new UnifiedResultException(Constant.INVALID_PROFILE_NUMBER);
                }
            }
        }

        // 验证重量限制，每个航班的每个航节总重（uwt、uaw）200000，接收旅客和行李需要总计在这个重量范围内
        this.validateUwtUaw(paResultDtos);

        // 验证完成后才进行数据的操作
        // 记录联程行李
        List<MnjxLuggage> luggageList = new ArrayList<>();
        // 记录各航班号下接收旅客产生的下一个BN号
        Map<String, Integer> flightBnNoMap = new HashMap<>(1024);
        String lastFlightNo = "";
        synchronized (this) {
            // 获取同一航节的旅客数
            Map<String, Integer> planSectionPsgNumMap = new HashMap<>();
            Map<String, Integer> planSectionExstNumMap = new HashMap<>();
            for (PaResultDto paResultDto : paResultDtos) {
                List<MnjxPlanSection> planSectionList = paResultDto.getPlanSectionList();
                for (MnjxPlanSection planSection : planSectionList) {
                    if (!planSectionPsgNumMap.containsKey(planSection.getPlanSectionId())) {
                        planSectionPsgNumMap.put(planSection.getPlanSectionId(), 1);
                    } else {
                        planSectionPsgNumMap.put(planSection.getPlanSectionId(), planSectionPsgNumMap.get(planSection.getPlanSectionId()) + 1);
                    }
                    if (paResultDto.isHaveExst()) {
                        if (!planSectionExstNumMap.containsKey(planSection.getPlanSectionId())) {
                            planSectionExstNumMap.put(planSection.getPlanSectionId(), 1);
                        } else {
                            planSectionExstNumMap.put(planSection.getPlanSectionId(), planSectionExstNumMap.get(planSection.getPlanSectionId()) + 1);
                        }
                    }
                }
            }
            Map<String, String> gsMap = new HashMap<>();
            for (PaResultDto paResultDto : paResultDtos) {
                // typeO为false时，表示处理到下一个旅客了，联程行李记录重置
                if (!paResultDto.isTypeO()) {
                    luggageList.clear();
                }
                // 每个航班的BN号处理
                String flightNo = paResultDto.getFlightNo();
                if (!lastFlightNo.equals(flightNo)) {
                    List<MnjxPnrSeg> allPnrSegList = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getFlightDate, paResultDto.getFlightDate())
                            .eq(MnjxPnrSeg::getFlightNo, flightNo)
                            .eq(MnjxPnrSeg::getOrg, paResultDto.getOrgAirport())
                            .eq(MnjxPnrSeg::getDst, paResultDto.getDstAirport())
                            .list();
                    Integer aboardNo = this.getNextAboardNo(allPnrSegList);
                    flightBnNoMap.put(flightNo, aboardNo);
                }
                Integer bnNo = flightBnNoMap.get(flightNo);
                // 接收旅客
                this.handlePa(paResultDto, bnNo, luggageList, gsMap, planSectionPsgNumMap, planSectionExstNumMap);
                // 接收完产生了新登机号，下一个登机号+1
                if (!paResultDto.isAdv() && !Constant.DL.equals(paResultDto.getOldCkiStatus())) {
                    flightBnNoMap.put(flightNo, ++bnNo);
                }
                // 数据获取完成，部分数据格式进行处理
                this.formatRenderData(paResultDto);
                lastFlightNo = flightNo;
            }
            Set<Map.Entry<String, String>> entries = gsMap.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                iMnjxPlanSectionService.lambdaUpdate()
                        .eq(MnjxPlanSection::getPlanSectionId, entry.getKey())
                        .set(MnjxPlanSection::getGoshowLimit, entry.getValue())
                        .update();
            }
        }

        // 获取旅客联程信息
        // 只能给每个旅客的最后一个联程段加联程信息。如旅客1 A-B-C，接收时PA 1,O，则将 O C 的信息加在B联程航段上，而不会把 O B,O C 信息加在A联程段上
        paResultDtos.forEach(p -> p.getInterlineResults().clear());
        Map<String, List<PaResultDto>> psgPaResultMap = paResultDtos.stream()
                .collect(Collectors.groupingBy(PaResultDto::getPnrNmId));
        for (Map.Entry<String, List<PaResultDto>> entry : psgPaResultMap.entrySet()) {
            List<PaResultDto> psgPaResultList = entry.getValue();
            PaResultDto lastPsg = psgPaResultList.get(psgPaResultList.size() - 1);
            iPrService.getInterline(lastPsg.getPnr().getPnrId(), lastPsg.getMnjxPsgCki().getPnrSegNo(), lastPsg.getPnrNmId(), lastPsg.getMnjxPnrSeg().getOrg(), lastPsg.getMnjxPnrSeg().getDst(), lastPsg.getInterlineResults(), null);
            // 当信息加在出港联程旅客上时，需要移除 I 的部分
            if (!lastPsg.equals(psgPaResultList.get(0))) {
                lastPsg.getInterlineResults().removeIf(i -> i.trim().startsWith("I"));
            }
        }

        // 预升降舱由于舱等销售舱位座位发生了变化，需要更新PD缓存
        if (paResultDtos.stream().anyMatch(p -> StrUtil.isNotEmpty(p.getGType()))) {
            MemoryData memoryData = MemoryDataUtils.getMemoryData();
            PdInfoDto pdDefaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
            if (ObjectUtil.isNotEmpty(pdDefaultInfo)) {
                String pdCmd = pdDefaultInfo.getCmdStr();
                PdParamDto pdParamDto = iPdService.dealCmd(pdCmd);
                iPdService.returnDefaultInfo(memoryData, pdParamDto);
            }
        }
    }

    private void validateCommon(List<PaResultDto> paResultDtos, List<MnjxPsgCki> psgCkiList) throws UnifiedResultException {
        for (int i = 0; i < paResultDtos.size(); i++) {
            PaResultDto paResultDto = paResultDtos.get(i);
            // 如果上一个出港联程有错误信息，则之后的出港联程都填充这个错误信息
            if (i > 0 && paResultDto.isTypeO() && paResultDtos.get(i - 1).isTypeO() && StrUtil.isNotEmpty(paResultDtos.get(i - 1).getErrorO())) {
                paResultDto.setErrorO(this.getErrorO(paResultDto));
            } else {
                // 验证航班状态和起飞时间
                this.validateFlightStatus(paResultDto.getFlightNo(), paResultDto.getFlightDate().contains(StrUtil.DASHED) ? paResultDto.getFlightDate() : DateUtils.com2ymd(paResultDto.getFlightDate()), paResultDto.getOrgAirport(), paResultDto);
            }
        }

        // 验证值机状态
        // 已经值机或登机，不允许再接收
        if (paResultDtos.stream().anyMatch(p -> StrUtil.equalsAny(p.getMnjxPsgCki().getCkiStatus(), Constant.ACC, Constant.GT))) {
            throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
        }

        // 验证所有旅客的座位号是否重复
        Map<String, List<PaResultDto>> map = paResultDtos.stream().collect(Collectors.groupingBy(s -> s.getFlightNo() + s.getFlightDate()));
        for (Map.Entry<String, List<PaResultDto>> entry : map.entrySet()) {
            List<PaResultDto> list = entry.getValue();
            List<PaResultDto> haveSeatNoList = list.stream().filter(p -> StrUtil.isNotEmpty(p.getSeatNo())).collect(Collectors.toList());
            long count = haveSeatNoList.stream().map(PaResultDto::getSeatNo).distinct().count();
            if (count != 0 && count < haveSeatNoList.size()) {
                throw new UnifiedResultException(Constant.SEAT_CONFLICT);
            }
        }

        // 预升降舱必须是#
        if (paResultDtos.stream().anyMatch(p -> !p.isAdv() && StrUtil.isNotEmpty(p.getGType()))) {
            throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
        }

        // 如果接收行李，且是候补旅客，需要判断GS足够，不够则报错
        this.judgeGs(paResultDtos, psgCkiList);
    }

    private String getErrorO(PaResultDto paResultDto) {
        return StrUtil.format(" TRANSFER  {} {}/{}{}{} CHECK-IN UNAVAILABLE", paResultDto.getOrgAirport(), paResultDto.getFlightNo(), paResultDto.getFlightDate().contains("-") ? DateUtils.ymd2Com(paResultDto.getFlightDate()) : paResultDto.getFlightDate(), paResultDto.getSellCabin(), paResultDto.getDstAirport());
    }

    /**
     * Title: preHandleO
     * Description: 如果有出港联程参数，重新构建旅客参数
     *
     * @param paResultDtos    paResultDtos
     * @param newPaResultDtos newPaResultDtos
     * @param psgOptionList   psgOptionList
     * <AUTHOR>
     * @date 2023/1/13 10:55
     */
    private void preHandleO(List<PaResultDto> paResultDtos, List<PaResultDto> newPaResultDtos, List<String> psgOptionList) throws UnifiedResultException {
        List<String> firstPsgFlightNoList = new ArrayList<>();
        // 对每个旅客循环
        for (int i = 0; i < paResultDtos.size(); i++) {
            PaResultDto paResultDto = paResultDtos.get(i);
            String flightNo = paResultDto.getFlightNo();
            String pnrNmId = paResultDto.getPnrNmId();
            String pnrId = iMnjxPnrNmService.getById(pnrNmId).getPnrId();
            String flightDate = paResultDto.getFlightDate();
            List<MnjxPnrSeg> allPnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getFlightDate, flightDate)
                    .eq(MnjxPnrSeg::getFlightNo, StrUtil.isNotEmpty(paResultDto.getShareFlightNo()) ? paResultDto.getShareFlightNo() : flightNo)
                    .eq(MnjxPnrSeg::getOrg, paResultDto.getOrgAirport())
                    .eq(MnjxPnrSeg::getDst, paResultDto.getDstAirport())
                    .list();
            MnjxPnrSeg currentSeg = allPnrSegList.stream()
                    .filter(s -> pnrId.equals(s.getPnrId()))
                    .collect(Collectors.toList())
                    .get(0);
//            if (i == 0) {
//                // 获取下一个登机号
//                Integer aboardNo = this.getNextAboardNo(allPnrSegList);
//                flightBnNoMap.put(flightNo, aboardNo);
//            }

            List<String> optionList = paResultDto.getOptionList();

            // 如果当前站旅客本身有特服项（通过订座产生或预接收产生），在正式接收需要将这些特服项覆盖到同时接收的出港联程段上
            List<String> currentSsrTypeList = new ArrayList<>();
            if (!paResultDto.isAdv()) {
                List<MnjxNmSsr> currentSsrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                        .eq(MnjxNmSsr::getPnrSegNo, currentSeg.getPnrSegNo())
                        .list();
                currentSsrList = currentSsrList.stream()
                        .filter(s -> !StrUtil.equalsAny(s.getSsrType(), Constant.SSR_TYPE_FOID, Constant.SSR_TYPE_CHLD, Constant.SSR_TYPE_INFT, Constant.SSR_TYPE_UMNR, Constant.TKNE, Constant.SSR_TYPE_FQTV) && !s.getSsrType().endsWith("ML") && !StrUtils.startWithAny(s.getSsrType(), "UPG", "DNG"))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(currentSsrList)) {
                    currentSsrTypeList = currentSsrList.stream()
                            .map(MnjxNmSsr::getSsrType)
                            .collect(Collectors.toList());
                    // 需要对一些特服进行拼接
                    // PETC BSCT要拼接数量和重量
                    MnjxPsgCki currentPsgCki = iMnjxPsgCkiService.lambdaQuery()
                            .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                            .eq(MnjxPsgCki::getPnrSegNo, currentSeg.getPnrSegNo())
                            .one();
                    List<MnjxPsgCkiOption> currentPsgCkiOptionList = iMnjxPsgCkiOptionService.lambdaQuery()
                            .eq(MnjxPsgCkiOption::getPsgCkiId, currentPsgCki.getPsgCkiId())
                            .list();
                    if (currentSsrTypeList.contains("PETC")) {
                        this.resetSsrTypeO(currentSsrTypeList, "PETC", currentPsgCkiOptionList);
                    }
                    if (currentSsrTypeList.contains("BSCT")) {
                        this.resetSsrTypeO(currentSsrTypeList, "BSCT", currentPsgCkiOptionList);
                    }
                    // PSM MSG PIL MEDA需要拼接具体信息
                    if (currentSsrTypeList.contains("PSM") || currentSsrTypeList.contains("PIL") || currentSsrTypeList.contains("MSG") || currentSsrTypeList.contains(Constant.SSR_TYPE_MEDA)) {
                        List<MnjxNmSsr> psmList = currentSsrList.stream()
                                .filter(s -> "PSM".equals(s.getSsrType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(psmList)) {
                            String psm = psmList.get(0).getInputValue();
                            List<String> allGroups = ReUtil.getAllGroups(PSM_PATTERN, psm);
                            psm = allGroups.get(1);
                            if ("/VIP".equals(psm)) {
                                psm = " /VIP";
                            }
                            currentSsrTypeList.removeIf("PSM"::equals);
                            currentSsrTypeList.add(StrUtil.format("PSM{}", psm));
                        }

                        // MSG
                        List<MnjxNmSsr> msgList = currentSsrList.stream()
                                .filter(s -> "MSG".equals(s.getSsrType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(msgList)) {
                            String msg = msgList.get(0).getInputValue();
                            List<String> allGroups = ReUtil.getAllGroups(MSG_PIL_PATTERN, msg);
                            msg = allGroups.get(2);
                            if (StrUtil.isNotEmpty(msg)) {
                                paResultDto.setMsg(msg);
                            }
                            currentSsrTypeList.removeIf("MSG"::equals);
                            currentSsrTypeList.add(StrUtil.format("MSG{}", msg));
                        }

                        // PIL
                        List<MnjxNmSsr> pilList = currentSsrList.stream()
                                .filter(s -> "PIL".equals(s.getSsrType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(pilList)) {
                            String pil = pilList.get(0).getInputValue();
                            List<String> allGroups = ReUtil.getAllGroups(MSG_PIL_PATTERN, pil);
                            pil = allGroups.get(2);
                            if (StrUtil.isNotEmpty(pil)) {
                                paResultDto.setPil(pil);
                            }
                            currentSsrTypeList.removeIf("PIL"::equals);
                            currentSsrTypeList.add(StrUtil.format("PIL{}", pil));
                        }

                        // MEDA
                        List<MnjxNmSsr> medaList = currentSsrList.stream()
                                .filter(s -> Constant.SSR_TYPE_MEDA.equals(s.getSsrType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(medaList)) {
                            MnjxNmSsr medaSsr = medaList.get(0);
                            String meda = StrUtil.format("MEDA- NN1 {}{}{}{}", medaSsr.getOrgDst(), flightNo.substring(2), currentSeg.getCabinClass(), DateUtils.ymd2Com(medaSsr.getFltDate()).substring(0, 5));
                            paResultDto.setMeda(meda);
                            currentSsrTypeList.removeIf("MEDA"::equals);
                            currentSsrTypeList.add(StrUtil.format("MEDA{}", meda));
                        }
                    }
                }
                // 如果旅客本身有婴儿，指令没有输入INF项，则给出港联程部分加上INF的参数
                MnjxNmXn xn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                        .one();
                // 如果当前站没有婴儿的SSR信息，且没有输入INF项，说明出港联程部分预接收过婴儿信息，当前站进行联程接收报错
                MnjxNmSsr currentInft = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .eq(MnjxNmSsr::getPnrSegNo, currentSeg.getPnrSegNo())
                        .one();
                if (ObjectUtil.isNotEmpty(xn) && optionList.stream().noneMatch(o -> o.startsWith("INF1"))) {
                    if (ObjectUtil.isEmpty(currentInft)) {
                        throw new UnifiedResultException(Constant.INF_COUNT_CONFLICT);
                    }
                    currentSsrTypeList.add(StrUtil.format("INF1{}", xn.getXnCname()));
                }
            }

            long oCount = optionList.stream()
                    .filter(Constant.O::equals)
                    .count();
            if (oCount > 0) {
                // 获取该pnr所有航段组及旅客信息
                List<PrDto> segList = prMapper.retrieveByInterline(pnrId, null);
                segList = segList.stream()
                        .filter(s -> pnrNmId.equals(s.getPnrNmId()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(firstPsgFlightNoList)) {
                    firstPsgFlightNoList.addAll(segList.stream().map(PrDto::getFlightNo).collect(Collectors.toList()));
                } else if (firstPsgFlightNoList.size() != segList.size() || !CollUtil.containsAll(firstPsgFlightNoList, segList.stream().map(PrDto::getFlightNo).collect(Collectors.toList()))) {
                    throw new UnifiedResultException(Constant.OUTBOUND_CONFLICT);
                }
                // 获取当前航段及出港联程部分的航段组及旅客信息
                List<PrDto> subList = CollUtil.sub(segList, segList.indexOf(segList.stream().filter(s -> (StrUtil.isNotEmpty(paResultDto.getShareFlightNo()) ? paResultDto.getShareFlightNo() : flightNo).equals(s.getFlightNo())).collect(Collectors.toList()).get(0)), segList.size());
                String dst = null;
                List<PrDto> oSegList = new ArrayList<>();
                for (PrDto prDto : subList) {
                    String org = prDto.getOrg();
                    if (StrUtil.isNotEmpty(dst) && !org.equals(dst)) {
                        break;
                    }
                    dst = prDto.getDst();
                    if (StrUtil.isNotEmpty(prDto.getFlightDate())) {
                        oSegList.add(prDto);
                    }
                }
                // 如果指令中O标识的数目大于实际联程航班的数目，报错
                if (CollUtil.isEmpty(oSegList) || oCount > oSegList.size() - 1) {
                    throw new UnifiedResultException(Constant.TRANSFER_FLIGHT_NBR);
                }
                // 设置当前航段及出港联程部分航段组中旅客每个航段的psg num
                iPrService.assemblePsgNum(oSegList);
                // 当前旅客的参数列表处理
                List<String> currentPsgOptionList = CollUtil.sub(optionList, 0, optionList.indexOf(Constant.O));
                // FF参数特殊处理，可以放O前O后，并且处理逻辑都是给当前段及O数量的出港联程段加上
                if (optionList.stream().anyMatch(o -> ReUtil.isMatch(FF_PATTERN, o))) {
                    currentPsgOptionList.addAll(optionList.stream().filter(o -> ReUtil.isMatch(FF_PATTERN, o)).collect(Collectors.toList()));
                    optionList.removeIf(o -> ReUtil.isMatch(FF_PATTERN, o));
                }
                // O前不能有升降舱参数
                if (currentPsgOptionList.stream().anyMatch(o -> ReUtil.isMatch(UPG_DNG_PATTERN, o))) {
                    throw new UnifiedResultException(Constant.CONFLICT);
                }
                currentPsgOptionList = CollUtil.distinct(currentPsgOptionList);
                paResultDto.setOptionList(currentPsgOptionList);
                if (CollUtil.isEmpty(psgOptionList)) {
                    psgOptionList.addAll(currentPsgOptionList);
                } else {
                    List<String> list = paResultDto.getOptionList();
                    psgOptionList.removeIf(o -> ReUtil.isMatch(BAG_PATTERN, o) || ReUtil.isMatch(BAG_DST, o) || ReUtil.isMatch(EXCESS_LUGGAGE_PATTERN, o) || ReUtil.isMatch(AVIH_PATTERN, o));
                    list.addAll(psgOptionList);
                    list = list.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    paResultDto.setOptionList(list);
                }
                newPaResultDtos.add(paResultDto);
                // 出港联程旅客的参数列表
                List<String> otherPsgOptionList = CollUtil.sub(optionList, optionList.indexOf(Constant.O), optionList.size());
                // O后不能有升降舱参数
                if (otherPsgOptionList.stream().anyMatch(o -> ReUtil.isMatch(UPG_DNG_PATTERN, o))) {
                    throw new UnifiedResultException(Constant.ITEM_SHOULD_PRECEDE_O_ITEM);
                }
                // 当前旅客有座位参数，出港联程没有座位参数时，沿用当前旅客的座位参数。或第二个出港联程没有座位参数，沿用第一个出港联程的座位参数
                String useLastSeat = "useLastSeat";
                if (currentPsgOptionList.stream().anyMatch(o -> ReUtils.matchAny(o, SEAT_PATTERN, SNR_PATTERN))) {
                    // 当前航段座位参数
                    List<String> seatParam = currentPsgOptionList.stream()
                            .filter(o -> ReUtils.matchAny(o, SEAT_PATTERN, SNR_PATTERN))
                            .collect(Collectors.toList());
                    // 按出港联程数转化每个出港联程的参数列表
                    List<List<String>> splitList = CollUtils.splitStringListByString(otherPsgOptionList, "O");
                    // 以O分隔开是空列表，说明出港联程段没有自己的特服或座位
                    if (CollUtil.isEmpty(splitList)) {
                        // 将当前航段的座位号复制给出港联程
                        List<String> tempList = new ArrayList<>(otherPsgOptionList);
                        otherPsgOptionList.clear();
                        for (String s : tempList) {
                            otherPsgOptionList.add(s);
                            otherPsgOptionList.addAll(seatParam);
                            otherPsgOptionList.add(useLastSeat);
                        }
                    } else {
                        // 清空之前的出港联程参数
                        otherPsgOptionList.clear();
                        // 重新组装出港联程参数
                        splitList.forEach(split -> {
                            // 每一个出港联程先加O
                            otherPsgOptionList.add("O");
                            // 添加自身的参数列表
                            otherPsgOptionList.addAll(split);
                            // 如果当前出港联程段没有座位参数，使用上一个段（可能是当前段，也可能是上一个联程段）的座位参数
                            if (split.stream().noneMatch(o -> ReUtils.matchAny(o, SEAT_PATTERN, SNR_PATTERN))) {
                                otherPsgOptionList.addAll(seatParam);
                                otherPsgOptionList.add(useLastSeat);
                            } else {
                                seatParam.clear();
                                seatParam.addAll(split.stream().filter(o -> ReUtils.matchAny(o, SEAT_PATTERN, SNR_PATTERN)).collect(Collectors.toList()));
                            }
                        });
                    }
                }
                // 去除空参数（没有输入任何航段座位参数的情况）
                otherPsgOptionList.removeIf(StrUtil::isEmpty);
                // 对该旅客的出港联程参数个数循环
                PaResultDto newPaResultDto = new PaResultDto();
                List<String> newOptionList = new ArrayList<>();
                int segCount = 1;
                // 保存一份没有oList参数的对象，用作给联程段复制
                PaResultDto originPaResultDto = new PaResultDto();
                BeanUtil.copyProperties(paResultDto, originPaResultDto);
                for (String otherPsgOption : otherPsgOptionList) {
                    if (Constant.O.equals(otherPsgOption)) {
                        // 构建出港联程的PaResult对象
                        newPaResultDto = new PaResultDto();
                        // 拷贝当前旅客信息到当前出港联程旅客，主要是获取旅客本身信息，航班、参数等会进行替换
                        BeanUtil.copyProperties(originPaResultDto, newPaResultDto);
                        // 给当前航段添加O列表，用于行李处理（如果参数带了行李）
                        paResultDto.getOList().add(Constant.O);
                        // 出港联程旅客的参数列表构建，特服参数会复制当前旅客的部分
                        newOptionList = new ArrayList<>(currentPsgOptionList);
                        if (!CollUtil.containsAll(newOptionList, currentSsrTypeList)) {
                            newOptionList.addAll(currentSsrTypeList);
                        }
                        newOptionList = CollUtil.distinct(newOptionList);
                        // 移除座位参数，上面已经处理了联程部分的座位参数，在else中加入。性别参数也不需要添加到联程上，只需要当前改一次
                        newOptionList.removeIf(o -> ReUtils.matchAny(o, BAG_PATTERN, EXCESS_LUGGAGE_PATTERN, BAG_DST, SEAT_PATTERN, SNR_PATTERN, SEX_PATTERN, AVIH_PATTERN));
                        // 重新覆盖当前出港联程旅客的参数列表
                        newPaResultDto.setOptionList(newOptionList);
                        // 重新构建当前出港联程旅客的航班信息数据
                        PrDto prDto = oSegList.get(segCount);
                        String oFlightNo = StrUtil.isNotEmpty(prDto.getCarrierFlight()) ? prDto.getCarrierFlight() : prDto.getFlightNo();
//                        if (i == 0) {
//                            // 出港联程部分的获取下一个登机号
//                            List<MnjxPnrSeg> oAllPnrSegList = iMnjxPnrSegService.lambdaQuery()
//                                    .eq(MnjxPnrSeg::getFlightDate, prDto.getFlightDate())
//                                    .eq(MnjxPnrSeg::getFlightNo, oFlightNo)
//                                    .eq(MnjxPnrSeg::getOrg, prDto.getOrg())
//                                    .eq(MnjxPnrSeg::getDst, prDto.getDst())
//                                    .list();
//                            Integer aboardNo = this.getNextAboardNo(oAllPnrSegList);
//                            flightBnNoMap.put(oFlightNo, aboardNo);
//                        }
                        // 构建航班部分信息
                        newPaResultDto.setFlightNo(oFlightNo);
                        newPaResultDto.setShareFlightNo(StrUtil.isNotEmpty(prDto.getCarrierFlight()) ? prDto.getFlightNo() : null);
                        newPaResultDto.setFlightDate(prDto.getFlightDate());
                        newPaResultDto.setDstAirport(prDto.getDst());
                        PdInfoDto pdInfoDto = iPrService.buildPdInfoDto(prDto, null);
                        newPaResultDto.setPlaneType(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[0] : null);
                        newPaResultDto.setPlaneVersion(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[1] : null);
                        newPaResultDto.setGate(StrUtil.isNotEmpty(pdInfoDto.getGtd()) ? (pdInfoDto.getGtd().split("/").length > 1 ? pdInfoDto.getGtd().split("/")[1] : pdInfoDto.getGtd()) : "????");
                        newPaResultDto.setBdt(pdInfoDto.getBoarding());
                        newPaResultDto.setSd(pdInfoDto.getEstimateOff());
                        newPaResultDto.setEd(pdInfoDto.getActualOff());
                        newPaResultDto.setSa("");
                        newPaResultDto.setFt("");
                        newPaResultDto.setAirlineCode(newPaResultDto.getFlightNo().substring(0, 2));
                        // 重新构建当前出港联程旅客的部分旅客信息
                        newPaResultDto.setOInterlink(null);
                        newPaResultDto.setOrgAirport(prDto.getOrg());
                        newPaResultDto.setSellCabin(prDto.getSellCabin());
                        newPaResultDto.setCabinClass(prDto.getCabinClass());
                        newPaResultDto.setPsgNum(prDto.getPsgNum());
                        newPaResultDto.setTypeO(true);
                        newPaResultDtos.add(newPaResultDto);
                        segCount++;
                    } else {
                        if (newOptionList.contains(otherPsgOption)) {
                            continue;
                        }
                        // 当前出港联程旅客自身的参数项，能放在O之后的参数只有 座位(RXXX SNRXXX)、特殊餐食、XBP
                        if (ReUtils.matchAny(otherPsgOption, SEAT_PATTERN, SNR_PATTERN, REG_XBP) || otherPsgOption.startsWith("SPML")) {
                            newOptionList.add(otherPsgOption);
                        } else if (otherPsgOption.equals(useLastSeat)) {
                            newPaResultDto.setUseLastSeatO(true);
                        } else {
                            throw new UnifiedResultException(Constant.ITEM_SHOULD_PRECEDE_O_ITEM);
                        }
                    }
                }
            }
        }
    }

    private void resetSsrTypeO(List<String> currentSsrTypeList, String ssrType, List<MnjxPsgCkiOption> currentPsgCkiOptionList) {
        AtomicReference<String> atomicReference = new AtomicReference<>("");
        currentSsrTypeList.forEach(s -> {
            if (ssrType.equals(s)) {
                MnjxPsgCkiOption ckiOption = currentPsgCkiOptionList.stream()
                        .filter(p -> s.equals(p.getOptionType()))
                        .collect(Collectors.toList())
                        .get(0);
                atomicReference.set(StrUtil.format("{}{}", s, ckiOption.getOptionValue()));
            }
        });
        String replace = atomicReference.get();
        currentSsrTypeList.removeIf(ssrType::equals);
        currentSsrTypeList.add(replace);
    }

    /**
     * Title: changeCap
     * Description: 旅客信息转换为CAP
     *
     * @param paResultDtos paResultDtos
     * <AUTHOR>
     * @date 2022/12/7 11:43
     */
    private void changeCap(List<PaResultDto> paResultDtos) throws UnifiedResultException {
        // 接收SB状态的，说明是HBJC指令，这时候仍然没有座位数量，要提示拒绝接收
        if (paResultDtos.stream().anyMatch(p -> Constant.CKI_STATUS_SB.equals(p.getMnjxPsgCki().getCkiStatus()) && !p.isTypeO() && StrUtil.startWithAny(p.getCmd(), Constant.CMD_HBJC, Constant.CMD_JC))) {
            throw new UnifiedResultException(Constant.ACCEPTANCE_REJECTED);
        }
        // 如果是联程部分的座位不够，报错
        if (paResultDtos.stream().anyMatch(p -> p.isCap() && p.isTypeO())) {
            // 联程部分因为额外占座导致座位不够的，报错SEATS
            if (paResultDtos.stream().anyMatch(p -> StrUtil.isNotEmpty(p.getExstType()))) {
                throw new UnifiedResultException(Constant.SEATS);
            }
            throw new UnifiedResultException(Constant.UNABLE_TRANSFER);
        }
        paResultDtos.forEach(p -> {
            // 当前航段没有座位（或额外占座座位不够）导致CAP SB，出港联程都添加错误信息
            if (p.isTypeO()) {
                p.setErrorO(this.getErrorO(p));
            }
            p.setCap(true);
            p.setSeatNo(null);
            p.setExstSeat(null);
            p.setNormalSeat(false);
            p.setSnr(false);
            p.setInputSeatNo(null);
            p.setMnjxSeatList(null);
            p.setPreOldSeat(null);
            p.getMnjxPsgSeat().setPsgSeat("");
            p.getMnjxPsgSeat().setSeatExst("");
            p.getMnjxPsgSeat().setSeatStatus("");
        });
    }

    @Override
    public void print(List<PaResultDto> paResultDtoList) {
        PaResultDto tmpDto = paResultDtoList.get(0);
        if (!tmpDto.isAdv()) {
            // 打票机开关开启才会调用
            MnjxConfig config = iMnjxConfigService.lambdaQuery()
                    .eq(MnjxConfig::getType, "PRINT")
                    .one();
            if (ObjectUtil.isNotEmpty(config) && Constant.STR_ONE.equals(config.getAvailable())) {
                log.info("打印机开关已开启");
                log.info("开始打印登机牌");
                String aboardStream = "";
                // 打印登机牌
                try {
                    aboardStream = this.printBoard(paResultDtoList);
                    // 为抓包程序构建PA结果集
                    List<MnjxPrintData> printDataList = new ArrayList<>();
                    Map<String, List<PaResultDto>> collect = paResultDtoList.stream()
                            .collect(Collectors.groupingBy(p -> p.getFlightNo() + "/" + p.getFlightDate()));
                    for (Map.Entry<String, List<PaResultDto>> entry : collect.entrySet()) {
                        Map<String, String> map = new HashMap<>();
                        for (int i = 0; i < entry.getValue().size(); i++) {
                            PaResultDto paResultDto = entry.getValue().get(i);
                            if (StrUtil.equalsAny(paResultDto.getCkiStatus(), Constant.CKI_STATUS_ACC, Constant.CKI_STATUS_GT)) {
                                map.put(StrUtil.toString(i + 1), paResultDto.getBnNo().contains("BN") ? paResultDto.getBnNo() : "BN" + paResultDto.getBnNo());
                            }
                        }
                        if (MapUtil.isNotEmpty(map)) {
                            String cmd = tmpDto.getCmd();
                            String printKey = StrUtil.format("{}/{}", entry.getKey(), cmd.split(StrUtil.COLON)[0]);
                            MnjxPrintData dbPrintData = iMnjxPrintDataService.lambdaQuery()
                                    .eq(MnjxPrintData::getPrintKey, printKey)
                                    .one();
                            if (ObjectUtil.isNotEmpty(dbPrintData)) {
                                dbPrintData.setPrintValue(JSONUtil.toJsonStr(map));
                                iMnjxPrintDataService.updateById(dbPrintData);
                            } else {
                                MnjxPrintData mnjxPrintData = new MnjxPrintData();
                                mnjxPrintData.setPrintKey(printKey);
                                mnjxPrintData.setPrintValue(JSONUtil.toJsonStr(map));
                                printDataList.add(mnjxPrintData);
                            }
                        }
                    }
                    if (CollUtil.isNotEmpty(printDataList)) {
                        iMnjxPrintDataService.saveBatch(printDataList);
                    }
                } catch (Exception e) {
                    log.error("值机成功,打印登机牌失败! 原因:" + e.getMessage());
                }
                // 如果只有一个旅客，并且没有输入XBP，则需要返回登机牌数据流
                if (paResultDtoList.size() == 1 && StrUtil.isNotEmpty(aboardStream)) {
                    List<String> optionList = tmpDto.getOptionList();
                    if (CollUtil.isEmpty(optionList) || !optionList.contains("XBP")) {
                        tmpDto.setAboardData(aboardStream);
                    }
                }
                // 打印行李牌
                try {
                    log.info("开始打印行李牌");
                    this.printBag(paResultDtoList);
                } catch (Exception e) {
                    log.error("登机成功,打印行李牌失败! 原因:" + e.getMessage());
                }
            } else {
                log.info("打印机开关已关闭");
            }
        }
    }

    private void judgeGs(List<PaResultDto> paResultDtoList, List<MnjxPsgCki> psgCkiList) throws UnifiedResultException {
        // 是否是直接接收
        long noAdvCount = paResultDtoList.stream()
                .filter(p -> !p.isAdv())
                .count();
        // 保存航班与gs限额关系
        Map<String, Integer> flightGsMap = new HashMap<>(1024);
        // 保存航班与对应航节列表关系
        Map<String, List<MnjxPlanSection>> flightNoPlanSectionMap = new HashMap<>(1024);
        List<String> flightNoList = paResultDtoList.stream()
                .map(PaResultDto::getFlightNo)
                .collect(Collectors.toList());
        for (PaResultDto paResultDto : paResultDtoList) {
            String flightNo = paResultDto.getFlightNo();
            int uresBagCount = 0;
            // 总共需要的GS限额数
            int needGsNum = 0;
            int sbCount = 0;
            int upgDngCount = 0;
            int exstCount = 0;
            Optional<MnjxPsgCki> first = psgCkiList.stream()
                    .filter(p -> paResultDto.getPsgCkiId().equals(p.getPsgCkiId()))
                    .findFirst();
            if (first.isPresent()) {
                MnjxPsgCki psgCki = first.get();
                boolean isUpgDng = false;
                // 预升降舱后再接收，需要1个
                // 候补旅客预升降舱同时输入了座位号，需要1个
                if ((Constant.STR_ONE.equals(psgCki.getPreUpgnOperate()) && !paResultDto.isAdv()) || (Constant.STR_ONE.equals(psgCki.getIsHb()) && paResultDto.isUpgnInputSeat())) {
                    upgDngCount++;
                    needGsNum++;
                    isUpgDng = true;
                }
                // 候补旅客接收，需要1个
                if (Constant.STR_ONE.equals(psgCki.getIsHb()) && noAdvCount > 0) {
                    // 统计候补接收并且带了行李
                    if (StrUtil.isNotEmpty(paResultDto.getBagNumber())) {
                        uresBagCount++;
                    }
                    // 统计该旅客的值机状态为SB数量
                    if (Constant.SB.equals(psgCki.getCkiStatus())) {
                        // SB状态的非CAP旅客接收才需要GS限额
                        if (!Constant.STR_ONE.equals(psgCki.getCap())) {
                            sbCount++;
                        } else {
                            needGsNum--;
                        }
                    }
                    if (!isUpgDng) {
                        needGsNum++;
                    }
                }
                // 额外占座接收，需要2个（拉下旅客且原有EXST没有被删掉重新接收不需要）
                // 额外占座同时是候补旅客或预升降舱，需要3个
                if (StrUtil.isNotEmpty(paResultDto.getExstType()) && paResultDto.getMnjxPsgCkiOptionList().stream().noneMatch(p -> "EXST".equals(p.getOptionType()))) {
                    needGsNum += 2;
                    exstCount++;
                }
                flightNoPlanSectionMap.put(flightNo, paResultDto.getPlanSectionList());
            }
            flightGsMap.put(flightNo + "gs", flightGsMap.containsKey(flightNo + "gs") ? flightGsMap.get(flightNo + "gs") + needGsNum : needGsNum);
            flightGsMap.put(flightNo + "sb", flightGsMap.containsKey(flightNo + "sb") ? flightGsMap.get(flightNo + "sb") + sbCount : sbCount);
            flightGsMap.put(flightNo + "upg", flightGsMap.containsKey(flightNo + "upg") ? flightGsMap.get(flightNo + "upg") + upgDngCount : upgDngCount);
            flightGsMap.put(flightNo + "exst", flightGsMap.containsKey(flightNo + "exst") ? flightGsMap.get(flightNo + "exst") + exstCount : exstCount);
            flightGsMap.put(flightNo + "bag", flightGsMap.containsKey(flightNo + "bag") ? flightGsMap.get(flightNo + "bag") + uresBagCount : uresBagCount);
        }
        for (String flightNo : flightNoList) {
            int needGsNum = flightGsMap.get(flightNo + "gs");
            if (needGsNum > 0) {
                int sbCount = flightGsMap.get(flightNo + "sb");
                int upgDngCount = flightGsMap.get(flightNo + "upg");
                int exstCount = flightGsMap.get(flightNo + "exst");
                int uresBagCount = flightGsMap.get(flightNo + "bag");
                for (MnjxPlanSection mnjxPlanSection : flightNoPlanSectionMap.get(flightNo)) {
                    boolean hasCabinClass = false;
                    String goshowLimit = mnjxPlanSection.getGoshowLimit();
                    if (StrUtil.isEmpty(goshowLimit)) {
                        if (uresBagCount > 0) {
                            throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                        } else {
                            if (sbCount > 0 || upgDngCount > 0 || exstCount > 0) {
                                throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                            }
                        }
                    } else {
                        String[] split = goshowLimit.split(StrUtil.SLASH);
                        for (String s : split) {
                            String gsNum = s.substring(1);
                            String gsCabinClass = s.substring(0, 1);
                            String psgCabinClass = paResultDtoList.get(0).getCabinClass();
                            if (StrUtil.isNotEmpty(paResultDtoList.get(0).getGType())) {
                                psgCabinClass = paResultDtoList.get(0).getGCabinClass();
                            }
                            if (gsCabinClass.equals(psgCabinClass)) {
                                hasCabinClass = true;
                                if (gsNum.startsWith(StrUtil.DASHED) || Integer.parseInt(gsNum) == 0 || Integer.parseInt(gsNum) < needGsNum) {
                                    if (uresBagCount > 0) {
                                        throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                                    } else {
                                        if (sbCount > 0 || upgDngCount > 0 || exstCount > 0) {
                                            throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!hasCabinClass) {
                        if (uresBagCount > 0) {
                            throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                        } else {
                            if (sbCount > 0 || upgDngCount > 0 || exstCount > 0) {
                                throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void printBag(List<PaResultDto> paResultDtoList) throws UnifiedResultException {
        for (PaResultDto paResultDto : paResultDtoList) {
            if ("XBT".equals(paResultDto.getXbt())) {
                continue;
            }
            String bag = paResultDto.getBagNumber();
            if (StrUtil.isNotEmpty(bag)) {
                List<String> bagNoList = paResultDto.getBagNoList();
                if (CollUtil.isNotEmpty(bagNoList)) {
                    StringBuilder bagNoBuffer = new StringBuilder();
                    for (int i = 0; i < bagNoList.size(); i++) {
                        String bagTagNo = bagNoList.get(i);
                        String bagNo = NumberUtil.equals(StrUtil.length(bagTagNo), 10) ? StrUtil.subSuf(bagTagNo, 4) : bagTagNo;
                        bagNoBuffer.append(bagNo);
                        if (i < bagNoList.size() - 1) {
                            bagNoBuffer.append(StrUtil.SLASH);
                        }
                    }
                    if (StrUtil.startWithAny(paResultDto.getCmd(), "HBPA", "HBPU")) {
                        iBagService.dealItBussiness(StrUtil.format("BAG:1,R{}", bagNoBuffer.toString()));
                    } else {
                        iBagService.dealItBussiness(StrUtil.format("BAG:{},R{}", paResultDto.getInputIndex(), bagNoBuffer.toString()));
                    }
                }
            }
        }
    }

    private String printBoard(List<PaResultDto> paResultDtoList) throws UnifiedResultException {
        String aboardDataStream = "";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < paResultDtoList.size(); i++) {
            PaResultDto paResultDto = paResultDtoList.get(i);
            if ("XBP".equals(paResultDto.getXbp())) {
                continue;
            }
            String aboardNo = paResultDto.getBnNo();
            if (!aboardNo.matches("BN\\d+")) {
                aboardNo = StrUtil.format("BN{}", aboardNo);
            }
            sb.append(aboardNo);
            sb.append(",R");
            if (i < paResultDtoList.size() - 1) {
                sb.append(";");
            }
        }
        PaResultDto paResultDto = paResultDtoList.get(0);
        String fltNo = paResultDto.getFlightNo();
        String fltDate = paResultDto.getFlightDate();
        String sellCabin = paResultDto.getSellCabin();
        String offCity = paResultDto.getOrgAirport();
        //BC:3U8890/15SEP21CCTU,BN002,R
        String printCmd = StrUtil.format("BC:{}/{}{}{},{}", fltNo, fltDate.contains(StrUtil.DASHED) ? DateUtils.ymd2Com(fltDate) : fltDate, sellCabin, offCity, sb.toString());
        log.info("登机调用BC打印指令：{}", printCmd);
        // 走打登机牌程序
        if (paResultDtoList.size() == 1) {
            aboardDataStream = iBcService.dealItBussiness(printCmd);
        } else {
            iBcService.dealItBussiness(printCmd);
        }
        try {
            log.info("两个打印之间我让它睡眠0.1秒，太快了");
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return aboardDataStream;
    }

    private List<MnjxLuggage> insertLuggages(MnjxPsgCki psgCki, int luggageNumber, int luggageWeight, String dst, PaResultDto paResultDto) throws UnifiedResultException {
        List<String> bagSegNoList = paResultDto.getBagSegNoList();
        String pnrNmId = psgCki.getPnrNmId();
        List<MnjxLuggage> luggageList = new ArrayList<>();
        String luggageGroupId = null;
        if (luggageNumber > 1) {
            luggageGroupId = IdUtil.getSnowflake(1, 1).nextIdStr();
        }
        int luggageNo = 0;
        // 只查系统行李
        List<MnjxLuggage> allLuggageList = iMnjxLuggageService.lambdaQuery()
                .in(MnjxLuggage::getLuggageType, Constant.STR_ONE, Constant.STR_THREE)
                .list();
        OptionalInt max = allLuggageList.stream()
                .mapToInt(l -> Integer.parseInt(l.getLuggageNo()))
                .distinct()
                .max();
        if (max.isPresent()) {
            luggageNo = max.getAsInt();
        }
        for (int i = 0; i < luggageNumber; i++) {
            luggageNo++;
            for (String bagSegNo : bagSegNoList) {
                MnjxLuggage mnjxLuggage = new MnjxLuggage();
                mnjxLuggage.setLuggageId(IdUtil.getSnowflake(1, 1).nextIdStr());
                mnjxLuggage.setPnrNmId(pnrNmId);
                mnjxLuggage.setLuggageGroupId(luggageGroupId);
                mnjxLuggage.setLuggageNo(StrUtil.fill(StrUtil.toString(luggageNo), '0', 10, true));
                mnjxLuggage.setBagSegNo(bagSegNo);
                mnjxLuggage.setDst(dst);
                mnjxLuggage.setLuggageWeight(luggageWeight);
                mnjxLuggage.setDeliverTime(DateUtil.now());
                mnjxLuggage.setDeliverResult(Constant.STR_ONE);
                mnjxLuggage.setLuggageType(Constant.STR_ONE);
                if (paResultDto.isAvih()) {
                    mnjxLuggage.setLuggageType(Constant.STR_THREE);
                }
                luggageList.add(mnjxLuggage);
            }
        }
        // 还原保存
        iAspectCkiService.insertLuggageList(luggageList, pnrNmId);
        // 保存行李数据
//        iMnjxLuggageService.saveBatch(luggageList);
        return luggageList;
    }

    /**
     * Title: getNextAboardNo
     * Description: 获取下一个登机号
     *
     * @param mnjxPnrSegs mnjxPnrSegs
     * @return 获取下一个登机号
     * <AUTHOR>
     */
    @Override
    public Integer getNextAboardNo(List<MnjxPnrSeg> mnjxPnrSegs) {
        String aboardNo = "000";
        if (CollUtil.isEmpty(mnjxPnrSegs)) {
            return 1;
        }
        List<String> pnrIdList = mnjxPnrSegs.stream()
                .map(MnjxPnrSeg::getPnrId)
                .collect(Collectors.toList());
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .in(MnjxPnrNm::getPnrId, pnrIdList)
                .list();
        List<String> nmIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxPsgCki> psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                .in(MnjxPsgCki::getPnrNmId, nmIdList)
                .list();
        for (MnjxPnrSeg pnrSeg : mnjxPnrSegs) {
            // 如果是多航段的，psgCki一个nmId会有多条数据，segNo不同，只对上次结果集的航段部分进行接收
            List<MnjxPsgCki> collect = psgCkiList.stream()
                    .filter(p -> Integer.parseInt(p.getPnrSegNo()) == pnrSeg.getPnrSegNo())
                    .collect(Collectors.toList());
            List<String> aboardNoList = collect.stream()
                    .map(MnjxPsgCki::getAboardNo)
                    .filter(StrUtil::isNotEmpty)
                    .sorted()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(aboardNoList)) {
                String tmpAboardNo = aboardNoList.get(aboardNoList.size() - 1);
                if (Integer.parseInt(tmpAboardNo) > Integer.parseInt(aboardNo)) {
                    aboardNo = tmpAboardNo;
                }
            }
        }
        return Integer.parseInt(aboardNo) + 1;
    }

    /**
     * Title: handleOption
     * Description: 处理选项数据
     *
     * @param paResultDto            paResultDto
     * @param mnjxPsgCkis            mnjxPsgCkis
     * @param existRandomSeatListMap existRandomSeatListMap
     * @param paResultDtos           paResultDtos
     * <AUTHOR>
     */
    private void handleOption(PaResultDto paResultDto, List<MnjxPsgCki> mnjxPsgCkis, Map<String, List<String>> existRandomSeatListMap, List<PaResultDto> paResultDtos) throws UnifiedResultException {
        // 构建需要从数据库获取的数据
        this.constructDbData(paResultDto, mnjxPsgCkis);
        // 筛选当前航班号的旅客
        List<PaResultDto> thisFlightNoPaResultDtos = paResultDtos.stream()
                .filter(p -> paResultDto.getFlightNo().equals(p.getFlightNo()))
                .collect(Collectors.toList());
        // 当前航班号总旅客数，设置座位时使用
        int psgNum = thisFlightNoPaResultDtos.size();
        boolean hasWc = false;
        // 需要添加SSR信息时，存储的SSR类型和SSR自由文本值
        Map<String, String> ssrTypeFreeTextMap = new HashMap<>(1024);
        List<MnjxPlanSection> planSections = paResultDto.getPlanSectionList();
        // 获取所有的计划航节ID
        List<String> allPlanSectionIdList = planSections.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        MnjxPsgCki psgCki = paResultDto.getMnjxPsgCki();
        // 查询当前旅客舱等对应的所有开舱数据
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, allPlanSectionIdList)
                .eq(MnjxOpenCabin::getCabinClass, psgCki.getCabinClass())
                .list();
        paResultDto.setCabinClassOpenCabinList(openCabinList);
        MnjxPsgSeat psgSeat = paResultDto.getMnjxPsgSeat();
        int seatOptionCount = 0;
        List<String> optionList = paResultDto.getOptionList();
        if (CollUtil.isNotEmpty(optionList)) {
            // 不允许同时输入行李和AVIH行李
            if (optionList.stream().anyMatch(l -> ReUtil.isMatch(BAG_PATTERN, l)) && optionList.stream().anyMatch(l -> ReUtil.isMatch(AVIH_PATTERN, l))) {
                throw new UnifiedResultException(Constant.ITEM_CONFLICT);
            }
            boolean hasUpgDng = optionList.stream().anyMatch(s -> ReUtil.isMatch(UPG_DNG_PATTERN, s));
            for (int i = 0; i < optionList.size(); i++) {
                String option = optionList.get(i);
                switch (option) {
                    case "BLND":
                    case "DEAF":
                    case "DEPA":
                    case "DEPU":
                    case "INAD":
                    case "MEDA":
                        ssrTypeFreeTextMap.put(option, null);
                        break;
                    case "STCR":
                        ssrTypeFreeTextMap.put(option, null);
                        paResultDto.setStcr("STCR");
                        break;
                    case "JMP":
                        ssrTypeFreeTextMap.put(option, null);
                        paResultDto.setJmp("JMP");
                        break;
                    case "GRP":

                        break;
                    case "XBP":
                        // 不打印登机牌
                        paResultDto.setXbp("XBP");
                        break;
                    case "XBT":
                        // 不打印行李牌
                        paResultDto.setXbt("XBT");
                        break;
                    default:
                        if (ReUtil.isMatch(PSG_TYPE_PATTERN, option)) {
                            this.parsePsgType(option, optionList, i, paResultDto, ssrTypeFreeTextMap);
                        } else if (ReUtil.isMatch(SPML_PATTERN, option)) {
                            this.parseSpml(option, ssrTypeFreeTextMap, paResultDto.getPnrNmId(), paResultDto.getMnjxPnrSeg().getPnrSegNo());
                        } else if (ReUtil.isMatch(WC_PATTERN, option)) {
                            if (hasWc) {
                                throw new UnifiedResultException(Constant.DUP_ID);
                            }
                            hasWc = true;

                            // 原来存在WCHC WCHR WCHS时不允许直接修改为WCBD WCBW WCMO WCOB
                            if (StrUtil.equalsAny(option, "WCBD", "WCBW", "WCMP", "WCOB") && paResultDto.getSsrList().stream().anyMatch(s -> StrUtil.equalsAny(s.getSsrType(), "WCHC", "WCHR", "WCHS"))) {
                                throw new UnifiedResultException(Constant.DUP_WHEELCHAIR);
                            }
                            // 原来存在WCBD WCBW WCMP WCOB时不允许直接修改为WCHC WCHR WCHS
                            if (StrUtil.equalsAny(option, "WCHC", "WCHR", "WCHS") && paResultDto.getSsrList().stream().anyMatch(s -> StrUtil.equalsAny(s.getSsrType(), "WCBD", "WCBW", "WCMP", "WCOB"))) {
                                throw new UnifiedResultException(Constant.DUP_WHEELCHAIR);
                            }
                            ssrTypeFreeTextMap.put(option, null);
                        } else if (ReUtil.isMatch(SEAT_PATTERN, option)) {
                            seatOptionCount++;
                            this.parseSeat(option, openCabinList, paResultDto, planSections.size(), hasUpgDng, existRandomSeatListMap, psgNum, thisFlightNoPaResultDtos);
                        } else if (ReUtil.isMatch(SNR_PATTERN, option)) {
                            seatOptionCount++;
                            this.parseSnrSeat(option, openCabinList, paResultDto, planSections.size(), existRandomSeatListMap);
                        } else if (ReUtil.isMatch(SEX_PATTERN, option)) {
                            this.parseSex(option, paResultDto);
                        } else if (ReUtil.isMatch(BAG_PATTERN, option)) {
                            this.parseBag(option, paResultDto);
                        } else if (ReUtil.isMatch(AVIH_PATTERN, option)) {
                            this.parseAvih(option, paResultDto);
                        } else if (ReUtil.isMatch(BAG_DST, option)) {
                            this.parseBagDst(option, paResultDto);
                        } else if (option.startsWith("PSM")) {
                            ssrTypeFreeTextMap.put("PSM", option.substring(3));
                            String[] optionSplit = option.split(" ");
                            for (String s : optionSplit) {
                                if ("/VIP".equals(s)) {
                                    paResultDto.setVip(true);
                                    break;
                                }
                            }
                        } else if (option.startsWith("MSG")) {
                            ssrTypeFreeTextMap.put("MSG", option.substring(3));
                            paResultDto.setMsg(option.substring(3));
                        } else if (option.startsWith("PIL")) {
                            ssrTypeFreeTextMap.put("PIL", option.substring(3));
                            paResultDto.setPil(option.substring(3));
                        } else if (option.startsWith("CTC/")) {
                            paResultDto.setCtc(option.substring(4));
                        } else if (ReUtil.isMatch(PETC_PATTERN, option)) {
                            this.parsePetc(option, paResultDto, ssrTypeFreeTextMap);
                        } else if (option.startsWith("FOID/")) {
                            if (!paResultDto.isAdv()) {
                                throw new UnifiedResultException(Constant.ELECTRONIC_TICKETING_UPDATE_NOT_ALLOWED);
                            }
                            paResultDto.setFoidParam(option);
                        } else if (ReUtil.isMatch(EXCESS_LUGGAGE_PATTERN, option)) {
                            this.parseExcessLuggage(option, paResultDto);
                        } else if (ReUtil.isMatch(UPG_DNG_PATTERN, option)) {
                            this.parseUpgDng(option, paResultDto, allPlanSectionIdList);
                        } else if (option.startsWith("CKIN/")) {
                            paResultDto.setCkin(option.split(StrUtil.SLASH)[1]);
                        } else if (ReUtil.isMatch(BSCT_PATTERN, option)) {
                            this.parseBsct(option, paResultDto, ssrTypeFreeTextMap);
                        } else if (ReUtil.isMatch(EXST_PATTER, option)) {
                            this.parseExst(option, paResultDto);
                        } else if (ReUtil.isMatch(ADSR_PATTER, option)) {
                            this.parseAdsr(option, paResultDto, ssrTypeFreeTextMap);
                        } else if (option.startsWith("FF")) {
                            this.parseFf(option, paResultDto, ssrTypeFreeTextMap);
                        } else {
                            throw new UnifiedResultException(Constant.ITEM);
                        }
                }
            }
        }
        if (seatOptionCount > 0 && StrUtil.isNotEmpty(paResultDto.getGType())) {
            paResultDto.setUpgnInputSeat(true);
        }
        paResultDto.setSsrTypeFreeTextMap(ssrTypeFreeTextMap);
        // HBPA不限制在有婴儿的时候必须输入INF选项
        if (!paResultDto.isHasInf() && !paResultDto.isHbpa()) {
            // 该旅客票面含有INF标识，而PA选择项中未输入INF相关信息
            if (ObjectUtil.isNotEmpty(paResultDto.getMnjxNmXn())) {
                throw new UnifiedResultException(Constant.INF_COUNT_CONFLICT);
            }
        }
        // 接收座位参数不能同时出现SNR 和 R，或不能同时输入多次
        if ((paResultDto.isSnr() && paResultDto.isNormalSeat()) || seatOptionCount > 1) {
            throw new UnifiedResultException(Constant.DUP_IS_FOUND);
        }
        // 正式接收旅客时，没有输入座位号，且之前没有预留座位时，随机分配一个可用座位，设置状态为 "."
        if (!paResultDto.isCap() && StrUtil.isEmpty(paResultDto.getSeatNo()) && StrUtil.isEmpty(paResultDto.getJmp()) && StrUtil.isEmpty(paResultDto.getStcr()) && !paResultDto.isAdv()) {
            // 之前没有预留过座位号
            // 不是NACC的候补旅客
            // 非预升降舱
            if (StrUtil.isEmpty(psgSeat.getPsgSeat())
//                    && !(StrUtil.equalsAny(psgCki.getCkiStatus(), Constant.NACC, Constant.DL) && (StrUtil.isNotEmpty(psgCki.getUres()) || StrUtil.isNotEmpty(psgCki.getNrec())))
                    && !(StrUtil.isNotEmpty(paResultDto.getGType()) && seatOptionCount == 0)) {
                this.getRandomAllowSeat(openCabinList, planSections.size(), paResultDto, existRandomSeatListMap, psgNum);
                seatOptionCount++;
            }
        }
        // 如果旅客是因为座位不够接收成CAP候补且之前加了额外占座，用HBJC接收时应分配对应的额外占座座位
        MnjxPsgCkiOption exstOption = iMnjxPsgCkiOptionService.lambdaQuery()
                .eq(MnjxPsgCkiOption::getPsgCkiId, psgCki.getPsgCkiId())
                .in(MnjxPsgCkiOption::getOptionType, "EXST", "CBBG", "COUR", "DIPL")
                .one();
        if (ObjectUtil.isNotEmpty(exstOption) && StrUtil.isEmpty(psgSeat.getSeatExst()) && StrUtil.isEmpty(paResultDto.getExstSeat())) {
            paResultDto.setHaveExst(true);
            this.getAdsrOrExstSeatNo(paResultDto);
            paResultDto.setExstType(exstOption.getOptionType());
            paResultDto.setExstWeight(exstOption.getOptionValue());
        }
        // 同时输入了座位号和STCR或JMP，报错
        if ((StrUtil.isNotEmpty(paResultDto.getSeatNo()) && (StrUtil.isNotEmpty(paResultDto.getJmp()) || StrUtil.isNotEmpty(paResultDto.getStcr()))) || (StrUtil.isNotEmpty(paResultDto.getJmp()) && StrUtil.isNotEmpty(paResultDto.getStcr()))) {
            throw new UnifiedResultException(Constant.SEATING_CONFLICT);
        }
        // 升降舱座位输入判断，不能是SNR指定座位
        if (StrUtil.isNotEmpty(paResultDto.getGType()) && (seatOptionCount > 1 || paResultDto.isSnr())) {
            throw new UnifiedResultException(Constant.INVALID_UPG_DNG);
        }
        // 验证O航段问题
        if (CollUtil.isNotEmpty(paResultDto.getOList())) {
            this.validateInterlineResults(paResultDto);
        }
        // 如果旅客被拉下后重新接收，本身有EXST，重新接收必须输入EXST
        if (paResultDto.getMnjxPsgCkiOptionList().stream().anyMatch(p -> "EXST".equals(p.getOptionType())) && optionList.stream().noneMatch(o -> o.startsWith("EXST"))) {
            throw new UnifiedResultException(Constant.CHECK_EXST_WEIGHT);
        }
    }

    /**
     * Title: parseFf
     * Description: 解析常客参数
     *
     * @param option
     * @param paResultDto
     * @param ssrTypeFreeTextMap
     * @return
     * <AUTHOR>
     * @date 2023/3/21 14:57
     */
    private void parseFf(String option, PaResultDto paResultDto, Map<String, String> ssrTypeFreeTextMap) throws UnifiedResultException {
        if ("FF".equals(option)) {
            throw new UnifiedResultException(Constant.PROFILE_NOT_FOUND);
        }
        if (ReUtil.isMatch(FF_PATTERN, option)) {
            List<String> allGroups = ReUtil.getAllGroups(FF_PATTERN, option);
            String ffAirlineCode = allGroups.get(1);
            if (ffAirlineCode.length() != 2) {
                throw new UnifiedResultException(Constant.FREQUENT_FLYER_PRIORITY);
            }
            MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                    .eq(MnjxAirline::getAirlineCode, ffAirlineCode)
                    .one();
            if (ObjectUtil.isEmpty(airline)) {
                throw new UnifiedResultException(Constant.FREQUENT_FLYER_PRIORITY);
            }
            if (!ffAirlineCode.equals(paResultDto.getAirlineCode())) {
                throw new UnifiedResultException(Constant.CHECK_FREQUENT_FLYER_ID);
            }
            if (StrUtils.isNotEmpty(allGroups.get(4)) && !StrUtils.equalsAny(allGroups.get(4), Constant.C, Constant.V)) {
                throw new UnifiedResultException(Constant.FFP_CODE_ERROR);
            }
            String ffLevel = allGroups.get(4);
            paResultDto.setFf("FF");
            paResultDto.setFfAirlineCode(ffAirlineCode);
            String ffNo = allGroups.get(2);
            if (!ffNo.matches("\\d{6,12}")) {
                throw new UnifiedResultException(Constant.NUMBER_ERROR);
            }
            paResultDto.setFfNo(ffNo);
            paResultDto.setFfLevel(StrUtils.isEmpty(ffLevel) ? Constant.C : ffLevel);
            ssrTypeFreeTextMap.put(Constant.SSR_TYPE_FQTV, null);
            // 查询该输入的卡号是否存在
            MnjxFrequenter dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                    .eq(MnjxFrequenter::getAirlineCode, ffAirlineCode)
                    .eq(MnjxFrequenter::getFrequenterCard, ffNo)
                    .one();
            if (ObjectUtil.isNotEmpty(dbFrequenter)) {
                if (StrUtils.isNotEmpty(ffLevel) && !ffLevel.equals(dbFrequenter.getFrequenterLevel())) {
                    throw new UnifiedResultException(Constant.FFP_CODE_ERROR);
                }
                paResultDto.setDbFrequenter(dbFrequenter);
                paResultDto.setFfLevel(dbFrequenter.getFrequenterLevel());
            } else {
                MnjxNmSsr foidSsr = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, paResultDto.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_FOID)
                        .one();
                String foidInfo = foidSsr.getInputValue().split(" ")[4].split(StrUtil.SLASH)[0];
                String foidType = foidInfo.substring(0, 2);
                String foidNo = foidInfo.substring(2);
                dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                        .eq(MnjxFrequenter::getAirlineCode, ffAirlineCode)
                        .eq(MnjxFrequenter::getFrequenterCertificateType, foidType)
                        .eq(MnjxFrequenter::getFrequenterCertificateNo, foidNo)
                        .one();
                if (ObjectUtil.isNotEmpty(dbFrequenter)) {
                    if (StrUtils.isNotEmpty(ffLevel) && !ffLevel.equals(dbFrequenter.getFrequenterLevel())) {
                        throw new UnifiedResultException(Constant.FFP_CODE_ERROR);
                    }
                    if (!dbFrequenter.getFrequenterCard().equals(ffNo)) {
                        throw new UnifiedResultException(StrUtils.format("PASSENGER ALREADY HAVE PROFILE NUMBER {}", dbFrequenter.getFrequenterCard()));
                    }
                    paResultDto.setDbFrequenter(dbFrequenter);
                    paResultDto.setFfLevel(dbFrequenter.getFrequenterLevel());
                }
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }

    /**
     * Title: parseBagDst
     * Description: 解析行李目的地
     *
     * @param option      option
     * @param paResultDto paResultDto
     * <AUTHOR>
     * @date 2022/12/8 16:53
     */
    private void parseBagDst(String option, PaResultDto paResultDto) throws UnifiedResultException {
        String bagDst = option.length() == 5 ? option.substring(2) : option;
        // 如果到达站不是当前航站，判断是否有出港联程，出港联程的到达站是否有和行李目的地相等的
        if (!bagDst.equals(paResultDto.getDstAirport())) {
            List<String> interlineResults = paResultDto.getInterlineResults();
            iPrService.getInterline(paResultDto.getPnr().getPnrId(), paResultDto.getMnjxPsgCki().getPnrSegNo(), paResultDto.getPnrNmId(), paResultDto.getMnjxPnrSeg().getOrg(), paResultDto.getMnjxPnrSeg().getDst(), interlineResults, null);
            if (CollUtil.isEmpty(interlineResults) || interlineResults.stream().noneMatch(r -> "O".equals(r.trim().substring(0, 1)) && bagDst.equals(r.trim().substring(r.trim().length() - Constant.THREE)))) {
                throw new UnifiedResultException(Constant.BAG_TAG_DESTINATION);
            }
            if (CollUtil.isNotEmpty(paResultDto.getOList())) {
                for (int i = 0; i < paResultDto.getOList().size(); i++) {
                    String io = interlineResults.get(i);
                    if (Constant.O.equals(io.trim().substring(0, 1)) && bagDst.equals(io.trim().substring(io.trim().length() - 3))) {
                        break;
                    }
                }
            } else {
                throw new UnifiedResultException(Constant.BAG_TAG_DESTINATION);
            }
        }
        paResultDto.setBagDst(bagDst);
    }

    /**
     * Title: parsePetc
     * Description: 解析客舱宠物
     *
     * @param option             option
     * @param paResultDto        paResultDto
     * @param ssrTypeFreeTextMap ssrTypeFreeTextMap
     * <AUTHOR>
     * @date 2022/12/7 16:17
     */
    private void parsePetc(String option, PaResultDto paResultDto, Map<String, String> ssrTypeFreeTextMap) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(PETC_PATTERN, option);
        String petcNum = allGroups.get(1);
        if (Integer.parseInt(petcNum) > Constant.TWO) {
            throw new UnifiedResultException(Constant.ONLY_TWO_PETC);
        }
        paResultDto.setPetcNum(petcNum);
        paResultDto.setPetcWeight(allGroups.get(2));
        ssrTypeFreeTextMap.put("PETC", null);
    }

    /**
     * Title: parseAdsr
     * Description: 解析邻近占座
     *
     * @param option             option
     * @param paResultDto        paResultDto
     * @param ssrTypeFreeTextMap ssrTypeFreeTextMap
     * <AUTHOR>
     * @date 2022/12/7 16:16
     */
    private void parseAdsr(String option, PaResultDto paResultDto, Map<String, String> ssrTypeFreeTextMap) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(ADSR_PATTER, option);
        String adsrWeight = allGroups.get(1);
        if (StrUtil.isNotEmpty(adsrWeight)) {
            if (Integer.parseInt(adsrWeight) > Constant.FIVE_HUNDRED_AND_TWELVE) {
                throw new UnifiedResultException(Constant.WEIGHT);
            } else if (Integer.parseInt(adsrWeight) > 0) {
                paResultDto.setAdsrWeight(adsrWeight);
            }
        }
        paResultDto.setAdsr("ADSR");
        ssrTypeFreeTextMap.put("ADSR", "ADSRSEAT");
    }

    /**
     * Title: parseExst
     * Description: 解析额外占座
     *
     * @param option      option
     * @param paResultDto paResultDto
     * <AUTHOR>
     * @date 2022/12/7 16:15
     */
    private void parseExst(String option, PaResultDto paResultDto) throws UnifiedResultException {
        paResultDto.setHaveExst(true);
        List<String> allGroups = ReUtil.getAllGroups(EXST_PATTER, option);
        String exstType = allGroups.get(1);
        String exstWeight = allGroups.get(2);
        paResultDto.setExstType(exstType);
        if (StrUtil.isNotEmpty(exstWeight)) {
            if (Integer.parseInt(exstWeight) > Constant.FIVE_HUNDRED_AND_TWELVE) {
                throw new UnifiedResultException(Constant.WEIGHT);
            }
            if (Integer.parseInt(exstWeight) > 0) {
                paResultDto.setExstWeight(exstWeight);
            }
        }
        if (StrUtil.isEmpty(paResultDto.getMnjxPsgSeat().getSeatExst())) {
            this.getAdsrOrExstSeatNo(paResultDto);
        }
    }

    /**
     * Title: parseBsct
     * Description: 解析摇篮
     *
     * @param option             option
     * @param paResultDto        paResultDto
     * @param ssrTypeFreeTextMap ssrTypeFreeTextMap
     * <AUTHOR>
     * @date 2022/12/7 16:13
     */
    private void parseBsct(String option, PaResultDto paResultDto, Map<String, String> ssrTypeFreeTextMap) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(BSCT_PATTERN, option);
        String bsctNum = allGroups.get(1);
        String bsctWeight = allGroups.get(2);
        if (!Constant.STR_ONE.equals(bsctNum)) {
            throw new UnifiedResultException(Constant.ONLY_ONE_BSCT);
        }
        paResultDto.setBsctNum(bsctNum);
        paResultDto.setBsctWeight(bsctWeight);
        ssrTypeFreeTextMap.put("BSCT", null);
    }

    /**
     * Title: parseUpgDng
     * Description: 解析升降舱
     *
     * @param option         option
     * @param paResultDto    paResultDto
     * @param planSectionIds planSectionIds
     * <AUTHOR>
     * @date 2022/12/7 16:12
     */
    private void parseUpgDng(String option, PaResultDto paResultDto, List<String> planSectionIds) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(UPG_DNG_PATTERN, option);
        String gType = allGroups.get(1);
        String sellCabin = allGroups.get(2);
        if (StrUtil.isEmpty(sellCabin)) {
            throw new UnifiedResultException(Constant.ITEM);
        }
        String voluntary = allGroups.get(3);
        paResultDto.setGType(gType);
        paResultDto.setGSellCabin(sellCabin);
        List<MnjxOpenCabin> allOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIds)
                .list();
        List<MnjxOpenCabin> upgDngOpenCabinList = allOpenCabinList.stream()
                .filter(o -> sellCabin.equals(o.getSellCabin()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(upgDngOpenCabinList)) {
            throw new UnifiedResultException(Constant.CABIN);
        }
        paResultDto.setGCabinClass(upgDngOpenCabinList.get(0).getCabinClass());
        paResultDto.setVoluntary(voluntary);
    }

    /**
     * Title: parseExcess
     * Description: 解析手工行李
     *
     * @param option      option
     * @param paResultDto paResultDto
     * <AUTHOR>
     * @date 2022/12/7 16:10
     */
    private void parseExcessLuggage(String option, PaResultDto paResultDto) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(EXCESS_LUGGAGE_PATTERN, option);
        String airCode = allGroups.get(1);
        String noStart = allGroups.get(2);
        String noEnd = allGroups.get(4);
        String dst = allGroups.get(5);
        // 目的地匹配
        this.parseBagDst(dst, paResultDto);
        // 航司验证
        if (!paResultDto.isTypeO() && !airCode.equalsIgnoreCase(paResultDto.getAirlineCode())) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        // 行李数量匹配与行李号数量匹配
        if (StrUtil.isBlank(paResultDto.getBagNumber())) {
            throw new UnifiedResultException(Constant.BAG);
        }
        if (StrUtil.isNotBlank(noEnd)) {
            String prefix = noStart.substring(0, 3);
            // 行李件数不为1
            int nums = Integer.parseInt(prefix + noEnd) - Integer.parseInt(noStart) + 1;
            if (nums != Integer.parseInt(paResultDto.getBagNumber())) {
                throw new UnifiedResultException(Constant.BAG);
            }
            List<String> luggageNoList = new ArrayList<>();
            for (int j = 0; j < nums; j++) {
                String end = StrUtil.fill(StrUtil.toString(Integer.parseInt(noStart.substring(3)) + j), '0', 3, true);
                luggageNoList.add(prefix + end);
            }
            paResultDto.setLuggageNoList(luggageNoList);
        } else {
            // 行李件数为1
            if (Integer.parseInt(paResultDto.getBagNumber()) != 1) {
                throw new UnifiedResultException(Constant.BAG);
            }
            List<String> luggageNoList = new ArrayList<>();
            luggageNoList.add(noStart);
            paResultDto.setLuggageNoList(luggageNoList);
        }
        if (CollUtil.isNotEmpty(paResultDto.getLuggageNoList())) {
            List<String> checkNos = new ArrayList<>();
            for (String luggageNoStr : paResultDto.getLuggageNoList()) {
                checkNos.add(StrUtil.fill(luggageNoStr, '0', 10, true));
            }
            List<String> nmIds = puMapper.retrieveFlightAllNms(paResultDto.getFlightNo(), paResultDto.getFlightDate());
            List<MnjxLuggage> exitsNos = iMnjxLuggageService.lambdaQuery()
                    .in(MnjxLuggage::getPnrNmId, nmIds)
                    .in(MnjxLuggage::getLuggageNo, checkNos)
                    .eq(MnjxLuggage::getLuggageType, Constant.STR_TWO)
                    .and(l -> l.isNull(MnjxLuggage::getIsDel).or().ne(MnjxLuggage::getIsDel, Constant.DELETE_TYPE))
                    .list();
            if (CollUtil.isNotEmpty(exitsNos)) {
                throw new UnifiedResultException(Constant.BAGTAG_REPEATED);
            }
        }
    }

    /**
     * Title: validateInterlineResults
     * Description: 验证O航段问题
     *
     * @param paResultDto paResultDto
     * <AUTHOR>
     * @date 2022/12/7 10:05
     */
    private void validateInterlineResults(PaResultDto paResultDto) throws UnifiedResultException {
        if (CollUtil.isEmpty(paResultDto.getInterlineResults())) {
            iPrService.getInterline(paResultDto.getPnr().getPnrId(), paResultDto.getMnjxPsgCki().getPnrSegNo(), paResultDto.getPnrNmId(), paResultDto.getMnjxPnrSeg().getOrg(), paResultDto.getMnjxPnrSeg().getDst(), paResultDto.getInterlineResults(), null);
            if (CollUtil.isEmpty(paResultDto.getInterlineResults())) {
                throw new UnifiedResultException(Constant.TRANSFER_FLIGHT_NBR);
            }
        }
        long oCount = paResultDto.getInterlineResults().stream()
                .filter(r -> "O".equals(r.trim().substring(0, 1)))
                .count();
        if (paResultDto.getOList().size() > oCount) {
            throw new UnifiedResultException(Constant.TRANSFER_FLIGHT_NBR);
        }
    }

    /**
     * Title: constructDbData
     * Description: 构建从数据库获取的数据
     *
     * @param paResultDto paResultDto
     * @param mnjxPsgCkis mnjxPsgCkis
     * <AUTHOR>
     * @date 2022/12/7 16:08
     */
    private void constructDbData(PaResultDto paResultDto, List<MnjxPsgCki> mnjxPsgCkis) throws UnifiedResultException {
        String pnrNmId = paResultDto.getPnrNmId();
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrNm.getPnrId())
                .eq(MnjxPnrSeg::getFlightNo, StrUtil.isNotEmpty(paResultDto.getShareFlightNo()) ? paResultDto.getShareFlightNo() : paResultDto.getFlightNo())
                .eq(MnjxPnrSeg::getFlightDate, paResultDto.getFlightDate())
                .eq(MnjxPnrSeg::getOrg, paResultDto.getOrgAirport())
                .eq(MnjxPnrSeg::getDst, paResultDto.getDstAirport())
                .one();
        if (ObjectUtil.isEmpty(pnrSeg)) {
            throw new UnifiedResultException(Constant.CHECK_SEG);
        }
        List<MnjxNmSsr> nmSsrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo().toString())
                .list();
        paResultDto.setSsrList(nmSsrList);

        // 验证旅客客票状态，不能是挂起状态
        List<MnjxPnrNmTn> tnList = new ArrayList<>();
        MnjxPnrNmTn tn = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                .one();
        tnList.add(tn);
        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                .one();
        if (ObjectUtil.isNotEmpty(nmXn)) {
            MnjxPnrNmTn xnNmTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                    .one();
            if (ObjectUtil.isNotEmpty(xnNmTn)) {
                tnList.add(xnNmTn);
            }
        }
        List<String> tnIdList = tnList.stream()
                .filter(t -> StrUtil.isNotEmpty(t.getIssuedTime()))
                .map(MnjxPnrNmTn::getTnId)
                .collect(Collectors.toList());
        // 候补没有票，需要判断
        if (CollUtil.isNotEmpty(tnIdList)) {
            List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                    .list();
            String segId = pnrSeg.getPnrSegId();
            if (nmTicketList.stream().anyMatch(t -> (segId.equals(t.getS1Id()) && Constant.TICKET_STATUS_SUSPENDED.equals(t.getTicketStatus1()))
                    || (StrUtil.isNotEmpty(t.getTicketStatus2()) && segId.equals(t.getS2Id()) && Constant.TICKET_STATUS_SUSPENDED.equals(t.getTicketStatus2())))) {
                throw new UnifiedResultException(Constant.TICKET_SUSPENDED);
            }
        }

        // 获取当前旅客的航节信息
        List<MnjxPlanSection> mnjxPlanSections = this.getMatchPlanSections(paResultDto);
        paResultDto.setPlanSectionList(mnjxPlanSections);
        MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery()
                .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo())
                .one();
        List<MnjxPsgCkiOption> psgCkiOptionList = iMnjxPsgCkiOptionService.lambdaQuery()
                .eq(MnjxPsgCkiOption::getPsgCkiId, psgCki.getPsgCkiId())
                .list();
        MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                .one();
        mnjxPsgCkis.add(psgCki);
        paResultDto.setPnrNm(pnrNm);
        paResultDto.setPnr(pnr);
        paResultDto.setMnjxPnrSeg(pnrSeg);
        paResultDto.setPsgCkiId(psgCki.getPsgCkiId());
        paResultDto.setMnjxPsgCki(psgCki);
        paResultDto.getMnjxPsgCkiOptionList().addAll(psgCkiOptionList);
        paResultDto.setMnjxPsgSeat(psgSeat);
        paResultDto.setCabinClass(psgCki.getCabinClass());
        paResultDto.setSellCabin(psgCki.getSellCabin());
        paResultDto.setMnjxNmXn(nmXn);
    }

    /**
     * Title: parseSex
     * Description: 解析性别
     *
     * @param option      option
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    private void parseSex(String option, PaResultDto paResultDto) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(SEX_PATTERN, option);
        String sex = allGroups.get(1);
        String number = allGroups.get(2);
        if (Integer.parseInt(number) != 1) {
            throw new UnifiedResultException(Constant.GENDER_CONFLICT);
        }
        paResultDto.setSex(sex);
    }

    /**
     * Title: parseBag
     * Description: 解析行李
     *
     * @param option      option
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    private void parseBag(String option, PaResultDto paResultDto) throws UnifiedResultException {
        // 预接收不允许添加行李参数
        if (paResultDto.isAdv()) {
            throw new UnifiedResultException(Constant.WEIGHT_NOT_ALLOWED);
        }
        List<String> allGroups = ReUtil.getAllGroups(BAG_PATTERN, option);
        String bagNumber = allGroups.get(1);
        String bagWeight = allGroups.get(2);
        paResultDto.setBagNumber(bagNumber);
        paResultDto.setBagWeight(bagWeight);
        // 如果是出港部分的对象，只需要设置值，验证在前面当前航段已经验证过了
        if (paResultDto.isTypeO()) {
            return;
        }
        List<MnjxConfigLuggage> mnjxConfigLuggages = iMnjxConfigLuggageService.list();
        MnjxConfigLuggage configLuggage = mnjxConfigLuggages.get(0);
        // 检查件数限制
        if (Integer.parseInt(bagNumber) > configLuggage.getMaxBags()) {
            throw new UnifiedResultException(Constant.OVER_MAX_BAG);
        }
        // 检查重量限制
        if (Integer.parseInt(bagWeight) > (configLuggage.getSingletonBaggesWeight() * Integer.parseInt(bagNumber))) {
            String errorMsg = paResultDto.getCmd().replace(option, StrUtil.format("EBAG{}", option));
            errorMsg = StrUtil.format("\u0010{}\r\n \r\n{}", errorMsg, Constant.OVER_MAX_BAG_WEIGHT);
            throw new UnifiedResultException(errorMsg);
        }
    }

    /**
     * Title: parseAvih
     * Description: 解析AVIH，AVIH不进行件数和重量限制判断<br>
     *
     * @param option
     * @param paResultDto
     * @return void
     * <AUTHOR>
     * @date 2024/4/22 15:23
     */
    private void parseAvih(String option, PaResultDto paResultDto) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(AVIH_PATTERN, option);
        String bagNumber = allGroups.get(1);
        String bagWeight = allGroups.get(2);
        if (StrUtil.isAllEmpty(bagNumber, bagWeight)) {
            throw new UnifiedResultException(Constant.COUNT_REQUIRED);
        }
        if (StrUtil.isEmpty(bagWeight)) {
            throw new UnifiedResultException(Constant.WEIGHT_REQUIRED);
        }
        paResultDto.setBagNumber(bagNumber);
        paResultDto.setBagWeight(bagWeight);
        paResultDto.setAvih(true);
        // 如果是出港部分的对象，只需要设置值，验证在前面当前航段已经验证过了
        if (paResultDto.isTypeO()) {
            return;
        }
        // 检查件数限制
        if (Integer.parseInt(bagNumber) > 84) {
            throw new UnifiedResultException(Constant.MAXIMUM_BAGTAGS_EXCEEDED);
        }
        // 检查重量限制
        if (Integer.parseInt(bagWeight) > 9999) {
            throw new UnifiedResultException(Constant.WEIGHT);
        }
    }

    /**
     * Title: parseSnrSeat
     * Description: 解析SNR 座位
     *
     * @param option         option
     * @param mnjxOpenCabins mnjxOpenCabins
     * @param paResultDto    paResultDto
     * <AUTHOR>
     */
    private void parseSnrSeat(String option, List<MnjxOpenCabin> mnjxOpenCabins, PaResultDto paResultDto, int planSectionSize, Map<String, List<String>> existRandomSeatListMap) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(SNR_PATTERN, option);
        String seatNo = allGroups.get(1);
        paResultDto.setSnr(true);
        this.setCorrectSeats(mnjxOpenCabins, null, seatNo, planSectionSize, paResultDto, existRandomSeatListMap);
        paResultDto.setInputSeatNo(seatNo);
    }

    /**
     * Title: parseSeat
     * Description: 解析座位
     *
     * @param option         option
     * @param mnjxOpenCabins mnjxOpenCabins
     * @param paResultDto    paResultDto
     * <AUTHOR>
     */
    private void parseSeat(String option, List<MnjxOpenCabin> mnjxOpenCabins, PaResultDto paResultDto, int planSectionSize, boolean hasUpgDng, Map<String, List<String>> existRandomSeatListMap, int psgNum, List<PaResultDto> thisFlightNoPaResultDtos) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(SEAT_PATTERN, option);
        String specialSeat = allGroups.get(2);
        paResultDto.setNormalSeat(true);
        if (StrUtil.isNotEmpty(specialSeat)) {
            int specialLength = specialSeat.length();
            // 大于3个报错SEATS
            if (specialLength > Constant.THREE) {
                throw new UnifiedResultException(Constant.SEATS);
            }
            // 特殊座位字符重复，报错SEATS
            String[] split = specialSeat.split("");
            List<String> collect = Arrays.stream(split)
                    .distinct()
                    .collect(Collectors.toList());
            if (collect.size() != specialLength) {
                throw new UnifiedResultException(Constant.SEATS);
            }
        }
        String seatNo = allGroups.get(3);
        String multiSeatNo = allGroups.get(4);
        String inputSeatNo = seatNo;
        // 多个旅客分配座位，座位格式为13AB类似的，进行处理为13A和13B
        if (StrUtil.isNotEmpty(multiSeatNo)) {
            if (psgNum == 1) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            List<String> seatGroup = ReUtil.getAllGroups(MULTI_SEAT_PATTER, multiSeatNo);
            List<String> multiSeatNoList = new ArrayList<>();
            String seatLine = seatGroup.get(5);
            String startStr = seatGroup.get(2);
            String endStr = seatGroup.get(4);
            // 有 - 符号的
            if (seatLine.contains(StrUtil.DASHED)) {
                String startLine = seatGroup.get(6);
                String endLine = seatGroup.get(8);
                int i1 = startLine.charAt(0);
                int i2 = endLine.charAt(0);
                if (i1 > i2) {
                    String tmp = startLine;
                    startLine = endLine;
                    endLine = tmp;
                } else if (i1 == i2) {
                    throw new UnifiedResultException(Constant.FORMAT);
                }
                MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getFlightNo, paResultDto.getFlightNo())
                        .one();
                MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                        .eq(MnjxTcard::getFlightId, flight.getFlightId())
                        .one();
                MnjxCnd cnd = iMnjxCndService.getById(tcard.getCndId());
                List<MnjxSeatModel> modelList = iMnjxSeatModelService.lambdaQuery()
                        .eq(MnjxSeatModel::getCndId, cnd.getCndId())
                        .isNotNull(MnjxSeatModel::getSeatNo)
                        .list();
                List<String> lineList = modelList.stream()
                        .map(m -> m.getSeatNo().substring(m.getSeatNo().length() - 1))
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                List<String> inputLineList = new ArrayList<>();
                for (String s : lineList) {
                    if (s.equals(startLine) || s.equals(endLine)) {
                        inputLineList.add(s);
                    } else if (CollUtil.isNotEmpty(inputLineList) && !inputLineList.contains(endLine)) {
                        inputLineList.add(s);
                    }
                }
                if (StrUtil.isNotEmpty(endStr)) {
                    inputSeatNo = StrUtil.format("{}-{}{}-{}", startStr, endStr, startLine, endLine);
                } else {
                    inputSeatNo = StrUtil.format("{}{}-{}", startStr, startLine, endLine);
                }
                for (String s : inputLineList) {
                    if (StrUtil.isNotEmpty(endStr)) {
                        for (int i = Integer.parseInt(startStr); i <= Integer.parseInt(endStr); i++) {
                            multiSeatNoList.add(StrUtil.format("{}{}", i, s));
                        }
                    } else {
                        multiSeatNoList.add(StrUtil.format("{}{}", startStr, s));
                    }
                }
            } else {
                String[] split = seatLine.split(StrUtil.EMPTY);
                if (split.length > 1) {
                    if (split[0].equals(split[split.length - 1])) {
                        throw new UnifiedResultException(Constant.FORMAT);
                    }
                    inputSeatNo = StrUtil.format("{}{}-{}", seatGroup.get(1), split[0], split[split.length - 1]);
                } else {
                    inputSeatNo = StrUtil.format("{}{}", seatGroup.get(1), split[0]);
                }
                for (String s : split) {
                    if (StrUtil.isNotEmpty(endStr)) {
                        for (int i = Integer.parseInt(startStr); i <= Integer.parseInt(endStr); i++) {
                            multiSeatNoList.add(StrUtil.format("{}{}", i, s));
                        }
                    } else {
                        multiSeatNoList.add(StrUtil.format("{}{}", startStr, s));
                    }
                }
            }
            // 解析输入获取的座位列表分配给该航班的旅客
            for (int i = 0; i < multiSeatNoList.size(); i++) {
                String psgSeatNo = multiSeatNoList.get(i);
                if (i < thisFlightNoPaResultDtos.size()) {
                    thisFlightNoPaResultDtos.get(i).setSeatNo(psgSeatNo);
                    thisFlightNoPaResultDtos.get(i).setInputSeatNo(inputSeatNo);
                    thisFlightNoPaResultDtos.get(i).setNormalSeat(true);
                    if (paResultDto.equals(thisFlightNoPaResultDtos.get(i))) {
                        seatNo = psgSeatNo;
                    }
                }
            }
            // 对旅客循环一次，如果分配的座位数不够，该旅客随机获取可用座位
            for (int i = 0; i < thisFlightNoPaResultDtos.size(); i++) {
                PaResultDto dto = thisFlightNoPaResultDtos.get(i);
                if (i < multiSeatNoList.size()) {
                    String psgSeatNo = multiSeatNoList.get(i);
                    dto.setSeatNo(psgSeatNo);
                    if (dto.equals(paResultDto)) {
                        seatNo = psgSeatNo;
                    }
                } else {
                    if (dto.equals(paResultDto)) {
                        this.getRandomAllowSeat(mnjxOpenCabins, planSectionSize, dto, existRandomSeatListMap, psgNum);
                        seatNo = dto.getSeatNo();
                    }
                }
                dto.setInputSeatNo(inputSeatNo);
                dto.setNormalSeat(true);
            }
        }
        // 如果有升降舱，座位放在后面再处理
        if (hasUpgDng) {
            paResultDto.setInputSeatNo(inputSeatNo);
            paResultDto.setSeatNo(seatNo);
            return;
        }
        this.setCorrectSeats(mnjxOpenCabins, specialSeat, seatNo, planSectionSize, paResultDto, existRandomSeatListMap);
        paResultDto.setInputSeatNo(StrUtil.isEmpty(inputSeatNo) ? specialSeat : inputSeatNo);
    }

    private void setPositionSeat(String specialSeat, List<String> openCabinIdList, PaResultDto paResultDto, List<String> seatNoList) throws UnifiedResultException {
        // 验证是否有冲突座位位置
        if (StrUtils.containsAll(specialSeat, "W", "I")
                || StrUtils.containsAll(specialSeat, "F", "A")
                || StrUtils.containsAll(specialSeat, "L", "R")) {
            throw new UnifiedResultException(Constant.SEATS);
        }
        // 获取符合条件的所有座位，除C外，这些都不可用了才查询C
        List<MnjxSeat> allSeatList = iMnjxSeatService.lambdaQuery()
                .in(MnjxSeat::getSeatStatus, "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S", "C")
                .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                .list();
        // 如果是空的或者座位数不够，旅客处理成候补
        if (CollUtil.isEmpty(allSeatList)) {
            paResultDto.setCap(true);
            return;
        }
        List<MnjxSeat> seatList = allSeatList.stream()
                .filter(s -> StrUtil.equalsAny(s.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S"))
                .collect(Collectors.toList());
        List<MnjxSeat> seatListC = allSeatList.stream()
                .filter(s -> "C".equals(s.getSeatStatus()))
                .collect(Collectors.toList());

        // 默认从左边找座位，有靠右从右边开始找
        boolean isRight = specialSeat.contains("R");
        boolean isLeft = specialSeat.contains("L");
        boolean isForward = specialSeat.contains("F");
        boolean isAfter = specialSeat.contains("A");
        // 有靠窗或靠过道，同时输入了靠左或靠右，只需要在靠窗靠过道的条件中处理
        boolean isWindow = specialSeat.contains("W");
        boolean isAisle = specialSeat.contains("I");

        List<MnjxSeat> availableSeatList = new ArrayList<>();
        // 正常可用座位
        if (CollUtil.isNotEmpty(seatList)) {
            int maxX = seatList.stream()
                    .mapToInt(MnjxSeat::getSeatX)
                    .max()
                    .getAsInt();
            int minX = seatList.stream()
                    .mapToInt(MnjxSeat::getSeatX)
                    .min()
                    .getAsInt();
            int maxY = seatList.stream()
                    .mapToInt(MnjxSeat::getSeatY)
                    .max()
                    .getAsInt();
            int minY = seatList.stream()
                    .mapToInt(MnjxSeat::getSeatY)
                    .min()
                    .getAsInt();
            if (isWindow) {
                // 靠窗
                this.setPositionWindow(specialSeat, isRight, isLeft, isForward, isAfter, minY, maxY, minX, maxX, seatList, availableSeatList);
            } else if (isAisle) {
                // 靠过道
                this.setPositionAisle(specialSeat, isRight, isLeft, isForward, isAfter, minY, maxY, minX, maxX, seatList, availableSeatList);
            } else {
                this.setPositionOther(specialSeat, minY, maxY, minX, maxX, seatList, availableSeatList);
            }
        }
        // 最后可利用座位
        else if (CollUtil.isNotEmpty(seatListC)) {
            int maxX = seatListC.stream()
                    .mapToInt(MnjxSeat::getSeatX)
                    .max()
                    .getAsInt();
            int minX = seatListC.stream()
                    .mapToInt(MnjxSeat::getSeatX)
                    .min()
                    .getAsInt();
            int maxY = seatListC.stream()
                    .mapToInt(MnjxSeat::getSeatY)
                    .max()
                    .getAsInt();
            int minY = seatListC.stream()
                    .mapToInt(MnjxSeat::getSeatY)
                    .min()
                    .getAsInt();
            if (isWindow) {
                // 靠窗
                this.setPositionWindow(specialSeat, isRight, isLeft, isForward, isAfter, minY, maxY, minX, maxX, seatListC, availableSeatList);
            } else if (isAisle) {
                // 靠过道
                this.setPositionAisle(specialSeat, isRight, isLeft, isForward, isAfter, minY, maxY, minX, maxX, seatListC, availableSeatList);
            } else {
                this.setPositionOther(specialSeat, minY, maxY, minX, maxX, seatListC, availableSeatList);
            }
        }

        if (CollUtil.isEmpty(availableSeatList)) {
            paResultDto.setCap(true);
            return;
        }
        seatNoList.addAll(availableSeatList.stream()
                .map(MnjxSeat::getSeatNo)
                .collect(Collectors.toList()));
    }

    private void setPositionOther(String specialSeat, int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        // 靠前靠右
        if (StrUtils.containsAll(specialSeat, "R", "F")) {
            this.setPositionRf(minY, maxY, minX, maxX, seatList, availableSeatList);
        }
        // 靠右靠后
        else if (StrUtils.containsAll(specialSeat, "R", "A")) {
            this.setPositionRa(minY, maxY, minX, maxX, seatList, availableSeatList);
        }
        // 靠左靠前
        else if (StrUtils.containsAll(specialSeat, "L", "F")) {
            this.setPositionLf(minY, maxY, minX, maxX, seatList, availableSeatList);
        }
        // 靠左靠后
        else if (StrUtils.containsAll(specialSeat, "L", "A")) {
            this.setPositionLa(minY, maxY, minX, maxX, seatList, availableSeatList);
        } else {
            switch (specialSeat) {
                case "A":
                    // 靠后
                    this.setPositionA(minY, maxY, minX, maxX, seatList, availableSeatList);
                    break;
                case "L":
                    // 靠左
                    this.setPositionL(minY, maxY, minX, maxX, seatList, availableSeatList);
                    break;
                case "R":
                    // 靠右
                    this.setPositionR(minY, maxY, minX, maxX, seatList, availableSeatList);
                    break;
                default:
                    // 输入F或者默认靠前
                    this.setPositionF(minY, maxY, minX, maxX, seatList, availableSeatList);
                    break;
            }
        }
    }

    private void setPositionWindow(String specialSeat, boolean isRight, boolean isLeft, boolean isForward, boolean isAfter, int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> windowSeatList = seatList.stream()
                .filter(s -> Constant.STR_ONE.equals(s.getIsWindow()))
                .collect(Collectors.toList());
        // 靠窗的都被占用，按正常流程走
        if (CollUtil.isEmpty(windowSeatList)) {
            String newSpecialSeat = specialSeat.replace("W", "");
            this.setPositionOther(newSpecialSeat, minY, maxY, minX, maxX, seatList, availableSeatList);
        } else {
            if (isRight && isForward) {
                this.setPositionRf(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isRight && isAfter) {
                this.setPositionRa(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isLeft && isForward) {
                this.setPositionLf(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isLeft && isAfter) {
                this.setPositionLa(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isRight) {
                this.setPositionR(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isLeft) {
                this.setPositionL(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else if (isAfter) {
                this.setPositionA(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            } else {
                // 默认取靠前的
                this.setPositionF(minY, maxY, minX, maxX, windowSeatList, availableSeatList);
            }
        }
    }

    private void setPositionAisle(String specialSeat, boolean isRight, boolean isLeft, boolean isForward, boolean isAfter, int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> aisleSeatList = seatList.stream()
                .filter(s -> Constant.STR_ONE.equals(s.getIsAisle()))
                .collect(Collectors.toList());
        // 靠过道的都被占用，按正常流程走
        if (CollUtil.isEmpty(aisleSeatList)) {
            String newSpecialSeat = specialSeat.replace("I", "");
            this.setPositionOther(newSpecialSeat, minY, maxY, minX, maxX, seatList, availableSeatList);
        } else {
            if (isRight && isForward) {
                this.setPositionRf(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isRight && isAfter) {
                this.setPositionRa(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isLeft && isForward) {
                this.setPositionLf(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isLeft && isAfter) {
                this.setPositionLa(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isRight) {
                this.setPositionR(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isLeft) {
                this.setPositionL(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else if (isAfter) {
                this.setPositionA(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            } else {
                // 默认取靠前的
                this.setPositionF(minY, maxY, minX, maxX, aisleSeatList, availableSeatList);
            }
        }
    }

    private void setPositionRf(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        // Y轴先找右半部分
        int halfY = (minY + maxY) / 2;
        outFor:
        for (int i = minX; i <= maxX; i++) {
            // 靠前
            int finalI = i;
            List<MnjxSeat> forwardSeatList = seatList.stream()
                    .filter(s -> finalI == s.getSeatX())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(forwardSeatList)) {
                for (int j = minY; j < halfY; j++) {
                    int finalJ = j;
                    availableSeatList.addAll(seatList.stream()
                            .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                            .collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(availableSeatList)) {
                        break outFor;
                    }
                }
            }
        }
        // 右半部分为空，找左半部分
        if (CollUtil.isEmpty(availableSeatList)) {
            outFor:
            for (int i = minX; i <= maxX; i++) {
                // 靠前
                int finalI = i;
                List<MnjxSeat> forwardSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(forwardSeatList)) {
                    for (int j = halfY; j <= maxY; j++) {
                        int finalJ = j;
                        availableSeatList.addAll(seatList.stream()
                                .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                                .collect(Collectors.toList()));
                        if (CollUtil.isNotEmpty(availableSeatList)) {
                            break outFor;
                        }
                    }
                }
            }
        }
    }

    private void setPositionRa(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        // Y轴先找右半部分
        int halfY = (minY + maxY) / 2;
        outFor:
        for (int i = maxX; i >= minX; i--) {
            // 靠后
            int finalI = i;
            List<MnjxSeat> forwardSeatList = seatList.stream()
                    .filter(s -> finalI == s.getSeatX())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(forwardSeatList)) {
                for (int j = minY; j < halfY; j++) {
                    int finalJ = j;
                    availableSeatList.addAll(seatList.stream()
                            .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                            .collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(availableSeatList)) {
                        break outFor;
                    }
                }
            }
        }
        // 右半部分为空，找左半部分
        if (CollUtil.isEmpty(availableSeatList)) {
            outFor:
            for (int i = maxX; i >= minX; i--) {
                // 靠后
                int finalI = i;
                List<MnjxSeat> forwardSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(forwardSeatList)) {
                    for (int j = halfY; j <= maxY; j++) {
                        int finalJ = j;
                        availableSeatList.addAll(seatList.stream()
                                .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                                .collect(Collectors.toList()));
                        if (CollUtil.isNotEmpty(availableSeatList)) {
                            break outFor;
                        }
                    }
                }
            }
        }
    }

    private void setPositionLf(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        // Y轴先找左半部分
        int halfY = (minY + maxY) / 2;
        outFor:
        for (int i = minX; i <= maxX; i++) {
            // 靠前
            int finalI = i;
            List<MnjxSeat> forwardSeatList = seatList.stream()
                    .filter(s -> finalI == s.getSeatX())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(forwardSeatList)) {
                for (int j = maxY; j > halfY; j--) {
                    int finalJ = j;
                    availableSeatList.addAll(seatList.stream()
                            .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                            .collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(availableSeatList)) {
                        break outFor;
                    }
                }
            }
        }
        // 左半部分为空，找右半部分
        if (CollUtil.isEmpty(availableSeatList)) {
            outFor:
            for (int i = minX; i <= maxX; i++) {
                // 靠前
                int finalI = i;
                List<MnjxSeat> forwardSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(forwardSeatList)) {
                    for (int j = halfY; j >= minY; j--) {
                        int finalJ = j;
                        availableSeatList.addAll(seatList.stream()
                                .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                                .collect(Collectors.toList()));
                        if (CollUtil.isNotEmpty(availableSeatList)) {
                            break outFor;
                        }
                    }
                }
            }
        }
    }

    private void setPositionLa(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        // Y轴先找左半部分
        int halfY = (minY + maxY) / 2;
        outFor:
        for (int i = maxX; i >= minX; i--) {
            // 靠后
            int finalI = i;
            List<MnjxSeat> forwardSeatList = seatList.stream()
                    .filter(s -> finalI == s.getSeatX())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(forwardSeatList)) {
                for (int j = maxY; j > halfY; j--) {
                    int finalJ = j;
                    availableSeatList.addAll(seatList.stream()
                            .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                            .collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(availableSeatList)) {
                        break outFor;
                    }
                }
            }
        }
        // 左半部分为空，找右半部分
        if (CollUtil.isEmpty(availableSeatList)) {
            outFor:
            for (int i = maxX; i >= minX; i--) {
                // 靠后
                int finalI = i;
                List<MnjxSeat> forwardSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(forwardSeatList)) {
                    for (int j = halfY; j >= minY; j--) {
                        int finalJ = j;
                        availableSeatList.addAll(seatList.stream()
                                .filter(s -> finalJ == s.getSeatY() && finalI == s.getSeatX())
                                .collect(Collectors.toList()));
                        if (CollUtil.isNotEmpty(availableSeatList)) {
                            break outFor;
                        }
                    }
                }
            }
        }
    }

    private void setPositionF(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> forwardSeatList = new ArrayList<>();
        outFor:
        for (int i = minX; i <= maxX; i++) {
            int finalI = i;
            for (int j = maxY; j >= minY; j--) {
                int finalJ = j;
                forwardSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX() && finalJ == s.getSeatY())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(forwardSeatList)) {
                    break outFor;
                }
            }
        }
        if (CollUtil.isNotEmpty(forwardSeatList)) {
            availableSeatList.addAll(forwardSeatList);
        }
    }

    private void setPositionA(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> afterSeatList = new ArrayList<>();
        outFor:
        for (int i = maxX; i >= minX; i--) {
            int finalI = i;
            for (int j = maxY; j >= minY; j--) {
                int finalJ = j;
                afterSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatX() && finalJ == s.getSeatY())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(afterSeatList)) {
                    break outFor;
                }
            }
        }
        if (CollUtil.isNotEmpty(afterSeatList)) {
            availableSeatList.addAll(afterSeatList);
        }
    }

    private void setPositionR(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> rightSeatList = new ArrayList<>();
        outFor:
        for (int i = minY; i <= maxY; i++) {
            int finalI = i;
            for (int j = minX; j <= maxX; j++) {
                int finalJ = j;
                rightSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatY() && finalJ == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(rightSeatList)) {
                    break outFor;
                }
            }
        }
        if (CollUtil.isNotEmpty(rightSeatList)) {
            availableSeatList.addAll(rightSeatList);
        }
    }

    private void setPositionL(int minY, int maxY, int minX, int maxX, List<MnjxSeat> seatList, List<MnjxSeat> availableSeatList) {
        List<MnjxSeat> leftSeatList = new ArrayList<>();
        outFor:
        for (int i = maxY; i >= minY; i--) {
            int finalI = i;
            for (int j = minX; j <= maxX; j++) {
                int finalJ = j;
                leftSeatList = seatList.stream()
                        .filter(s -> finalI == s.getSeatY() && finalJ == s.getSeatX())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(leftSeatList)) {
                    break outFor;
                }
            }
        }
        if (CollUtil.isNotEmpty(leftSeatList)) {
            availableSeatList.addAll(leftSeatList);
        }
    }

    /**
     * Title: setCorrectSeats
     * Description: 获取设置可用的座位号和座位数据
     *
     * @param mnjxOpenCabins  mnjxOpenCabins
     * @param specialSeat     specialSeat
     * @param seatNo          seatNo
     * @param planSectionSize planSectionSize
     * @param paResultDto     paResultDto
     * <AUTHOR>
     */
    @Override
    public void setCorrectSeats(List<MnjxOpenCabin> mnjxOpenCabins, String specialSeat, String seatNo, int planSectionSize, PaResultDto paResultDto, Map<String, List<String>> existRandomSeatListMap) throws UnifiedResultException {
        List<MnjxSeat> mnjxSeatList;
        // 将开舱数据按航节计划分组
        Map<String, List<MnjxOpenCabin>> collect = mnjxOpenCabins.stream()
                .collect(Collectors.groupingBy(MnjxOpenCabin::getPlanSectionId));
        Set<Map.Entry<String, List<MnjxOpenCabin>>> entries = collect.entrySet();
        List<String> seatNoList = new ArrayList<>();
        // 获取所有的开舱ID
        List<String> allOpenCabinIdList = mnjxOpenCabins.stream()
                .map(MnjxOpenCabin::getOpenCabinId)
                .collect(Collectors.toList());
        // 遍历每一组航节的开舱数据
        for (Map.Entry<String, List<MnjxOpenCabin>> entry : entries) {
            List<MnjxOpenCabin> openCabinList = entry.getValue();
            // 获取当前航节的开舱ID
            List<String> openCabinIdList = openCabinList.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            // 特殊座位处理
            if (StrUtil.isNotEmpty(specialSeat)) {
                this.setPositionSeat(specialSeat, openCabinIdList, paResultDto, seatNoList);
            }
            // 直接输入座位号的处理
            else {
                // 获取当前航节输入的座位号的座位数据
                List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                        .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                        .list();
                String finalSeatNo = seatNo;
                List<MnjxSeat> canUseSeatList = seatList.stream()
                        .filter(s -> finalSeatNo.equals(s.getSeatNo()))
                        .collect(Collectors.toList());
                if (MapUtil.isNotEmpty(existRandomSeatListMap) && existRandomSeatListMap.containsKey(paResultDto.getFlightNo()) && existRandomSeatListMap.get(paResultDto.getFlightNo()).contains(seatNo)) {
                    canUseSeatList = seatList.stream()
                            .filter(s -> !finalSeatNo.equals(s.getSeatNo()) && !"C".equals(s.getSeatStatus()))
                            .collect(Collectors.toList());
                }
                // 输入的座位号是否属于当前舱位
                if (CollUtil.isEmpty(canUseSeatList)) {
                    if (paResultDto.isUseLastSeatO()) {
                        // 当前出港段没有输入座位号，沿用的上一段的座位号，这时候座位不符合需要重新随机分配
                        canUseSeatList = seatList.stream()
                                .filter(s -> !"C".equals(s.getSeatStatus()))
                                .collect(Collectors.toList());
                    } else {
                        throw new UnifiedResultException(Constant.SEATING_CONFLICT);
                    }
                }
                MnjxSeat seat = canUseSeatList.get(0);
                MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                        .eq(MnjxPsgSeat::getPsgCkiId, paResultDto.getPsgCkiId())
                        .eq(MnjxPsgSeat::getPsgSeat, seatNo)
                        .one();
                if (ObjectUtil.isNotEmpty(psgSeat) && psgSeat.getSeatStatus().equals(seat.getSeatStatus())) {
                    return;
                }
                // 座位不可用，如果是SNR则C可用
                if ((paResultDto.isNormalSeat() && !StrUtil.equalsAny(seat.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S"))
                        || (paResultDto.isSnr() && !StrUtil.equalsAny(seat.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S", "C"))) {
                    if (StrUtil.isNotEmpty(paResultDto.getGType())) {
                        paResultDto.setPreOldSeat(seat.getSeatNo());
                    }
                    // 如果输入的座位不可用，随机分配一个可用的座位
                    List<MnjxSeat> otherSeatList = seatList.stream()
                            .filter(s -> "*".equals(s.getSeatStatus()) && !finalSeatNo.equals(s.getSeatNo()))
                            .collect(Collectors.toList());
                    if (CollUtil.isEmpty(otherSeatList)) {
                        otherSeatList = seatList.stream()
                                .filter(s -> StrUtil.equalsAny(s.getSeatStatus(), "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S"))
                                .collect(Collectors.toList());
                    }
                    if (CollUtil.isEmpty(otherSeatList)) {
                        otherSeatList = seatList.stream()
                                .filter(s -> "C".equals(s.getSeatStatus()))
                                .collect(Collectors.toList());
                        if (CollUtil.isEmpty(otherSeatList)) {
                            paResultDto.setCap(true);
                            return;
                        }
                    }
                    int randomInt = otherSeatList.size() > 1 ? RandomUtil.randomInt(0, otherSeatList.size()) : 0;
                    seat = otherSeatList.get(randomInt);
                    paResultDto.setNrs("NRS");
                } else if (StrUtil.isNotEmpty(paResultDto.getGType())) {
                    paResultDto.setPreOldSeat(seat.getSeatNo());
                }
                seatNo = seat.getSeatNo();
                seatNoList.add(seatNo);
            }
        }
        // 直达航班中间有经停的，座位需要同时满足所有航段都可用。比如：CTU-PEK-SHA，旅客直接订的CTU-SHA，则需要满足座位在CTU-PEK PEK-SHA都可用
        if (CollUtil.isNotEmpty(seatNoList)) {
            // 上面筛选的所有座位号，选出有重复并且重复条数等于航节数的座位号
            List<String> correctSeatNoList = seatNoList.stream()
                    .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() == planSectionSize)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(correctSeatNoList)) {
                paResultDto.setCap(true);
                return;
            }
            if (StrUtil.isNotEmpty(specialSeat)) {
                seatNo = correctSeatNoList.get(0);
            } else {
                if (existRandomSeatListMap.containsKey(paResultDto.getFlightNo())) {
                    List<String> existRandomSeatList = existRandomSeatListMap.get(paResultDto.getFlightNo());
                    for (String s : correctSeatNoList) {
                        if (!existRandomSeatList.contains(s)) {
                            seatNo = s;
                            existRandomSeatList.add(s);
                            break;
                        }
                    }
                } else {
                    List<String> existRandomSeatList = new ArrayList<>();
                    existRandomSeatList.add(correctSeatNoList.get(0));
                    seatNo = correctSeatNoList.get(0);
                    existRandomSeatListMap.put(paResultDto.getFlightNo(), existRandomSeatList);
                }
            }
            mnjxSeatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, allOpenCabinIdList)
                    .eq(MnjxSeat::getSeatNo, seatNo)
                    .list();
        } else {
            paResultDto.setCap(true);
            return;
        }
        paResultDto.setSeatNo(seatNo);
        paResultDto.setMnjxSeatList(mnjxSeatList);
    }

    /**
     * Title: parseSpml
     * Description: 解析特殊餐食
     *
     * @param option             option
     * @param ssrTypeFreeTextMap ssrTypeFreeTextMap
     * <AUTHOR>
     */
    private void parseSpml(String option, Map<String, String> ssrTypeFreeTextMap, String pnrNmId, int segNo) {
        List<String> allGroups = ReUtil.getAllGroups(SPML_PATTERN, option);
        String spml = allGroups.get(1);
//        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
//                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
//                .list();
//        if (CollUtil.isNotEmpty(ssrList)) {
//            // 餐食只允许有一个，如果指令输入了其他餐食，直接忽略
//            long mlCount = ssrList.stream()
//                    .filter(s -> s.getSsrType().endsWith("ML") && segNo == s.getPnrSegNo())
//                    .count();
//            if (mlCount > 0) {
//                return;
//            }
//        }
        if (!Constant.SPML_CN_MAP.containsKey(spml)) {
            ssrTypeFreeTextMap.put(Constant.SSR_TYPE_SPML, spml);
        } else {
            ssrTypeFreeTextMap.put(spml, null);
        }
    }

    /**
     * Title: parsePsgType
     * Description: 解析旅客类型的option
     *
     * @param option             option
     * @param optionList         optionList
     * @param i                  i
     * @param paResultDto        paResultDto
     * @param ssrTypeFreeTextMap ssrTypeFreeTextMap
     * <AUTHOR>
     */
    private void parsePsgType(String option, List<String> optionList, int i, PaResultDto paResultDto, Map<String, String> ssrTypeFreeTextMap) throws UnifiedResultException {
        List<String> allGroups = ReUtil.getAllGroups(PSG_TYPE_PATTERN, option);
        String psgType = allGroups.get(1);
        String number = allGroups.get(2);
        String infName = allGroups.get(3);
        if (Constant.CHD.equals(psgType)) {
            if (StrUtil.isNotEmpty(infName)) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            // 如果选择项同时含有CHD，INF并且指令是按照CHD,INF顺序时，提示“CHILD CAN NOT BE ACCEPTED WITH INFANT”
            if (i < optionList.size() - 1 && optionList.get(i + 1).startsWith(Constant.INF)) {
                throw new UnifiedResultException(Constant.CHD_CAN_NOT_WITH_INF);
            }
            paResultDto.setHasChd(true);
            ssrTypeFreeTextMap.put(Constant.SSR_TYPE_CHLD, null);
        } else if (Constant.INF.equals(psgType)) {
            if (StrUtil.isEmpty(infName) || StrUtil.isEmpty(number)) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            // 婴儿只能携带一个
            if (Integer.parseInt(number) > 1) {
                throw new UnifiedResultException(Constant.ONLY_ONE_INF);
            } else if (Integer.parseInt(number) == 0) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            paResultDto.setHasInf(true);
            ssrTypeFreeTextMap.put(Constant.SSR_TYPE_INFT, null);
            paResultDto.setInfName(infName);
        } else if (Constant.UM.equals(psgType)) {
            if (StrUtil.isNotEmpty(infName)) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            if (StrUtil.isEmpty(number)) {
                throw new UnifiedResultException(Constant.UM_AGE);
            }
            if (Integer.parseInt(number) > Constant.NINETY_NINE || Integer.parseInt(number) < 1) {
                throw new UnifiedResultException(Constant.NUMBER_ERROR);
            }
            paResultDto.setUmAge(number);
            paResultDto.setHasUm(true);
            ssrTypeFreeTextMap.put(Constant.SSR_TYPE_UMNR, null);
        }
    }

    /**
     * Title: getMatchPlanSections
     * Description: 获取符合上次结果集航段信息，主要是处理直达航段中间航段的组合
     *
     * @param paResultDto paResultDto
     * @return 获取符合上次结果集航段信息，主要是处理直达航段中间航段的组合
     * <AUTHOR>
     */
    @Override
    public List<MnjxPlanSection> getMatchPlanSections(PaResultDto paResultDto) {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, paResultDto.getFlightNo())
                .one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getFlightDate, paResultDto.getFlightDate().contains(StrUtil.DASHED) ? paResultDto.getFlightDate() : DateUtils.com2ymd(paResultDto.getFlightDate()))
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .one();

        // 获取CND NO
        String cndNo = planFlight.getCndNo();
        paResultDto.setCndNo(cndNo);

        String orgAirportCode = paResultDto.getOrgAirport();
        String dstAirportCode = paResultDto.getDstAirport();
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, orgAirportCode)
                .one();
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, dstAirportCode)
                .one();
        String cityEname = iMnjxCityService.getById(dstAirport.getCityId()).getCityEname();
        paResultDto.setCityName(cityEname);
        paResultDto.setAirportInfo(dstAirport.getAirportEname());
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff)
                .list();
        boolean findFirstAirport = false;
        List<MnjxPlanSection> thisPsgPlanSectionList = new ArrayList<>();
        for (MnjxPlanSection mnjxPlanSection : planSectionList) {
            // 输入的航段是多航段中的某一个航段，或者是单航段
            if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId()) && dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                thisPsgPlanSectionList.add(mnjxPlanSection);
            }
            // 输入的航段是多航段中多个航段的组合
            else {
                // 出发
                if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId())) {
                    findFirstAirport = true;
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                }
                // 如果都没有匹配到，说明是中间航段，也需要把开舱数据加进去进行筛选
                else if (findFirstAirport) {
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                    if (dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                        findFirstAirport = false;
                    }
                }
            }
        }
        return thisPsgPlanSectionList;
    }

    /**
     * Title: getRandomAllowSeat
     * Description: 随机获取可用的座位
     *
     * @param mnjxOpenCabins  mnjxOpenCabins
     * @param planSectionSize planSectionSize
     * @param paResultDto     paResultDto
     * <AUTHOR>
     */
    private void getRandomAllowSeat(List<MnjxOpenCabin> mnjxOpenCabins, int planSectionSize, PaResultDto paResultDto, Map<String, List<String>> existRandomSeatListMap, Integer psgNum) {
        List<String> seatNoList = new ArrayList<>();
        // 获取所有的开舱ID
        List<String> allOpenCabinIdList = mnjxOpenCabins.stream()
                .map(MnjxOpenCabin::getOpenCabinId)
                .collect(Collectors.toList());
        // 将开舱数据按航节计划分组
        Map<String, List<MnjxOpenCabin>> collect = mnjxOpenCabins.stream()
                .collect(Collectors.groupingBy(MnjxOpenCabin::getPlanSectionId));
        Set<Map.Entry<String, List<MnjxOpenCabin>>> entries = collect.entrySet();
        // 遍历每一组航节的开舱数据
        for (Map.Entry<String, List<MnjxOpenCabin>> entry : entries) {
            List<MnjxOpenCabin> openCabinList = entry.getValue();
            // 获取当前航节的开舱ID
            List<String> openCabinIdList = openCabinList.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .list();
            List<MnjxSeat> canUseSeatList = seatList.stream()
                    .filter(s -> StrUtil.equalsAny(s.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S", "C"))
                    .collect(Collectors.toList());
            // 如果没有找到可用座位，座位已使用完，无法进行分配
            if (CollUtil.isEmpty(canUseSeatList) || (ObjectUtil.isNotEmpty(psgNum) && canUseSeatList.size() < psgNum)) {
                paResultDto.setCap(true);
                return;
            }
            seatNoList.addAll(canUseSeatList.stream()
                    .map(MnjxSeat::getSeatNo)
                    .collect(Collectors.toList()));
        }
        // 上面筛选的所有座位号，选出有重复并且重复条数等于航节数的座位号，因为多航段的航班需要每个航段那个座位号都可用，如果没有可用，按候补处理
        List<String> correctSeatNoList = seatNoList.stream()
                .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream()
                .filter(entry -> entry.getValue() == planSectionSize)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(correctSeatNoList) || (ObjectUtil.isNotEmpty(psgNum) && correctSeatNoList.size() < psgNum)) {
            paResultDto.setCap(true);
            return;
        }
        // 座位数不够，旅客接收成SB状态，并添加CAP标识
        int needSeatNum = ObjectUtil.isNotEmpty(psgNum) ? psgNum : 0;
        if (needSeatNum > correctSeatNoList.size()) {
            paResultDto.setCap(true);
            return;
        }
        // 随机选取可用的座位
        int randomInt = correctSeatNoList.size() == 1 ? 0 : RandomUtil.randomInt(0, correctSeatNoList.size());
        String seatNo = correctSeatNoList.get(randomInt);
        // 防重复
        if (MapUtil.isNotEmpty(existRandomSeatListMap) && existRandomSeatListMap.containsKey(paResultDto.getFlightNo())) {
            while (existRandomSeatListMap.get(paResultDto.getFlightNo()).contains(seatNo)) {
                randomInt = correctSeatNoList.size() == 1 ? 0 : RandomUtil.randomInt(0, correctSeatNoList.size());
                seatNo = correctSeatNoList.get(randomInt);
            }
            existRandomSeatListMap.get(paResultDto.getFlightNo()).add(seatNo);
        }
        List<MnjxSeat> mnjxSeatList = iMnjxSeatService.lambdaQuery()
                .in(MnjxSeat::getOpenCabinId, allOpenCabinIdList)
                .eq(MnjxSeat::getSeatNo, seatNo)
                .list();
        if (MapUtil.isEmpty(existRandomSeatListMap) || !existRandomSeatListMap.containsKey(paResultDto.getFlightNo())) {
            List<String> existRandomSeatList = new ArrayList<>();
            existRandomSeatList.add(seatNo);
            existRandomSeatListMap.put(paResultDto.getFlightNo(), existRandomSeatList);
        }
        paResultDto.setSeatNo(seatNo);
        paResultDto.setMnjxSeatList(mnjxSeatList);
    }

    /**
     * Title: handlePa
     * Description: 处理接收旅客
     *
     * @param paResultDto paResultDto
     * @param aboardNo    aboardNo
     * @param luggageList luggageList
     * <AUTHOR>
     */
    private void handlePa(PaResultDto paResultDto, Integer aboardNo, List<MnjxLuggage> luggageList, Map<String, String> gsMap, Map<String, Integer> planSectionPsgNumMap, Map<String, Integer> planSectionExstNumMap) throws UnifiedResultException {
        List<MnjxPsgOperateRecord> psgOperateRecordList = new ArrayList<>();
        MnjxPnrSeg pnrSeg = paResultDto.getMnjxPnrSeg();
        MnjxPsgCki psgCki = paResultDto.getMnjxPsgCki();
        paResultDto.setPsgCkiId(psgCki.getPsgCkiId());

        // 判断座位冲突
        // 如果预接收了座位号，这次执行有JMP STCR，则返回冲突
        // 如果预接收了JMP，这次执行有座位号或STCR，返回冲突
        // 如果预接收了STCR，这次执行有座位号或JMP，返回冲突
        List<MnjxNmSsr> ssrList = paResultDto.getSsrList();
        List<MnjxPnrRecord> pnrRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, paResultDto.getPnr().getPnrId())
                .list();
        paResultDto.setPnrRecordList(pnrRecordList);
        if (StrUtil.isNotEmpty(paResultDto.getSeatNo())) {
            if (ssrList.stream().anyMatch(s -> StrUtil.equalsAny(s.getSsrType(), Constant.SSR_TYPE_JMP, Constant.SSR_TYPE_STCR))) {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
        }
        if (StrUtil.isNotEmpty(paResultDto.getJmp())) {
            if (ssrList.stream().anyMatch(s -> StrUtil.equals(s.getSsrType(), Constant.SSR_TYPE_STCR))) {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
            Integer count = iMnjxPsgSeatService.lambdaQuery()
                    .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                    .isNotNull(MnjxPsgSeat::getPsgSeat)
                    .count();
            if (count > 0) {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
        }
        if (StrUtil.isNotEmpty(paResultDto.getStcr())) {
            if (ssrList.stream().anyMatch(s -> StrUtil.equals(s.getSsrType(), Constant.SSR_TYPE_JMP))) {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
            Integer count = iMnjxPsgSeatService.lambdaQuery()
                    .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                    .isNotNull(MnjxPsgSeat::getPsgSeat)
                    .count();
            if (count > 0) {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
        }
        String foidParam = paResultDto.getFoidParam();
        if (StrUtil.isNotEmpty(foidParam)) {
            this.handleFoid(foidParam, ssrList, paResultDto);
        }

        List<MnjxPlanSection> thisPsgPlanSections = paResultDto.getPlanSectionList();
        // 接收候补验证
        int dlToSbCount = 0;
        int dlToAccCount = 0;
        boolean haveExst = StrUtil.isNotEmpty(paResultDto.getExstType()) && paResultDto.getMnjxPsgCkiOptionList().stream().noneMatch(p -> "EXST".equals(p.getOptionType()));
        // CAP存在，不进行GS的修改，因为旅客会被接收为SB候补
        if (!paResultDto.isCap()
                && ((Constant.SB.equals(psgCki.getCkiStatus()) && !Constant.STR_ONE.equals(psgCki.getCap()))
                || StrUtil.isNotEmpty(psgCki.getUres())
                || StrUtil.isNotEmpty(psgCki.getNrec())
                || (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate()) && !paResultDto.isAdv())
                || haveExst)) {
            for (MnjxPlanSection mnjxPlanSection : thisPsgPlanSections) {
                String planSectionId = mnjxPlanSection.getPlanSectionId();
                StringBuilder cabinClassBuilder = new StringBuilder();
                String goshowLimit = mnjxPlanSection.getGoshowLimit();
                if (StrUtil.isEmpty(goshowLimit)) {
                    // HBJC时，操作的SB状态旅客为ACC，没有限额报错
                    if (Constant.SB.equals(psgCki.getCkiStatus())) {
                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                    } else if (Constant.DL.equals(psgCki.getCkiStatus()) || Constant.NACC.equals(psgCki.getCkiStatus())) {
                        // 如果做过预升降舱才接收的候补，需要判断GS
                        // 如果正在做预升降舱，并且输入了座位号，需要判断GS
                        if (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate()) || paResultDto.isUpgnInputSeat()) {
                            throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                        }
                        if (StrUtil.isNotEmpty(psgCki.getAboardNo())) {
                            dlToAccCount++;
                        } else {
                            dlToSbCount++;
                        }
                    }
                } else {
                    if (!gsMap.containsKey(planSectionId)) {
                        gsMap.put(planSectionId, goshowLimit);
                    } else {
                        goshowLimit = gsMap.get(planSectionId);
                    }
                    String[] split = goshowLimit.split(StrUtil.SLASH);
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < split.length; i++) {
                        String s = split[i];
                        String gsNum = s.substring(1);
                        String gsCabinClass = s.substring(0, 1);
                        cabinClassBuilder.append(gsCabinClass);
                        if (gsCabinClass.equals(paResultDto.getCabinClass())) {
                            if (gsNum.startsWith(StrUtil.DASHED) || Integer.parseInt(gsNum) == 0) {
                                // HBJC时，操作的SB状态旅客为ACC，没有限额报错
                                if (Constant.SB.equals(psgCki.getCkiStatus())) {
                                    throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                                } else if (Constant.DL.equals(psgCki.getCkiStatus()) || Constant.NACC.equals(psgCki.getCkiStatus())) {
                                    if (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate())) {
                                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                                    }
                                    if (StrUtil.isNotEmpty(psgCki.getAboardNo())) {
                                        dlToAccCount++;
                                    } else {
                                        dlToSbCount++;
                                    }
                                }
                            } else {
                                int gs;
                                // 自带消耗1个GS限额的类型，需要先减1
                                if ((Constant.SB.equals(psgCki.getCkiStatus()) && !Constant.STR_ONE.equals(psgCki.getCap()))
                                        || StrUtil.isNotEmpty(psgCki.getUres())
                                        || StrUtil.isNotEmpty(psgCki.getNrec())
                                        || (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate()) && !paResultDto.isAdv())) {
                                    if (planSectionPsgNumMap.get(planSectionId) > Integer.parseInt(gsNum)
                                            || (haveExst && planSectionPsgNumMap.get(planSectionId) + planSectionExstNumMap.get(planSectionId) * 2 > Integer.parseInt(gsNum))) {
                                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                                    }
                                    gs = Integer.parseInt(gsNum) - 1;
                                }
                                // 只有额外占座，不属于自身会消耗GS限额的类型时，判断GS时不需要判断人的份额
                                else {
                                    if (haveExst && planSectionExstNumMap.get(planSectionId) * 2 > Integer.parseInt(gsNum)) {
                                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                                    }
                                    gs = Integer.parseInt(gsNum);
                                }
                                // 有额外占座还需要再消耗2个
                                if (haveExst) {
                                    gs = gs - 2;
                                }
                                s = StrUtil.format("{}{}", gsCabinClass, StrUtil.fill(StrUtil.toString(gs), '0', 3, true));
                                dlToAccCount++;
                            }
                        }
                        // 候补旅客预升降舱输入了座位号
                        else if (gsCabinClass.equals(paResultDto.getGCabinClass())
                                && paResultDto.isUpgnInputSeat()
                                && (gsNum.startsWith(StrUtil.DASHED) || Integer.parseInt(gsNum) == 0)) {
                            throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                        }
                        sb.append(s);
                        if (i < split.length - 1) {
                            sb.append(StrUtil.SLASH);
                        }
                    }
                    gsMap.put(planSectionId, sb.toString());
                    planSectionPsgNumMap.put(planSectionId, planSectionPsgNumMap.get(planSectionId) - 1);
                    if (haveExst) {
                        planSectionExstNumMap.put(planSectionId, planSectionExstNumMap.get(planSectionId) - 1);
                    }
//                    mnjxPlanSection.setGoshowLimit(sb.toString());
                }
                if (!cabinClassBuilder.toString().contains(paResultDto.getCabinClass())) {
                    if (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate())) {
                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                    }
                    if (Constant.SB.equals(psgCki.getCkiStatus())) {
                        throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                    } else if (Constant.DL.equals(psgCki.getCkiStatus()) || Constant.NACC.equals(psgCki.getCkiStatus())) {
                        if (StrUtil.isNotEmpty(psgCki.getAboardNo())) {
                            dlToAccCount++;
                        } else {
                            dlToSbCount++;
                        }
                    }
                }
                // 候补旅客预升降舱输入了座位号
                else if (StrUtil.isNotEmpty(paResultDto.getGCabinClass())
                        && !cabinClassBuilder.toString().contains(paResultDto.getGCabinClass())
                        && paResultDto.isUpgnInputSeat()) {
                    throw new UnifiedResultException(Constant.GS_NOT_AVAILABLE);
                }
            }
//            iMnjxPlanSectionService.updateBatchById(thisPsgPlanSections);
        }
        if (dlToSbCount > 0 || paResultDto.isCap()) {
            paResultDto.setDlOrNaccToSb(true);
        }
        if (dlToAccCount > 0 || (dlToAccCount == 0 && dlToSbCount == 0)) {
            paResultDto.setDlOrNaccToAcc(true);
        }

        // 预升降舱处理
        this.handlePreDngUpg(paResultDto, psgCki, pnrSeg, psgOperateRecordList, gsMap);

        // 处理邻近占座
        if (StrUtil.isNotEmpty(paResultDto.getAdsr())) {
            MnjxPsgCkiOption dbPsgCkiOption = iMnjxPsgCkiOptionService.lambdaQuery()
                    .eq(MnjxPsgCkiOption::getPsgCkiId, psgCki.getPsgCkiId())
                    .eq(MnjxPsgCkiOption::getOptionType, "ADSR")
                    .one();
            this.handleAdsr(paResultDto, psgCki, dbPsgCkiOption);
        }

        // 处理选项中行李的数据更新
        this.handleBagData(paResultDto, psgCki, psgOperateRecordList);
        if (CollUtil.isNotEmpty(paResultDto.getLuggageList())) {
            luggageList.clear();
            luggageList.addAll(paResultDto.getLuggageList());
        }
        // 如果是联程行李，出港联程的部分需要添加操作记录
        if (paResultDto.isTypeO() && StrUtil.isNotEmpty(paResultDto.getBagNumber()) && StrUtil.isNotEmpty(paResultDto.getBagWeight()) && CollUtil.isNotEmpty(luggageList)) {
            this.setOlinkBagOperateRecord(paResultDto, psgCki, psgOperateRecordList, luggageList);
        }

        // 处理BSCT
        if (StrUtil.isNotEmpty(paResultDto.getBsctNum())) {
            this.handlePsgCkiOption("BSCT", psgCki.getPsgCkiId(), paResultDto, psgOperateRecordList);
        }

        // 处理PETC
        if (StrUtil.isNotEmpty(paResultDto.getPetcNum())) {
            this.handlePsgCkiOption("PETC", psgCki.getPsgCkiId(), paResultDto, psgOperateRecordList);
        }

        // 处理额外占座
        if (paResultDto.isHaveExst()) {
            this.handleExst(paResultDto, psgCki, pnrSeg);
        }

        // 处理选项中SSR的数据更新
        this.handleSsrData(paResultDto, pnrSeg, psgCki);

        //todo 处理CKIN
        if (StrUtil.isNotEmpty(paResultDto.getCkin())) {

        }

        // 处理nm数据
        this.handleNmData(paResultDto, pnrSeg, psgCki);

        // 处理常客数据
        if (StrUtil.isNotEmpty(paResultDto.getFf())) {
            this.handleFrequenter(paResultDto);
            psgOperateRecordList.add(this.constructOperateRecord("ACC", "FF", psgCki.getPsgCkiId(), paResultDto, null));
        }

        // 处理电话数据
        if (StrUtil.isNotEmpty(paResultDto.getCtc())) {
            this.handlePnrCt(paResultDto, pnrSeg.getPnrId(), psgCki);
        }

        // 修改登机口
        MnjxPlanSection firstPlanSection = thisPsgPlanSections.get(0);
        psgCki.setGate(firstPlanSection.getGate());

        paResultDto.setUres(psgCki.getUres());
        paResultDto.setNrec(psgCki.getNrec());
        // 处理座位(升降舱时座位提前处理过了，不用再次处理)
        // NACC状态的候补旅客在接收成SB状态的操作，也不会自动分配座位。如果输入了座位，接收该座位
        // 预留座位
        if (!paResultDto.isCap()) {
            if (StrUtil.isEmpty(paResultDto.getGType()) && ((dlToSbCount > 0 && StrUtil.isNotEmpty(paResultDto.getSeatNo())) || paResultDto.isAdv() || dlToAccCount > 0 || (dlToAccCount == 0 && dlToSbCount == 0))) {
                this.handleSeatData(psgCki.getPsgCkiId(), paResultDto);
            }
        }

        // 如果没有预留座位，则直接进行接收，产生登机号，修改值机状态，更新值机人数统计
        // 预接收特服时不进行修改值机状态操作
        if (!paResultDto.isAdv()) {
            paResultDto.setOldCkiStatus(psgCki.getCkiStatus());

            // 修改值机状态
            if ((dlToAccCount > 0 || (dlToAccCount == 0 && dlToSbCount == 0)) && !paResultDto.isCap()) {
                // 生成登机号，如果旅客是被拉下重新接收的，不产生新的登机号
                if (StrUtil.isEmpty(psgCki.getAboardNo())) {
                    psgCki.setAboardNo(StrUtil.fill(StrUtil.toString(aboardNo), '0', 3, true));
                }
                paResultDto.setCkiStatus(Constant.ACC);
                psgCki.setCkiStatus(Constant.ACC);
                psgCki.setCheckInAirportCode(MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo().substring(0, 3));
                // 旅客是通程值机或多联程航班值机时，指令带O，则标记通程值机，SY T统计使用
                if (paResultDto.isTypeO() || CollUtil.isNotEmpty(paResultDto.getOList())) {
                    psgCki.setIsThroughCheckIn(Constant.STR_ONE);
                } else {
                    psgCki.setIsThroughCheckIn(Constant.STR_ZERO);
                }
                if (Constant.STR_ONE.equals(psgCki.getPreUpgnOperate())) {
                    psgCki.setPreUpgnOperate(Constant.STR_ZERO);
                }
                if (Constant.STR_ONE.equals(psgCki.getCap())) {
                    psgCki.setCap(Constant.STR_ZERO);
                    // 提供HBJC使用
                    if (paResultDto.getCmd().startsWith(Constant.CMD_HBJC)) {
                        paResultDto.setCap(true);
                    }
                }
                // 生成操作记录
                if (psgOperateRecordList.stream().noneMatch(r -> Constant.ACC.equals(r.getOperateType()))) {
                    psgOperateRecordList.add(this.constructOperateRecord(Constant.ACC, null, psgCki.getPsgCkiId(), paResultDto, null));
                }
                // 处理票号
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, paResultDto.getPnrNmId())
                        .one();
                List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                        .eq(MnjxPnrNmTn::getPnrNmId, paResultDto.getPnrNmId())
                        .or(ObjectUtil.isNotEmpty(nmXn), p -> p.eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId()))
                        .list();
                List<String> tnIdList = pnrNmTnList.stream()
                        .map(MnjxPnrNmTn::getTnId)
                        .collect(Collectors.toList());
                List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                        .and(m -> m.eq(MnjxPnrNmTicket::getS1Id, paResultDto.getMnjxPnrSeg().getPnrSegId())
                                .or().eq(MnjxPnrNmTicket::getS2Id, paResultDto.getMnjxPnrSeg().getPnrSegId()))
                        .eq(MnjxPnrNmTicket::getIsEt, Constant.STR_ONE)
                        .list();
                if (CollUtil.isNotEmpty(nmTicketList)) {
                    String pnrSegId = paResultDto.getMnjxPnrSeg().getPnrSegId();
                    // 票面状态改为CHECKED IN
                    nmTicketList.forEach(n -> {
                        if (pnrSegId.equals(n.getS1Id())) {
                            n.setTicketStatus1(Constant.CHECKED_IN);
                        } else if (pnrSegId.equals(n.getS2Id())) {
                            n.setTicketStatus2(Constant.CHECKED_IN);
                        }
                    });
                    // 记录票面状态变化历史
                    iAspectCkiService.updatePnrNmTicketList(nmTicketList, paResultDto.getPnrNmId());
//                    iMnjxPnrNmTicketService.updateBatchById(nmTicketList);
                }
                // 更新航节各舱等值机人数
                for (MnjxPlanSection planSection : thisPsgPlanSections) {
                    String gradeCkNumber = planSection.getGradeCkNumber();
                    String[] split = gradeCkNumber.split(StrUtil.SLASH);
                    StringBuilder sb = new StringBuilder();
                    for (String cabinPsgNum : split) {
                        String cabinClass = cabinPsgNum.substring(0, 1);
                        if (StrUtil.isNotEmpty(sb.toString())) {
                            sb.append(StrUtil.SLASH);
                        }
                        if (paResultDto.getCabinClass().equals(cabinClass)) {
                            int psgNum = Integer.parseInt(cabinPsgNum.substring(1));
                            psgNum++;
                            sb.append(cabinClass).append(StrUtil.fill(StrUtil.toString(psgNum), '0', 3, true));
                        } else {
                            sb.append(cabinPsgNum);
                        }
                    }
                    planSection.setGradeCkNumber(sb.toString());
                }
                iMnjxPlanSectionService.updateBatchById(thisPsgPlanSections);
            } else if (dlToSbCount > 0 || paResultDto.isCap()) {
                paResultDto.setCkiStatus(Constant.SB);
                psgCki.setCkiStatus(Constant.SB);
                psgCki.setIsHb(Constant.STR_ONE);
                if (paResultDto.isCap()) {
                    psgCki.setCap(Constant.STR_ONE);
                    if (StrUtil.isNotEmpty(paResultDto.getExstType())) {
                        psgCki.setUres(Constant.STR_ONE);
                    }
                } else if ((StrUtil.isEmpty(psgCki.getUres()) || Constant.STR_ZERO.equals(psgCki.getUres()))
                        && (StrUtil.isEmpty(psgCki.getNrec()) || Constant.STR_ZERO.equals(psgCki.getNrec()))) {
                    psgCki.setUres(Constant.STR_ONE);
                }
                // 接收为候补，产生候补号
                if (StrUtil.isEmpty(psgCki.getHbNo())) {
                    int hbNbNo = 1;
                    List<MnjxPsgCki> allPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                            .isNotNull(MnjxPsgCki::getHbNo)
                            .list();
                    if (CollUtil.isNotEmpty(allPsgCkiList)) {
                        OptionalInt max = allPsgCkiList.stream()
                                .mapToInt(p -> Integer.parseInt(p.getHbNo()))
                                .max();
                        hbNbNo = max.getAsInt() + 1;
                    }
                    psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                }
                // 生成操作记录
                psgOperateRecordList.add(this.constructOperateRecord("SBY", null, psgCki.getPsgCkiId(), paResultDto, null));
            }
            paResultDto.setBnNo(psgCki.getAboardNo());
            paResultDto.setHbNo(psgCki.getHbNo());

            // 旅客值机表数据更新
            iMnjxPsgCkiService.updateById(psgCki);
            paResultDto.setMnjxPsgCki(psgCki);
        }
        // 插入操作记录
        iAspectCkiService.insertPsgOperateRecordList(psgOperateRecordList, paResultDto.getPnrNmId());
//        iMnjxPsgOperateRecordService.saveBatch(psgOperateRecordList);
    }

    /**
     * Title: handleFrequenter
     * Description: 处理常客数据
     *
     * @param paResultDto
     * @return
     * <AUTHOR>
     * @date 2023/3/21 15:07
     */
    @Override
    public void handleFrequenter(PaResultDto paResultDto) {
        MnjxFrequenter dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                .eq(MnjxFrequenter::getAirlineCode, paResultDto.getFfAirlineCode())
                .eq(MnjxFrequenter::getFrequenterCard, paResultDto.getFfNo())
                .one();
        if (ObjectUtil.isEmpty(dbFrequenter)) {
            MnjxNmSsr foidSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, paResultDto.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_FOID)
                    .one();
            String foidInfo = foidSsr.getInputValue().split(" ")[4].split(StrUtil.SLASH)[0];
            String foidType = foidInfo.substring(0, 2);
            String foidNo = foidInfo.substring(2);
            MnjxFrequenter frequenter = new MnjxFrequenter();
            frequenter.setFrequenterId(IdUtil.getSnowflake(1, 1).nextIdStr());
            frequenter.setAirlineCode(paResultDto.getFfAirlineCode());
            frequenter.setFrequenterCertificateType(foidType);
            frequenter.setFrequenterCertificateNo(foidNo);
            frequenter.setFrequenterLevel(paResultDto.getFfLevel());
            frequenter.setFrequenterCard(paResultDto.getFfNo());
            iAspectCkiService.insertFrequenter(frequenter, paResultDto.getPnrNmId());
//            iMnjxFrequenterService.save(frequenter);
            paResultDto.setDbFrequenter(frequenter);
        } else {
            paResultDto.setDbFrequenter(dbFrequenter);
        }
    }

    /**
     * Title: validateUwtUaw
     * Description: 验证本次接收或修改是否会导致航节计划总重量超出uwt uaw重量上限<br>
     *
     * @param paResultDtos
     * @return void
     * <AUTHOR>
     * @date 2024/4/28 13:29
     */
    @Override
    public void validateUwtUaw(List<PaResultDto> paResultDtos) throws UnifiedResultException {
        if (paResultDtos.get(0).isAdv()) {
            return;
        }
        // 按旅客分组，组内包含旅客当前航段和出港联程航段
        Map<String, List<PaResultDto>> paResultDtoMap = paResultDtos.stream().collect(Collectors.groupingBy(PaResultDto::getPnrNmId));
        for (Map.Entry<String, List<PaResultDto>> entry : paResultDtoMap.entrySet()) {
            List<PaResultDto> collect = entry.getValue().stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getBagWeight()))
                    .collect(Collectors.toList());
            // 如果输入了行李，暂时给联程段加上行李重量，供下面判断航节总重量使用，判断完成后再设置为空
            if (CollUtil.isNotEmpty(collect) && collect.size() != entry.getValue().size()) {
                entry.getValue().stream()
                        .filter(p -> StrUtil.isEmpty(p.getBagWeight()))
                        .forEach(p -> p.setBagWeight(collect.get(0).getBagWeight()));
            }
        }
        String cmd = paResultDtos.get(0).getCmd();
        // 按航节计划分组
        Map<MnjxPlanSection, List<PaResultDto>> map = new HashMap<>();
        for (PaResultDto paResultDto : paResultDtos) {
            List<MnjxPlanSection> planSectionList = paResultDto.getPlanSectionList();
            for (MnjxPlanSection planSection : planSectionList) {
                if (map.containsKey(planSection)) {
                    map.get(planSection).add(paResultDto);
                } else {
                    List<PaResultDto> value = new ArrayList<>();
                    value.add(paResultDto);
                    map.put(planSection, value);
                }
            }
        }
        // 按航节计划查询统计该航节的uwt uaw，并将本次新增的数据进行统计判断是否超过上限200000
        boolean haveLuggage = false;
        for (Map.Entry<MnjxPlanSection, List<PaResultDto>> entry : map.entrySet()) {
            // 统计本次新增涉及的航节计划旅客及行李重量
            int newWeight = 0;
            List<PaResultDto> value = entry.getValue();
            for (PaResultDto paResultDto : value) {
                // 新接收旅客才会计算人的重量
                // PA HBPA
                if (StrUtil.startWithAny(cmd, Constant.CMD_PA, Constant.CMD_HBPA)) {
                    List<MnjxNmSsr> ssrList = paResultDto.getSsrList();
                    if (ssrList.stream().anyMatch(s -> Constant.SSR_TYPE_CHLD.equals(s.getSsrType()))) {
                        newWeight += Constant.CHILD_WEIGHT;
                    } else {
                        newWeight += Constant.ADULT_WEIGHT;
                    }
                    if (ObjectUtil.isNotEmpty(paResultDto.getMnjxNmXn()) || paResultDto.isHasInf()) {
                        newWeight += Constant.INFANT_WEIGHT;
                    }
                }
                // PU HBPU
                else {
                    // PU HBPU对未接收的联程旅客可以进行接收处理，这里也需要计算人的重量
                    if (!paResultDto.isAdv() && (Constant.NACC.equals(paResultDto.getCkiStatus()) || Constant.DL.equals(paResultDto.getCkiStatus()))) {
                        List<MnjxNmSsr> ssrList = paResultDto.getSsrList();
                        if (ssrList.stream().anyMatch(s -> Constant.SSR_TYPE_CHLD.equals(s.getSsrType()))) {
                            newWeight += Constant.CHILD_WEIGHT;
                        } else {
                            newWeight += Constant.ADULT_WEIGHT;
                        }
                        if (ObjectUtil.isNotEmpty(paResultDto.getMnjxNmXn()) || paResultDto.isHasInf()) {
                            newWeight += Constant.INFANT_WEIGHT;
                        }
                    }
                    // 单独给旅客添加婴儿
                    else if (paResultDto.isHasInf()) {
                        newWeight += Constant.INFANT_WEIGHT;
                    }
                }
                if (StrUtil.isNotEmpty(paResultDto.getBagWeight())) {
                    newWeight += Integer.parseInt(paResultDto.getBagWeight());
                    haveLuggage = true;
                }
                // 如果本次执行的是接收旅客，还要加上旅客之前预添加的系统行李重量
                if (StrUtil.equalsAny(paResultDto.getCkiStatus(), Constant.CKI_STATUS_NACC, Constant.CKI_STATUS_DL)) {
                    List<MnjxLuggage> preLuggageList = iMnjxLuggageService.lambdaQuery()
                            .eq(MnjxLuggage::getPnrNmId, paResultDto.getPnrNmId())
                            .eq(MnjxLuggage::getBagSegNo, paResultDto.getMnjxPnrSeg().getPnrSegNo())
                            .eq(MnjxLuggage::getLuggageType, Constant.STR_ONE)
                            .and(l -> l.isNull(MnjxLuggage::getIsDel).or().ne(MnjxLuggage::getIsDel, Constant.DELETE_TYPE))
                            .list();
                    if (CollUtil.isNotEmpty(preLuggageList)) {
                        List<MnjxLuggage> preSingleLuggageList = preLuggageList.stream()
                                .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(preSingleLuggageList)) {
                            newWeight += preSingleLuggageList.stream()
                                    .mapToInt(MnjxLuggage::getLuggageWeight)
                                    .sum();
                        }
                        List<MnjxLuggage> preGroupLuggageList = preLuggageList.stream()
                                .filter(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(preGroupLuggageList)) {
                            Map<String, List<MnjxLuggage>> preGroupMap = preGroupLuggageList.stream()
                                    .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                            for (Map.Entry<String, List<MnjxLuggage>> preGroupEntry : preGroupMap.entrySet()) {
                                newWeight += preGroupEntry.getValue().get(0).getLuggageWeight();
                            }
                        }
                        haveLuggage = true;
                    }
                }
            }
            // 没有新增的重量，可能是PU HBPU修改其他特服，跳过
            if (newWeight == 0) {
                continue;
            }
            if (newWeight > 200000) {
                // 联程时报错
                if (paResultDtos.stream().anyMatch(p -> CollUtil.isNotEmpty(p.getOList()) || p.isTypeO())) {
                    if (StrUtil.startWithAny(cmd, Constant.CMD_PU, Constant.HBPU_CODE)) {
                        throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                    } else {
                        throw new UnifiedResultException(Constant.UNABLE_TRANSFER);
                    }
                }
                // 非联程报错
                // 带行李参数
                if (paResultDtos.stream().anyMatch(p -> StrUtil.isNotEmpty(p.getBagWeight())) || haveLuggage) {
                    if (StrUtil.startWithAny(cmd, Constant.CMD_PU, Constant.HBPU_CODE)) {
                        throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                    } else {
                        throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                    }
                }
                // 不带行李参数
                else {
                    throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                }
            }

            // 统计涉及的航节计划已存在旅客及行李的总重量
            MnjxPlanSection planSection = entry.getKey();
            // 查询该航节所有已接收的旅客以及他们携带的行李重量
            // 通过出发到达站日期获取PnrSeg列表（有订座）
            String org = iMnjxAirportService.getById(planSection.getDepAptId()).getAirportCode();
            String dst = iMnjxAirportService.getById(planSection.getArrAptId()).getAirportCode();
            MnjxPlanFlight planFlight = iMnjxPlanFlightService.getById(planSection.getPlanFlightId());
            MnjxTcard tcard = iMnjxTcardService.getById(planFlight.getTcardId());
            MnjxFlight flight = iMnjxFlightService.getById(tcard.getFlightId());
            List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getFlightNo, flight.getFlightNo())
                    .eq(MnjxPnrSeg::getFlightDate, planFlight.getFlightDate())
                    .eq(MnjxPnrSeg::getOrg, org)
                    .eq(MnjxPnrSeg::getDst, dst)
                    .list();

            if (CollUtil.isNotEmpty(segList)) {
                // 获取订座旅客姓名列表
                List<MnjxPnrNm> nmList = iMnjxPnrNmService.lambdaQuery()
                        .in(MnjxPnrNm::getPnrId, segList.stream().map(MnjxPnrSeg::getPnrId).collect(Collectors.toList()))
                        .list();
                List<String> nmIdList = nmList.stream()
                        .map(MnjxPnrNm::getPnrNmId)
                        .collect(Collectors.toList());
                // 获取当前航节计划对应的订座航段序号下的值机或登机旅客
                List<MnjxPsgCki> dbAccGtPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                        .in(MnjxPsgCki::getPnrNmId, nmIdList)
                        .in(MnjxPsgCki::getCkiStatus, Constant.CKI_STATUS_ACC, Constant.CKI_STATUS_GT)
                        .list();
                List<MnjxPsgCki> accGtPsgCkiList = new ArrayList<>();
                for (MnjxPnrSeg pnrSeg : segList) {
                    List<String> filterNmIdList = nmList.stream()
                            .filter(n -> pnrSeg.getPnrId().equals(n.getPnrId()))
                            .map(MnjxPnrNm::getPnrNmId)
                            .collect(Collectors.toList());
                    // 获取当前航节计划对应的订座航段序号下的值机或登机旅客
                    for (String nmId : filterNmIdList) {
                        accGtPsgCkiList.addAll(dbAccGtPsgCkiList.stream()
                                .filter(p -> nmId.equals(p.getPnrNmId()) && pnrSeg.getPnrSegNo().toString().equals(p.getPnrSegNo()))
                                .collect(Collectors.toList()));
                    }
                }
                if (CollUtil.isNotEmpty(accGtPsgCkiList)) {
                    accGtPsgCkiList = accGtPsgCkiList.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    int totalWeight = this.getPlanSectionTotalWeight(nmIdList, nmList, accGtPsgCkiList);
                    if (totalWeight + newWeight > 200000) {
                        // 联程时报错
                        if (paResultDtos.stream().anyMatch(p -> CollUtil.isNotEmpty(p.getOList()) || p.isTypeO())) {
                            if (StrUtil.startWithAny(cmd, Constant.CMD_PU, Constant.HBPU_CODE)) {
                                throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                            } else {
                                throw new UnifiedResultException(Constant.UNABLE_TRANSFER);
                            }
                        }
                        // 非联程报错
                        // 带行李参数
                        if (paResultDtos.stream().anyMatch(p -> StrUtil.isNotEmpty(p.getBagWeight())) || haveLuggage) {
                            if (StrUtil.startWithAny(cmd, Constant.CMD_PU, Constant.HBPU_CODE)) {
                                throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                            } else {
                                throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                            }
                        }
                        // 不带行李参数
                        else {
                            throw new UnifiedResultException(Constant.MODIFICATION_WOULD_CAUSE_STANDBYING_WTL);
                        }
                    }
                }
            }
        }
        paResultDtos.stream()
                .filter(p -> StrUtil.isEmpty(p.getBagNumber()) && StrUtil.isNotEmpty(p.getBagWeight()))
                .forEach(p -> p.setBagWeight(null));
    }

    /**
     * Title: getPlanSectionTotalWeight
     * Description: 获取当前航节计划中已接收或登机的旅客及行李总重量<br>
     *
     * @param nmIdList
     * @param nmList
     * @param accGtPsgCkiList
     * @return {@link int}
     * <AUTHOR>
     * @date 2024/4/28 13:22
     */
    private int getPlanSectionTotalWeight(List<String> nmIdList, List<MnjxPnrNm> nmList, List<MnjxPsgCki> accGtPsgCkiList) {
        int totalWeight = 0;
        nmIdList.clear();
        nmIdList.addAll(accGtPsgCkiList.stream()
                .map(MnjxPsgCki::getPnrNmId)
                .collect(Collectors.toList()));
        // 重新设置已值机或登机旅客的姓名列表
        nmList = nmList.stream()
                .filter(n -> nmIdList.contains(n.getPnrNmId()))
                .collect(Collectors.toList());
        // 获取儿童
        List<MnjxNmSsr> chldSsrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, nmIdList)
                .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_CHLD)
                .list();
        if (CollUtil.isNotEmpty(chldSsrList)) {
            List<String> chldNmIdList = chldSsrList.stream()
                    .map(MnjxNmSsr::getPnrNmId)
                    .collect(Collectors.toList());
            List<MnjxPnrNm> chldNmList = nmList.stream()
                    .filter(n -> chldNmIdList.contains(n.getPnrNmId()))
                    .collect(Collectors.toList());
            totalWeight += chldNmList.size() * Constant.CHILD_WEIGHT;
            nmList = nmList.stream()
                    .filter(n -> !chldNmIdList.contains(n.getPnrNmId()))
                    .collect(Collectors.toList());
        }
        // 获取成人
        totalWeight += nmList.size() * Constant.ADULT_WEIGHT;
        // 获取携带的婴儿
        List<MnjxNmXn> xnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, nmIdList)
                .list();
        if (CollUtil.isNotEmpty(xnList)) {
            totalWeight += xnList.size() * Constant.INFANT_WEIGHT;
        }
        // 获取携带的行李
        List<MnjxLuggage> luggageList = iMnjxLuggageService.lambdaQuery()
                .in(MnjxLuggage::getPnrNmId, nmIdList)
                .and(l -> l.isNull(MnjxLuggage::getIsDel).or().ne(MnjxLuggage::getIsDel, Constant.DELETE_TYPE))
                .list();
        if (CollUtil.isNotEmpty(luggageList)) {
            // 重量池
            List<MnjxLuggage> weightPoolLuggageList = luggageList.stream()
                    .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(weightPoolLuggageList)) {
                // 每个旅客的重量池重量
                Map<String, List<MnjxLuggage>> weightPoolMap = weightPoolLuggageList.stream()
                        .collect(Collectors.groupingBy(MnjxLuggage::getPnrNmId));
                for (Map.Entry<String, List<MnjxLuggage>> weightPoolEntry : weightPoolMap.entrySet()) {
                    totalWeight += weightPoolEntry.getValue().get(0).getWeightPool();
                }
            }
            // 非重量池
            // 单件行李
            List<MnjxLuggage> singleLuggageList = luggageList.stream()
                    .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                    .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                    .filter(l -> {
                        String pnrSegNo = accGtPsgCkiList.stream()
                                .filter(p -> l.getPnrNmId().equals(p.getPnrNmId()))
                                .collect(Collectors.toList())
                                .get(0)
                                .getPnrSegNo();
                        return pnrSegNo.equals(l.getBagSegNo());
                    })
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(singleLuggageList)) {
                totalWeight += singleLuggageList.stream()
                        .mapToInt(MnjxLuggage::getLuggageWeight)
                        .sum();
            }
            // 组合行李
            List<MnjxLuggage> groupLuggageList = luggageList.stream()
                    .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                    .filter(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))
                    .filter(l -> {
                        String pnrSegNo = accGtPsgCkiList.stream()
                                .filter(p -> l.getPnrNmId().equals(p.getPnrNmId()))
                                .collect(Collectors.toList())
                                .get(0)
                                .getPnrSegNo();
                        return pnrSegNo.equals(l.getBagSegNo());
                    })
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(groupLuggageList)) {
                Map<String, List<MnjxLuggage>> groupLuggageMap = groupLuggageList.stream()
                        .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                for (Map.Entry<String, List<MnjxLuggage>> groupEntry : groupLuggageMap.entrySet()) {
                    totalWeight += groupEntry.getValue().get(0).getLuggageWeight();
                }
            }
        }
        return totalWeight;
    }

    private void setOlinkBagOperateRecord(PaResultDto paResultDto, MnjxPsgCki psgCki, List<MnjxPsgOperateRecord> psgOperateRecordList, List<MnjxLuggage> newLuggageList) {
        MnjxPsgOperateRecord bagRecord = this.constructOperateRecord("BAG", null, psgCki.getPsgCkiId(), paResultDto, newLuggageList);
        psgOperateRecordList.add(bagRecord);
    }

    private void handleFoid(String foidParam, List<MnjxNmSsr> ssrList, PaResultDto paResultDto) throws UnifiedResultException {
        String[] split = foidParam.split(StrUtil.SLASH);
        String foidType = split[1].substring(0, 2);
        String certificateNumber = split[1].substring(2);
        // 验证证件类型
        if (!StrUtil.equalsAny(foidType, Constant.CERTIFICATE_TYPE_NI, Constant.CERTIFICATE_TYPE_ID, Constant.CERTIFICATE_TYPE_PP)) {
            throw new UnifiedResultException(Constant.INVALID_FOID);
        }
        // 验证号码长度
        if (certificateNumber.length() > Constant.THIRTY) {
            throw new UnifiedResultException(Constant.LENGTH);
        }
        // 验证身份证号格式
        if (foidType.equals(Constant.CERTIFICATE_TYPE_NI) && !ReUtil.isMatch(Constant.CERTIFICATE_PATTERN, certificateNumber)) {
            throw new UnifiedResultException(Constant.INVALID_FOID);
        }
        // 验证身份证有效性
        if (Constant.FOID_TYPE_NI.equals(foidType)) {
            iSsrService.validateCertificateNumber(certificateNumber);
        }
        List<MnjxNmSsr> foidSsrList = ssrList.stream()
                .filter(s -> Constant.SSR_TYPE_FOID.equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(foidSsrList)) {
            MnjxNmSsr foidSsr = new MnjxNmSsr();
            foidSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            foidSsr.setPnrNmId(paResultDto.getPnrNmId());
            foidSsr.setSsrType(Constant.SSR_TYPE_FOID);
            foidSsr.setActionCode(Constant.HK);
            foidSsr.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));
            String newInputValue = StrUtil.format("SSR FOID {} HK1 {}/P1", paResultDto.getFlightNo().substring(0, 2), split[1]);
            foidSsr.setSsrInfo(newInputValue);
            foidSsr.setInputValue(newInputValue);
            iAspectCkiService.insertSsr(foidSsr, paResultDto.getPnrNmId());
//            iMnjxNmSsrService.save(foidSsr);
        } else {
            MnjxNmSsr foidSsr = foidSsrList.get(0);
            String oldInputValue = foidSsr.getInputValue();
            String[] foidSplit = oldInputValue.split(StrUtil.SPACE);
            String newInputValue = StrUtil.format("SSR FOID {} HK1 {}/{}", foidSplit[2], split[1], foidSplit[4].split(StrUtil.SLASH)[1]);
            foidSsr.setInputValue(newInputValue);
            foidSsr.setSsrInfo(newInputValue);
            iMnjxNmSsrService.updateById(foidSsr);
        }
    }

    /**
     * Title: handlePnrCt
     * Description: 处理电话数据
     *
     * @param paResultDto paResultDto
     * @param pnrId       pnrId
     * <AUTHOR>
     */
    @Override
    public void handlePnrCt(PaResultDto paResultDto, String pnrId, MnjxPsgCki psgCki) {
        MnjxPnr pnr = paResultDto.getPnr();
        List<MnjxPnrCt> ctList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnrId)
                .list();
        if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
            for (MnjxPnrCt ct : ctList) {
                this.insertPnrRecord(paResultDto, "CT", ct.getInputValue(), "X");
            }
        }
//        iMnjxPnrCtService.lambdaUpdate()
//                .eq(MnjxPnrCt::getPnrId, pnrId)
//                .remove();
        iAspectCkiService.deletePnrCtList(ctList, paResultDto.getPnrNmId());
        MnjxPnrCt mnjxPnrCt = new MnjxPnrCt();
        mnjxPnrCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxPnrCt.setPnrId(pnrId);
        mnjxPnrCt.setCityCode(paResultDto.getDstAirport());
        mnjxPnrCt.setCtText(paResultDto.getCtc());
        mnjxPnrCt.setInputValue(paResultDto.getCtc());
        mnjxPnrCt.setPnrIndex(pnr.getMaxIndex());
//        iMnjxPnrCtService.save(mnjxPnrCt);
        iAspectCkiService.insertPnrCt(mnjxPnrCt, paResultDto.getPnrNmId());
        if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
            this.insertPnrRecord(paResultDto, "CT", paResultDto.getCtc(), null);
        }
        pnr.setMaxIndex(pnr.getMaxIndex() + 1);
        iMnjxPnrService.updateById(pnr);
        paResultDto.setPnr(pnr);
    }

    /**
     * Title: handleSeatData
     * Description: 处理座位数据
     *
     * @param psgCkiId    psgCkiId
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    @Override
    public void handleSeatData(String psgCkiId, PaResultDto paResultDto) throws UnifiedResultException {
        String newSeatNo = paResultDto.getSeatNo();
        // HBPA预接收特服选项时，没有输入座位号就不自动分配座位
        if ((StrUtil.isEmpty(newSeatNo) && paResultDto.isAdv()) || StrUtil.isNotEmpty(paResultDto.getJmp()) || StrUtil.isNotEmpty(paResultDto.getStcr())) {
            return;
        }
        // 是否预留了P座位或V座位或D座，或是否是候补旅客先添加了座位，然后添加GS限额进行接收
        MnjxPsgSeat psgSeat = paResultDto.getMnjxPsgSeat();
        if (!paResultDto.isAdv() && StrUtil.isNotEmpty(psgSeat.getPsgSeat()) && (StrUtil.equalsAny(psgSeat.getSeatStatus(), Constant.SEAT_STATUS_P, Constant.SEAT_STATUS_V, Constant.SEAT_STATUS_D) || paResultDto.isDlOrNaccToAcc())) {
            newSeatNo = psgSeat.getPsgSeat();
            if (!paResultDto.isSnr()) {
                paResultDto.setNormalSeat(true);
            }
        }

        List<MnjxPlanSection> planSections = paResultDto.getPlanSectionList();
        List<String> planSectionIdList = planSections.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());

        // 再检查该座位在座位表是否被其他操作锁定
        List<MnjxOpenCabin> openCabinList = paResultDto.getCabinClassOpenCabinList();
        if (StrUtil.isEmpty(newSeatNo)) {
            this.getRandomAllowSeat(openCabinList, planSections.size(), paResultDto, new HashMap<>(), null);
            newSeatNo = paResultDto.getSeatNo();
        }
        // 当额外占座时，不用再次走确认座位逻辑
        if (!paResultDto.isHaveExst()) {
            this.setCorrectSeats(openCabinList, null, newSeatNo, planSections.size(), paResultDto, new HashMap<>(1024));
        }
        if (StrUtil.isNotEmpty(paResultDto.getSeatNo()) && !paResultDto.getSeatNo().equals(newSeatNo)) {
            newSeatNo = paResultDto.getSeatNo();
        }

        List<MnjxSeat> seatList;
        // 之前的座位号
        String oldSeatNo = psgSeat.getPsgSeat();
        // 如果不为空，则设置之前的座位号
        if (StrUtil.isNotEmpty(oldSeatNo) && !oldSeatNo.equals(newSeatNo)) {
            psgSeat.setBeforeSeat(oldSeatNo);
            // 修改seat表数据，将之前的座位释放出来，状态改回原来的状态
            seatList = iMnjxSeatService.lambdaQuery()
                    .eq(MnjxSeat::getSeatNo, oldSeatNo)
                    .ne(MnjxSeat::getSeatStatus, "T")
                    .in(MnjxSeat::getPlanSectionId, planSectionIdList)
                    .list();
            seatList.forEach(s -> {
                String seatStatusOld = s.getSeatStatusOld();
                String seatStatus = s.getSeatStatus();
                s.setSeatStatus(seatStatusOld);
                s.setSeatStatusOld(seatStatus);
            });
            if (!paResultDto.isDlOrNaccToSb()) {
//                iMnjxSeatService.updateBatchById(seatList);
                // 此处需要调用记录还原
                iAspectCkiService.updateSeatList(seatList, paResultDto.getPnrNmId());
            }
        }
        // 修改新座位号和座位状态
        psgSeat.setPsgSeat(newSeatNo);
        String seatStatus;
        if (paResultDto.isAdv()) {
            if (StrUtil.isEmpty(paResultDto.getJcr()) && StrUtil.isEmpty(paResultDto.getJcs())) {
                seatStatus = Constant.SEAT_STATUS_P;
            } else if ("JCS".equals(paResultDto.getJcs())) {
                seatStatus = Constant.SEAT_STATUS_D;
            } else {
                seatStatus = Constant.SEAT_STATUS_V;
            }
        } else {
            seatStatus = StrUtil.DOT;
        }
        seatList = iMnjxSeatService.lambdaQuery()
                .eq(MnjxSeat::getSeatNo, newSeatNo)
                .in(MnjxSeat::getPlanSectionId, planSectionIdList)
                .list();
        String finalSeatStatus = seatStatus;
        seatList.forEach(s -> {
            String oldSeatStatus = s.getSeatStatus();
            s.setSeatStatus(finalSeatStatus);
            s.setSeatStatusOld(oldSeatStatus);
        });
        // 此处需要调用记录还原
        if (!paResultDto.isDlOrNaccToSb()) {
//            iMnjxSeatService.updateBatchById(seatList);
            iAspectCkiService.updateSeatList(seatList, paResultDto.getPnrNmId());
        }
        paResultDto.setSeatNo(newSeatNo);
        psgSeat.setSeatStatus(seatStatus);
        iMnjxPsgSeatService.updateById(psgSeat);
    }

    /**
     * Title: handleNmData
     * Description: 处理旅客NM表数据
     *
     * @param paResultDto paResultDto
     * @param pnrSeg      pnrSeg
     * @param psgCki      psgCki
     * <AUTHOR>
     */
    @Override
    public void handleNmData(PaResultDto paResultDto, MnjxPnrSeg pnrSeg, MnjxPsgCki psgCki) {
        String pnrNmId = paResultDto.getPnrNmId();
        MnjxPnrNm pnrNm = paResultDto.getPnrNm();
        MnjxPnr pnr = paResultDto.getPnr();
        // 更新旅客类型
        if (paResultDto.isHasChd()) {
            iMnjxPnrNmService.lambdaUpdate()
                    .eq(MnjxPnrNm::getPnrNmId, pnrNmId)
                    .set(MnjxPnrNm::getPsgType, Constant.CHILD_TYPE)
                    .update();
            pnrNm.setPsgType(Constant.CHILD_TYPE);
        }
        if (paResultDto.isHasUm()) {
            iMnjxPnrNmService.lambdaUpdate()
                    .eq(MnjxPnrNm::getPnrNmId, pnrNmId)
                    .set(MnjxPnrNm::getPsgType, Constant.UM_TYPE)
                    .update();
            pnrNm.setPsgType(Constant.UM_TYPE);
            MnjxPnrNmUm nmUm = new MnjxPnrNmUm();
            nmUm.setNmUmId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmUm.setPnrNmId(pnrNmId);
            nmUm.setUmAge(Integer.parseInt(paResultDto.getUmAge()));
            MnjxPnrNmUm dbUm = iMnjxPnrNmUmService.lambdaQuery()
                    .eq(MnjxPnrNmUm::getPnrNmId, pnrNmId)
                    .one();
            if (ObjectUtil.isNotEmpty(dbUm)) {
                dbUm.setUmAge(nmUm.getUmAge());
                iAspectCkiService.updatePnrNmUm(dbUm, pnrNmId);
//                iMnjxPnrNmUmService.updateById(dbUm);
            } else {
                iAspectCkiService.insertPnrNmUm(nmUm, pnrNmId);
//                iMnjxPnrNmUmService.save(nmUm);
            }
        }
        MnjxNmXn xnDb = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                .one();
        if (paResultDto.isHasInf()) {
            iMnjxPnrNmService.lambdaUpdate()
                    .eq(MnjxPnrNm::getPnrNmId, pnrNmId)
                    .set(MnjxPnrNm::getPsgType, Constant.INF_TYPE)
                    .update();
            pnrNm.setPsgType(Constant.INF_TYPE);
            if (ObjectUtil.isEmpty(xnDb)) {
                Integer maxPnrIndex = pnr.getMaxIndex();
                // 同时还需要生成XN信息
                MnjxNmXn mnjxNmXn = new MnjxNmXn();
                mnjxNmXn.setNmXnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                mnjxNmXn.setPnrNmId(pnrNmId);
                mnjxNmXn.setXnCname(paResultDto.getInfName());
                String comInfBirth = paResultDto.getInfBirth();
                if (StrUtil.isEmpty(comInfBirth)) {
                    // 婴儿年龄默认3个月前
                    Calendar nowCal = Calendar.getInstance();
                    nowCal.add(Calendar.MONTH, -3);
                    Date date = nowCal.getTime();
                    String ymdDate = DateUtils.date2ymd(date);
                    comInfBirth = DateUtils.ymd2Com(ymdDate);
                    paResultDto.setInfBirth(comInfBirth);
                    mnjxNmXn.setXnBirthday(ymdDate.substring(0, 7));
                }
                String inputValue = StrUtil.format("XN/IN/{} INF({})/P{}", paResultDto.getInfName(), comInfBirth.substring(2), pnrNm.getPsgIndex());

                mnjxNmXn.setInputValue(inputValue);
                mnjxNmXn.setPnrIndex(maxPnrIndex + 1);
                iAspectCkiService.insertXn(mnjxNmXn, pnrNmId);
//                iMnjxNmXnService.save(mnjxNmXn);
                if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
                    this.insertPnrRecord(paResultDto, "XN", inputValue, null);
                }
                maxPnrIndex++;
                // 生成SSR INFT信息
                this.insertSsr("INFT", maxPnrIndex + 1, paResultDto, pnrSeg, "INFT", psgCki);
                maxPnrIndex++;
                // 生成OSI YY INF信息
                MnjxNmOsi mnjxNmOsi = new MnjxNmOsi();
                mnjxNmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                mnjxNmOsi.setPnrNmId(pnrNmId);
                mnjxNmOsi.setPnrOsiInfo(StrUtil.format("OSI YY 1INF {} INF/P{}", paResultDto.getInfName(), pnrNm.getPsgIndex()));
                mnjxNmOsi.setInputValue(mnjxNmOsi.getPnrOsiInfo());
                mnjxNmOsi.setPnrIndex(maxPnrIndex + 1);
                iAspectCkiService.insertNmOsi(mnjxNmOsi, pnrNmId);
//                iMnjxNmOsiService.save(mnjxNmOsi);
                if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
                    this.insertPnrRecord(paResultDto, "NM OSI", mnjxNmOsi.getInputValue(), null);
                }
                maxPnrIndex++;
                // 生成婴儿票号信息
                // TN
                MnjxAirline mnjxAirline = iMnjxAirlineService.lambdaQuery()
                        .eq(MnjxAirline::getAirlineCode, pnrSeg.getFlightNo().substring(0, 2))
                        .one();
                // 获取航司结算码
                String settlementCode = mnjxAirline.getAirlineSettlementCode();
                MnjxPnrNmTn tn = iMnjxPnrNmTnService.lambdaQuery()
                        .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                        .one();
                // 白屏F12添加的候补旅客携带的婴儿，不涉及出票信息，需要判断从成人获取的tn查询的打票机是否存在，票号初始化为null
                MnjxPrinter printer = iMnjxPrinterService.getById(tn.getPrinterId());
                String ticketNo = null;
                BigInteger lastTicketNo = null;
                if (ObjectUtil.isNotEmpty(printer)) {
                    BigInteger issueTicket = printer.getLastTicket();
                    lastTicketNo = issueTicket.add(BigInteger.valueOf(1));
                    ticketNo = StrUtil.format("{}{}", settlementCode, lastTicketNo);
                    printer.setLastTicket(lastTicketNo);
                }
                MnjxPnrNmTn mnjxPnrNmTn = new MnjxPnrNmTn();
                mnjxPnrNmTn.setTnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                mnjxPnrNmTn.setNmXnId(mnjxNmXn.getNmXnId());
                mnjxPnrNmTn.setPnrIndex(maxPnrIndex + 1);
                mnjxPnrNmTn.setPrinterId(tn.getPrinterId());
                mnjxPnrNmTn.setIssuedTime(tn.getIssuedTime());
                mnjxPnrNmTn.setIssuedSiId(tn.getIssuedSiId());
                mnjxPnrNmTn.setIssuedAirline(tn.getIssuedAirline());
                mnjxPnrNmTn.setInputValue(StrUtil.format("TN/IN/{}-{}/P1", settlementCode, lastTicketNo));
                iAspectCkiService.insertPnrNmTn(mnjxPnrNmTn, pnrNmId);
                // NM_TICKET
                List<String> pnrSegIdList = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getFlightNo, pnrSeg.getFlightNo())
                        .eq(MnjxPnrSeg::getFlightDate, pnrSeg.getFlightDate())
                        .list()
                        .stream()
                        .map(MnjxPnrSeg::getPnrSegId)
                        .collect(Collectors.toList());
                List<MnjxPnrNmTicket> ticketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getS1Id, pnrSegIdList)
                        .orderByDesc(MnjxPnrNmTicket::getHbnb1)
                        .list();
                int hbNb1 = Integer.parseInt(ticketList.get(0).getHbnb1()) + 1;
                MnjxPnrNmTicket mnjxPnrNmTicket = new MnjxPnrNmTicket();
                mnjxPnrNmTicket.setNmTicketId(IdUtil.getSnowflake(1, 1).nextIdStr());
                mnjxPnrNmTicket.setPnrNmTnId(mnjxPnrNmTn.getTnId());
                mnjxPnrNmTicket.setHbnb1(StrUtil.fill(StrUtil.toString(hbNb1), '0', 4, true));
                mnjxPnrNmTicket.setTicketNo(ticketNo);
                mnjxPnrNmTicket.setTicketStatus1(Constant.TICKET_STATUS_OPEN_FOR_USE);
                mnjxPnrNmTicket.setReceiptPrint(Constant.STR_ZERO);
                mnjxPnrNmTicket.setS1Id(pnrSeg.getPnrSegId());
                mnjxPnrNmTicket.setIsEt(Constant.STR_ONE);
                iAspectCkiService.insertPnrNmTicket(mnjxPnrNmTicket, mnjxPnrNmTn.getTnId());
                if (ObjectUtil.isNotEmpty(printer)) {
                    iAspectCkiService.updatePrinter(printer);
                }
                maxPnrIndex++;
                pnr.setMaxIndex(maxPnrIndex);
            } else {
                // 存在则修改婴儿姓名
                MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrRecord::getPnrType, "XN")
                        .eq(MnjxPnrRecord::getInputValue, xnDb.getInputValue())
                        .one();
                if (paResultDto.isTypeO()) {
                    this.insertSsr("INFT", pnr.getMaxIndex() + 1, paResultDto, pnrSeg, "INFT", psgCki);
                    pnr.setMaxIndex(pnr.getMaxIndex() + 1);
                }
                if (StrUtil.isEmpty(psgCki.getUres()) && StrUtil.isEmpty(psgCki.getNrec())) {
                    String oldName = xnDb.getXnCname();
                    String inputValue = xnDb.getInputValue().replace(StrUtil.format("XN/IN/{}", oldName), StrUtil.format("XN/IN/{}", paResultDto.getInfName()));
                    xnDb.setInputValue(inputValue);
                    xnDb.setXnCname(paResultDto.getInfName());
//                    iMnjxNmXnService.updateById(xnDb);
                    iAspectCkiService.updateNmXn(xnDb, pnrNmId);
                    record.setInputValue(inputValue);
//                    iMnjxPnrRecordService.updateById(record);
                    iAspectCkiService.updatePnrRecord(record, pnrNmId);
                }
            }
        } else if (ObjectUtil.isNotEmpty(xnDb)) {
            MnjxNmSsr inft = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                    .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo())
                    .eq(MnjxNmSsr::getSsrType, "INFT")
                    .one();
            if (ObjectUtil.isNotEmpty(inft)) {
                paResultDto.setInfName(xnDb.getXnCname());
            }
        }
        // 更新性别
        if (StrUtil.isNotEmpty(paResultDto.getSex())) {
            iMnjxPnrNmService.lambdaUpdate()
                    .eq(MnjxPnrNm::getPnrNmId, pnrNmId)
                    .set(MnjxPnrNm::getSex, paResultDto.getSex())
                    .update();
            pnrNm.setSex(paResultDto.getSex());
        }
        // VIP
        List<MnjxNmOsi> nmOsiList = iMnjxNmOsiService.lambdaQuery()
                .eq(MnjxNmOsi::getPnrNmId, pnrNmId)
                .list();
        long count = nmOsiList.stream()
                .filter(o -> StrUtil.isNotEmpty(o.getPnrOsiType()) && Constant.VIP_TYPE.equals(o.getPnrOsiType()))
                .count();
        // 如果有vip，不进行新增修改操作
        if (count > 0) {
            if (!paResultDto.isVip()) {
                paResultDto.setVip(true);
            }
        } else {
            // 没有VIP，选项中有VIP，则新增一个OSI VIP项
            if (paResultDto.isVip()) {
                MnjxNmOsi nmOsi = new MnjxNmOsi();
                nmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmOsi.setPnrNmId(pnrNmId);
                nmOsi.setPnrIndex(pnr.getMaxIndex() + 1);
                nmOsi.setAirlineCode(paResultDto.getAirlineCode());
                String info = StrUtil.format("OSI {} VIP/P{}", paResultDto.getAirlineCode(), pnrNm.getPsgIndex());
                nmOsi.setPnrOsiInfo(info);
                nmOsi.setInputValue(info);
                nmOsi.setPnrOsiType(Constant.VIP_TYPE);
                pnr.setMaxIndex(pnr.getMaxIndex() + 1);
                iAspectCkiService.insertNmOsi(nmOsi, pnrNmId);
//                iMnjxNmOsiService.save(nmOsi);
                if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
                    this.insertPnrRecord(paResultDto, "NM OSI", info, null);
                }
            }
        }
        iMnjxPnrService.updateById(pnr);
        paResultDto.setPnr(pnr);
        paResultDto.setPnrNm(pnrNm);
    }

    /**
     * Title: handlePreDngUpg
     * Description: 处理预升降舱
     *
     * @param paResultDto           paResultDto
     * @param psgCki                psgCki
     * @param pnrSeg                pnrSeg
     * @param mnjxPsgOperateRecords mnjxPsgOperateRecords
     * <AUTHOR>
     */
    private void handlePreDngUpg(PaResultDto paResultDto, MnjxPsgCki psgCki, MnjxPnrSeg pnrSeg, List<MnjxPsgOperateRecord> mnjxPsgOperateRecords, Map<String, String> gsMap) throws UnifiedResultException {
        if (StrUtil.isEmpty(paResultDto.getGType())) {
            return;
        }

        String currentCabinClass = paResultDto.getCabinClass();
        String gCabinClass = paResultDto.getGCabinClass();
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, paResultDto.getFlightNo())
                .one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, paResultDto.getFlightDate())
                .one();
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, pnrSeg.getOrg())
                .one();
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, pnrSeg.getDst())
                .one();
        MnjxCnd cnd = iMnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getCndNo, planFlight.getCndNo())
                .one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .list();
        // 筛选符合的航节。多航段时将多个航节一起处理
        List<MnjxPlanSection> filterPlanSectionList = planSectionList.stream()
                .filter(p -> orgAirport.getAirportId().equals(p.getDepAptId()) && dstAirport.getAirportId().equals(p.getArrAptId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(filterPlanSectionList)) {
            for (MnjxPlanSection planSection : planSectionList) {
                if (orgAirport.getAirportId().equals(planSection.getDepAptId())) {
                    filterPlanSectionList.add(planSection);
                } else if (dstAirport.getAirportId().equals(planSection.getArrAptId())) {
                    filterPlanSectionList.add(planSection);
                    break;
                } else if (CollUtil.isNotEmpty(filterPlanSectionList)) {
                    filterPlanSectionList.add(planSection);
                }
            }
        }
        List<String> planSectionIdList = filterPlanSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        List<MnjxOpenCabin> gOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .eq(MnjxOpenCabin::getCabinClass, gCabinClass)
                .list();
        List<MnjxOpenCabin> currentOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .eq(MnjxOpenCabin::getCabinClass, currentCabinClass)
                .list();
        // 设置座位号
        if (StrUtil.isNotEmpty(paResultDto.getSeatNo())) {
            this.setCorrectSeats(gOpenCabinList, null, paResultDto.getSeatNo(), filterPlanSectionList.size(), paResultDto, new HashMap<>(1024));
        }
        // 验证舱等
        this.validateUpgDngSellCabin(cnd, paResultDto);
        // 如果候补旅客预升降舱同时输入了座位号，需要消耗GS
        if (Constant.STR_ONE.equals(psgCki.getIsHb()) && paResultDto.isUpgnInputSeat()) {
            this.updateGsLimit(filterPlanSectionList, paResultDto, gsMap);
        }
        // 升降舱数据处理
        this.handlePreUpgDngData(gOpenCabinList, currentOpenCabinList, paResultDto, pnrSeg, psgCki, mnjxPsgOperateRecords);
    }

    /**
     * Title: updateGsLimit
     * Description: 更新GS限额
     *
     * @param mnjxPlanSections mnjxPlanSections
     * @param paResultDto      paResultDto
     * <AUTHOR>
     */
    private void updateGsLimit(List<MnjxPlanSection> mnjxPlanSections, PaResultDto paResultDto, Map<String, String> gsMap) {
        String gCabinClass = paResultDto.getGCabinClass();
        if (StrUtil.isEmpty(gCabinClass)) {
            List<MnjxNmSsr> gSsrList = paResultDto.getSsrList().stream()
                    .filter(s -> s.getSsrType().startsWith("UPG") || s.getSsrType().startsWith("DNG"))
                    .collect(Collectors.toList());
            String upgDng = gSsrList.get(gSsrList.size() - 1).getSsrType();
            gCabinClass = upgDng.substring(upgDng.length() - 1);
        }
        String finalUpgDngCabinClass = gCabinClass;
        mnjxPlanSections.forEach(f -> {
            StringBuilder sb = new StringBuilder();
            String goshowLimit = f.getGoshowLimit();
            if (StrUtil.isNotEmpty(goshowLimit)) {
                if (!gsMap.containsKey(f.getPlanSectionId())) {
                    gsMap.put(f.getPlanSectionId(), goshowLimit);
                } else {
                    goshowLimit = gsMap.get(f.getPlanSectionId());
                }
            }
            String[] split = goshowLimit.split(StrUtil.SLASH);
            boolean hasUpdateCabinClassGs = false;
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                String gsCabinClass = s.substring(0, 1);
                String gsNum = s.substring(1);
                if (gsCabinClass.equals(finalUpgDngCabinClass)) {
                    int gs = Integer.parseInt(gsNum) - 1;
                    s = StrUtil.format("{}{}", gsCabinClass, StrUtil.fill(StrUtil.toString(gs), '0', 3, true));
                } else if (gsCabinClass.equals(paResultDto.getCabinClass())) {
                    hasUpdateCabinClassGs = true;
                    int gs;
                    if (gsNum.startsWith(StrUtil.DASHED)) {
                        gs = (-Integer.parseInt(gsNum.substring(1))) + 1;
                    } else {
                        gs = Integer.parseInt(gsNum.substring(1)) + 1;
                    }
                    if (gs > 0) {
                        s = StrUtil.format("{}{}", gsCabinClass, StrUtil.fill(StrUtil.toString(gs), '0', 3, true));
                    } else {
                        s = StrUtil.format("{}-{}", gsCabinClass, StrUtil.fill(StrUtil.toString(gs), '0', 3, true));
                    }
                }
                sb.append(s);
                if (i < split.length - 1) {
                    sb.append(StrUtil.SLASH);
                }
            }
            if (!hasUpdateCabinClassGs) {
                String s = StrUtil.format("{}{}", paResultDto.getCabinClass(), StrUtil.fill(Constant.STR_ONE, '0', 3, true));
                sb.append(StrUtil.SLASH);
                sb.append(s);
            }
            gsMap.put(f.getPlanSectionId(), sb.toString());
//            f.setGoshowLimit(sb.toString());
        });
//        iMnjxPlanSectionService.updateBatchById(mnjxPlanSections);
    }

    /**
     * Title: handlePreUpgDngData
     * Description: 预升降舱数据处理
     *
     * @param gOpenCabins       gOpenCabins
     * @param currentOpenCabins currentOpenCabins
     * @param paResultDto       paResultDto
     * @param pnrSeg            pnrSeg
     * @param psgCki            psgCki
     * @param psgOperateRecords psgOperateRecords
     * <AUTHOR>
     */
    private void handlePreUpgDngData(List<MnjxOpenCabin> gOpenCabins, List<MnjxOpenCabin> currentOpenCabins, PaResultDto paResultDto, MnjxPnrSeg pnrSeg, MnjxPsgCki psgCki, List<MnjxPsgOperateRecord> psgOperateRecords) {
        psgCki.setPreUpgnOperate(Constant.STR_ONE);
        if (Constant.UPG.equals(paResultDto.getGType())) {
            if (StrUtil.isEmpty(paResultDto.getVoluntary())) {
                psgCki.setUpgn("Y");
            } else {
                psgCki.setUpgn("ZY");
            }
        } else if (Constant.DNG.equals(paResultDto.getGType())) {
            if (StrUtil.isEmpty(paResultDto.getVoluntary())) {
                psgCki.setUpgn("N");
            } else {
                psgCki.setUpgn("ZN");
            }
        }
        MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                .one();
        // 之前执行过预接收座位或者预升降舱，存在座位号，需要还原以前的座位
        String oldSeat = psgSeat.getPsgSeat();
        if (StrUtil.isNotEmpty(oldSeat)) {
            // 还原seat表数据
            List<String> oldOpenCabinIdList = currentOpenCabins.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            List<MnjxSeat> oldSeatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, oldOpenCabinIdList)
                    .eq(MnjxSeat::getSeatNo, oldSeat)
                    .list();
            oldSeatList.forEach(o -> {
                String seatStatus = o.getSeatStatus();
                o.setSeatStatus("*");
                o.setSeatStatusOld(seatStatus);
            });
            iAspectCkiService.updateSeatList(oldSeatList, paResultDto.getPnrNmId());
//            iMnjxSeatService.updateBatchById(oldSeatList);
        }
        String gSeatNo = paResultDto.getSeatNo();
        if (StrUtil.isNotEmpty(gSeatNo)) {
            // 占用新座位的seat表数据更新
            List<String> gOpenCabinIdList = gOpenCabins.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            List<MnjxSeat> gSeatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, gOpenCabinIdList)
                    .eq(MnjxSeat::getSeatNo, gSeatNo)
                    .list();
            gSeatList.forEach(g -> {
                g.setSeatStatusOld(g.getSeatStatus());
                g.setSeatStatus("P");
            });
            iAspectCkiService.updateSeatList(gSeatList, paResultDto.getPnrNmId());
//            iMnjxSeatService.updateBatchById(gSeatList);

            // psg_seat更新新座位
            psgSeat.setBeforeSeat(psgSeat.getPsgSeat());
            psgSeat.setPsgSeat(gSeatNo);
            psgSeat.setSeatStatus("P");
            iMnjxPsgSeatService.updateById(psgSeat);
        }

        paResultDto.setCabinClass(paResultDto.getGCabinClass());
        paResultDto.setSellCabin(paResultDto.getGSellCabin());
        String oldSellCabin = psgCki.getSellCabin();
        psgCki.setCabinClass(paResultDto.getCabinClass());
        psgCki.setSellCabin(paResultDto.getSellCabin());
        iMnjxPsgCkiService.updateById(psgCki);

        // 新增SSR和该SSR的pnr_record记录
        String ssrType = StrUtil.format("{}{}{}", paResultDto.getGType(), oldSellCabin, StrUtil.emptyToDefault(paResultDto.getVoluntary(), ""));
        MnjxPnr pnr = iMnjxPnrService.getById(pnrSeg.getPnrId());
        Integer maxIndex = pnr.getMaxIndex();
        this.insertSsr(ssrType, maxIndex, paResultDto, pnrSeg, null, psgCki);

        // 产生值机操作记录
        Integer npaCount = iMnjxPsgOperateRecordService.lambdaQuery()
                .eq(MnjxPsgOperateRecord::getPsgCkiId, psgCki.getPsgCkiId())
                .eq(MnjxPsgOperateRecord::getOperateType, "NPA")
                .count();
        paResultDto.setNpaCount(npaCount);
        if (npaCount == 0) {
            psgOperateRecords.add(this.constructOperateRecord("NPA", null, psgCki.getPsgCkiId(), paResultDto, new ArrayList<>()));
            npaCount++;
            paResultDto.setNpaCount(npaCount);
        }
        psgOperateRecords.add(this.constructOperateRecord("NPA", null, psgCki.getPsgCkiId(), paResultDto, new ArrayList<>()));
    }

    /**
     * Title: validateUpgDngCabinClass
     * Description: 验证升降舱的舱等是否允许<br>
     *
     * @param cnd         cnd
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    private void validateUpgDngSellCabin(MnjxCnd cnd, PaResultDto paResultDto) throws UnifiedResultException {
        String currentSellCabin = paResultDto.getSellCabin();
        String gSellCabin = paResultDto.getGSellCabin();
        String gType = paResultDto.getGType();
        String firstCabinClass = cnd.getFirstCabinClass();
        String secondCabinClass = cnd.getSecondCabinClass();
        String thirdCabinClass = cnd.getThirdCabinClass();
        String fourthCabinClass = cnd.getFourthCabinClass();
        String fifthCabinClass = cnd.getFifthCabinClass();
        List<String> cabinClassList = new ArrayList<>();
        cabinClassList.add(firstCabinClass);
        cabinClassList.add(secondCabinClass);
        cabinClassList.add(thirdCabinClass);
        cabinClassList.add(fourthCabinClass);
        cabinClassList.add(fifthCabinClass);
        cabinClassList = CollUtil.removeEmpty(cabinClassList);
        String firstSellCabin = cnd.getFirstSellCabin();
        String secondSellCabin = cnd.getSecondSellCabin();
        String thirdSellCabin = cnd.getThirdSellCabin();
        String fourthSellCabin = cnd.getFourthSellCabin();
        String fifthSellCabin = cnd.getFifthSellCabin();
        List<String> sellCabinList = new ArrayList<>();
        sellCabinList.add(firstSellCabin);
        sellCabinList.add(secondSellCabin);
        sellCabinList.add(thirdSellCabin);
        sellCabinList.add(fourthSellCabin);
        sellCabinList.add(fifthSellCabin);
        sellCabinList = CollUtil.removeEmpty(sellCabinList);
        int currentIndex = 0;
        int gIndex = 0;
        for (int i = 0; i < sellCabinList.size(); i++) {
            String cndSellCabin = sellCabinList.get(i);
            if (cndSellCabin.contains(currentSellCabin)) {
                currentIndex = i;
            }
            if (cndSellCabin.contains(gSellCabin)) {
                gIndex = i;
                paResultDto.setGCabinClass(cabinClassList.get(i));
            }
        }
        switch (gType) {
            case Constant.DNG:
                if (currentIndex >= gIndex) {
                    throw new UnifiedResultException(Constant.INVALID_UPG_DNG);
                }
                break;
            case Constant.UPG:
                if (currentIndex <= gIndex) {
                    throw new UnifiedResultException(Constant.INVALID_UPG_DNG);
                }
                break;
            default:
                break;
        }
    }

    /**
     * Title: handleBagData
     * Description: 处理行李数据更新
     *
     * @param paResultDto          paResultDto
     * @param psgCki               psgCki
     * @param psgOperateRecordList psgOperateRecordList
     * <AUTHOR>
     */
    @Override
    public void handleBagData(PaResultDto paResultDto, MnjxPsgCki psgCki, List<MnjxPsgOperateRecord> psgOperateRecordList) throws UnifiedResultException {
        List<MnjxLuggage> xLuggageList = iMnjxLuggageService.lambdaQuery()
                .eq(MnjxLuggage::getPnrNmId, paResultDto.getPnrNmId())
                .eq(MnjxLuggage::getIsDel, "X")
                .list();
        // PA HBPA在接收的时候才会删除已标记为X的行李数据，PU HBPU添加新行李不删除数据
        if (CollUtil.isNotEmpty(xLuggageList) && StrUtil.isNotEmpty(paResultDto.getCmd()) && StrUtil.startWithAny(paResultDto.getCmd(), "PA", "HBPA")) {
            iAspectCkiService.deleteLuggageList(xLuggageList, paResultDto.getPnrNmId());
        }
        if ((StrUtil.isEmpty(paResultDto.getBagNumber()) && StrUtil.isEmpty(paResultDto.getBagWeight())) || paResultDto.isTypeO()) {
            return;
        }
        // PA预接收AVIH行李或PA正式接收（如果已有预接收的AVIH，正式接收也会覆盖掉已预接收的）时，如果之前预接收过AVIH的行李，需要先将以前的都删掉
        if (StrUtil.isNotEmpty(paResultDto.getCmd()) && StrUtil.startWithAny(paResultDto.getCmd(), "PA", "HBPA") && (paResultDto.isAdv() || StrUtil.equalsAny(psgCki.getCkiStatus(), Constant.CKI_STATUS_DL, Constant.CKI_STATUS_NACC))) {
            List<MnjxLuggage> avihLuggageList = iMnjxLuggageService.lambdaQuery()
                    .eq(MnjxLuggage::getPnrNmId, paResultDto.getPnrNmId())
                    .in(MnjxLuggage::getLuggageType, Constant.STR_THREE, Constant.STR_FOUR)
                    .list();
            if (CollUtil.isNotEmpty(avihLuggageList)) {
                iAspectCkiService.deleteLuggageList(avihLuggageList, paResultDto.getPnrNmId());
            }
        }
        String bagNumber = paResultDto.getBagNumber();
        String bagWeight = paResultDto.getBagWeight();
        int bagNum = Integer.parseInt(bagNumber);
        int weight = Integer.parseInt(bagWeight);
        List<MnjxLuggage> newLuggageList = new ArrayList<>();
        // 新增行李
        if (StrUtil.isNotEmpty(bagNumber) && StrUtil.isNotEmpty(bagWeight)) {
            String bagDst = paResultDto.getBagDst();
            // 确认添加的行李所属航段
            if (CollUtil.isNotEmpty(paResultDto.getInterlineResults())) {
                // 没有默认旅客航程最终目的地，即最后一段航班的到达地
                List<String> collect = paResultDto.getInterlineResults().stream()
                        .filter(r -> "O".equals(r.trim().substring(0, 1)))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(collect)) {
                    bagDst = StrUtil.isEmpty(bagDst) ? paResultDto.getDstAirport() : bagDst;
                    paResultDto.getBagSegNoList().add(StrUtil.toString(paResultDto.getMnjxPnrSeg().getPnrSegNo()));
                } else {
                    bagDst = StrUtil.isEmpty(bagDst) ? collect.get(paResultDto.getOList().size() - 1).trim().substring(collect.get(paResultDto.getOList().size() - 1).trim().length() - 3) : bagDst;
                    int segNo = paResultDto.getMnjxPnrSeg().getPnrSegNo();

                    List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getPnrId, paResultDto.getMnjxPnrSeg().getPnrId())
                            .isNotNull(MnjxPnrSeg::getFlightNo)
                            .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                            .list();
                    List<MnjxPnrSeg> oPnrSegList = new ArrayList<>();
                    boolean canAddOPnrSeg = false;
                    for (MnjxPnrSeg seg : pnrSegList) {
                        if (seg.getPnrSegId().equals(paResultDto.getMnjxPnrSeg().getPnrSegId())) {
                            canAddOPnrSeg = true;
                            continue;
                        }
                        if (canAddOPnrSeg) {
                            oPnrSegList.add(seg);
                        }
                    }

                    paResultDto.getBagSegNoList().add(StrUtil.toString(segNo));
                    if (CollUtil.isNotEmpty(paResultDto.getOList()) && !bagDst.equals(paResultDto.getDstAirport())) {
                        for (int i = 0; i < paResultDto.getOList().size(); i++) {
                            String io = collect.get(i);
                            paResultDto.getBagSegNoList().add(StrUtil.toString(oPnrSegList.get(i).getPnrSegNo()));
                            if (bagDst.equals(io.trim().substring(io.trim().length() - 3))) {
                                break;
                            }
                        }
                    }
                }
            } else {
                bagDst = StrUtil.isEmpty(bagDst) ? paResultDto.getDstAirport() : bagDst;
                paResultDto.getBagSegNoList().add(StrUtil.toString(paResultDto.getMnjxPnrSeg().getPnrSegNo()));
            }
            // 手工行李添加
            if (CollUtil.isNotEmpty(paResultDto.getLuggageNoList())) {
                String luggageGroupId = null;
                if (paResultDto.getLuggageNoList().size() > 1) {
                    luggageGroupId = IdUtil.getSnowflake(1, 1).nextIdStr();
                }
                for (String no : paResultDto.getLuggageNoList()) {
                    for (String bagSegNo : paResultDto.getBagSegNoList()) {
                        MnjxLuggage mnjxLuggage = new MnjxLuggage();
                        mnjxLuggage.setLuggageId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        mnjxLuggage.setPnrNmId(paResultDto.getPnrNmId());
                        mnjxLuggage.setLuggageGroupId(luggageGroupId);
                        mnjxLuggage.setLuggageNo(StrUtil.fill(no, '0', 10, true));
                        mnjxLuggage.setBagSegNo(bagSegNo);
                        mnjxLuggage.setDst(paResultDto.getBagDst());
                        mnjxLuggage.setLuggageWeight(weight);
                        mnjxLuggage.setDeliverTime(DateUtil.now());
                        mnjxLuggage.setDeliverResult(Constant.STR_ONE);
                        mnjxLuggage.setLuggageType(Constant.STR_TWO);
                        if (paResultDto.isAvih()) {
                            mnjxLuggage.setLuggageType(Constant.STR_FOUR);
                        }
                        newLuggageList.add(mnjxLuggage);
                    }
                }
                // 保存行李数据
                iAspectCkiService.insertLuggageList(newLuggageList, paResultDto.getPnrNmId());
            }
            // 系统行李添加
            else {
                newLuggageList = this.insertLuggages(psgCki, bagNum, weight, bagDst, paResultDto);
            }

            // 多目的地添加行李时，因为参数只在第一个航段产生并执行，需要查询行李出发到目的地涉及到的psgCki，对当前段和本次指令涉及到的出港联程段都添加行李操作记录
            List<String> bagSegNoList = paResultDto.getBagSegNoList().stream()
                    .sorted()
                    .collect(Collectors.toList());
            String minBagSegNo = bagSegNoList.get(0);
            String maxBagSegNo = bagSegNoList.get(bagSegNoList.size() - 1);
            List<MnjxPsgCki> psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                    .eq(MnjxPsgCki::getPnrNmId, paResultDto.getPnrNmId())
                    .ge(MnjxPsgCki::getPnrSegNo, minBagSegNo)
                    .le(MnjxPsgCki::getPnrSegNo, maxBagSegNo)
                    .list();
            for (MnjxPsgCki mnjxPsgCki : psgCkiList) {
                // 手工行李操作记录设置
                if (CollUtil.isNotEmpty(paResultDto.getLuggageNoList())) {
                    MnjxPsgOperateRecord bagRecord = null;
                    if (paResultDto.isAdv()) {
                        if (StrUtil.startWithAny(paResultDto.getCmd(), "PA", "HBPA")) {
                            paResultDto.setNpaCount(0);
                            bagRecord = this.constructOperateRecord("NPA", null, mnjxPsgCki.getPsgCkiId(), paResultDto, new ArrayList<>());
                            psgOperateRecordList.add(bagRecord);
                        } else if (StrUtil.startWithAny(paResultDto.getCmd(), "PU", "HBPU")) {
                            bagRecord = this.constructOperateRecord("MOD", null, mnjxPsgCki.getPsgCkiId(), paResultDto, new ArrayList<>());
                            bagRecord.setContent(bagRecord.getContent().replace("/PSM", "/AVIH"));
                            psgOperateRecordList.add(bagRecord);
                        }
                    } else {
                        if (StrUtil.equalsAny(mnjxPsgCki.getCkiStatus(), Constant.NACC, Constant.DL)) {
                            bagRecord = this.constructOperateRecord("ACC", null, mnjxPsgCki.getPsgCkiId(), paResultDto, newLuggageList);
                        } else {
                            bagRecord = this.constructOperateRecord("MOD", null, mnjxPsgCki.getPsgCkiId(), paResultDto, null);
                            if (paResultDto.isAvih()) {
                                bagRecord.setContent(bagRecord.getContent().replace("/PSM", "/AVIH"));
                            }
                        }
                        bagRecord.setContent(StrUtil.format("{}/{}/T", bagRecord.getContent(), paResultDto.getBagDst()));
                    }
                    if (ObjectUtil.isNotNull(bagRecord)) {
                        psgOperateRecordList.add(bagRecord);
                    }
                }
                // 系统行李操作记录设置
                else {
                    if (paResultDto.isAdv()) {
                        if (StrUtil.startWithAny(paResultDto.getCmd(), "PA", "HBPA")) {
                            paResultDto.setNpaCount(0);
                            MnjxPsgOperateRecord bagRecord = this.constructOperateRecord("NPA", null, mnjxPsgCki.getPsgCkiId(), paResultDto, new ArrayList<>());
                            psgOperateRecordList.add(bagRecord);
                        } else if (StrUtil.startWithAny(paResultDto.getCmd(), "PU", "HBPU")) {
                            MnjxPsgOperateRecord bagRecord = this.constructOperateRecord("MOD", null, mnjxPsgCki.getPsgCkiId(), paResultDto, new ArrayList<>());
                            bagRecord.setContent(bagRecord.getContent().replace("/PSM", "/AVIH"));
                            psgOperateRecordList.add(bagRecord);
                        }
                    } else {
                        MnjxPsgOperateRecord bagRecord = this.constructOperateRecord("BAG", null, mnjxPsgCki.getPsgCkiId(), paResultDto, newLuggageList);
                        psgOperateRecordList.add(bagRecord);
                    }
                }
            }
            paResultDto.setLuggageList(newLuggageList);
            paResultDto.setBagNoList(newLuggageList.stream().map(MnjxLuggage::getLuggageNo).collect(Collectors.toList()));
        }
    }

    /**
     * Title: handlePsgCkiOption
     * Description: 处理部分特服的新增、更新
     * PETC BSCT
     *
     * @param optionType  optionType
     * @param psgCkiId    psgCkiId
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    private void handlePsgCkiOption(String optionType, String psgCkiId, PaResultDto paResultDto, List<MnjxPsgOperateRecord> psgOperateRecordList) {
        MnjxPsgCkiOption dbPsgCkiOption = iMnjxPsgCkiOptionService.lambdaQuery()
                .eq(MnjxPsgCkiOption::getPsgCkiId, psgCkiId)
                .eq(MnjxPsgCkiOption::getOptionType, optionType)
                .one();
        String optionValue = "";
        if (StrUtil.isNotEmpty(paResultDto.getBsctNum())) {
            optionValue = StrUtil.format("{}/{}", paResultDto.getBsctNum(), paResultDto.getBsctWeight());
        } else if (StrUtil.isNotEmpty(paResultDto.getPetcNum())) {
            optionValue = StrUtil.format("{}/{}", paResultDto.getPetcNum(), paResultDto.getPetcWeight());
        }
        if (ObjectUtil.isNotEmpty(dbPsgCkiOption)) {
            dbPsgCkiOption.setOptionValue(optionValue);
            iMnjxPsgCkiOptionService.updateById(dbPsgCkiOption);
        } else {
            MnjxPsgCkiOption mnjxPsgCkiOption = new MnjxPsgCkiOption();
            mnjxPsgCkiOption.setPsgCkiOptionId(IdUtil.getSnowflake(1, 1).nextIdStr());
            mnjxPsgCkiOption.setPsgCkiId(psgCkiId);
            mnjxPsgCkiOption.setOptionType(optionType);
            mnjxPsgCkiOption.setOptionValue(optionValue);
            iAspectCkiService.insertPsgCkiOption(mnjxPsgCkiOption, paResultDto.getPnrNmId());
//            iMnjxPsgCkiOptionService.save(mnjxPsgCkiOption);
        }
        psgOperateRecordList.add(this.constructOperateRecord("MOD", optionType, paResultDto.getPsgCkiId(), paResultDto, null));
    }

    /**
     * Title: handleExst
     * Description: 处理额外订座
     *
     * @param paResultDto paResultDto
     * @param psgCki      psgCki
     * @param pnrSeg      pnrSeg
     * <AUTHOR>
     */
    private void handleExst(PaResultDto paResultDto, MnjxPsgCki psgCki, MnjxPnrSeg pnrSeg) {
        String pnrNmId = paResultDto.getPnrNmId();
        MnjxNmSsr exstSsr = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo())
                .in(MnjxNmSsr::getSsrType, "EXST", "CBBG", "COUR", "DIPL")
                .one();
        if (ObjectUtil.isNotEmpty(exstSsr)) {
            String oldType = exstSsr.getSsrType();
            exstSsr.setSsrType(paResultDto.getExstType());
            String inputValue = exstSsr.getInputValue();
            inputValue = inputValue.replace(oldType, paResultDto.getExstType());
            exstSsr.setInputValue(inputValue);
            exstSsr.setSsrInfo(inputValue);
            iMnjxNmSsrService.updateById(exstSsr);
        } else {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
            this.insertSsr(paResultDto.getExstType(), pnr.getMaxIndex() + 1, paResultDto, pnrSeg, "OVERSIZED", psgCki);
            pnr.setMaxIndex(pnr.getMaxIndex() + 1);
            iMnjxPnrService.updateById(pnr);
        }
        MnjxPsgCkiOption dbOption = iMnjxPsgCkiOptionService.lambdaQuery()
                .eq(MnjxPsgCkiOption::getPsgCkiId, paResultDto.getPsgCkiId())
                .in(MnjxPsgCkiOption::getOptionType, "EXST", "CBBG", "COUR", "DIPL")
                .one();
        if (ObjectUtil.isNotEmpty(dbOption)) {
            dbOption.setOptionValue(StrUtil.isEmpty(paResultDto.getExstWeight()) ? "" : paResultDto.getExstWeight());
            iMnjxPsgCkiOptionService.updateById(dbOption);
        } else {
            MnjxPsgCkiOption mnjxPsgCkiOption = new MnjxPsgCkiOption();
            mnjxPsgCkiOption.setPsgCkiOptionId(IdUtil.getSnowflake(1, 1).nextIdStr());
            mnjxPsgCkiOption.setPsgCkiId(paResultDto.getPsgCkiId());
            mnjxPsgCkiOption.setOptionType(paResultDto.getExstType());
            mnjxPsgCkiOption.setOptionValue(paResultDto.getExstWeight());
            iAspectCkiService.insertPsgCkiOption(mnjxPsgCkiOption, pnrNmId);
//            iMnjxPsgCkiOptionService.save(mnjxPsgCkiOption);
        }
        if (!paResultDto.isCap()) {
            this.getAdsrOrExstSeatNo(paResultDto);
            String exstSeat = paResultDto.getExstSeat();
            MnjxPsgSeat psgSeat = paResultDto.getMnjxPsgSeat();
            psgSeat.setSeatExst(exstSeat);
            iMnjxPsgSeatService.lambdaUpdate()
                    .eq(MnjxPsgSeat::getPsgSeatId, psgSeat.getPsgSeatId())
                    .set(MnjxPsgSeat::getSeatExst, exstSeat)
                    .update();
            List<MnjxOpenCabin> cabinClassOpenCabinList = paResultDto.getCabinClassOpenCabinList();
            List<String> openCabinIdList = cabinClassOpenCabinList.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());

            List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .eq(MnjxSeat::getSeatNo, exstSeat)
                    .list();
            seatList.forEach(s -> s.setSeatStatus(StrUtil.DOT));
            iAspectCkiService.updateSeatList(seatList, pnrNmId);
//            iMnjxSeatService.lambdaUpdate()
//                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
//                    .eq(MnjxSeat::getSeatNo, exstSeat)
//                    .set(MnjxSeat::getSeatStatus, StrUtil.DOT)
//                    .update();
        }
    }

    /**
     * Title: handleAdsr
     * Description: 处理邻近占座
     *
     * @param paResultDto paResultDto
     * @param psgCki      psgCki
     * <AUTHOR>
     */
    private void handleAdsr(PaResultDto paResultDto, MnjxPsgCki psgCki, MnjxPsgCkiOption dbPsgCkiOption) {
        MnjxPsgSeat mnjxPsgSeat = paResultDto.getMnjxPsgSeat();
        if (StrUtil.isNotEmpty(mnjxPsgSeat.getSeatExst())) {
            return;
        }
        String optionValue = paResultDto.getAdsrWeight();
        if (ObjectUtil.isNotEmpty(dbPsgCkiOption)) {
            dbPsgCkiOption.setOptionValue(optionValue);
            iMnjxPsgCkiOptionService.updateById(dbPsgCkiOption);
        } else {
            MnjxPsgCkiOption mnjxPsgCkiOption = new MnjxPsgCkiOption();
            mnjxPsgCkiOption.setPsgCkiOptionId(IdUtil.getSnowflake(1, 1).nextIdStr());
            mnjxPsgCkiOption.setPsgCkiId(psgCki.getPsgCkiId());
            mnjxPsgCkiOption.setOptionType("ADSR");
            mnjxPsgCkiOption.setOptionValue(optionValue);
            iAspectCkiService.insertPsgCkiOption(mnjxPsgCkiOption, paResultDto.getPnrNmId());
//            iMnjxPsgCkiOptionService.save(mnjxPsgCkiOption);
        }
        if (paResultDto.isDlOrNaccToSb()) {
            return;
        }
        this.getAdsrOrExstSeatNo(paResultDto);
    }

    /**
     * Title: getAdsrOrExstSeatNo
     * Description: 额外占座、邻近占座可用座位验证。额外占座预分配，邻近占座更新数据
     *
     * @param paResultDto
     * @return
     * <AUTHOR>
     * @date 2023/2/2 15:32
     */
    private void getAdsrOrExstSeatNo(PaResultDto paResultDto) {
        // 旅客座位对象
        MnjxPsgSeat mnjxPsgSeat = paResultDto.getMnjxPsgSeat();
        // 要更新的座位状态
        String toUpdateStatus = StrUtil.isNotEmpty(paResultDto.getAdsr()) ? "+" : ".";
        // 旅客所属舱等的开舱数据
        List<MnjxOpenCabin> openCabinList = paResultDto.getCabinClassOpenCabinList();
        // 开舱ID
        List<String> cabinClassOpenCabinIdList = openCabinList.stream()
                .filter(o -> StrUtil.isEmpty(paResultDto.getGType()) ? paResultDto.getCabinClass().equals(o.getCabinClass()) : paResultDto.getGCabinClass().equals(o.getCabinClass()))
                .map(MnjxOpenCabin::getOpenCabinId)
                .collect(Collectors.toList());
        // 该开舱下的可用座位数据
        List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                .in(MnjxSeat::getOpenCabinId, cabinClassOpenCabinIdList)
                .in(MnjxSeat::getSeatStatus, "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S", "C")
                .list();
        // 座位不为空，进行座位分配
        if (CollUtil.isNotEmpty(seatList)) {
            // 邻近占座的可用座位状态的座位数据
            List<MnjxSeat> adsrOrExstSeatList = seatList.stream()
                    .filter(s -> StrUtil.equalsAny(s.getSeatStatus(), "*", StrUtil.SLASH))
                    .collect(Collectors.toList());
            // 额外占座可用座位状态的座位数据
            if (paResultDto.isHaveExst()) {
                adsrOrExstSeatList = seatList;
            }
            // 如果按航节计划id分组key大于1，说明该旅客订座是个多航段的直达航班，需要筛选每一个航段的座位号相同的座位
            if (adsrOrExstSeatList.stream().collect(Collectors.groupingBy(MnjxSeat::getPlanSectionId)).size() > 1) {
                Map<String, List<MnjxSeat>> groupSeatList = adsrOrExstSeatList.stream().collect(Collectors.groupingBy(MnjxSeat::getSeatNo));
                adsrOrExstSeatList.clear();
                for (String key : groupSeatList.keySet()) {
                    if (groupSeatList.get(key).size() > 1) {
                        adsrOrExstSeatList.addAll(groupSeatList.get(key));
                    }
                }
                if (paResultDto.isHaveExst() && adsrOrExstSeatList.size() < Constant.FOUR) {
                    paResultDto.setCap(true);
                    return;
                }
            }
            if (CollUtil.isEmpty(adsrOrExstSeatList)) {
                paResultDto.setCap(true);
                return;
            }
            // 获取座位号
            String seatNo = StrUtil.isEmpty(paResultDto.getSeatNo()) ? mnjxPsgSeat.getPsgSeat() : paResultDto.getSeatNo();
            // 额外占座或邻近占座的座位号
            String adsrOrExstSeatNo = null;
            if (StrUtil.isNotEmpty(seatNo)) {
                // 获取当前座位号
                List<MnjxSeat> collect = adsrOrExstSeatList.stream()
                        .filter(a -> seatNo.equals(a.getSeatNo()))
                        .collect(Collectors.toList());
                // 获取靠近的座位号座位临近占座或额外占座的座位号
                adsrOrExstSeatNo = this.getOtherSeat(paResultDto, collect, seatNo);
                if (StrUtil.isEmpty(adsrOrExstSeatNo)) {
                    for (MnjxSeat mnjxSeat : seatList) {
                        collect = adsrOrExstSeatList.stream()
                                .filter(a -> mnjxSeat.getSeatNo().equals(a.getSeatNo()))
                                .collect(Collectors.toList());
                        adsrOrExstSeatNo = this.getOtherSeat(paResultDto, collect, mnjxSeat.getSeatNo());
                        if (StrUtil.isNotEmpty(adsrOrExstSeatNo)) {
                            break;
                        }
                    }
                }
            }
            // 没有输入座位号
            else {
                for (MnjxSeat mnjxSeat : seatList) {
                    List<MnjxSeat> collect = adsrOrExstSeatList.stream()
                            .filter(a -> mnjxSeat.getSeatNo().equals(a.getSeatNo()))
                            .collect(Collectors.toList());
                    adsrOrExstSeatNo = this.getOtherSeat(paResultDto, collect, mnjxSeat.getSeatNo());
                    if (StrUtil.isNotEmpty(adsrOrExstSeatNo)) {
                        break;
                    }
                }
            }
            // 如果分配到额外占座或临近占座的座位号
            if (StrUtil.isNotEmpty(adsrOrExstSeatNo)) {
                // 额外占座当前只判断，不进行数据交互
                if (paResultDto.isHaveExst()) {
                    paResultDto.setExstSeat(adsrOrExstSeatNo);
                } else {
                    String finalAdsrSeatNo = adsrOrExstSeatNo;
                    List<MnjxSeat> toUpdateSeatList = adsrOrExstSeatList.stream()
                            .filter(a -> finalAdsrSeatNo.equals(a.getSeatNo()))
                            .collect(Collectors.toList());
                    toUpdateSeatList.forEach(o -> {
                        String seatStatus = o.getSeatStatus();
                        o.setSeatStatus(toUpdateStatus);
                        o.setSeatStatusOld(seatStatus);
                    });
                    iAspectCkiService.updateSeatList(toUpdateSeatList, paResultDto.getPnrNmId());
//                    iMnjxSeatService.updateBatchById(toUpdateSeatList);
                    mnjxPsgSeat.setSeatExst(adsrOrExstSeatNo);
                    iMnjxPsgSeatService.updateById(mnjxPsgSeat);
                }
            }
            // 没有分配到座位号，额外占座时设置为CAP的候补旅客
            else if (paResultDto.isHaveExst()) {
                paResultDto.setCap(true);
            }
        }
        // 没有可用座位，且是额外占座，设置为CAP候补旅客
        else if (paResultDto.isHaveExst()) {
            paResultDto.setCap(true);
        }
    }

    private String getOtherSeat(PaResultDto paResultDto, List<MnjxSeat> seatList, String seatNo) {
        String result = null;
        List<MnjxSeat> ySeatAddOneList = new ArrayList<>();
        List<MnjxSeat> ySeatReduceOneList = new ArrayList<>();
        //判断每个航节的座位状态是否可用
        for (MnjxSeat mnjxSeat : seatList) {
            MnjxSeat ySeatAddOne = iMnjxSeatService.lambdaQuery()
                    .eq(MnjxSeat::getPlanSectionId, mnjxSeat.getPlanSectionId())
                    .eq(MnjxSeat::getSeatRow, mnjxSeat.getSeatRow())
                    .eq(MnjxSeat::getSeatY, mnjxSeat.getSeatY() + 1)
                    .one();
            MnjxSeat ySeatReduceOne = iMnjxSeatService.lambdaQuery()
                    .eq(MnjxSeat::getPlanSectionId, mnjxSeat.getPlanSectionId())
                    .eq(MnjxSeat::getSeatRow, mnjxSeat.getSeatRow())
                    .eq(MnjxSeat::getSeatY, mnjxSeat.getSeatY() - 1)
                    .one();
            if (ObjectUtil.isNotNull(ySeatAddOne)) {
                boolean matchExstStatus = paResultDto.isHaveExst() && StrUtil.equalsAny(ySeatAddOne.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S", "C");
                boolean matchAdsrStatus = StrUtil.isNotEmpty(paResultDto.getAdsr()) && StrUtil.equalsAny(ySeatAddOne.getSeatStatus(), "*", StrUtil.SLASH);
                if (matchExstStatus || matchAdsrStatus) {
                    ySeatAddOneList.add(ySeatAddOne);
                }
            }
            if (ObjectUtil.isNotNull(ySeatReduceOne)) {
                boolean matchExstStatus = paResultDto.isHaveExst() && StrUtil.equalsAny(ySeatReduceOne.getSeatStatus(), "*", "Q", "B", "N", "U", "H", "L", "A", "/", "I", "S", "C");
                boolean matchAdsrStatus = StrUtil.isNotEmpty(paResultDto.getAdsr()) && StrUtil.equalsAny(ySeatReduceOne.getSeatStatus(), "*", StrUtil.SLASH);
                if (matchExstStatus || matchAdsrStatus) {
                    ySeatReduceOneList.add(ySeatReduceOne);
                }
            }
        }
        if (CollUtil.isNotEmpty(ySeatReduceOneList) && ySeatReduceOneList.size() == seatList.size()) {
            result = ySeatReduceOneList.get(0).getSeatNo();
            paResultDto.setSeatNo(seatNo);
        }
        if (CollUtil.isNotEmpty(ySeatAddOneList) && ySeatAddOneList.size() == seatList.size()) {
            result = ySeatAddOneList.get(0).getSeatNo();
            paResultDto.setSeatNo(seatNo);
        }
        return result;
    }

    /**
     * Title: handleSsrData
     * Description: 处理SSR选项更新数据
     *
     * @param paResultDto paResultDto
     * @param pnrSeg      pnrSeg
     * <AUTHOR>
     */
    @Override
    public void handleSsrData(PaResultDto paResultDto, MnjxPnrSeg pnrSeg, MnjxPsgCki psgCki) {
        MnjxPnr pnr = paResultDto.getPnr();
        Integer maxIndex = pnr.getMaxIndex();
        // 处理SSR
        Map<String, String> ssrTypeFreeTextMap = paResultDto.getSsrTypeFreeTextMap();
        if (ObjectUtil.isEmpty(ssrTypeFreeTextMap)) {
            return;
        }
        for (Map.Entry<String, String> entry : ssrTypeFreeTextMap.entrySet()) {
            String ssrType = entry.getKey();
            String inputValue;
            if (StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_SPML, "PSM", "MSG", "PIL", "ADSR")) {
                inputValue = StrUtil.emptyIfNull(entry.getValue());
            } else if (StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_INFT, Constant.SSR_TYPE_CHLD, Constant.SSR_TYPE_UMNR)) {
                inputValue = ssrType;
            } else {
                inputValue = "";
            }
            this.insertSsr(ssrType, maxIndex, paResultDto, pnrSeg, inputValue.trim(), psgCki);
            maxIndex += 1;
        }
        // 如果输入了UM，没有输入CHD，判断原SSR是否有CHLD，如果有则删除
        if (paResultDto.isHasUm() && !paResultDto.isHasChd()) {
            List<MnjxNmSsr> chldSsrList = paResultDto.getSsrList()
                    .stream()
                    .filter(s -> Constant.SSR_TYPE_CHLD.equals(s.getSsrType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chldSsrList)) {
                MnjxNmSsr chldSsr = chldSsrList.get(0);
                chldSsr.deleteById();
                paResultDto.getSsrList().removeIf(next -> Constant.SSR_TYPE_CHLD.equals(next.getSsrType()));
                maxIndex--;
                if (StrUtil.isEmpty(psgCki.getUres()) && StrUtil.isEmpty(psgCki.getNrec())) {
                    List<MnjxPnrRecord> changeAtList = paResultDto.getPnrRecordList()
                            .stream()
                            .filter(r -> StrUtil.isNotEmpty(r.getChangeAtNo()))
                            .collect(Collectors.toList());

                    MnjxPnrRecord record = paResultDto.getPnrRecordList()
                            .stream()
                            .filter(r -> "SSR".equals(r.getPnrType()) && chldSsr.getInputValue().equals(r.getInputValue()))
                            .collect(Collectors.toList())
                            .get(0);
                    record.setChangeMark("X");
                    if (CollUtil.isNotEmpty(changeAtList)) {
                        OptionalInt max = changeAtList.stream()
                                .mapToInt(r -> Integer.parseInt(r.getChangeAtNo()))
                                .max();
                        record.setChangeAtNo(StrUtil.fill(StrUtil.toString(max.getAsInt() + 1), '0', 3, true));
                    } else {
                        record.setChangeAtNo("001");
                    }
                    iAspectCkiService.updatePnrRecord(record, paResultDto.getPnrNmId());
//                    iMnjxPnrRecordService.updateById(record);
                    paResultDto.getPnrRecordList().removeIf(next -> next.getPnrRecordId().equals(record.getPnrRecordId()));
                    paResultDto.getPnrRecordList().add(record);
                }
            }
        }
        // 更新PNR最大序号
        pnr.setMaxIndex(maxIndex);
        paResultDto.setPnr(pnr);
        iMnjxPnrService.updateById(pnr);
    }

    /**
     * Title: constructOperateRecord
     * Description: 生成旅客值机操作记录
     *
     * @param operateType operateType
     * @param paResultDto paResultDto
     * @return 生成旅客值机操作记录
     * <AUTHOR>
     */
    private MnjxPsgOperateRecord constructOperateRecord(String operateType, String endType, String psgCkiId, PaResultDto paResultDto, List<MnjxLuggage> luggageList) {
        MnjxPsgOperateRecord psgOperateRecord = new MnjxPsgOperateRecord();
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataCabinet activeMemoryDataCabinet = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet();
        MnjxSi mnjxSi = activeMemoryDataCabinet.getMnjxSi();
        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();
        String recordContent = "";
        switch (operateType) {
            case "ACC":
            case "SBY":
                recordContent = StrUtil.format("{}{} AGT{}/{}{}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                        DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                if ("FF".equals(endType)) {
                    recordContent = StrUtil.format("{}/FF", recordContent);
                }
                break;
            case "BAG":
                List<String> luggageNoList = luggageList.stream()
                        .map(MnjxLuggage::getLuggageNo)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                if (luggageNoList.size() == 1) {
                    String luggageNo = luggageNoList.get(0);
                    BigDecimal luggageWeight = new BigDecimal(paResultDto.getBagWeight());
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/{}/{}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                            DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()), luggageNo.substring(luggageNo.length() - 3), luggageWeight);
                } else {
                    OptionalInt max = luggageNoList.stream()
                            .mapToInt(Integer::parseInt)
                            .max();
                    OptionalInt min = luggageNoList.stream()
                            .mapToInt(Integer::parseInt)
                            .min();
                    String start = StrUtil.fill(StrUtil.toString(min.getAsInt()), '0', 3, true);
                    start = start.substring(start.length() - 3);
                    String end = StrUtil.fill(StrUtil.toString(max.getAsInt()), '0', 3, true);
                    end = end.substring(end.length() - 3);
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/{}-{}/{}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                            DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()),
                            start,
                            end,
                            paResultDto.getBagWeight());
                }
                break;
            case "MOD":
                recordContent = StrUtil.format("{}{} AGT{}/{}{}/PSM", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                        DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                if (StrUtil.isNotEmpty(paResultDto.getPetcNum()) && "PETC".equals(endType)) {
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/PETC", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                            DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                }
                if (StrUtil.isNotEmpty(paResultDto.getBsctNum()) && "BSCT".equals(endType)) {
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/BSCT", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                            DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                }
                break;
            case "NPA":
                if (paResultDto.getNpaCount() == 0) {
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                            DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                } else {
                    if (StrUtil.isAllNotEmpty(paResultDto.getPreOldSeat(), paResultDto.getSeatNo())) {
                        recordContent = StrUtil.format("{}{} AGT{}/{}{} R{} {}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                                DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()), paResultDto.getPreOldSeat(), paResultDto.getSeatNo());
                    } else {
                        recordContent = StrUtil.format("{}{} AGT{}/{}{}", mnjxOffice.getOfficeNo().substring(0, 3), mnjxSi.getSiPid(), mnjxSi.getSiNo(),
                                DateUtils.ymd2Com(DateUtil.today()).substring(0, 5), DateUtils.ymdhms2hm(DateUtil.now()));
                    }
                }
                break;
            default:
                break;
        }

        psgOperateRecord.setPsgCkiId(psgCkiId);
        psgOperateRecord.setOperator(mnjxOffice.getOfficeNo());
        psgOperateRecord.setOperateType(operateType);
        psgOperateRecord.setContent(recordContent);
        psgOperateRecord.setOperateTime(DateUtil.parseDateTime(DateUtil.now()));
        return psgOperateRecord;
    }

    /**
     * Title: insertSsr
     * Description: 接收旅客添加SSR信息
     *
     * @param ssrType     ssrType
     * @param maxIndex    maxIndex
     * @param paResultDto paResultDto
     * @param pnrSeg      pnrSeg
     * @param inputValue  inputValue
     * <AUTHOR>
     */
    private void insertSsr(String ssrType, Integer maxIndex, PaResultDto paResultDto, MnjxPnrSeg pnrSeg, String inputValue, MnjxPsgCki psgCki) {
        List<MnjxNmSsr> ssrList = paResultDto.getSsrList();
        // 白屏F12添加候补旅客带婴儿时，ssrList是空的，需要加一次判断
        if (CollUtil.isNotEmpty(ssrList)) {
            ssrList = ssrList.stream()
                    .filter(s -> ssrType.equals(s.getSsrType()))
                    .collect(Collectors.toList());
        }
        // 如果存在这种类型的SSR，不做任何操作
        if (CollUtil.isNotEmpty(ssrList) && ssrList.stream().noneMatch(s -> StrUtil.equalsAny(s.getSsrType(), Constant.SSR_TYPE_SPML, Constant.SSR_TYPE_PSM, Constant.SSR_TYPE_MSG, Constant.SSR_TYPE_PIL, Constant.SSR_TYPE_MEDA))) {
            return;
        }
        // SPML的先把原来的SPML删除，再添加新的
        List<String> mlKeys = new ArrayList(Constant.SPML_CN_MAP.keySet());
        List<MnjxNmSsr> mlSsrs = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, paResultDto.getPnrNmId())
                .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo().toString())
                .in(MnjxNmSsr::getSsrType, mlKeys)
                .list();
        if (CollUtil.isNotEmpty(mlSsrs)) {
            iAspectCkiService.deleteSsrList(mlSsrs, paResultDto.getPnrNmId());
        }
        MnjxNmSsr mnjxNmSsr = new MnjxNmSsr();
        MnjxPnrNm pnrNm = paResultDto.getPnrNm();
        mnjxNmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxNmSsr.setPnrNmId(paResultDto.getPnrNmId());
        if (!StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_CHLD, Constant.SSR_TYPE_FOID, Constant.SSR_TYPE_UMNR)) {
            mnjxNmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
        }
        mnjxNmSsr.setSsrType(ssrType);
        mnjxNmSsr.setActionCode(Constant.HK);
        mnjxNmSsr.setPnrIndex(maxIndex);
        String airlineCode = StrUtil.isNotEmpty(paResultDto.getShareFlightNo()) ? paResultDto.getShareFlightNo().substring(0, 2) : paResultDto.getFlightNo().substring(0, 2);
        mnjxNmSsr.setAirlineCode(airlineCode);
        mnjxNmSsr.setFltDate(paResultDto.getFlightDate().contains(StrUtil.DASHED) ? paResultDto.getFlightDate() : DateUtils.com2ymd(paResultDto.getFlightDate()));
        mnjxNmSsr.setOrgDst(StrUtil.format("{}{}", paResultDto.getOrgAirport(), paResultDto.getDstAirport()));
        // 通用SSR信息构建
        Integer psgIndex = pnrNm.getPsgIndex();
        // 通用构建
        if (StrUtil.isEmpty(inputValue)) {
            inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/P{}", ssrType, airlineCode, mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(),
                    paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), DateUtils.ymd2Com(mnjxNmSsr.getFltDate()), ssrType, psgIndex);
            if (StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_PETC, Constant.SSR_TYPE_BSCT)) {
                inputValue = StrUtil.format("SSR {} {} HK1 {} {} {}{}/P{}", ssrType, airlineCode, mnjxNmSsr.getOrgDst(),
                        paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), DateUtils.ymd2Com(mnjxNmSsr.getFltDate()), psgIndex);
            } else if (Constant.SSR_TYPE_FQTV.equals(ssrType)) {
                inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}{}/P{}", ssrType, airlineCode, mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(),
                        paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), DateUtils.ymd2Com(mnjxNmSsr.getFltDate()), paResultDto.getFfAirlineCode(), paResultDto.getFfNo(), psgIndex);
            }
            // 如果原来有轮椅，接收的时候用新轮椅类型修改原来的轮椅类型
            if (ReUtil.isMatch(WC_PATTERN, ssrType)) {
                List<MnjxNmSsr> wcSsrList = paResultDto.getSsrList()
                        .stream()
                        .filter(s -> s.getSsrType().startsWith("WC"))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(wcSsrList)) {
                    MnjxNmSsr wcSsr = wcSsrList.get(0);
                    String oldInputValue = wcSsr.getInputValue();
                    if (StrUtil.isEmpty(psgCki.getUres()) && StrUtil.isEmpty(psgCki.getNrec())) {
                        MnjxPnrRecord oldRecord = paResultDto.getPnrRecordList()
                                .stream()
                                .filter(r -> "SSR".equals(r.getPnrType()) && oldInputValue.equals(r.getInputValue()))
                                .collect(Collectors.toList())
                                .get(0);
                        oldRecord.setInputValue(inputValue);
                        iAspectCkiService.updatePnrRecord(oldRecord, paResultDto.getPnrNmId());
//                        iMnjxPnrRecordService.updateById(oldRecord);
                        paResultDto.getPnrRecordList().removeIf(next -> next.getPnrRecordId().equals(oldRecord.getPnrRecordId()));
                        paResultDto.getPnrRecordList().add(oldRecord);
                    }
                    wcSsr.setSsrType(ssrType);
                    wcSsr.setInputValue(inputValue);
                    wcSsr.setSsrInfo(inputValue);
                    iMnjxNmSsrService.updateById(wcSsr);
                    paResultDto.getSsrList().removeIf(next -> wcSsr.getNmSsrId().equals(next.getNmSsrId()));
                    paResultDto.getSsrList().add(wcSsr);
                    return;
                }
            }
        }
        // 特殊构建
        else {
            String today = DateUtil.today();
            DateTime nowDate = DateUtil.parseDate(today);
            Calendar nowCal = Calendar.getInstance();
            nowCal.setTime(nowDate);
            switch (ssrType) {
                case "CHLD":
                    // 儿童年龄默认5岁
                    nowCal.add(Calendar.YEAR, -5);
                    Date date = nowCal.getTime();
                    String birthDate = DateUtils.ymd2Com(DateUtils.date2ymd(date));
                    inputValue = StrUtil.format("SSR CHLD {} {}1 {}/P{}", airlineCode, mnjxNmSsr.getActionCode(), birthDate, psgIndex);
                    break;
                case "INFT":
                    // 婴儿年龄默认3个月前
                    nowCal.add(Calendar.MONTH, -3);
                    date = nowCal.getTime();
                    String inftBirthDay = DateUtils.ymd2Com(DateUtils.date2ymd(date));
                    paResultDto.setInfBirth(inftBirthDay);
                    inputValue = StrUtil.format("SSR INFT {} {}1 {} {} {}{} {} {}/P{}", airlineCode, mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(),
                            paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), paResultDto.getFlightDate().contains(StrUtil.DASHED) ? DateUtils.ymd2Com(paResultDto.getFlightDate()) : paResultDto.getFlightDate(), paResultDto.getInfName(), paResultDto.getInfBirth(), psgIndex);
                    break;
                case "UMNR":
                    inputValue = StrUtil.format("SSR UMNR YY {}1 UM{}/P{}/S{}", mnjxNmSsr.getActionCode(), paResultDto.getUmAge(), psgIndex, pnrSeg.getPnrSegNo());
                    break;
                case "SPML":
                    // 餐食
                    inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/P{}", ssrType, airlineCode, mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(),
                            paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), DateUtils.ymd2Com(paResultDto.getFlightDate()), inputValue, psgIndex);
                    break;
                case "EXST":
                case "CBBG":
                case "COUR":
                case "DIPL":
                    inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/P{}", ssrType, airlineCode, mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(),
                            paResultDto.getFlightNo().substring(2), paResultDto.getSellCabin(), DateUtils.ymd2Com(mnjxNmSsr.getFltDate()), inputValue, psgIndex);
                    break;
                case "ADSR":
                    inputValue = StrUtil.format("SSR {} {} HK1 {} {} {} {}/P{}/S{}", ssrType, airlineCode, mnjxNmSsr.getOrgDst(),
                            paResultDto.getFlightNo().substring(2), DateUtils.ymd2Com(mnjxNmSsr.getFltDate()), inputValue, psgIndex, pnrSeg.getPnrSegNo());
                    break;
                default:
                    if (StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_PSM, Constant.SSR_TYPE_MSG, Constant.SSR_TYPE_PIL)) {
                        // PSM MSG PIL 需要更新信息的，先将以前的移除
                        inputValue = StrUtil.format("SSR {} {}/P{}", ssrType, inputValue, psgIndex);
                        List<MnjxNmSsr> toRemoveSsrList = iMnjxNmSsrService.lambdaQuery()
                                .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                                .eq(MnjxNmSsr::getSsrType, ssrType)
                                .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo())
                                .list();
                        iAspectCkiService.deleteSsrList(toRemoveSsrList, paResultDto.getPnrNmId());
                        paResultDto.getSsrList().removeIf(next -> ssrType.equals(next.getSsrType()) && pnrSeg.getPnrSegNo().intValue() == next.getPnrSegNo().intValue());
                        if (Constant.SSR_TYPE_PSM.equals(ssrType)) {
                            this.constructOperateRecord("MOD", null, paResultDto.getPsgCkiId(), paResultDto, null);
                        }
                    } else if (ssrType.endsWith("ML")) {
                        // SSR DBML CA HK1 CTUPEK 1234 Y21MAR/P1
                        inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{}/P{}", ssrType, paResultDto.getAirlineCode(), mnjxNmSsr.getActionCode(), mnjxNmSsr.getOrgDst(), paResultDto.getFlightNo().substring(2),
                                paResultDto.getSellCabin(), DateUtils.ymd2Com(paResultDto.getFlightDate()), psgIndex);
                    }
                    break;
            }
        }
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setSsrInfo(inputValue);
        iAspectCkiService.insertSsr(mnjxNmSsr, pnrNm.getPnrNmId());
//        iMnjxNmSsrService.save(mnjxNmSsr);
        paResultDto.getSsrList().add(mnjxNmSsr);
        if (StrUtil.isEmpty(psgCki.getNrec()) && StrUtil.isEmpty(psgCki.getUres())) {
            this.insertPnrRecord(paResultDto, "SSR", inputValue, null);
        }
    }

    /**
     * Title: formatRenderData
     * Description: 数据格式进行处理
     *
     * @param paResultDto paResultDto
     * <AUTHOR>
     */
    @Override
    public void formatRenderData(PaResultDto paResultDto) {
        String pnrNmId = paResultDto.getPnrNmId();
        String cndNo = paResultDto.getCndNo();
        MnjxCnd cnd = iMnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getCndNo, cndNo)
                .one();
        // 获取最新的mnjx_psg_cki
        MnjxPsgCki psgCki = iMnjxPsgCkiService.getById(paResultDto.getPsgCkiId());
        // 获取所有SSR信息
        List<MnjxNmSsr> nmSsrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .and(s -> s.isNull(MnjxNmSsr::getPnrSegNo).or().eq(MnjxNmSsr::getPnrSegNo, paResultDto.getMnjxPnrSeg().getPnrSegNo()))
                .list();

        // 所有存在的SSR类型
        List<String> allSsrTypeList = nmSsrList.stream()
                .map(MnjxNmSsr::getSsrType)
                .collect(Collectors.toList());

        // 部分SSR显示位置不一样，除FOID 餐食 INFT VIP外（后续继续补充），将SSR_TYPE显示在上面区域
        List<String> topAreaSsrList = allSsrTypeList.stream()
                .filter(s -> !StrUtil.equalsAny(s, "FOID", "INFT", "VIP", "PSM", "MSG", "PIL", "STCR", "JMP", "PETC", "EXST", "BSCT", "CBBG", "COUR", "DIPL", "TKNE", "ADSR", "FQTV", "WCBD", "WCBW", "WCMP", "WCOB", "MEDA") && !s.endsWith("ML"))
                .collect(Collectors.toList());
        // UMNR需要显示为 UM+年龄
        String formatUm = "";
        // CHLD需要显示为 CHD1/0
        String formatChd = "";
        for (String ssr : topAreaSsrList) {
            if (Constant.SSR_TYPE_UMNR.equals(ssr)) {
                if (StrUtil.isNotEmpty(paResultDto.getUmAge())) {
                    formatUm = StrUtil.format("UM{}", paResultDto.getUmAge());
                } else {
                    MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery()
                            .eq(MnjxPnrNmUm::getPnrNmId, pnrNmId)
                            .one();
                    if (ObjectUtil.isNotEmpty(nmUm)) {
                        formatUm = StrUtil.format("UM{}", nmUm.getUmAge());
                    }
                }
            } else if (Constant.SSR_TYPE_CHLD.equals(ssr)) {
                formatChd = "CHD1/0";
                MnjxNmSsr chldSsr = nmSsrList.stream()
                        .filter(s -> Constant.SSR_TYPE_CHLD.equals(s.getSsrType()))
                        .collect(Collectors.toList())
                        .get(0);
                String inputValue = chldSsr.getInputValue();
                String[] split = inputValue.trim().split(StrUtil.SPACE);
                String chld = StrUtil.format("CHLD {} {}", split[3], split[4].split(StrUtil.SLASH)[0]);
                paResultDto.setChld(chld);
            }
        }
        if (StrUtil.isNotEmpty(formatUm)) {
            topAreaSsrList.remove(Constant.SSR_TYPE_UMNR);
            topAreaSsrList.add(formatUm);
        }
        if (StrUtil.isNotEmpty(formatChd)) {
            topAreaSsrList.remove(Constant.SSR_TYPE_CHLD);
            topAreaSsrList.add(formatChd);
        }
        paResultDto.setOtherSsrList(topAreaSsrList);

        // SPML
        List<String> mlList = allSsrTypeList.stream()
                .filter(s -> s.endsWith("ML"))
                .collect(Collectors.toList());
        List<MnjxNmSsr> list = nmSsrList.stream()
                .filter(s -> Constant.SSR_TYPE_SPML.equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            MnjxNmSsr spmlSsr = list.get(0);
            String spmlInputValue = spmlSsr.getInputValue();
            if (ReUtil.isMatch(SPML_SSR_PATTERN, spmlInputValue)) {
                List<String> allGroups = ReUtil.getAllGroups(SPML_SSR_PATTERN, spmlInputValue);
                paResultDto.setSpml(StrUtil.isNotEmpty(allGroups.get(4)) ? allGroups.get(4) : Constant.SSR_TYPE_SPML);
            } else {
                paResultDto.setSpml(Constant.SSR_TYPE_SPML);
            }
        } else if (CollUtil.isNotEmpty(mlList)) {
            // 因为餐食只允许加一个，直接取第一个
            paResultDto.setSpml(mlList.get(0));
        }

        // PSM
        List<MnjxNmSsr> psmList = nmSsrList.stream()
                .filter(s -> "PSM".equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(psmList)) {
            String psm = psmList.get(0).getInputValue();
            List<String> allGroups = ReUtil.getAllGroups(PSM_PATTERN, psm);
            psm = allGroups.get(1);
            if (StrUtil.isNotEmpty(psm)) {
                if (psm.endsWith("/VIP")) {
                    psm = psm.substring(0, psm.length() - 4);
                }
                paResultDto.setPsm(psm.trim());
            }
        }

        // MSG
        List<MnjxNmSsr> msgList = nmSsrList.stream()
                .filter(s -> "MSG".equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(msgList)) {
            String msg = msgList.get(0).getInputValue();
            List<String> allGroups = ReUtil.getAllGroups(MSG_PIL_PATTERN, msg);
            msg = allGroups.get(2);
            if (StrUtil.isNotEmpty(msg)) {
                paResultDto.setMsg(msg);
            }
        }

        // PIL
        List<MnjxNmSsr> pilList = nmSsrList.stream()
                .filter(s -> "PIL".equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(pilList)) {
            String pil = pilList.get(0).getInputValue();
            List<String> allGroups = ReUtil.getAllGroups(MSG_PIL_PATTERN, pil);
            pil = allGroups.get(2);
            if (StrUtil.isNotEmpty(pil)) {
                paResultDto.setPil(pil);
            }
        }

        // MEDA
        List<MnjxNmSsr> medaList = nmSsrList.stream()
                .filter(s -> Constant.SSR_TYPE_MEDA.equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(medaList)) {
            MnjxNmSsr medaSsr = medaList.get(0);
            paResultDto.setMeda(StrUtil.format("MEDA- NN1 {}{}{}{}", medaSsr.getOrgDst(), paResultDto.getFlightNo().substring(2), paResultDto.getCabinClass(), DateUtils.ymd2Com(medaSsr.getFltDate()).substring(0, 5)));
        }

        // WCBD WCBW WCMP WCOB
        if (nmSsrList.stream().anyMatch(s -> StrUtil.equalsAny(s.getSsrType(), "WCBD", "WCBW", "WCMP", "WCOB"))) {
            paResultDto.setOtherWc(nmSsrList.stream().filter(s -> StrUtil.equalsAny(s.getSsrType(), "WCBD", "WCBW", "WCMP", "WCOB")).collect(Collectors.toList()).get(0).getSsrType());
        }

        // 航班日期转为航信日期格式
        String flightDate = paResultDto.getFlightDate();
        if (flightDate.contains(StrUtil.DASHED)) {
            paResultDto.setFlightDate(DateUtils.ymd2Com(flightDate));
        }

        // 处理电话号码
        String pnrId = iMnjxPnrNmService.getById(pnrNmId).getPnrId();
        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnrId)
                .list();
        if (CollUtil.isNotEmpty(pnrCtList)) {
            paResultDto.setPhone(pnrCtList.get(0).getCtText());
        }

        // 免费行李
        int fbaWeight = this.getCndCabinWeight(cnd, paResultDto.getCabinClass());
        String fbaWeightStr = StrUtil.format("FBA/{}KG", StrUtil.toString(fbaWeight));
        paResultDto.setFba(fbaWeightStr);
        // 身份证/护照/通行证
        List<MnjxNmSsr> foidList = nmSsrList.stream()
                .filter(s -> Constant.SSR_TYPE_FOID.equals(s.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(foidList)) {
            String foidInputValue = foidList.get(0).getInputValue();
            String foidNo = foidInputValue.split(" ")[4].split(StrUtil.SLASH)[0];
            paResultDto.setFoidNo(foidNo);

            //常旅客
            if (ObjectUtil.isNotEmpty(paResultDto.getDbFrequenter())) {
                paResultDto.setFf("FF");
                paResultDto.setFfAirlineCode(paResultDto.getDbFrequenter().getAirlineCode());
                paResultDto.setFfNo(paResultDto.getDbFrequenter().getFrequenterCard());
                paResultDto.setFfLevel(paResultDto.getDbFrequenter().getFrequenterLevel());
                paResultDto.setFfWeight(fbaWeightStr);
            } else if (nmSsrList.stream().anyMatch(s -> Constant.SSR_TYPE_FQTV.equals(s.getSsrType()))) {
                MnjxNmSsr fqtvSsr = nmSsrList.stream()
                        .filter(s -> Constant.SSR_TYPE_FQTV.equals(s.getSsrType()))
                        .collect(Collectors.toList())
                        .get(0);
                MnjxFrequenter frequenter = iMnjxFrequenterService.lambdaQuery()
                        .eq(MnjxFrequenter::getAirlineCode, fqtvSsr.getAirlineCode())
                        .eq(MnjxFrequenter::getFrequenterCertificateType, foidNo.substring(0, 2))
                        .eq(MnjxFrequenter::getFrequenterCertificateNo, foidNo.substring(2))
                        .one();
                if (ObjectUtil.isNotEmpty(frequenter)) {
                    paResultDto.setFf("FF");
                    paResultDto.setFfAirlineCode(fqtvSsr.getAirlineCode());
                    paResultDto.setFfNo(frequenter.getFrequenterCard());
                    paResultDto.setFfLevel(frequenter.getFrequenterLevel());
                    paResultDto.setFfWeight(fbaWeightStr);
                }
            }
        }

        // 行李
        List<MnjxLuggage> luggageList = iMnjxLuggageService.lambdaQuery()
                .eq(MnjxLuggage::getPnrNmId, pnrNmId)
                .list();
        // 存在行李，设置件数重量
        if (CollUtil.isNotEmpty(luggageList)) {
            StringBuilder weightSb = new StringBuilder();
            iPrService.getLuggageWeight(luggageList, psgCki.getPnrSegNo(), weightSb);
            paResultDto.setWeight(weightSb.toString().trim());
        }

        // AV剩余座位布局
        String originLayout = cnd.getLayout().replace(StrUtil.SLASH, "");
        String av = this.getAvLayout(originLayout, paResultDto.getPlanSectionList());
        // pad布局
        String pad = originLayout.replaceAll("\\d+", "0");
        paResultDto.setAvLayout(av);
        paResultDto.setPadLayout(pad);
        // 登机口是空的，需要显示????
        if (StrUtil.isEmpty(paResultDto.getGate())) {
            paResultDto.setGate("????");
        }
        List<MnjxPsgCkiOption> psgCkiOptionList = iMnjxPsgCkiOptionService.lambdaQuery()
                .eq(MnjxPsgCkiOption::getPsgCkiId, paResultDto.getPsgCkiId())
                .list();
        // PETC BSCT
        Optional<MnjxPsgCkiOption> petcOptional = psgCkiOptionList.stream()
                .filter(p -> "PETC".equals(p.getOptionType()))
                .findFirst();
        if (petcOptional.isPresent()) {
            MnjxPsgCkiOption petc = petcOptional.get();
            paResultDto.setPetcNum(petc.getOptionValue().split(StrUtil.SLASH)[0]);
            paResultDto.setPetcWeight(petc.getOptionValue().split(StrUtil.SLASH)[1]);
        }
        Optional<MnjxPsgCkiOption> bsctOptional = psgCkiOptionList.stream()
                .filter(p -> "BSCT".equals(p.getOptionType()))
                .findFirst();
        if (bsctOptional.isPresent()) {
            MnjxPsgCkiOption bsct = bsctOptional.get();
            paResultDto.setBsctNum(bsct.getOptionValue().split(StrUtil.SLASH)[0]);
            paResultDto.setBsctWeight(bsct.getOptionValue().split(StrUtil.SLASH)[1]);
        }
        // 额外占座
        Optional<MnjxPsgCkiOption> exstOptional = psgCkiOptionList.stream()
                .filter(p -> StrUtil.equalsAny(p.getOptionType(), "EXST", "COUR", "CBBG", "DIPL"))
                .findFirst();
        if (exstOptional.isPresent()) {
            MnjxPsgCkiOption exstType = exstOptional.get();
            paResultDto.setExstType(exstType.getOptionType());
            paResultDto.setExstWeight(exstType.getOptionValue());
            MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                    .eq(MnjxPsgSeat::getPsgCkiId, paResultDto.getPsgCkiId())
                    .one();
            paResultDto.setExstSeat(psgSeat.getSeatExst());
        }
        // 邻近占座
        Optional<MnjxPsgCkiOption> adsrOptional = psgCkiOptionList.stream()
                .filter(p -> "ADSR".equals(p.getOptionType()))
                .findFirst();
        if (adsrOptional.isPresent()) {
            MnjxPsgCkiOption adsr = adsrOptional.get();
            paResultDto.setAdsr(adsr.getOptionType());
            paResultDto.setAdsrWeight(adsr.getOptionValue());
        }

        // 处理票号
        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, paResultDto.getPnrNmId())
                .one();
        List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, paResultDto.getPnrNmId())
                .or(ObjectUtil.isNotEmpty(nmXn), p -> p.eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId()))
                .list();
        List<String> tnIdList = pnrNmTnList.stream()
                .map(MnjxPnrNmTn::getTnId)
                .collect(Collectors.toList());
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                .and(m -> m.eq(MnjxPnrNmTicket::getS1Id, paResultDto.getMnjxPnrSeg().getPnrSegId())
                        .or().eq(MnjxPnrNmTicket::getS2Id, paResultDto.getMnjxPnrSeg().getPnrSegId()))
                .eq(MnjxPnrNmTicket::getIsEt, Constant.STR_ONE)
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();
        if (CollUtil.isNotEmpty(nmTicketList)) {
            List<String> ticketNoList = nmTicketList.stream()
                    .map(MnjxPnrNmTicket::getTicketNo)
                    .collect(Collectors.toList());
            paResultDto.setEt(true);
            paResultDto.setTicketNoList(ticketNoList);
        }

        // 姓名处理
        MnjxPnrNm pnrNm = paResultDto.getPnrNm();
        if (ObjectUtil.isEmpty(pnrNm)) {
            pnrNm = iMnjxPnrNmService.getById(paResultDto.getPnrNmId());
        }
        String queryName = pnrNm.getQueryName().toUpperCase();
        if (StrUtil.isNotEmpty(pnrNm.getIsCnin())) {
            if (queryName.length() > Constant.SEVENTEEN) {
                queryName = StrUtil.format("{}/", queryName.substring(0, 17));
            } else {
                queryName = StrUtil.fill(queryName + StrUtil.SLASH, ' ', 18, false);
            }
        } else {
            if (queryName.length() > Constant.SEVENTEEN) {
                queryName = StrUtil.format("{}+", queryName.substring(0, 17));
            } else {
                queryName = StrUtil.fill(queryName, ' ', 18, false);
            }
        }
        paResultDto.setQueryName(queryName);
        MnjxPnr pnr = paResultDto.getPnr();
        if (ObjectUtil.isEmpty(pnr)) {
            pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        }
        paResultDto.setGroupName(StrUtil.fill(StrUtil.emptyToDefault(pnr.getDefaultGroupName(), ""), ' ', 3, false));

        // 接收额外占座但是座位数不足产生CAP
        if (Constant.STR_ONE.equals(psgCki.getCap())) {
            paResultDto.setCap(true);
        }
    }

    @Override
    public String getAvLayout(String originLayout, List<MnjxPlanSection> planSectionList) {
        return iSyService.orderLayoutStr(originLayout, this.getAvSeats(planSectionList));
    }

    /**
     * Title: getAvLayout
     * Description: 获取AV剩余座位
     *
     * @param mnjxPlanSections mnjxPlanSections
     * @return 获取AV剩余座位
     * <AUTHOR>
     */
    private String getAvSeats(List<MnjxPlanSection> mnjxPlanSections) {
        List<String> planSectionIdList = mnjxPlanSections.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .list();
        Map<String, List<MnjxOpenCabin>> cabinMap = openCabinList.stream()
                .collect(Collectors.groupingBy(MnjxOpenCabin::getCabinClass));
        Set<Map.Entry<String, List<MnjxOpenCabin>>> entries = cabinMap.entrySet();
        StringBuilder avSb = new StringBuilder();
        for (Map.Entry<String, List<MnjxOpenCabin>> entry : entries) {
            String cabinClass = entry.getKey();
            if (StrUtil.isEmpty(cabinClass)) {
                continue;
            }
            List<MnjxOpenCabin> cabinList = entry.getValue();
            List<String> openCabinIdList = cabinList.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            List<MnjxSeat> avSeatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .isNotNull(MnjxSeat::getSeatNo)
                    .notIn(MnjxSeat::getSeatStatus, "X", "T", ".")
                    .list();
            avSb.append(cabinClass).append(avSeatList.size());
        }
        return avSb.toString();
    }

    /**
     * Title: getCndCabinWeight
     * Description: 获取cnd默认行李配重
     *
     * @param cnd        cnd
     * @param cabinClass cabinClass
     * @return 获取cnd默认行李配重
     * <AUTHOR>
     */
    private int getCndCabinWeight(MnjxCnd cnd, String cabinClass) {
        int weight;
        if (cabinClass.equals(cnd.getFirstCabinClass())) {
            weight = cnd.getFirstWeight();
        } else if (cabinClass.equals(cnd.getSecondCabinClass())) {
            weight = cnd.getSecondWeight();
        } else if (cabinClass.equals(cnd.getThirdCabinClass())) {
            weight = cnd.getThirdWeight();
        } else if (cabinClass.equals(cnd.getFourthCabinClass())) {
            weight = cnd.getFourthWeight();
        } else {
            weight = cnd.getFifthWeight();
        }
        return weight;
    }

    private void insertPnrRecord(PaResultDto paResultDto, String pnrType, String inputValue, String changeMark) {
        String pnrId = paResultDto.getPnr().getPnrId();
        List<MnjxPnrRecord> recordList = paResultDto.getPnrRecordList();
        OptionalInt maxRecordIndex = recordList.stream()
                .mapToInt(MnjxPnrRecord::getPnrIndex)
                .max();
        OptionalInt maxAtNum = recordList.stream()
                .mapToInt(r -> Integer.parseInt(r.getAtNo()))
                .max();

        MnjxPnrRecord record = new MnjxPnrRecord();
        record.setPnrRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
        record.setPnrId(pnrId);
        record.setAtNo(StrUtil.fill(StrUtil.toString(maxAtNum.getAsInt() + 1), '0', 3, true));
        record.setPnrIndex(maxRecordIndex.getAsInt() + 1);
        record.setPnrType(pnrType);
        record.setInputValue(inputValue);
        if (StrUtil.isNotEmpty(changeMark)) {
            record.setChangeMark(changeMark);
            long count = recordList.stream()
                    .filter(r -> StrUtil.isNotEmpty(r.getChangeAtNo()))
                    .count();
            if (count > 0) {
                OptionalInt maxChangeAtNum = recordList.stream()
                        .filter(r -> StrUtil.isNotEmpty(r.getChangeAtNo()))
                        .mapToInt(r -> Integer.parseInt(r.getChangeAtNo()))
                        .max();
                record.setChangeAtNo(StrUtil.fill(StrUtil.toString(maxChangeAtNum.getAsInt() + 1), '0', 3, true));
            } else {
                record.setChangeAtNo("001");
            }
        }
        iAspectCkiService.insertPnrRecord(record, paResultDto.getPnrNmId());
//        iMnjxPnrRecordService.save(record);
    }

    @Override
    @Transactional(rollbackFor = UnifiedResultException.class)
    public void handleUres(List<HbpaCmdDto> hbpaCmdDtoList, List<PaResultDto> paResultDtoList) throws UnifiedResultException {
        HbpaCmdDto firstHbpaCmdDto = hbpaCmdDtoList.get(0);
        String flightNo = firstHbpaCmdDto.getFlightNo();
        String flightDate = firstHbpaCmdDto.getFlightDate();
        String ymdDate = flightDate.contains(StrUtil.DASHED) ? flightDate : DateUtils.com2ymd(flightDate);
        String comDate = DateUtils.ymd2Com(ymdDate);
        String weekend = DateUtils.ymd2WeekEn(ymdDate).substring(0, 2);
        String dstCityCode = firstHbpaCmdDto.getDstCityCode();
        String sellCabin = firstHbpaCmdDto.getSellCabin();
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();
        String officeNo = mnjxOffice.getOfficeNo();
        String org = officeNo.substring(0, 3);
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        if (ObjectUtil.isEmpty(flight)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        List<MnjxTcardSection> tcardSectionList = iMnjxTcardSectionService.lambdaQuery()
                .eq(MnjxTcardSection::getTcardId, tcard.getTcardId())
                .orderByAsc(MnjxTcardSection::getSectionNo)
                .list();
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, org)
                .one();
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, dstCityCode)
                .one();
        // 验证目的地
        List<MnjxTcardSection> noFirstTcardSectionList = tcardSectionList.stream()
                .filter(t -> t.getSectionNo() != 1)
                .collect(Collectors.toList());
        if (noFirstTcardSectionList.stream().noneMatch(t -> StrUtil.equals(t.getAirportId(), dstAirport.getAirportId()))) {
            throw new UnifiedResultException(Constant.DESTINATION);
        }
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, ymdDate)
                .one();
        if (ObjectUtil.isEmpty(planFlight)) {
            throw new UnifiedResultException(Constant.DATE);
        }
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .list();
        // 验证日期
        DateTime flightDateTime = DateUtil.parseDate(ymdDate);
        DateTime todayDateTime = DateUtil.parseDate(DateUtil.today());
        if (DateUtil.compare(flightDateTime, todayDateTime) < 0) {
            throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
        }
        if (ymdDate.equals(DateUtil.today())) {
            for (MnjxPlanSection planSection : planSectionList) {
                String estimateBoarding = planSection.getEstimateBoarding();
                String offDate = StrUtil.format("{} {}:{}:00", ymdDate, estimateBoarding.substring(0, 2), estimateBoarding.substring(2));
                DateTime offDateTime = DateUtil.parseDateTime(offDate);
                DateTime nowDateTime = DateUtil.parseDateTime(DateUtil.now());
                if (DateUtil.compare(offDateTime, nowDateTime) < 0) {
                    throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
                }
            }
        }
        // 筛选符合的航节。多航段时将多个航节一起处理
        List<MnjxPlanSection> filterPlanSectionList = planSectionList.stream()
                .filter(p -> orgAirport.getAirportId().equals(p.getDepAptId()) && dstAirport.getAirportId().equals(p.getArrAptId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(filterPlanSectionList)) {
            for (MnjxPlanSection planSection : planSectionList) {
                if (orgAirport.getAirportId().equals(planSection.getDepAptId())) {
                    filterPlanSectionList.add(planSection);
                } else if (dstAirport.getAirportId().equals(planSection.getArrAptId())) {
                    filterPlanSectionList.add(planSection);
                    break;
                } else if (CollUtil.isNotEmpty(filterPlanSectionList)) {
                    filterPlanSectionList.add(planSection);
                }
            }
        }
        if (CollUtil.isEmpty(filterPlanSectionList)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        Map<String, String> gsMap = filterPlanSectionList.stream()
                .collect(Collectors.toMap(MnjxPlanSection::getPlanSectionId, v -> StrUtil.emptyToDefault(v.getGoshowLimit(), "")));
        List<String> planSectionIdList = filterPlanSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .eq(MnjxOpenCabin::getSellCabin, sellCabin)
                .list();
        String cabinClass = openCabinList.get(0).getCabinClass();
        List<MnjxOpenCabin> cabinClassOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .eq(MnjxOpenCabin::getCabinClass, cabinClass)
                .list();

        List<MnjxPsgCki> hbPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                .isNotNull(MnjxPsgCki::getHbNo)
                .list();
        int hbNbNo = 1;
        Integer aboardNo = null;
        if (CollUtil.isNotEmpty(hbPsgCkiList)) {
            OptionalInt max = hbPsgCkiList.stream()
                    .mapToInt(p -> Integer.parseInt(p.getHbNo()))
                    .max();
            hbNbNo = max.getAsInt() + 1;
        }

        MnjxSi mnjxSi = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi();

        List<MnjxPsgCki> psgCkiList = new ArrayList<>();
        List<MnjxPsgSeat> psgSeatList = new ArrayList<>();
        List<MnjxPnr> pnrList = new ArrayList<>();
        List<MnjxPnrNm> pnrNmList = new ArrayList<>();
        List<MnjxPnrSeg> pnrSegList = new ArrayList<>();
        List<MnjxPnrNmTn> pnrNmTnList = new ArrayList<>();
        List<MnjxPnrNmTicket> pnrNmTicketList = new ArrayList<>();
        List<PdNmDto> nmDtoList = new ArrayList<>();
        int seatOptionCount = 0;
        int hbNb1 = 0;
        Map<String, List<String>> existRandomSeatListMap = new HashMap<>(1024);
        int psgNum = hbpaCmdDtoList.size();
        Integer totalGsNum = null;
        for (HbpaCmdDto hbpaCmdDto : hbpaCmdDtoList) {
            PaResultDto paResultDto = new PaResultDto();
            PdNmDto pdNmDto = new PdNmDto();
            MnjxPsgCki psgCki = new MnjxPsgCki();
            MnjxPsgSeat psgSeat = new MnjxPsgSeat();
            MnjxPnr pnr = new MnjxPnr();
            MnjxPnrNm pnrNm = new MnjxPnrNm();
            MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
            MnjxPnrNmTn tn = new MnjxPnrNmTn();
            MnjxPnrNmTicket ticket = new MnjxPnrNmTicket();
            // 先生成候补旅客数据
            List<String> optionList = hbpaCmdDto.getOptionList();
            for (int i = 0; i < optionList.size(); i++) {
                String option = optionList.get(i);
                if (i == 0) {
                    if (!option.matches("\\d+[A-Z/]+")) {
                        throw new UnifiedResultException(Constant.CHECK_NAME);
                    }
                    String[] split = option.split("\\d+");
                    String name = split[1];
                    String index = option.replace(name, "");
                    if (!Constant.STR_ONE.equals(index)) {
                        throw new UnifiedResultException(Constant.NUMBER_ERROR);
                    }
                    // 开始构建候补的PNR
                    String pnrId = IdUtil.getSnowflake(1, 1).nextIdStr();
                    pnr.setPnrId(pnrId);
                    pnr.setPnrStatus(Constant.OP);
                    pnr.setCreateOfficeNo(officeNo);
                    pnr.setCreateSiId(mnjxSi.getSiId());
                    pnr.setCreateTime(new Date());
                    // 构建nm
                    pnrNm.setPnrNmId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrNm.setPnrId(pnrId);
                    pnrNm.setName(name);
                    pnrNm.setQueryName(name);
                    pnrNm.setPsgIndex(Constant.ONE);
                    pnrNm.setPsgType(Constant.STR_ZERO);
                    pnrNm.setInputValue(name);
                    // 构建seg
                    pnrSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrSeg.setPnrId(pnrId);
                    pnrSeg.setFlightNo(flightNo);
                    pnrSeg.setFlightDate(ymdDate);
                    pnrSeg.setPnrSegNo(Constant.ONE);
                    pnrSeg.setCabinClass(cabinClass);
                    pnrSeg.setSellCabin(sellCabin);
                    pnrSeg.setOrg(org);
                    pnrSeg.setDst(dstCityCode);
                    pnrSeg.setActionCode(Constant.ACTION_CODE_HK);
                    pnrSeg.setSeatNumber(Constant.ONE);
                    pnrSeg.setEstimateOff(filterPlanSectionList.get(0).getEstimateOff());
                    pnrSeg.setEstimateArr(filterPlanSectionList.get(filterPlanSectionList.size() - 1).getEstimateArr());
                    pnrSeg.setPnrSegType(Constant.SEG_TYPE_SD);
                    pnrSeg.setPlaneVersion(tcard.getEqt());
                    // CA8916 K TH22SEP22 CTUPEK HK1 1741 2041 303A  0 R E T1T2
                    pnrSeg.setInputValue(StrUtil.format("  {} {} {}{} {}{} HK1 {} {} {}  0 R E T1T2", flightNo, sellCabin, weekend, comDate, org, dstCityCode, pnrSeg.getEstimateOff(), pnrSeg.getEstimateArr(), pnrSeg.getPlaneVersion()));
                    // 构建psg_cki
                    String psgCkiId = IdUtil.getSnowflake(1, 1).nextIdStr();
                    psgCki.setPsgCkiId(psgCkiId);
                    psgCki.setPnrNmId(pnrNm.getPnrNmId());
                    psgCki.setPnrSegNo(Constant.STR_ONE);
                    psgCki.setCabinClass(cabinClass);
                    psgCki.setSellCabin(sellCabin);
                    // 构建psg_seat
                    psgSeat.setPsgCkiId(psgCkiId);
                    // 构建tn
                    tn.setPnrNmId(pnrNm.getPnrNmId());
                    tn.setTnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    tn.setIssuedAirline(flightNo.substring(0, 2));
                    tn.setInputValue("TN/P1");
                    tn.setIssuedSiId(mnjxSi.getSiId());
                    // 构建ticket
                    ticket.setPnrNmTnId(tn.getTnId());
                    ticket.setS1Id(pnrSeg.getPnrSegId());
                    if (hbNb1 == 0) {
                        List<String> pnrSegIdList = iMnjxPnrSegService.lambdaQuery()
                                .eq(MnjxPnrSeg::getFlightNo, flightNo)
                                .eq(MnjxPnrSeg::getFlightDate, ymdDate)
                                .list()
                                .stream()
                                .map(MnjxPnrSeg::getPnrSegId)
                                .collect(Collectors.toList());
                        if (CollUtil.isEmpty(pnrSegIdList)) {
                            hbNb1 = 1;
                        } else {
                            List<MnjxPnrNmTicket> ticketList = iMnjxPnrNmTicketService.lambdaQuery()
                                    .in(MnjxPnrNmTicket::getS1Id, pnrSegIdList)
                                    .orderByDesc(MnjxPnrNmTicket::getHbnb1)
                                    .list();
                            if (CollUtil.isEmpty(ticketList) || ticketList.stream().noneMatch(t -> StrUtil.isNotEmpty(t.getHbnb1()))) {
                                hbNb1 = 1;
                            } else {
                                hbNb1 = Integer.parseInt(ticketList.get(0).getHbnb1()) + 1;
                            }
                        }
                    } else {
                        hbNb1++;
                    }
                    ticket.setHbnb1(StrUtil.fill(StrUtil.toString(hbNb1), '0', 4, true));
                    paResultDto.setPsgNum(StrUtil.fill(StrUtil.toString(hbNb1), ' ', 6, false));
                    pnr.setMaxIndex(3);
                } else {
                    if ("URES".equals(option)) {
                        psgCki.setUres(Constant.STR_ONE);
                        paResultDto.setUres(option);
                    } else if ("NREC".equals(option)) {
                        psgCki.setNrec(Constant.STR_ONE);
                        paResultDto.setNrec(option);
                    } else if (ReUtil.isMatch(BAG_PATTERN, option)) {
                        this.parseBag(option, paResultDto);
                    } else if (ReUtil.isMatch(AVIH_PATTERN, option)) {
                        this.parseAvih(option, paResultDto);
                    } else if (ReUtil.isMatch(BAG_DST, option)) {
                        String bagDst = option.substring(2);
                        if (!bagDst.equals(dstCityCode)) {
                            throw new UnifiedResultException(Constant.BAG_TAG_DESTINATION);
                        }
                        paResultDto.setBagDst(bagDst);
                    } else if (ReUtil.isMatch(SEAT_PATTERN, option)) {
                        seatOptionCount++;
                        this.parseSeat(option, cabinClassOpenCabinList, paResultDto, filterPlanSectionList.size(), false, existRandomSeatListMap, psgNum, paResultDtoList);
                    } else if (ReUtil.isMatch(SNR_PATTERN, option)) {
                        seatOptionCount++;
                        this.parseSnrSeat(option, cabinClassOpenCabinList, paResultDto, filterPlanSectionList.size(), existRandomSeatListMap);
                    } else if (option.startsWith("FOID/")) {
                        String[] split = option.split(StrUtil.SLASH);
                        String s = split[1];
                        String type = s.substring(0, 2);
                        String number = s.substring(2);
                        if (Constant.NI.equals(type)) {
                            iSsrService.validateCertificateNumber(number);
                        }
                        paResultDto.setFoidParam(option);
                    } else if (option.startsWith("INF1")) {
                        paResultDto.setHasInf(true);
                        paResultDto.setInfName(option.substring(4));
                    } else if ("XBT".equals(option)) {
                        paResultDto.setXbt(option);
                    } else if ("XBP".equals(option)) {
                        paResultDto.setXbp(option);
                    } else {
                        throw new UnifiedResultException(Constant.ITEM);
                    }
                }
            }
            // 接收座位参数不能同时出现SNR 和 R，或不能同时输入多次
            if ((paResultDto.isSnr() && paResultDto.isNormalSeat()) || seatOptionCount > 1) {
                throw new UnifiedResultException(Constant.DUP_IS_FOUND);
            }
            if (StrUtil.isNotEmpty(hbpaCmdDto.getNrec())) {
                psgCki.setNrec(Constant.STR_ONE);
                paResultDto.setNrec(hbpaCmdDto.getNrec());
            }
            psgCkiList.add(psgCki);
            psgSeatList.add(psgSeat);
            pnrList.add(pnr);
            pnrNmList.add(pnrNm);
            pnrSegList.add(pnrSeg);
            pnrNmTnList.add(tn);
            pnrNmTicketList.add(ticket);
            paResultDto.setCabinClassOpenCabinList(cabinClassOpenCabinList);
            paResultDto.setMnjxPsgCki(psgCki);
            paResultDto.setPnr(pnr);
            paResultDto.setPnrNm(pnrNm);
            paResultDto.setMnjxPnrSeg(pnrSeg);
            paResultDto.setMnjxPsgSeat(psgSeat);
            paResultDto.setPnrNmId(pnrNm.getPnrNmId());
            paResultDto.setSellCabin(sellCabin);
            paResultDto.setCabinClass(cabinClass);
            paResultDto.setQueryName(StrUtil.fill(pnrNm.getQueryName(), ' ', 13, false));
            paResultDto.setAdv(hbpaCmdDto.isAdv());
            paResultDto.setAirlineCode(flightNo.substring(0, 2));
            paResultDto.setFlightNo(flightNo);
            paResultDto.setFlightDate(comDate);
            paResultDto.setBdt(filterPlanSectionList.get(0).getEstimateBoarding());
            paResultDto.setCnin(false);
            paResultDto.setDstAirport(dstCityCode);
            paResultDto.setName(StrUtil.fill(pnrNm.getName(), ' ', 13, false));
            paResultDto.setOrgAirport(org);
            String cndNo = planFlight.getCndNo();
            paResultDto.setCndNo(cndNo);
            MnjxPlane plane = iMnjxPlaneService.getById(filterPlanSectionList.get(0).getPlaneId());
            MnjxPlaneModel planeModel = iMnjxPlaneModelService.getById(plane.getPlaneModelId());
            paResultDto.setPlaneType(planeModel.getPlaneModelType());
            paResultDto.setPlaneVersion(planeModel.getPlaneModelVersion());
            paResultDto.setPsgCkiId(psgCki.getPsgCkiId());
            String cityEname = iMnjxCityService.getById(dstAirport.getCityId()).getCityEname();
            paResultDto.setCityName(cityEname);
            paResultDto.setAirportInfo(orgAirport.getAirportEname());
            paResultDto.setGate(StrUtil.isEmpty(filterPlanSectionList.get(0).getGate()) ? "????" : filterPlanSectionList.get(0).getGate());
            // 校验GS限额足够  如果携带了行李，预接收的候补也需要消耗gs限额
            if (!hbpaCmdDto.isAdv() || StrUtil.isNotEmpty(paResultDto.getBagNumber())) {
                for (MnjxPlanSection planSection : filterPlanSectionList) {
                    String goshowLimit = planSection.getGoshowLimit();
                    if (StrUtil.isEmpty(goshowLimit)) {
                        if (StrUtil.isNotEmpty(paResultDto.getBagNumber())) {
                            throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                        }
                        paResultDto.setCkiStatus(Constant.SB);
                        psgCki.setCkiStatus(Constant.SB);
                        psgCki.setIsHb(Constant.STR_ONE);
                        psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                        paResultDto.setHbNo(psgCki.getHbNo());
                        hbNbNo++;
                    } else {
                        String[] split = goshowLimit.split(StrUtil.SLASH);
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < split.length; i++) {
                            String s = split[i];
                            if (s.substring(0, 1).equals(cabinClass)) {
                                String substring = s.substring(1);
                                if (substring.startsWith(StrUtil.DASHED) || Integer.parseInt(substring) == 0) {
                                    if (StrUtil.isNotEmpty(paResultDto.getBagNumber())) {
                                        throw new UnifiedResultException(Constant.BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED);
                                    }
                                    paResultDto.setCkiStatus(Constant.SB);
                                    psgCki.setCkiStatus(Constant.SB);
                                    psgCki.setIsHb(Constant.STR_ONE);
                                    psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                                    paResultDto.setHbNo(psgCki.getHbNo());
                                    hbNbNo++;
                                } else {
                                    int gs = Integer.parseInt(substring);
                                    if (ObjectUtil.isEmpty(totalGsNum)) {
                                        totalGsNum = gs;
                                    }
                                    if (totalGsNum < psgNum) {
                                        paResultDto.setCkiStatus(Constant.SB);
                                        psgCki.setCkiStatus(Constant.SB);
                                        psgCki.setIsHb(Constant.STR_ONE);
                                        psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                                        paResultDto.setHbNo(psgCki.getHbNo());
                                        hbNbNo++;
                                    } else {
                                        gs = gs - 1;
                                        s = StrUtil.format("{}{}", cabinClass, StrUtil.fill(StrUtil.toString(gs), '0', 3, true));
                                        if (hbpaCmdDto.isAdv()) {
                                            paResultDto.setCkiStatus(Constant.NACC);
                                            psgCki.setCkiStatus(Constant.NACC);
                                            psgCki.setIsHb(Constant.STR_ONE);
                                        } else {
                                            paResultDto.setCkiStatus(Constant.ACC);
                                            psgCki.setCkiStatus(Constant.ACC);
                                        }
                                    }
                                }
                            }
                            sb.append(s);
                            if (i < split.length - 1) {
                                sb.append(StrUtil.SLASH);
                            }
                        }
                        // GS验证之后如果重新构造的GS没有包含该舱等，说明没有该舱等的限额，设置成SB
                        if (!sb.toString().contains(cabinClass)) {
                            paResultDto.setCkiStatus(Constant.SB);
                            psgCki.setCkiStatus(Constant.SB);
                            psgCki.setIsHb(Constant.STR_ONE);
                            psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                            paResultDto.setHbNo(psgCki.getHbNo());
                            hbNbNo++;
                        }
                        planSection.setGoshowLimit(sb.toString());
                    }
                }
            } else {
                paResultDto.setCkiStatus(Constant.NACC);
                psgCki.setCkiStatus(Constant.NACC);
                psgCki.setIsHb(Constant.STR_ONE);
            }
            BeanUtil.copyProperties(paResultDto, pdNmDto);
            paResultDto.setPlanSectionList(filterPlanSectionList);
            paResultDtoList.add(paResultDto);
            pdNmDto.setIsEt(Constant.STR_ONE);
            pdNmDto.setTnId(tn.getTnId());
            pdNmDto.setPnrSegId(pnrSeg.getPnrSegId());
            pdNmDto.setPnrSegNo(StrUtil.toString(pnrSeg.getPnrSegNo()));
            pdNmDto.setOrg(pnrSeg.getOrg());
            pdNmDto.setDst(pnrSeg.getDst());
            pdNmDto.setPnrIcs(pnr.getPnrIcs());
            pdNmDto.setPnrId(pnr.getPnrId());
            nmDtoList.add(pdNmDto);
        }

        // 先执行一次座位的逻辑，看舱等是否还有剩余可用的座位，没有问题之后再进行数据存储
        for (PaResultDto paResultDto : paResultDtoList) {
            MnjxPsgCki psgCki = psgCkiList.stream()
                    .filter(p -> paResultDto.getPsgCkiId().equals(p.getPsgCkiId()))
                    .collect(Collectors.toList())
                    .get(0);
            if (Constant.ACC.equals(psgCki.getCkiStatus()) && StrUtil.isEmpty(paResultDto.getSeatNo())) {
                this.getRandomAllowSeat(cabinClassOpenCabinList, filterPlanSectionList.size(), paResultDto, new HashMap<>(), psgNum);
            }
        }
        // 座位不足引起候补
        if (paResultDtoList.stream().anyMatch(PaResultDto::isCap)) {
            // 恢复上面扣掉的GS限额
            filterPlanSectionList.forEach(s -> s.setGoshowLimit(gsMap.get(s.getPlanSectionId())));
            for (PaResultDto paResultDto : paResultDtoList) {
                paResultDto.setCkiStatus(Constant.SB);
                paResultDto.setCap(true);
                paResultDto.setSeatNo(null);
                paResultDto.setExstSeat(null);
                paResultDto.setNormalSeat(false);
                paResultDto.setSnr(false);
                paResultDto.setInputSeatNo(null);
                paResultDto.setMnjxSeatList(null);
                paResultDto.setPreOldSeat(null);
                paResultDto.getMnjxPsgSeat().setPsgSeat("");
                paResultDto.getMnjxPsgSeat().setSeatExst("");
                paResultDto.getMnjxPsgSeat().setSeatStatus("");
                for (MnjxPsgCki psgCki : psgCkiList) {
                    if (paResultDto.getPsgCkiId().equals(psgCki.getPsgCkiId())) {
                        psgCki.setCap(Constant.STR_ONE);
                        psgCki.setCkiStatus(Constant.SB);
                        psgCki.setIsHb(Constant.STR_ONE);
                        psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
                        paResultDto.setHbNo(psgCki.getHbNo());
                        hbNbNo++;
                    }
                }
            }
//            paResultDtoList.forEach(p -> {
//                p.setCkiStatus(Constant.SB);
//                p.setCap(true);
//                p.setSeatNo(null);
//                p.setExstSeat(null);
//                p.setNormalSeat(false);
//                p.setSnr(false);
//                p.setInputSeatNo(null);
//                p.setMnjxSeatList(null);
//                p.setPreOldSeat(null);
//                p.getMnjxPsgSeat().setPsgSeat("");
//                p.getMnjxPsgSeat().setSeatExst("");
//                p.getMnjxPsgSeat().setSeatStatus("");
//                psgCkiList.stream()
//                        .filter(psgCki -> p.getPsgCkiId().equals(psgCki.getPsgCkiId()))
//                        .forEach(psgCki -> {
//                            psgCki.setCap(Constant.STR_ONE);
//                            psgCki.setCkiStatus(Constant.SB);
//                            psgCki.setIsHb(Constant.STR_ONE);
//                            psgCki.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
//                            p.setHbNo(psgCki.getHbNo());
//                            hbNbNo++;
//                        });
//            });
        }

        iMnjxPnrService.saveBatch(pnrList);
        iMnjxPnrSegService.saveBatch(pnrSegList);
        iMnjxPnrNmService.saveBatch(pnrNmList);
        iMnjxPsgCkiService.saveBatch(psgCkiList);
        iMnjxPsgSeatService.saveBatch(psgSeatList);
        iMnjxPnrNmTnService.saveBatch(pnrNmTnList);
        iMnjxPnrNmTicketService.saveBatch(pnrNmTicketList);
        // 更新航节计划，主要是GS限额的修改
        iMnjxPlanSectionService.updateBatchById(filterPlanSectionList);

        for (PaResultDto paResultDto : paResultDtoList) {
            List<MnjxPsgOperateRecord> psgOperateRecordList = new ArrayList<>();
            PdNmDto nmDto = nmDtoList.stream()
                    .filter(n -> paResultDto.getPnrNmId().equals(n.getPnrNmId()))
                    .collect(Collectors.toList())
                    .get(0);
            MnjxPsgCki psgCki = psgCkiList.stream()
                    .filter(p -> paResultDto.getPsgCkiId().equals(p.getPsgCkiId()))
                    .collect(Collectors.toList())
                    .get(0);
            // GS足够接收候补旅客时，添加登机号
            if (Constant.ACC.equals(psgCki.getCkiStatus())) {
                if (ObjectUtil.isEmpty(aboardNo)) {
                    List<MnjxPnrSeg> thisFlightSegList = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getFlightNo, flightNo)
                            .eq(MnjxPnrSeg::getFlightDate, ymdDate)
                            .eq(MnjxPnrSeg::getOrg, org)
                            .eq(MnjxPnrSeg::getDst, dstCityCode)
                            .list();
                    aboardNo = this.getNextAboardNo(thisFlightSegList);
                } else {
                    aboardNo++;
                }
                psgCki.setAboardNo(StrUtil.fill(StrUtil.toString(aboardNo), '0', 3, true));
                paResultDto.setBnNo(psgCki.getAboardNo());
                iMnjxPsgCkiService.updateById(psgCki);
            }

            for (MnjxPnrSeg pnrSeg : pnrSegList) {
                this.handleNmData(paResultDto, pnrSeg, psgCki);
            }
            if (StrUtil.isNotEmpty(paResultDto.getFoidParam())) {
                MnjxNmSsr nmSsr = new MnjxNmSsr();
                nmSsr.setPnrNmId(paResultDto.getPnrNmId());
                nmSsr.setActionCode(Constant.HK);
                nmSsr.setSsrType(Constant.SSR_TYPE_FOID);
                nmSsr.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));
                String inputValue = StrUtil.format("SSR FOID {} HK1 {}/P1", nmSsr.getAirlineCode(), paResultDto.getFoidParam().split(StrUtil.SLASH)[1]);
                nmSsr.setSsrInfo(inputValue);
                nmSsr.setInputValue(inputValue);
                iMnjxNmSsrService.save(nmSsr);
            }
            this.handleBagData(paResultDto, psgCki, psgOperateRecordList);
            psgOperateRecordList.add(this.constructOperateRecord("SBY", null, psgCki.getPsgCkiId(), paResultDto, null));
            if (StrUtil.isNotEmpty(paResultDto.getSeatNo()) && !paResultDto.isCap()) {
                this.handleSeatData(psgCki.getPsgCkiId(), paResultDto);
            }
            iMnjxPsgOperateRecordService.saveBatch(psgOperateRecordList);
            this.formatRenderData(paResultDto);
            BeanUtil.copyProperties(paResultDto, nmDto);
            if (StrUtil.isEmpty(paResultDto.getBagNumber()) && StrUtil.isEmpty(paResultDto.getBagWeight())) {
                nmDto.setLuggageNum(paResultDto.getBagNumber());
                nmDto.setLuggageWeight(paResultDto.getBagWeight());
                nmDto.setLuggageNos(paResultDto.getBagNoList());
            }
            if (StrUtil.isNotEmpty(paResultDto.getBnNo())) {
                nmDto.setAboardNo(StrUtil.fill(paResultDto.getBnNo().trim(), '0', 3, true));
            }
            if (StrUtil.isNotEmpty(paResultDto.getHbNo())) {
                nmDto.setHbNo(StrUtil.fill(paResultDto.getHbNo().trim(), '0', 4, true));
            }
            nmDto.setPsgNum(StrUtil.fill(paResultDto.getPsgNum().trim(), '0', 4, true));
            nmDto.setIsCnin(null);
        }

        // 由于新加了旅客，需要更新PD的缓存，否则后续指令无法拿到这些新的旅客
        PdInfoDto pdDefaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
        if (ObjectUtil.isNotEmpty(pdDefaultInfo)) {
            String pdCmd = pdDefaultInfo.getCmdStr();
            PdParamDto pdParamDto = iPdService.dealCmd(pdCmd);
            iPdService.returnDefaultInfo(memoryData, pdParamDto);
        }
    }
}