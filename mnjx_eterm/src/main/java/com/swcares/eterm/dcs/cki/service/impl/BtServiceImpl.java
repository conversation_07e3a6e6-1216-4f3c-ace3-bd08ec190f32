package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.mapper.BtMapper;
import com.swcares.eterm.dcs.cki.obj.dto.BtDto;
import com.swcares.eterm.dcs.cki.service.IBtService;
import com.swcares.eterm.dcs.cki.service.ISeService;
import com.swcares.service.IMnjxSeatService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * BT业务处理
 *
 * <AUTHOR>
 */
@Service
public class BtServiceImpl implements IBtService {

    @Resource
    private BtMapper btMapper;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private ISeService iSeService;

    /***
     * >BT：航班号/日期/舱位/航段/座位号
     */
    private static final String BT_PATTERN = "^BT:(\\w{5,7})/(\\d{1,2}[A-Z]{3}(\\d{2})?|[+-.])/([A-Z*])/([A-Z]{6})/(-)?(\\d{1,3}[A-Z])$";


    @Override
    public BtDto parse(String cmd) throws UnifiedResultException {
        // 指令格式验证
        if (!ReUtils.isMatch(BT_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 参数解析
        List<String> allGroups = ReUtils.getAllGroups(Pattern.compile(BT_PATTERN), cmd);
        BtDto btDto = new BtDto();
        // 航班号
        btDto.setFlightNo(allGroups.get(1));
        // 航班日期
        btDto.setFlightDate(DateUtils.com2ymd(allGroups.get(2)));
        // 舱位
        btDto.setCabin(allGroups.get(4));
        // 航段
        btDto.setDep(allGroups.get(5).substring(0, 3));
        btDto.setArr(allGroups.get(5).substring(3, 6));
        // 释放标识
        btDto.setDash(allGroups.get(6));
        // 座位号
        btDto.setSeatNo(allGroups.get(7));
        // 回显用se指令
        btDto.setSeCmd(StrUtil.format("SE:{}/{}/{}{}", allGroups.get(1), allGroups.get(2), allGroups.get(4), allGroups.get(5)));
        return btDto;
    }

    @Override
    public String handler(BtDto btDto) throws UnifiedResultException {
        if (Constant.ASTERISK.equals(btDto.getCabin())){
            btDto.setCabin(null);
        }else {
            // 如果输入时为子舱位代码，需要向上查询到舱等
            List<MnjxOpenCabin> openCabinList;
            openCabinList = btMapper.retrieveCabin(btDto)
                    .stream().filter(o -> ObjectUtil.isNotEmpty(o) && btDto.getCabin().equals(o.getCabinClass()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(openCabinList)){
                openCabinList = btMapper.retrieveCabin(btDto)
                        .stream().filter(o -> ObjectUtil.isNotEmpty(o) && btDto.getCabin().equals(o.getSellCabin()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(openCabinList)){
                    throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
                }
                btDto.setCabin(openCabinList.get(0).getCabinClass());
            }
        }
        // 查询该航班该舱位等级的座位状态
        List<MnjxSeat> mnjxSeats = btMapper.retrieveSeatStatus(btDto);
        // 验证座位号是否存在
        if (CollUtil.isEmpty(mnjxSeats)){
            throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
        }
        for (MnjxSeat mnjxSeat:mnjxSeats){
            // 锁定座位
            if (StrUtil.isEmpty(btDto.getDash())){
                // 如果原座位是X或 "." 点，则不可锁定，报错：UNABLE
                if (!Constant.SEAT_STATUS_POINT.equals(mnjxSeat.getSeatStatus()) && !Constant.SEAT_STATUS_X.equals(mnjxSeat.getSeatStatus())){
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatus());
                    mnjxSeat.setSeatStatus(Constant.SEAT_STATUS_T);
                }else {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
            }else if (StrUtil.DASHED.equals(btDto.getDash())){
                //释放座位
                if (Constant.SEAT_STATUS_T.equals(mnjxSeat.getSeatStatus())){
                    // 将座位恢复为原座位
                    mnjxSeat.setSeatStatus(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(Constant.SEAT_STATUS_T);
                }else {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
            }
        }
        iMnjxSeatService.updateBatchById(mnjxSeats);
        return iSeService.handler(btDto.getSeCmd());
    }
}
