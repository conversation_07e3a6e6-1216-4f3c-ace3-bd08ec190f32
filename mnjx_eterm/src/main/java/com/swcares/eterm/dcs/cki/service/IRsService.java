package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.RsDto;

/**
 * RS指令数据处理
 *
 * <AUTHOR>
 */

public interface IRsService {
    /**
     * 参数解析
     *
     * @param cmd RS指令
     * @return 参数对象
     * @throws UnifiedResultException 统一异常处理
     */
    RsDto parseRs(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     * @param rsDto 参数对象
     * @return 修改后的座位图
     * @throws UnifiedResultException 统一异常处理
     */
    String handler(RsDto rsDto) throws UnifiedResultException;

}
