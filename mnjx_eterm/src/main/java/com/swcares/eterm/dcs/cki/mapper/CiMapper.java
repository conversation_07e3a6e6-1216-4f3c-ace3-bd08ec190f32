package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.eterm.dcs.cki.obj.dto.CiDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CiMapper {

    /**
     * retrieveFlightInfo
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return retrieveFlightInfo
     */
    List<CiDto> retrieveFlightInfo(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);
}
