package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.PwCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;
import com.swcares.eterm.dcs.cki.service.IPwService;

import javax.annotation.Resource;
import java.util.List;

/**
 * description：PwHandler <br>
 *
 * <AUTHOR> <br>
 * date 2022/08/13 <br>
 * @version v1.0 <br>
 */
@OperateType(action = "PW", template = "dcs/cki/pw.jf", shorthand = true, fullScreen = true)
public class PwHandler implements Handler {

    @Resource
    private IPwService iPwService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        List<PwCmdDto> pwDtoList = iPwService.parsePw(cmd);
        List<PwResultDto> pwResultDtoList = iPwService.handle(MemoryDataUtils.getMemoryData(), pwDtoList);
        iPwService.savePwForPrint(pwResultDtoList, "PW");
        unifiedResult.setArgs(new Object[]{cmd.split(":")[1]});
        unifiedResult.setResults(pwResultDtoList.toArray());
        return unifiedResult;
    }
}
