package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022/5/5-15:48
 */
@Data
public class FiPlanFltDto {
    /**
     * 航班主控（部门号）
     */
    private String officeNo;
    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 规则有效起始日期（需要转换成航信日期）
     */
    private String startDate;
    /**
     * 规则有效结束日期
     */
    private String endDate;

    /**
     * 班期 存储如 D,X12,123这样的日期规则代码，最多4位
     */
    private String cycle;
    /**
     * 航班类型:国际/国内
     */
    private String type;
    /**
     * 航班状态 ：STAGED，过渡区航班 MASTER CANCEL DELETE ACTIVE
     */
    private String mfStatus;
    private String mpfStatus;
    /**
     * cnd表号
     */
    private String cndNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 航班计划id
     */
    private String planFlightId;
    /**
     * 航班计划id
     */
    private String tcardId;
    /**
     * 机型
     */
    private String eqt;

    /**
     * 版本号
     */
    private String vers;

    /**
     * 起飞/到达 城市（用于显示输入机场查询的情况，会多出一行显示信息）
     */
    private String city;

    /**
     * 设置传入的参数城市的值
     */
    private String paramCity;

    /**
     * 计划航节数据
     */
    private List<FiPlanSectionDto> planSectionList = new ArrayList<>();
    /**
     * 判断是否FU指令修改过航节的登机口，起飞，到达时间等
     */
    private Boolean isFuUpdate;
}
