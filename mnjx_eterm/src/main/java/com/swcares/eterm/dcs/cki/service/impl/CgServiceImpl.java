package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.crs.obj.vo.CgVo;
import com.swcares.eterm.dcs.cki.mapper.CgMapper;
import com.swcares.eterm.dcs.cki.service.ICgService;
import com.swcares.service.IMnjxAirlineService;
import com.swcares.service.IMnjxAirportService;
import com.swcares.service.IMnjxCndService;
import com.swcares.service.IMnjxPlaneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CgServiceImpl implements ICgService {

    @Resource
    private CgMapper cgMapper;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    /**
     * 匹配航空公司代码 3U、CA、EU、TV 等
     * CG:CA CG:3U
     */
    private static final Pattern REGEX = Pattern.compile("CG:([a-zA-Z0-9]{2})");

    /**
     * 匹配航班号、日期及出发城市三字码 3U8888/./CTU 3U8888/05JUN20/CTU
     */
    private static final Pattern REGEX2 = Pattern.compile("CG:([a-zA-z0-9]{5,7})/([+-.|a-zA-Z0-9]{1,10})((/)([a-zA-Z]{3}))?");


    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        if (!ReUtil.isMatch(REGEX, cmd) && !ReUtil.isMatch(REGEX2, cmd)) {
            return Constant.FORMAT;
        }
        if (ReUtil.isMatch(REGEX, cmd)) {
            List<String> allGroups = ReUtil.getAllGroups(REGEX, cmd);
            String airCode = allGroups.get(1);
            return this.dealCgCode(airCode);
        } else {
            List<String> allGroups = ReUtil.getAllGroups(REGEX2, cmd);
            // 航班号
            String flightNo = allGroups.get(1);
            // 日期
            String flightDate = allGroups.get(2);
            // 始发站
            String orgCity = allGroups.get(5);
            return this.dealCg(flightNo, flightDate, orgCity);
        }
    }

    private UnifiedResult dealCg(String flightNo, String flightDate, String orgCity) throws UnifiedResultException {
        UnifiedResult unifiedResult = new UnifiedResult();
        try {
            flightDate = DateUtils.com2ymd(flightDate);
        } catch (Exception e) {
            throw new UnifiedResultException(Constant.DATE_ERROR);
        }

        // 继续验证：输入的航班是否存在
        List<CgVo> cgVos = cgMapper.retrieveFlightPlanSection(flightNo, flightDate);
        if (ObjectUtil.isEmpty(cgVos)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        if (StrUtil.isNotEmpty(orgCity)) {
            // 查询航站
            MnjxAirport airport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, orgCity)
                    .one();
            if (ObjectUtil.isEmpty(airport)) {
                throw new UnifiedResultException(Constant.DESTINATION_CITY);
            }

            // 匹配出发或到达机场三字码
            cgVos = cgVos.stream().filter(k -> k.getAirportId().equals(airport.getAirportId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(cgVos)) {
                throw new UnifiedResultException(Constant.DESTINATION_CITY);
            }
        } else {
            cgVos = cgVos.stream().sorted(Comparator.comparing(CgVo::getSectionNo)).collect(Collectors.toList());
            CgVo cgVo = cgVos.get(0);
            orgCity = cgVo.getAirportCode();
        }

        // 查询机型和版本号
        List<MnjxPlaneModel> planeList = cgMapper.retrieveFltJoinPlaneJoinPlaneType(flightNo, flightDate, orgCity);
        if (CollUtil.isEmpty(planeList)) {
            throw new UnifiedResultException(Constant.UNABLE);
        }

        MnjxPlaneModel mnjxPlaneModel = planeList.get(0);
        String type = mnjxPlaneModel.getPlaneModelType();
        String version = mnjxPlaneModel.getPlaneModelVersion();

        // 查询出CND
        MnjxCnd mnjxCnd = iMnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getPlaneModelId, mnjxPlaneModel.getPlaneModelId())
                .eq(MnjxCnd::getCndNo, cgVos.get(0).getCndNo())
                .one();

        if (ObjectUtil.isEmpty(mnjxCnd)) {
            throw new UnifiedResultException(Constant.UNABLE);
        }

        // 从CND中获取舱等
        String cabin = assembleSellCabin(mnjxCnd);

        if (StrUtil.isNotEmpty(flightDate)) {
            flightDate = DateUtils.ymd2Com(flightDate);
        }
        String cmdBuilder = String.format("\u0010CG:%s/%s/%s", flightNo, flightDate, orgCity) +
                "\r\n" +
                StrUtil.format(" {}    {}/{}    {}",
                        orgCity, type, version, cabin);
        unifiedResult.setResults(Collections.singletonList(cmdBuilder).toArray());
        return unifiedResult;
    }

    /**
     * 组装sellCabin数据
     *
     * @param mnjxCnd mnjxCnd
     * @return 组装sellCabin数据
     */
    private String assembleSellCabin(MnjxCnd mnjxCnd) {
        // 从CND中获取舱等
        StringBuilder cabin = new StringBuilder();
        if (StrUtil.isNotEmpty(mnjxCnd.getFirstCabinClass())) {
            String sellCabin = mnjxCnd.getFirstSellCabin();
            Integer seatNumber = mnjxCnd.getFirstSeats();
            cabin.append(StrUtil.format("{}{}", sellCabin, seatNumber));
        }
        if (StrUtil.isNotEmpty(mnjxCnd.getSecondCabinClass())) {
            String sellCabin = mnjxCnd.getSecondSellCabin();
            Integer seatNumber = mnjxCnd.getSecondSeats();
            cabin.append(StrUtil.format("{}{}", sellCabin, seatNumber));
        }
        if (StrUtil.isNotEmpty(mnjxCnd.getThirdCabinClass())) {
            String sellCabin = mnjxCnd.getThirdSellCabin();
            Integer seatNumber = mnjxCnd.getThirdSeats();
            cabin.append(StrUtil.format("{}{}", sellCabin, seatNumber));
        }
        if (StrUtil.isNotEmpty(mnjxCnd.getFourthCabinClass())) {
            String sellCabin = mnjxCnd.getFourthSellCabin();
            Integer seatNumber = mnjxCnd.getFourthSeats();
            cabin.append(StrUtil.format("{}{}", sellCabin, seatNumber));
        }
        if (StrUtil.isNotEmpty(mnjxCnd.getFifthCabinClass())) {
            String sellCabin = mnjxCnd.getFifthSellCabin();
            Integer seatNumber = mnjxCnd.getFifthSeats();
            cabin.append(StrUtil.format("{}{}", sellCabin, seatNumber));
        }
        return cabin.toString();
    }

    private UnifiedResult dealCgCode(String airCode) throws UnifiedResultException {
        UnifiedResult unifiedResult = new UnifiedResult();
        MnjxAirline mnjxAirline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineCode, airCode).one();
        if (ObjectUtil.isEmpty(mnjxAirline)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        List<MnjxPlane> planeList = iMnjxPlaneService.lambdaQuery()
                .eq(MnjxPlane::getAirlineId, mnjxAirline.getAirlineId())
                .eq(MnjxPlane::getIsUse, Constant.STR_ONE)
                .list();

        if (CollUtil.isEmpty(planeList)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }

        // 取出
        List<String> cndIds = planeList.stream().map(MnjxPlane::getCndId).distinct().collect(Collectors.toList());
        List<MnjxCnd> cnds = iMnjxCndService.lambdaQuery().in(MnjxCnd::getCndId, cndIds).list();
        if (CollUtil.isEmpty(cnds)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }

        // 默认的舱等排序规则
        String defaultCabinClass = "UFPJADEHCIKLMNOQRSTGWVXZBY";
        // 按舱等
        List<String> sortCabinClass = Arrays.asList(defaultCabinClass.split(""));
        List<String> cabinClassList = new ArrayList<>();
        cabinClassList.addAll(cnds.stream().map(MnjxCnd::getFirstCabinClass).collect(Collectors.toList()));
        cabinClassList.addAll(cnds.stream().map(MnjxCnd::getSecondCabinClass).collect(Collectors.toList()));
        cabinClassList.addAll(cnds.stream().map(MnjxCnd::getThirdCabinClass).collect(Collectors.toList()));
        cabinClassList.addAll(cnds.stream().map(MnjxCnd::getFourthCabinClass).collect(Collectors.toList()));
        cabinClassList.addAll(cnds.stream().map(MnjxCnd::getFifthCabinClass).collect(Collectors.toList()));

        // 对舱位进行排序
        sortCabinClass = sortCabinClass.stream().filter(cabinClassList::contains).collect(Collectors.toList());

        Map<String, Set<String>> cabinClassMap = new HashMap<>(1024);
        buildCabinClass(cabinClassMap, cnds);

        // 统计舱位
        StringBuffer cClass = new StringBuffer();
        for (String cabinClass : sortCabinClass) {
            if (cabinClassMap.containsKey(cabinClass)) {
                Set<String> sellCabinsList = cabinClassMap.get(cabinClass);
                String sellCabin = String.join("", sellCabinsList);
                if (StrUtil.isNotEmpty(cClass)) {
                    cClass.append("/").append(cabinClass).append(sellCabin);
                } else {
                    cClass.append(cabinClass).append(sellCabin);
                }
            }
        }
        String cabin = StrUtil.format("{}", cClass);
        unifiedResult.setResults(Arrays.asList("AIR", airCode, cabin).toArray());
        return unifiedResult;
    }


    /**
     * 构建舱等、销售舱位
     *
     * @param resultMap resultMap
     * @param cnds      cnds
     */
    private void buildCabinClass(Map<String, Set<String>> resultMap, List<MnjxCnd> cnds) {
        // 一等舱
        Map<String, List<MnjxCnd>> firstCabinClassList = cnds.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k.getFirstCabinClass()))
                .collect(Collectors.groupingBy(MnjxCnd::getFirstCabinClass));
        statisticsCndSellCabin(resultMap, firstCabinClassList, 1);
        // 二等舱
        Map<String, List<MnjxCnd>> secondCabinClassList = cnds.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k.getSecondCabinClass()))
                .collect(Collectors.groupingBy(MnjxCnd::getSecondCabinClass));
        statisticsCndSellCabin(resultMap, secondCabinClassList, 2);
        // 三等舱
        Map<String, List<MnjxCnd>> thirdCabinClassList = cnds.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k.getThirdCabinClass()))
                .collect(Collectors.groupingBy(MnjxCnd::getThirdCabinClass));
        statisticsCndSellCabin(resultMap, thirdCabinClassList, 3);
        // 四等舱
        Map<String, List<MnjxCnd>> fourthCabinClassList = cnds.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k.getFourthCabinClass()))
                .collect(Collectors.groupingBy(MnjxCnd::getFourthCabinClass));
        statisticsCndSellCabin(resultMap, fourthCabinClassList, 4);
        // 五等舱
        Map<String, List<MnjxCnd>> fifthCabinClassList = cnds.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k.getFifthCabinClass()))
                .collect(Collectors.groupingBy(MnjxCnd::getFifthCabinClass));
        statisticsCndSellCabin(resultMap, fifthCabinClassList, 5);
    }

    /**
     * 统计销售舱位
     *
     * @param resultMap resultMap
     * @param gradeCabinClassList gradeCabinClassList
     * @param grade grade
     */
    private void statisticsCndSellCabin(Map<String, Set<String>> resultMap, Map<String, List<MnjxCnd>> gradeCabinClassList, int grade) {
        if (CollUtil.isNotEmpty(gradeCabinClassList)) {
            for (String gradeCabinClass : gradeCabinClassList.keySet()) {
                List<MnjxCnd> cndList = gradeCabinClassList.get(gradeCabinClass);
                Set<String> sellCabinList = new HashSet<>();
                List<String> sellCabin = cndList.stream().map(k -> {
                    String salesSpace = null;
                    switch (grade) {
                        case 1:
                            salesSpace = k.getFirstSellCabin();
                            break;
                        case 2:
                            salesSpace = k.getSecondSellCabin();
                            break;
                        case 3:
                            salesSpace = k.getThirdSellCabin();
                            break;
                        case 4:
                            salesSpace = k.getFourthSellCabin();
                            break;
                        case 5:
                            salesSpace = k.getFifthSellCabin();
                            break;
                        default:
                            break;
                    }
                    return salesSpace;
                }).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
                sellCabin.forEach(k -> sellCabinList.addAll(Arrays.asList(k.split(""))));
                resultMap.put(gradeCabinClass, sellCabinList);
            }
        }
    }
}
