/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

@SuppressWarnings("AlibabaClassMustHaveAuthor")
@Data
public class SeatDto {

    /**
     * 功能代码
     */
    private String funtionId;

    /**
     * 功能的排序
     */
    private int value;

    @Override
    public boolean equals(Object obj) {
        if (ObjectUtil.isEmpty(obj)) {
            return false;
        }
        SeatDto seatDto = (SeatDto) obj;
        if (this.value == seatDto.value) {
            return true;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return value;
    }
}
