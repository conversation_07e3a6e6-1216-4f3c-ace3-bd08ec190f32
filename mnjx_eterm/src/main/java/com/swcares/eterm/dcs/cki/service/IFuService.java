package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.FuRetrieveDto;


/**
 * FU修改航班属性，一次只能修改一项 【航班号】/【日期】/【起飞城市】/【表示符】/【修改数据】
 *
 * <AUTHOR>
 */
public interface IFuService {

    /**
     * 参数解析
     *
     * @param cmd FU指令
     * @return fuRetrieve对象参数
     * @throws UnifiedResultException 统一异常处理
     */
    FuRetrieveDto parseFu(String cmd) throws UnifiedResultException;

    /**
     * fu指令业务处理
     *
     * @param fuRetrieveDto 解析的参数对象
     * @return 返回业务处理成功或者失败
     * @throws UnifiedResultException 统一异常处理
     */
    String handle(FuRetrieveDto fuRetrieveDto) throws UnifiedResultException;
}
