package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.RsDto;

import com.swcares.eterm.dcs.cki.service.IRsService;

import javax.annotation.Resource;

/**
 * RS指令处理
 *
 * <AUTHOR>
 */
@OperateType(action = "RS", shorthand = true, needPaging = false)
public class RsHandler implements Handler {
    @Resource
    private IRsService iRsService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        //参数解析
        RsDto rsDto = iRsService.parseRs(cmd);
        //业务处理
        return iRsService.handler(rsDto);
    }
}
