package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.AlDto;

/**
 * <AUTHOR>
 */
public interface IAlService {
    /***
     * AL参数解析
     * @param cmd
     * @return
     * @throws UnifiedResultException
     */
    AlDto pares(String cmd) throws UnifiedResultException;

    /***
     * AL业务处理
     * @param alDto
     * @return
     * @throws UnifiedResultException
     */
    String handler(AlDto alDto) throws UnifiedResultException;
}
