package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PdInfoDto implements Serializable{

	private List<PdNmDto> nms;

	private String ckStatus;

	private String planeType;

	private String gtd;

	private String boarding;

	private String estimateOff;

	private String actualOff;

	private String estimateArr;

	private String timeDiff;

	private String order;

	private String flightNo;

	private String date;

	private String cabin;

	private String city;

	private String paramsStr;

	private String planSectionId;
	
	private String cmdStr;

}
