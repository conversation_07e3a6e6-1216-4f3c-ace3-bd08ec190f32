/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * SB指令实体对象
 *
 * <AUTHOR>
 */
@Data
@ToString
public class SbDto {

    private String name;

    private String fltNo;

    private String fltDate;

    private String cabin;

    private String sellCabin;

    private String cabinClass;

    private String seat;

    private String nmId;

    private String planSectionId;

    private String segId;

    /**
     * 默认查询候补数据。
     */
    private String isHb = "1";
    /**
     * 过滤掉ACC
     */
    private String passCkiStatus;

    private String hbNo;

    private String reason;

    private String dstCity;

    private String level;

    private String offCity;

    private String psgType;

    private String isUres;
    /**
     * 起始机场编号
     */
    private String depAirportCode;
    /**
     * 结尾机场编号
     */
    private String arrAirportCode;
    /**
     * 航段编号
     */
    private List<Integer> pnrSegNo;
}
