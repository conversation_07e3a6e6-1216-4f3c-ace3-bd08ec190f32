package com.swcares.eterm.dcs.fdc.dto;

import com.swcares.entity.MnjxCnd;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BfTcardT1Vo {

	/**
	 * 序号
	 */
	private String alg;

	/**
	 * 机型
	 */
	private String eqt;

	/**
	 * 是否夜航
	 */
	private String n;

	/**
	 * 国际航班/国内航班
	 */
	private String t;

	/**
	 * 用途，必填，必须为DL
	 */
	private String appl;

	/**
	 * 未知字段，不解析，不保存
	 */
	private String intt;

	/**
	 * 版本号
	 */
	private String vers;

	/**
	 * cnd
	 */
	private String configuration;

	private MnjxCnd mnjxCnd;
}
