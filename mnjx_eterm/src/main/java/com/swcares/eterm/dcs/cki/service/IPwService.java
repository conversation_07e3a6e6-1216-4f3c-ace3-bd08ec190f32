package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxLuggage;
import com.swcares.entity.MnjxPsgOperateRecord;
import com.swcares.entity.MnjxPsgSeat;
import com.swcares.eterm.dcs.cki.obj.dto.PwCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;

import java.util.List;

/**
 * description：IPwService <br>
 *
 * <AUTHOR> <br>
 * date 2022/08/06 <br>
 * @version v1.0 <br>
 */
public interface IPwService {

    /**
     * Title：parsePw <br>
     * description：解析参数 <br>
     *
     * @param cmd 指令
     * @return 解析参数
     * @throws UnifiedResultException 统一异常<br>
     */
    List<PwCmdDto> parsePw(String cmd) throws UnifiedResultException;

    /**
     * Title：handle <br>
     * description：处理业务 <br>
     *
     * @param pwDtoList  参数处理后的集合
     * @param memoryData 内存对象
     * @return 处理业务
     * @throws UnifiedResultException 统一异常<br>
     */
    List<PwResultDto> handle(MemoryData memoryData, List<PwCmdDto> pwDtoList) throws UnifiedResultException;

    /**
     * Title：getReturnData <br>
     * description：getReturnData <br>
     *
     * @param pwResultDto 对象
     * @throws UnifiedResultException 统一异常<br>
     */
    void getReturnData(PwResultDto pwResultDto) throws UnifiedResultException;

    /**
     * Title：delSsr <br>
     * description：delSsr <br>
     *
     * @param pnrNmId     pnrNmId
     * @param ssrTypeList ssrTypeList<br>
     */
    void delSsr(String pnrNmId, List<String> ssrTypeList);

    /**
     * 删除行李
     *
     * @param luggageList luggageList
     * @param iWeight     输入重量
     * @throws UnifiedResultException 统一异常<br>
     */
    void delBag(int iWeight, List<MnjxLuggage> luggageList);

    /**
     * Title：delBag <br>
     * description：删除行李 <br>
     *
     * @param pwr        pwr
     * @param iLuggageNo 输入的行李号
     * @param iWeight    输入的重量
     * @param flag       为true时，记录行李操作记录
     * @return 删除行李
     * @throws UnifiedResultException 统一异常<br>
     */
    MnjxPsgOperateRecord delBag(PwResultDto pwr, String iLuggageNo, int iWeight, boolean flag, int pwResultNo, boolean isAvih) throws UnifiedResultException;

    /**
     * Title：delBag <br>
     * description：删除所有行李 <br>
     *
     * @param pnrNmId  pnrNmId
     * @param psgCkiId psgCkiId
     * @param flag     是否记录日志
     * @return 删除所有行李
     * @throws UnifiedResultException 统一异常<br>
     */
    MnjxPsgOperateRecord delBag(PwResultDto pwResultDto, String pnrNmId, String psgCkiId, boolean flag, boolean isAvih);

    /**
     * Title：checkExbgExpc <br>
     * description：检查删除逾重行李 <br>
     *
     * @param exType   逾重行李类型
     * @param psgCkiId psgCkiId
     * @throws UnifiedResultException 统一异常<br>
     */
    void checkExbgExpc(String exType, String psgCkiId) throws UnifiedResultException;

    /**
     * Title：delExbgExpc <br>
     * description：删除逾重行李 <br>
     *
     * @param exType   逾重行李类型
     * @param psgCkiId psgCkiId
     * @throws UnifiedResultException 统一异常<br>
     */
    void delExbgExpc(String exType, String psgCkiId, String pnrNmId) throws UnifiedResultException;


    /**
     * Title：delCkiOption <br>
     * description：删除ckiOption<br>
     *
     * @param psgCkiId   psgCkiId
     * @param optionType optionType
     */
    void delCkiOption(String psgCkiId, String optionType, String pnrNmId);

    /**
     * Title：handleCkiOption <br>
     * description：处理ckioption选项<br>
     *
     * @param psgCkiId    psgCkiId
     * @param optionType  类型
     * @param pwResultDto 对象
     * @throws UnifiedResultException 统一异常<br>
     */
    void handleCkiOption(String psgCkiId, String optionType, PwResultDto pwResultDto) throws UnifiedResultException;

    /**
     * Title：handlePw <br>
     * description：pw处理 <br>
     *
     * @param pwResultDto 对象
     * @throws UnifiedResultException 统一异常<br>
     */
    void handlePw(PwResultDto pwResultDto) throws UnifiedResultException;

    /**
     * Title：queryPsgSeat <br>
     * description：根据psgCkiId查询mnjxPsgSeat对象 <br>
     *
     * @param psgCkiId psgCkiId
     * @return <br>
     * author：zhaokan <br>
     * date：2022/11/29 <br>
     */
    MnjxPsgSeat queryPsgSeat(String psgCkiId);

    /**
     * 处理旅客的多段
     *
     * @param pwrList pwrList
     * @param cmd当前指令
     * @return 处理旅客的多段
     * @throws UnifiedResultException 统一异常<br>
     * <AUTHOR> <br>
     * @date 2023/01/29 <br>
     */
    List<PwResultDto> handlePsg(List<PwResultDto> pwrList, String cmd) throws UnifiedResultException;

    /**
     * 获取psgcki,psgseat
     *
     * @param pwResultDto pwResultDto
     * @param pnrSegNo    pnrSegNo
     * @throws UnifiedResultException 统一异常<br>
     * <AUTHOR> <br>
     * @date 2023/01/29 <br>
     */
    void getCki(PwResultDto pwResultDto, String pnrSegNo) throws UnifiedResultException;

    /**
     * 当前操作航段有多个行李并且目的地不一样 ，不带行李号
     *
     * @param pwResultDto pwResultDto
     * @param luggageNo   luggageNo
     * @throws UnifiedResultException 统一异常<br>
     * <AUTHOR> <br>
     * @date 2023/01/30 <br>
     */
    void multiSegBagCheck(PwResultDto pwResultDto, String luggageNo, boolean isAvih) throws UnifiedResultException;

    /**
     * 联程返回数据
     *
     * @param pwResultDtoList pwResultDtoList<br>
     * <AUTHOR> <br>
     * @date 2023/02/01 <br>
     */
    void handleLcReData(List<PwResultDto> pwResultDtoList);

    /**
     * 获取当前航段行李重量
     *
     * @param luggageList luggageList
     * @return 获取当前航段行李重量<br>
     * <AUTHOR> <br>
     * @date 2023/02/02 <br>
     */
    Integer getBagWeight(List<MnjxLuggage> luggageList);

    void savePwForPrint(List<PwResultDto> pwResultDtoList, String cmd);
}
