package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.obj.dto.SuDto;
import com.swcares.eterm.dcs.cki.mapper.SuMapper;
import com.swcares.eterm.dcs.cki.service.ISeService;
import com.swcares.eterm.dcs.cki.service.ISuService;
import com.swcares.service.IMnjxSeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SU指令的服务提供方法
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SuServiceImpl implements ISuService {

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private ISeService iSeService;

    @Resource
    private SuMapper suMapper;

    /**
     * >SU:+座位符号/座位
     * >SU:-座位符号/座位
     */
    private static final String SU_PATTERN = "SU:([+-])([A-Z])/([0-9A-Za-z,-]*)";

    /**
     * 某一个座位 12L
     */
    private static final String SEAT_PATTERN = "([0-9]{1,3})[A-Za-z]";

    /**
     * 某一排座位 12
     */
    private static final String ROW_PATTERN = "[0-9]{1,3}";

    /**
     * 某一排到某一排座位 12-15
     */
    private static final String ROWS_PATTERN = "([0-9]{1,3})[-]([0-9]{1,3})";

    /**
     * SE：航班号/日期/舱位/航段
     */
    private static final String SE_PATTERN = "SE:([0-9A-Za-z]{5,7})/([0-9]{1,2}[A-Z]{3}([0-9]{2})?|[+-.])/([A-Z|*])([A-Z]{6})?";
    /**
     * 允许输入的座位状态
     */
    private static final String SEAT_STATUS_PATTERN = "[ABCU]";

    @Override
    public String handler(String cmd) throws UnifiedResultException {
        MemoryDataHandlerResult memoryDataHandlerResult = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult();
        // 判断上一个指令是否为SE
        String lastAction = memoryDataHandlerResult.getLastAction();
        if (!Constant.SE.equals(lastAction)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        // 从SE指令获取航班号、日期和航段
        String seReCmd = memoryDataHandlerResult.getRecords().get(0);
        List<String> seGroups = ReUtil.getAllGroups(Pattern.compile(SE_PATTERN), seReCmd);
        SuDto suDto = new SuDto();
        // se指令
        String seCmd = seGroups.get(0);
        // 航班号
        suDto.setFlightNo(seGroups.get(1));
        // 航班日期
        suDto.setFlightDate(DateUtils.com2ymd(seGroups.get(2)));
        // 航段
        suDto.setDep(seGroups.get(5).substring(0, 3));
        suDto.setArr(seGroups.get(5).substring(3, 6));
        // 验证航班状态
        List<String> ckStatusList = suMapper.retrieveCkStatus(suDto);
        for (String ckStatus : ckStatusList) {
            if (Constant.CL.equals(ckStatus)) {
                throw new UnifiedResultException(Constant.FLIGHT_CLOSED_FOR_LDP);
            } else if (Constant.CC.equals(ckStatus)) {
                throw new UnifiedResultException(Constant.FLIGHT_CLOSED);
            } else if (Constant.EC.equals(ckStatus)) {
                throw new UnifiedResultException(Constant.PROT_COV);
            }
        }
        // 指令格式验证
        if (!ReUtils.isMatch(SU_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        } else if (cmd.endsWith(StrUtil.DASHED) || cmd.endsWith(StrUtil.COMMA)) {
            throw new UnifiedResultException(Constant.UNABLE);
        }
        List<String> allGroups = ReUtils.getAllGroups(Pattern.compile(SU_PATTERN), cmd);
        // 更新或还原（+/-）
        suDto.setParam(allGroups.get(1));
        // 校验输入的座位状态
        if (!ReUtils.isMatch(SEAT_STATUS_PATTERN, allGroups.get(Constant.TWO))) {
            throw new UnifiedResultException("SUD");
        }
        suDto.setSeatStatus(allGroups.get(2));
        // 分类前的座位排数与座位编号的集合
        List<String> seats = CollUtil.toList(allGroups.get(3).split(","));
        // 查询该航班所有座位的座位状态
        List<MnjxSeat> mnjxSeats = suMapper.retrieveSeatStatus(suDto);
        // 需要更新的座位实体
        List<MnjxSeat> newMnjxSeats = new ArrayList<>();
        // 分类并处理
        for (String seat : seats) {
            // 座位号如果为0或以-结尾报错UNABLE
            if (seat.startsWith(Constant.STR_ZERO) || seat.endsWith(StrUtil.DASHED)) {
                throw new UnifiedResultException(Constant.UNABLE);
            } else if (ReUtils.isMatch(SEAT_PATTERN, seat)) {
                checkSeatNo(suDto, mnjxSeats, seat, newMnjxSeats);
            } else if (ReUtils.isMatch(ROW_PATTERN, seat)) {
                // 将排号转化为座位号处理
                List<String> seatNos = getSeatNos(seat, mnjxSeats);
                for (String seatNo : seatNos) {
                    checkSeatNo(suDto, mnjxSeats, seatNo, newMnjxSeats);
                }
            } else if (ReUtils.isMatch(ROWS_PATTERN, seat)) {
                List<String> seatRows = getSeatRows(seat);
                for (String seatRow : seatRows) {
                    List<String> seatNos = getSeatNos(seatRow, mnjxSeats);
                    for (String seatNo : seatNos) {
                        checkSeatNo(suDto, mnjxSeats, seatNo, newMnjxSeats);
                    }
                }
            } else {
                throw new UnifiedResultException(Constant.FORMAT);
            }
        }
        iMnjxSeatService.updateBatchById(newMnjxSeats);
        return iSeService.handler(seCmd);
    }

    /**
     * 排号转化成座位号
     *
     * @param seatRow   排号
     * @param mnjxSeats 当前航班的座位实体集合
     * @return
     */
    private List<String> getSeatNos(String seatRow, List<MnjxSeat> mnjxSeats) throws UnifiedResultException {
        List<String> seatNos = new ArrayList<>();
        List<MnjxSeat> collect = mnjxSeats.stream()
                .filter(k -> ObjectUtil.isNotEmpty(k) && seatRow.equals(k.getSeatRow()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)){
            throw new UnifiedResultException(Constant.SEATS);
        }
        for (MnjxSeat seat : collect) {
            // 多航段需要去重
            if (!seatNos.contains(seat.getSeatNo())) {
                seatNos.add(seat.getSeatNo());
            }
        }
        return seatNos;
    }

    /**
     * 验证座位编号是否合规
     *
     * @param suDto
     * @param mnjxSeats
     * @param seatNo
     * @param newMnjxSeats
     * @throws UnifiedResultException
     */
    private void checkSeatNo(SuDto suDto, List<MnjxSeat> mnjxSeats, String seatNo, List<MnjxSeat> newMnjxSeats) throws UnifiedResultException {
        String row = ReUtils.getGroup1(Pattern.compile(SEAT_PATTERN), seatNo);
        // 座位号的排数不能超过100
        if (Integer.valueOf(row) >= Constant.ONE_HUNDRED) {
            throw new UnifiedResultException(Constant.UNABLE);
        }
        List<MnjxSeat> mnjxSeatList = mnjxSeats.stream()
                .filter(s -> ObjectUtil.isNotEmpty(s) && seatNo.equals(s.getSeatNo()))
                .collect(Collectors.toList());
        // 验证座位号是否存在
        if (CollUtil.isEmpty(mnjxSeatList)){
            throw new UnifiedResultException(Constant.SEATS);
        }
        for (MnjxSeat mnjxSeat : mnjxSeatList) {
            // 如果输入的座位号不存在或当前系统座位状态为“.”,“X”,“D”，提示“SEATS”
            if (StrUtil.isEmpty(mnjxSeat.getSeatStatus()) || Constant.SEAT_STATUS_POINT.equals(mnjxSeat.getSeatStatus()) ||
                    Constant.SEAT_STATUS_X.equals(mnjxSeat.getSeatStatus()) || Constant.SEAT_STATUS_D.equals(mnjxSeat.getSeatStatus())) {
                throw new UnifiedResultException(Constant.SEATS);
            } else if (StrUtils.CROSS.equals(suDto.getParam())) {
                crossParamHandler(suDto, mnjxSeat);
                // 还原
            } else if (StrUtil.DASHED.equals(suDto.getParam())) {
                // 旧座位状态不能为空
                if (StrUtil.isEmpty(mnjxSeat.getSeatStatusOld())){
                    if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())){
                        throw new UnifiedResultException(Constant.ALREADY_DONE);
                    }else {
                        throw new UnifiedResultException(Constant.SEATS);
                    }
                }
                // 系统当前座位状态为C,输入的状态为-B/-U的情况，按优先级还原
                if (Constant.SEAT_STATUS_C.equals(mnjxSeat.getSeatStatus())){
                    // 输入的状态为-B
                    if (Constant.SEAT_STATUS_B.equals(suDto.getSeatStatus())){
                        if (!Constant.SEAT_STATUS_B.equals(mnjxSeat.getSeatStatusOld())){
                            throw new UnifiedResultException(Constant.SEATS);
                        }
                        mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                        mnjxSeat.setSeatStatusOldTwo(null);
                    // 输入的状态为-U
                    }else if (Constant.SEAT_STATUS_U.equals(suDto.getSeatStatus())){
                        if (!Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatusOld()) && !Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatusOldTwo())){
                            throw new UnifiedResultException(Constant.SEATS);
                        }else if(Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatusOld())){
                            mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                            mnjxSeat.setSeatStatusOldTwo(null);
                        }else if (Constant.SEAT_STATUS_B.equals(mnjxSeat.getSeatStatusOld()) && Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatusOldTwo())){
                            mnjxSeat.setSeatStatusOldTwo(null);
                        }
                    // 输入状态为-C
                    } else if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())){
                        mnjxSeat.setSeatStatus(mnjxSeat.getSeatStatusOld());
                        mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                        mnjxSeat.setSeatStatusOldTwo(null);
                    // 输入状态为其他
                    }else {
                        throw new UnifiedResultException(Constant.SEATS);
                    }
                // 系统当前座位状态为B,按优先级还原
                }else if (Constant.SEAT_STATUS_B.equals(mnjxSeat.getSeatStatus())){
                    // 输入状态为-U
                    if (Constant.SEAT_STATUS_U.equals(suDto.getSeatStatus())){
                        if (!Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatusOld())){
                            throw new UnifiedResultException(Constant.SEATS);
                        }
                        mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                        mnjxSeat.setSeatStatusOldTwo(null);
                    // 输入状态为-C
                    }else if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())){
                        throw new UnifiedResultException(Constant.ALREADY_DONE);
                    // 输入状态为-B
                    }else if (Constant.SEAT_STATUS_B.equals(suDto.getSeatStatus())){
                        mnjxSeat.setSeatStatus(mnjxSeat.getSeatStatusOld());
                        mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                        mnjxSeat.setSeatStatusOldTwo(null);
                    }else {
                        throw new UnifiedResultException(Constant.SEATS);
                    }
                // 其余正常还原
                }else {
                    //系统当前座位号和输入座位号必须相同
                    if (!suDto.getSeatStatus().equals(mnjxSeat.getSeatStatus())){
                        if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())){
                            throw new UnifiedResultException(Constant.ALREADY_DONE);
                        }else {
                            throw new UnifiedResultException(Constant.SEATS);
                        }
                    }
                    mnjxSeat.setSeatStatus(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatusOldTwo());
                    mnjxSeat.setSeatStatusOldTwo(null);
                }
            }
            newMnjxSeats.add(mnjxSeat);
        }
    }

    private void crossParamHandler(SuDto suDto, MnjxSeat mnjxSeat) throws UnifiedResultException {
        // 修改时，系统当前座位号和输入座位号不能相同
        if (suDto.getSeatStatus().equals(mnjxSeat.getSeatStatus())) {
            if (Constant.SEAT_STATUS_U.equals(suDto.getSeatStatus()) || Constant.SEAT_STATUS_B.equals(suDto.getSeatStatus())) {
                throw new UnifiedResultException(Constant.SEATS);
            } else if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())) {
                throw new UnifiedResultException(Constant.ALREADY_DONE);
            }
        } else if (Constant.SEAT_STATUS_A.equals(suDto.getSeatStatus())) {
            // TODO 将座位改成W表示自助值机，如果为出票时为W,出票后为K，暂时不做
            throw new UnifiedResultException("A的情况暂时不做");
        }
        // 如果系统当前座位状态为C(输入状态为B/U时,需要按照C,B,U的优先级依次存入seat_status，seat_status_old，seat_status_old_two字段中)
        if (Constant.SEAT_STATUS_C.equals(mnjxSeat.getSeatStatus())) {
            // 输入状态不能和seat_status_old相同
            if (suDto.getSeatStatus().equals(mnjxSeat.getSeatStatusOld())){
                throw new UnifiedResultException(Constant.SEATS);
            // 输入状态为 B
            }else if (Constant.SEAT_STATUS_B.equals(suDto.getSeatStatus())){
                mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                mnjxSeat.setSeatStatusOld(Constant.SEAT_STATUS_B);
            //输入状态为 U
            }else if (Constant.SEAT_STATUS_U.equals(suDto.getSeatStatus())){
                // seat_status_old 为B时 按优先级存
                if (Constant.SEAT_STATUS_B.equals(mnjxSeat.getSeatStatusOld())){
                    mnjxSeat.setSeatStatusOldTwo(Constant.SEAT_STATUS_U);
                }else {
                    mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(Constant.SEAT_STATUS_U);
                }
            }else {
                mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                mnjxSeat.setSeatStatusOld(suDto.getSeatStatus());
            }
        } else {
            // 输入状态为C，可以覆盖*，+,R,U,B,/,G,A,O的座位
            if (Constant.SEAT_STATUS_C.equals(suDto.getSeatStatus())) {
                if (Constant.ASTERISK.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_R.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_B.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_SLASH.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_G.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_A.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_O.equals(mnjxSeat.getSeatStatus())) {
                    mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatus());
                    mnjxSeat.setSeatStatus(suDto.getSeatStatus());
                }
            } else if (Constant.SEAT_STATUS_B.equals(suDto.getSeatStatus())) {
                //输入状态为B,可以覆盖*,G,U
                if (Constant.ASTERISK.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_U.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_G.equals(mnjxSeat.getSeatStatus())) {
                    mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatus());
                    mnjxSeat.setSeatStatus(suDto.getSeatStatus());
                }
            } else if (Constant.SEAT_STATUS_U.equals(suDto.getSeatStatus())) {
                //输入状态为U,可以覆盖*,G
                if (Constant.ASTERISK.equals(mnjxSeat.getSeatStatus()) ||
                        Constant.SEAT_STATUS_G.equals(mnjxSeat.getSeatStatus())) {
                    mnjxSeat.setSeatStatusOldTwo(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatus());
                    mnjxSeat.setSeatStatus(suDto.getSeatStatus());
                }
            }
        }
    }

    /**
     * 将包含“-”排号转化成排号集合
     *
     * @param seat
     * @return
     * @throws UnifiedResultException
     */
    private List<String> getSeatRows(String seat) throws UnifiedResultException {
        List<String> seatRowScope = Arrays.asList(seat.split(StrUtil.DASHED));
        List<String> seatRowList = new ArrayList<>();
        Integer firstRow = Integer.valueOf(seatRowScope.get(0));
        Integer lastRow = Integer.valueOf(seatRowScope.get(1));
        if (firstRow > lastRow) {
            throw new UnifiedResultException(Constant.SEATS);
        }
        for (int i = firstRow; i <= lastRow; i++) {
            seatRowList.add(String.valueOf(i));
        }
        return seatRowList;
    }
}
