package com.swcares.eterm.dcs.cki.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.FiPlanFltDto;
import com.swcares.eterm.dcs.cki.service.IFiService;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * FI指令处理：航班号、日期 显示指定日期的航班信息，飞机的布局表号及飞机号
 * FI的指令格式：
 * 格式一：>FI：航班号                     FI：CA1444  显示CA1444航班的基本信息（注意：后面不跟日期的话默认显示当天航班信息）
 * 格式二>>FI：航班号/日期（不加年份）       FI：CA1444/08MAY
 * 格式三>>FI：航班号/日期                  FI：CA1444/08MAY22
 * 格式四>>FI：航班号/起始地                FI：ca1444/kwe
 * 格式五>>FI：航班号/目的地                fi：ca1444/pek
 * 格式六>>FI：航班号/日期/起始地（不加年）   fi：ca1444/29apr/kwe
 * 格式七>>FI：航班号/日期/起始地（加年）     fi：ca1444/29apr22/kwe
 * 格式八>>FI：航班号/日期/目的地（不加年）   fi：ca1444/29apr/pek
 * 格式九>>FI：航班号/日期/目的地（加年）     fi：ca1444/29apr22/pek
 *
 * <AUTHOR>
 *  2022-04-30 10:04:27
 */
@OperateType(action = "FI", template = "/dcs/cki/fi.jf",shorthand = true)
public class FiHandler implements Handler {

    @Resource
    private IFiService iFiService;
    private static final String FI_REGX = "FI:((\\w{2})(\\d{3,4}))(/([.+-]|\\w{5,7}))?(/(\\w{3}))?";

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        //格式验证
        if (!ReUtil.isMatch(FI_REGX, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 参数
        unifiedResult.setArgs(ReUtil.getAllGroups(Pattern.compile(FI_REGX), cmd, true).toArray());
        // 要返回的结果容器
        FiPlanFltDto fiPlanFltDto = iFiService.handle(unifiedResult);
        unifiedResult.setResults(ListUtil.toList(fiPlanFltDto).toArray());
        return unifiedResult;
    }
}
