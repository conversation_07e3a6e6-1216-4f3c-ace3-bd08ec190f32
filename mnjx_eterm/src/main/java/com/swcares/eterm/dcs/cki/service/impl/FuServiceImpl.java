package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.constant.FuConstant;
import com.swcares.eterm.dcs.cki.obj.dto.FuPlanSection;
import com.swcares.eterm.dcs.cki.obj.dto.FuRetrieveDto;
import com.swcares.eterm.dcs.cki.obj.dto.FuTcardSection;
import com.swcares.eterm.dcs.cki.mapper.FuMapper;
import com.swcares.eterm.dcs.cki.service.IFuService;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.swcares.eterm.dcs.cki.constant.FuConstant.ETA;

/**
 * FU修改航班属性，一次只能修改一项 【航班号】/【日期】/【起飞城市】/【表示符】/【修改数据】
 * 实际着陆时间;实际起飞时间;预计登机时间;预计进港时间;预计离港时间;登机口;飞机停靠位置;飞机布局表号
 * ETD ：离港(起飞)时间   ETD/0830        实际起飞时间,最多不能提前 4 小时，推迟无限制
 * CTN：布局表号         CTN/300         先查找新的 CND 表号，并以新表号 300 更换飞机
 * ARN：飞机注册号       ARN/B2824       添加/修改飞机注册号
 * GATE：登机口         GATE/3          在登机牌上打登机口，只能在 FU 中修改
 * BDT：登机时间        BDT/0810        实际登机时间，默认提前半小时，只能在FU 中修改
 * ETA：到达时间        ETA/1900        修改到达时间，城市使用到达城市
 * <p>
 * 取消航班;恢复取消航班;取消一切修改
 * RTNS：取消一切修改   RTNS            恢复到 T-CARD 原始状态
 * ERS：取消单项修改   GATE/ERS  删除登机口   ARN/ERS  删除飞机号
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FuServiceImpl implements IFuService {

    @Resource
    private FuMapper fuMapper;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;
    @Resource
    private IMnjxPlaneService iMnjxPlaneService;
    @Resource
    private IMnjxFlightService iMnjxFlightService;

    /**
     * FU:【航班号】/【日期】/【起飞城市】/【表示符】/【修改数据】
     * FU:CA1511/16NOV00/PEK/GATE/02
     * FU:CA1511/PEK/GATE/02 报错 date
     * FU:CA1511/16NOV00/PEK/RTNS
     * FU:CA1511/16NOV00/PEK/GATE/ERS
     * FU:CA1511/16NOV00/PEK/ETA/1900
     * FU:CA1511/16NOV00/PEK/ARN/B2824
     */
    private static final Pattern FU_PATTERN = Pattern.compile("FU:(\\w{6})(/(\\w{5,7}|[+-.]))?/([A-Za-z]{3})(/(\\w*))?(/(\\w*))?");

    /**
     * 参数解析
     *
     * @param cmd FU指令
     * @return fuRetrieve对象参数
     * @throws UnifiedResultException 统一异常处理
     */
    @Override
    public FuRetrieveDto parseFu(String cmd) throws UnifiedResultException {
        if (!ReUtil.isMatch(FU_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        FuRetrieveDto fuRetrieveDto = new FuRetrieveDto();
        List<String> allGroups = ReUtil.getAllGroups(FU_PATTERN, cmd, false);
        String fltNo = allGroups.get(0);
        String fltDate = allGroups.get(2);
        String city = allGroups.get(3);
        String opt = allGroups.get(5);
        String data = allGroups.get(7);
        if (StrUtil.isEmpty(fltDate)) {
            throw new UnifiedResultException(Constant.DATE_ERROR);
        }
        if (StrUtil.isEmpty(opt)) {
            throw new UnifiedResultException(Constant.NO_CHANGE);
        }
        if (FuConstant.RTNS.equals(opt)) {
            //RTNS的情况
            data = StrUtil.EMPTY;
        }
        fuRetrieveDto.setFltDate(DateUtils.com2ymd(fltDate));
        fuRetrieveDto.setFltNo(fltNo);
        fuRetrieveDto.setCity(city);
        fuRetrieveDto.setOpt(opt);
        fuRetrieveDto.setData(data);
        return fuRetrieveDto;
    }

    /**
     * FU指令业务处理
     *
     * @param fuRetrieveDto 解析的参数对象
     * @return 返回业务处理成功或者失败
     */
    @Override
    public String handle(FuRetrieveDto fuRetrieveDto) throws UnifiedResultException {
        //参数验证
        checkParams(fuRetrieveDto);
        //业务处理
        return fuServiceHandle(fuRetrieveDto);
    }


    /**
     * 参数验证
     *
     * @param fuRetrieveDto 解析的参数对象
     */
    private void checkParams(FuRetrieveDto fuRetrieveDto) throws UnifiedResultException {
        //如果是共享航班，报错
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, fuRetrieveDto.getFltNo()).one();
        if (ObjectUtil.isEmpty(mnjxFlight) || StrUtil.isNotEmpty(mnjxFlight.getCarrierFlight())) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        //验证航班是否在有效期内
        Integer count = fuMapper.retrieveFltDate(fuRetrieveDto.getFltNo(), fuRetrieveDto.getFltDate());
        if (Constant.ZERO == count) {
            throw new UnifiedResultException(Constant.INVALID_FLT);
        }
        // 验证航节信息是否能修改 （根据航班号与航班日期查询该航班的航节飞行计划信息）
        List<FuPlanSection> planSectionList = fuMapper.retrievePlanSection(fuRetrieveDto.getFltNo(), fuRetrieveDto.getFltDate());
        if (CollUtil.isEmpty(planSectionList)) {
            throw new UnifiedResultException(Constant.NOT_PLAN_SECTION);
        }
        //判断航班是否被保护 （航班是PC或者EC状态，航班被保护中，不允许修改）
        if (Constant.PC.equals(planSectionList.get(0).getCkStatus()) || Constant.EC.equals(planSectionList.get(0).getCkStatus())) {
            throw new UnifiedResultException(Constant.PROT_COV);
        }
        //查询tcard_section, 获取航站信息
        List<FuTcardSection> fuTcardSectionList = fuMapper.retrieveTcardSection(fuRetrieveDto.getFltNo());
        //检查输入的城市与该航班查询的航站不一致
        boolean matchCity = fuTcardSectionList.stream().noneMatch(t -> t.getAirportCode().equals(fuRetrieveDto.getCity()));
        if (matchCity) {
            throw new UnifiedResultException(Constant.NO_OP);
        }
        switch (fuRetrieveDto.getOpt()) {
            case ETA:
                // 当输入城市为起点站时不能修改到达时间
                boolean matchDst = fuTcardSectionList.stream().noneMatch(t -> t.getAirportCode().equals(fuRetrieveDto.getCity()) && Constant.STR_ONE.equals(t.getIsLastSection()));
                if (matchDst) {
                    throw new UnifiedResultException(FuConstant.ORIGIN);
                }
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    //输入时间校验
                    DateUtils.comHm2hmis(fuRetrieveDto.getData());
                }
                break;
            case FuConstant.BDT:
            case FuConstant.ETD:
            case FuConstant.GATE:
                // 当输入城市为终点站时不能修改起飞时间和登机口，停靠位置
                boolean matchEta = fuTcardSectionList.stream().noneMatch(t -> t.getAirportCode().equals(fuRetrieveDto.getCity()) && Constant.STR_ZERO.equals(t.getIsLastSection()));
                if (matchEta) {
                    throw new UnifiedResultException(FuConstant.DESTINATION);
                }
                //输入时间校验
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    if (FuConstant.BDT.equals(fuRetrieveDto.getOpt()) || FuConstant.ETD.equals(fuRetrieveDto.getOpt())) {
                        DateUtils.comHm2hmis(fuRetrieveDto.getData());
                    }
                }
                //根据机场航站查询所有登机口，如果不能匹配就不能修改，需要结合AZ指令查询某个航站的登机口（目前没有AZ指令的需求，暂时先不做验证）
                break;
            default:
                break;
        }
    }

    /**
     * FU指令业务处理
     */
    private String fuServiceHandle(FuRetrieveDto fuRetrieveDto) throws UnifiedResultException {
        // 根据航班号与航班日期查询该航班的航节飞行计划信息
        List<FuPlanSection> planSectionList = fuMapper.retrievePlanSection(fuRetrieveDto.getFltNo(), fuRetrieveDto.getFltDate());
        List<FuPlanSection> planSections;
        String estimateOff;
        String estimateDate;
        String paramDate;
        if (FuConstant.CTN.equals(fuRetrieveDto.getOpt())) {
            throw new UnifiedResultException(Constant.PLS_USE_STANDARD_AEC_PROCEDURE_FOR_INITIALIZED_FLIGHT);
        }
        switch (fuRetrieveDto.getOpt()) {
            case FuConstant.ETD:
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    //离港(起飞) 时间 实际起飞时间最多不能提前4小时，推迟无限制
                    planSections = planSectionList.stream().filter(p -> p.getOrg().equals(fuRetrieveDto.getCity())).collect(Collectors.toList());
                    estimateOff = DateUtils.comHm2hmis(planSections.get(0).getEstimateOff());
                    String paramOffTime = DateUtils.comHm2hmis(fuRetrieveDto.getData());
                    estimateDate = StrUtil.format("{} {}", fuRetrieveDto.getFltDate(), estimateOff);
                    paramDate = StrUtil.format("{} {}", fuRetrieveDto.getFltDate(), paramOffTime);
                    if (DateUtils.compare(DateUtils.ymdhms2Date(estimateDate), DateUtils.ymdhms2Date(paramDate)) > 0) {
                        throw new UnifiedResultException("实际起飞时间不能小于计划起飞时间");
                    }
                    if (DateUtils.offsetHour(DateUtils.ymdhms2Date(estimateDate), -Constant.FOUR).toString().compareTo(paramDate) > 0) {
                        throw new UnifiedResultException("实际起飞时间最多不能提前4小时");
                    }
                    //修改实际起飞时间
                    updatePlanSection(fuRetrieveDto, filterPlanSectionIdByOffCity(fuRetrieveDto, planSectionList));
                } else {
                    //单项取消修改
                    updateCancel(fuRetrieveDto, filterPlanSectionIdByOffCity(fuRetrieveDto, planSectionList));
                }

                break;
            case FuConstant.CTN:
                //cnd布局 先查找新的cnd表号，并以新表号更换飞机 （目前需求还不是很明确，暂时不开发）
                break;
            case FuConstant.ARN:
                //飞机注册号
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    //查询飞机注册号是否存在
                    MnjxAirline mnjxAirline = retrieveAirline(StrUtil.subPre(fuRetrieveDto.getFltNo(), 2));
                    MnjxPlane mnjxPlane = retrievePlane(fuRetrieveDto.getData(), mnjxAirline.getAirlineId());
                    if (ObjectUtil.isEmpty(mnjxPlane)) {
                        throw new UnifiedResultException(FuConstant.PLANE_NOT_EXIST);
                    }
                    //新增或者修改飞机注册号
                    fuMapper.updatePlanSectionAndPlaneNo(planSectionList, mnjxPlane.getPlaneId(), StrUtil.EMPTY);
                } else {
                    //取消单项修改，删除飞机注册号
                    fuMapper.updatePlanSectionAndPlaneNo(planSectionList, StrUtil.EMPTY, FuConstant.ERS);
                }
                break;
            case FuConstant.GATE:
            case FuConstant.BDT:
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    //登机口、登机时间、
                    updatePlanSection(fuRetrieveDto, filterPlanSectionIdByOffCity(fuRetrieveDto, planSectionList));
                } else {
                    //单项取消修改
                    updateCancel(fuRetrieveDto, filterPlanSectionIdByOffCity(fuRetrieveDto, planSectionList));
                }

                break;
            case FuConstant.ETA:
                if (!FuConstant.ERS.equals(fuRetrieveDto.getData())) {
                    //到达时间 不能早于起飞时间
                    planSections = planSectionList.stream().filter(p -> p.getDst().equals(fuRetrieveDto.getCity())).collect(Collectors.toList());
                    estimateOff = DateUtils.comHm2hmis(StrUtil.isNotEmpty(planSections.get(0).getActualOff()) ? planSections.get(0).getActualOff() : planSections.get(0).getEstimateOff());
                    estimateDate = StrUtil.format("{} {}", fuRetrieveDto.getFltDate(), estimateOff);
                    String paramArrTime = DateUtils.comHm2hmis(fuRetrieveDto.getData());
                    paramDate = StrUtil.format("{} {}", fuRetrieveDto.getFltDate(), paramArrTime);
                    if (DateUtils.compare(DateUtils.ymd2Date(estimateDate), DateUtils.ymd2Date(paramDate)) > 0) {
                        throw new UnifiedResultException("到达时间不能早于起飞时间");
                    }
                    updatePlanSection(fuRetrieveDto, filterPlanSectionIdByArrCity(fuRetrieveDto, planSectionList));
                } else {
                    //单项取消修改
                    updateCancel(fuRetrieveDto, filterPlanSectionIdByArrCity(fuRetrieveDto, planSectionList));
                }
                break;
            case FuConstant.RTNS:
                //取消一切修改，恢复到TCard 原始状态
                updateCancel(fuRetrieveDto, filterPlanSectionIdByOffCity(fuRetrieveDto, planSectionList));
                //还原飞机注册号
                fuMapper.updatePlanSectionAndPlaneNo(planSectionList, StrUtil.EMPTY, FuConstant.RTNS);
            default:
                break;
        }
        return FuConstant.ACCEPTED;
    }


    /**
     * 根据出发城市筛选航节计划id
     *
     * @param fuRetrieveDto   传入的参数
     * @param planSectionList 数据库查询的航节计划数据
     * @return 航节计划id （单航节只有一条数据，多航节就是多条）
     */
    private List<String> filterPlanSectionIdByOffCity(FuRetrieveDto fuRetrieveDto, List<FuPlanSection> planSectionList) {
        return planSectionList.stream()
                .filter(ps -> ps.getOrg().equals(fuRetrieveDto.getCity()))
                .map(FuPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
    }

    /**
     * 根据到达城市筛选航节计划id
     *
     * @param fuRetrieveDto   传入的参数
     * @param planSectionList 数据库查询的航节计划数据
     * @return 航节计划id （单航节只有一条数据，多航节就是多条）
     */
    private List<String> filterPlanSectionIdByArrCity(FuRetrieveDto fuRetrieveDto, List<FuPlanSection> planSectionList) {
        return planSectionList.stream()
                .filter(ps -> ps.getDst().equals(fuRetrieveDto.getCity()))
                .map(FuPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
    }

    /**
     * 根据航空二字码查询航空公司的数据
     *
     * @param airlineCode 航空二字码
     * @return 航空公司的数据
     */
    private MnjxAirline retrieveAirline(String airlineCode) {
        return iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, airlineCode)
                .one();
    }

    /**
     * 根据航空公司、飞机注册号查询飞机数据
     *
     * @param planeNo   飞机注册号
     * @param airlineId 航空公司id
     * @return 查询飞机数据
     */
    private MnjxPlane retrievePlane(String planeNo, String airlineId) {
        return iMnjxPlaneService.lambdaQuery()
                .eq(MnjxPlane::getAirlineId, airlineId)
                .eq(MnjxPlane::getPlaneNo, planeNo)
                .one();
    }

    /**
     * 修改计划航节计划信息表
     */
    private void updatePlanSection(FuRetrieveDto fuRetrieveDto, List<String> planSectionIds) {
        if (CollUtil.isNotEmpty(planSectionIds)) {
            iMnjxPlanSectionService.lambdaUpdate()
                    .in(MnjxPlanSection::getPlanSectionId, planSectionIds)
                    .set(FuConstant.GATE.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getGate, fuRetrieveDto.getData())
                    .set(FuConstant.ETD.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualOff, fuRetrieveDto.getData())
                    .set(FuConstant.BDT.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualBoarding, fuRetrieveDto.getData())
                    .set(FuConstant.ETA.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualArr, fuRetrieveDto.getData())
                    .update();
        }
    }

    /**
     * 单项取消修改
     */
    private void updateCancel(FuRetrieveDto fuRetrieveDto, List<String> planSectionIds) {
        if (CollUtil.isNotEmpty(planSectionIds)) {
            iMnjxPlanSectionService.lambdaUpdate()
                    .in(MnjxPlanSection::getPlanSectionId, planSectionIds)
                    .set(FuConstant.GATE.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getGate, StrUtil.EMPTY)
                    .set(FuConstant.ETD.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualOff, StrUtil.EMPTY)
                    .set(FuConstant.BDT.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualBoarding, StrUtil.EMPTY)
                    .set(FuConstant.ETA.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualArr, StrUtil.EMPTY)
                    .set(FuConstant.RTNS.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getGate, StrUtil.EMPTY)
                    .set(FuConstant.RTNS.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualOff, StrUtil.EMPTY)
                    .set(FuConstant.RTNS.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualBoarding, StrUtil.EMPTY)
                    .set(FuConstant.RTNS.equals(fuRetrieveDto.getOpt()), MnjxPlanSection::getActualArr, StrUtil.EMPTY)
                    .update();
        }
    }
}
