package com.swcares.eterm.dcs.fdc.mapper;

import com.swcares.entity.MnjxTcard;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface BfMapper {

    /**
     * 通过航班id、航班时间
     *
     * @param flightId   flightId
     * @param flightDate flightDate
     * @return 通过航班id、航班时间
     */
    MnjxTcard retrieveFlightByDate(@Param("flightId") String flightId, @Param("flightDate") String flightDate);
}
