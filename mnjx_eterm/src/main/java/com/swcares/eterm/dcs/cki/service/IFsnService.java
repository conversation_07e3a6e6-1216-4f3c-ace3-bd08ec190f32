package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;

/**
 * <AUTHOR>
 */
public interface IFsnService {

    /**
     * 业务处理
     *
     * @param memoryData 内存对象
     * @param cmd        指令
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    Object handle(MemoryData memoryData, String cmd) throws UnifiedResultException;
}
