package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.type.OperateType;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.RaDto;
import com.swcares.eterm.dcs.cki.service.IRaService;

import javax.annotation.Resource;

/**
 * RA 指令处理
 *
 * <AUTHOR>
 */
@OperateType(action = "RA", needPaging = false)
public class RaHandler implements Handler {

    @Resource
    private IRaService iRaService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        //参数解析
        RaDto raDto = iRaService.parseRa(cmd);
        //业务处理
        return iRaService.handler(raDto);
    }
}
