package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.RaDto;

/**
 * RA指令处理
 *
 * <AUTHOR>
 */

public interface IRaService {
    /**
     * 参数解析
     *
     * @param cmd RA指令
     * @return 参数对象
     * @throws UnifiedResultException 统一异常处理
     */
    RaDto parseRa(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     * @param raDto 参数对象
     * @return 修改后的座位图
     * @throws UnifiedResultException 统一异常处理
     */
    String handler(RaDto raDto) throws UnifiedResultException;
}
