package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlDto;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlResult;
import com.swcares.eterm.dcs.cki.service.ISpmlService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/25 15:40
 */
@OperateType(action = "SPML", shorthand = true, template = "/dcs/cki/spml.jf", fullScreen = true)
public class SpmlHandler implements Handler {

    @Resource
    private ISpmlService iSpmlService;

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        SpmlDto spmlDto = iSpmlService.parseCmd(cmd);
        List<SpmlResult> spmlResultList = iSpmlService.handle(spmlDto);
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setResults(spmlResultList.toArray());
        unifiedResult.setArgs(Collections.singletonList(spmlDto).toArray());
        return unifiedResult;
    }
}
