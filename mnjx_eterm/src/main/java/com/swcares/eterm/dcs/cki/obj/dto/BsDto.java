package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.*;

/**
 * <AUTHOR> @Desc
 * @date 2022/8/8 - 11:39
 * @Version
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BsDto {

    /**
     * 航班号
     */
    private String flightNo;

    /**
     *航班日期
     */
    private String flightDate;

    /**
     *出发航站
     */
    private String dep;

    /**
     *到达航站
     */
    private String arr;

    /**
     * 座位主键ID
     */
    private String seatId;

    /**
     * 舱等
     */
    private String cabinClass;

    /**
     * 座位号
     */
    private String seatNo;

    /**
     * 座位行号
     */
    private String seatRow;

    /**
     * 当前座位状态
     */
    private String seatStatus;

    /**
     * 旧座位状态
     */
    private String seatStatusOld;
}
