package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxFlight;
import com.swcares.eterm.dcs.cki.obj.dto.FtDto;
import com.swcares.eterm.dcs.cki.mapper.FtMapper;
import com.swcares.eterm.dcs.cki.service.IFtService;
import com.swcares.service.IMnjxFlightService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * FT:设置默认航班
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FtServiceImpl implements IFtService {

    @Resource
    private FtMapper ftMapper;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    /**
     * FT:3U8989/01JUN22
     */
    private static final Pattern FT_PATTERN_DATE = Pattern.compile("FT:(\\w{6})(/([+-.]|\\w{5,7}))?");
    /**
     * FT:   显示缺省航班
     * FT:- 取消缺省航班
     */
    private static final Pattern FT_PATTERN_CANCEL = Pattern.compile("FT:?(-)?");

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        if (!ReUtil.isMatch(FT_PATTERN_DATE, cmd) && !ReUtil.isMatch(FT_PATTERN_CANCEL, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        MemoryDataFt memoryDataFt = MemoryDataUtils.getMemoryData().getMemoryDataFt();
        String result = StrUtil.EMPTY;
        if (ReUtil.isMatch(FT_PATTERN_DATE, cmd)) {
            List<String> allGroups = ReUtil.getAllGroups(FT_PATTERN_DATE, cmd, false);
            String fltNo = allGroups.get(0);
            String fltDate = allGroups.get(2);
            fltDate = StrUtil.isEmpty(fltDate) ? DateUtil.today() : DateUtils.com2ymd(fltDate);
            //查询航班是否存在
            MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, fltNo)
                    .one();
            if (ObjectUtil.isEmpty(mnjxFlight)) {
                throw new UnifiedResultException(Constant.FLIGHT);
            }
            //设置缺省航班的业务处理
            setFltNoHandle(memoryDataFt, fltNo, fltDate);
            result = Constant.ACCEPTED;
        } else if (ReUtil.isMatch(FT_PATTERN_CANCEL, cmd)) {
            String param = ReUtil.getGroup1(FT_PATTERN_CANCEL, cmd);
            param = StrUtil.isNotEmpty(param) ? param : StrUtil.EMPTY;
            switch (param) {
                case StrUtil.DASHED:
                    //- 取消缺省航班
                    memoryDataFt.setFlightNo(StrUtil.EMPTY);
                    memoryDataFt.setFlightDate(StrUtil.EMPTY);
                    result = Constant.ACCEPTED;
                    break;
                case StrUtil.EMPTY:
                    //显示缺省航班
                    String mdFlt = memoryDataFt.getFlightNo();
                    String mdFltDate = memoryDataFt.getFlightDate();
                    if (StrUtil.isEmpty(mdFlt)) {
                        throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
                    }
                    result = StrUtil.format("\u0010FT:{}/{}", mdFlt, DateUtils.ymd2Com(mdFltDate));
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    /**
     * 设置缺省航班的业务处理
     *
     * @param memoryDataFt 内存对象
     * @param fltNo        航班号
     * @param fltDate      航班日期
     * @throws UnifiedResultException 统一异常处理
     */
    private void setFltNoHandle(MemoryDataFt memoryDataFt, String fltNo, String fltDate) throws UnifiedResultException {
        String mdFlt = memoryDataFt.getFlightNo();
        String mdFltDate = memoryDataFt.getFlightDate();
        if (StrUtil.isNotEmpty(mdFlt)) {
            //设置的默认航班是否和已经设置的默认航班时间重复
            if (fltNo.equals(mdFlt) && DateUtil.isSameDay(DateUtils.ymd2Date(fltDate), DateUtils.ymd2Date(mdFltDate))) {
                throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
            }
        }
        //查询航班是否初始化
        FtDto ftDto = ftMapper.retrievePlanFlt(fltNo, fltDate);
        if (ObjectUtil.isEmpty(ftDto)) {
            throw new UnifiedResultException(Constant.ERROR);
        }
        if (!Constant.Y.equals(ftDto.getIsFlightInitial())) {
            throw new UnifiedResultException(Constant.NEED_INITIALIZE);
        }
        //设置航班信息到内存中
        memoryDataFt.setFlightNo(fltNo);
        memoryDataFt.setFlightDate(fltDate);
    }

}
