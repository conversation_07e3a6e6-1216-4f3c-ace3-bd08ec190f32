package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.BtDto;
import com.swcares.eterm.dcs.cki.service.IBtService;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@OperateType(action = "BT", needPaging = false)
public class BtHandler implements Handler {

    @Resource
    private IBtService iBtService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // 参数解析
        BtDto btDto = iBtService.parse(cmd);
        // 业务处理
        return iBtService.handler(btDto);
    }

}
