package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.SyDto;
import com.swcares.eterm.dcs.cki.obj.dto.SyResultDto;

/**
 * SY指令的接口
 *
 * <AUTHOR>
 */
public interface ISyService {

    /**
     * 数据解析
     *
     * @param cmd 指令
     * @return 数据解析
     * @throws UnifiedResultException 统一异常
     */
    SyDto parseSy(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param syDto         syDto
     * @param unifiedResult unifiedResult
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    SyResultDto handle(SyDto syDto, UnifiedResult unifiedResult) throws UnifiedResultException;

    /**
     * orderLayoutStr
     *
     * @param origin origin
     * @param target target
     * @return orderLayoutStr
     */
    String orderLayoutStr(String origin, String target);
}

