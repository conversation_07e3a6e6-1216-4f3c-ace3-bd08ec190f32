package com.swcares.eterm.dcs.cki.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.eterm.dcs.cki.obj.dto.HbpaCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdNmDto;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.eterm.dcs.cki.service.IHbjcService;
import com.swcares.eterm.dcs.cki.service.IPaService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Hbjc指令处理
 *
 * <AUTHOR>
 */
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class HbjcServiceImpl implements IHbjcService {

    /**
     * HBJC:CA8541/12MAR24JSHATFU/127;128
     * HBJC:CA8541/12MAR24JSHATFU/129
     */
    private static final Pattern HBJC_PATTERN = Pattern.compile("(\\w{5,6})/(\\d{2}[A-Z]{3}(\\d{2})?)([A-Z])?(\\w{3})?(\\w{3})?/((\\d+)(;\\d+)*)");

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Resource
    private IPaService iPaService;

    @Override
    public List<HbpaCmdDto> parseHbjc(String cmd) throws UnifiedResultException {
        String paramStr = cmd.split(":")[1];
        if (!ReUtils.isMatch(HBJC_PATTERN, paramStr)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> allGroups = ReUtils.getAllGroups(HBJC_PATTERN, paramStr, false);
        String fltNo = allGroups.get(0);
        String fltDate = allGroups.get(1);
        String cabin = allGroups.get(2);
        String org = allGroups.get(4);
        String dst = allGroups.get(5);
        String psgNo = allGroups.get(6);
        List<HbpaCmdDto> hbpaCmdDtoList = new ArrayList<>();
        for (String no : psgNo.split(";")) {
            HbpaCmdDto hbpaCmdDto = new HbpaCmdDto();
            hbpaCmdDto.setFlightNo(fltNo);
            hbpaCmdDto.setFlightDate(fltDate);
            hbpaCmdDto.setOrgCityCode(org);
            hbpaCmdDto.setDstCityCode(dst);
            hbpaCmdDto.setSellCabin(cabin);
            hbpaCmdDto.setHbnbNo(Integer.parseInt(no));
            hbpaCmdDto.setCmd(cmd);
            hbpaCmdDtoList.add(hbpaCmdDto);
        }
        return hbpaCmdDtoList;
    }

    @Override
    @CachePut(key = "'hbpaResult' + #memoryData.memoryDataId")
    public List<PaResultDto> handle(MemoryData memoryData, List<HbpaCmdDto> hbpaCmdDtoList) throws UnifiedResultException {
        List<PaResultDto> paResultDtoList = new ArrayList<>();
        PdInfoDto pdInfoDto = iCkiCacheService.getPdSbyInfo(memoryData);
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
            if (ObjectUtil.isEmpty(pdInfoDto)) {
                pdInfoDto = iCkiCacheService.getPrCache(memoryData);
                if (ObjectUtil.isEmpty(pdInfoDto)) {
                    pdInfoDto = iCkiCacheService.getHbprCache(memoryData);
                }
            }
        }
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        this.constructFromPd(paResultDtoList, pdInfoDto, hbpaCmdDtoList);

        if (CollUtil.isEmpty(paResultDtoList)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        iPaService.parseAndUpdateData(paResultDtoList);
        return paResultDtoList;
    }

    @Override
    public void print(List<PaResultDto> paResultDtoList) {
        iPaService.print(paResultDtoList);
    }

    /**
     * Title: constructFromPd
     * Description: 从PD结果构建数据
     *
     * @param paResultDtoList
     * @param pdInfoDto
     * @param hbpaCmdDtoList
     * @return
     * <AUTHOR>
     * @date 2022/8/10 10:35
     */
    private void constructFromPd(List<PaResultDto> paResultDtoList, PdInfoDto pdInfoDto, List<HbpaCmdDto> hbpaCmdDtoList) throws UnifiedResultException {
        List<PdNmDto> pdNmDtoList = pdInfoDto.getNms();
        for (HbpaCmdDto hbpaCmdDto : hbpaCmdDtoList) {
            int hbnbNo = hbpaCmdDto.getHbnbNo();
            if (ObjectUtil.isNull(hbnbNo)) {
                throw new UnifiedResultException(Constant.ITEM);
            }
            List<PdNmDto> collect = pdNmDtoList.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getPsgNum()) && Integer.parseInt(p.getPsgNum().trim()) == hbnbNo)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                throw new UnifiedResultException(Constant.NUMBER_ERROR);
            }
            PdNmDto pdNmDto = collect.get(0);
            PaResultDto paResultDto = new PaResultDto();
            paResultDto.setHbpa(true);
            paResultDto.setCmd(hbpaCmdDto.getCmd());
            paResultDto.setAdv(hbpaCmdDto.isAdv());
            paResultDto.setOptionList(hbpaCmdDto.getOptionList());
            // 构建航班信息
            paResultDto.setFlightNo(hbpaCmdDto.getFlightNo());
            paResultDto.setFlightDate(DateUtils.com2ymd(hbpaCmdDto.getFlightDate()));
            paResultDto.setDstAirport(StrUtil.isNotEmpty(hbpaCmdDto.getDstCityCode()) ? hbpaCmdDto.getDstCityCode() : pdNmDto.getDst());
            paResultDto.setPlaneType(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[0] : null);
            paResultDto.setPlaneVersion(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[1] : null);
            paResultDto.setGate(StrUtil.isNotEmpty(pdInfoDto.getGtd()) ? (pdInfoDto.getGtd().split("/").length > 1 ? pdInfoDto.getGtd().split("/")[1] : pdInfoDto.getGtd()) : "????");
            paResultDto.setBdt(pdInfoDto.getBoarding());
            paResultDto.setSd(pdInfoDto.getEstimateOff());
            paResultDto.setEd(pdInfoDto.getActualOff());
            paResultDto.setSa("");
            paResultDto.setFt("");
            paResultDto.setAirlineCode(paResultDto.getFlightNo().substring(0, 2));
            // 构建旅客信息数据
            paResultDto.setQueryName(pdNmDto.getQueryName().matches("\\d+.+") ? pdNmDto.getQueryName().substring(1) : pdNmDto.getQueryName());
            paResultDto.setQueryName(StrUtil.fill(paResultDto.getQueryName(), ' ', 13, false));
            paResultDto.setPnrNmId(pdNmDto.getPnrNmId());
            paResultDto.setCnin(StrUtil.isNotEmpty(pdNmDto.getIsCnin()));
            paResultDto.setName(pdNmDto.getName());
            paResultDto.setOInterlink(pdNmDto.getOInterlink());
            paResultDto.setOrgAirport(pdNmDto.getOrg());
            paResultDto.setSellCabin(pdNmDto.getSellCabin());
            paResultDto.setCabinClass(pdNmDto.getCabinClass());

            paResultDtoList.add(paResultDto);
        }
    }
}
