/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import com.swcares.entity.MnjxAirport;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * SB指令实体对象
 *
 * <AUTHOR>
 */
@Data
@ToString
public class SbAirportDto {
        /**
         * 航段编号
         */
        private String planSectionId;
        /**
         * 出发机场编号
         */
        private String depAirportCode;
        /**
         * 到达机场编号
         */
        private String arrAirportCode;
}
