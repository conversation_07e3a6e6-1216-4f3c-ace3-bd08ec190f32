package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.HbpwCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;
import com.swcares.eterm.dcs.cki.service.IHbpwService;

import javax.annotation.Resource;
import java.util.List;

/**
 * description：HbpwHandler <br>
 * <AUTHOR> <br>
 * date 2022/08/17 <br>
 * @version v1.0 <br>
 */
@OperateType(action = "HBPW", template = "dcs/cki/hbpw.jf", fullScreen = true)
public class HbpwHandler implements Handler {

	@Resource
	private IHbpwService iHbpwService;

	@Override
	public Object handle(String cmd) throws UnifiedResultException {
		// 统一返回对象
		UnifiedResult unifiedResult = new UnifiedResult();
		List<HbpwCmdDto> pwDtoList = iHbpwService.parsePw(cmd);
		List<PwResultDto> pwResultDtoList = iHbpwService.handle(MemoryDataUtils.getMemoryData(), pwDtoList);
		iHbpwService.savePwForPrint(pwResultDtoList);
		unifiedResult.setArgs(new Object[] { cmd.split(":")[1] });
		unifiedResult.setResults(pwResultDtoList.toArray());
		return unifiedResult;
	}
}
