/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 行李信息dto
 *
 * <AUTHOR>
 */
@Data
@ToString
@Builder
public class BagInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 旅客姓名
     */
    private String name;

    /**
     * 行李号
     */
    private String bagNo;

    /**
     * 起飞机场
     */
    private String offAirPort;

    /**
     * 航空公司代码
     */
    private String airLineCode;

    /**
     * 飞机号
     */
    private String planeNo;

    /**
     * 日期
     */
    private String date;
}
