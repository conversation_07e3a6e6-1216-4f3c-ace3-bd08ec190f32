package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PdNmDto implements Serializable{

	private String pnrNmId;

	private String queryName;

	private String name;

	private String groupName;

	private String psgSeat;

	private String sellCabin;

	private String dst;

	private String pnrIcs;

	private String isCnin;

	private String psgType;

	private String defaultGroupName;

	private String interlink;

	private String ssrInfo;

	private String index;

	private String psmType;

	private String info;

	private String pnrId;

	private String org;

	private String iInterlink;

	private String oInterlink;

	private String cabinClass;

	private String luggageNum;

	private String luggageWeight;

	private boolean avih;

	private String psgNum;

	private String isAec;

	private String aboardNo;

	private String ckiStatus;

	private String tnId;

	private String pnrSegId;

	private String pnrSegNo;

	private List<InterlinkInfo> iInterlinkInfo = new ArrayList<InterlinkInfo>();

	private List<String> luggageNos = new ArrayList<String>();

	private String vip;

	private String seatOtherStr;

	private String isEt;

	private String infStatus;

	private String hbNo;
	
	private String ures;
	
	private String nrec;
	
	private String shareFlight;
	
	private String carrierFlight;
	
	private String flightNo;
}
