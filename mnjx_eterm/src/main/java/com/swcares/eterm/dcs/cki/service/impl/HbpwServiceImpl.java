package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxPnrNm;
import com.swcares.eterm.base.service.ITransactionalOperateService;
import com.swcares.eterm.dcs.cki.obj.dto.HbpwCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdNmDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.eterm.dcs.cki.service.IHbpwService;
import com.swcares.eterm.dcs.cki.service.IPwService;
import com.swcares.service.IMnjxPnrNmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * description：HbpwServiceImpl <br>
 *
 * <AUTHOR> <br>
 * date 2022/08/17 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class HbpwServiceImpl implements IHbpwService {

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IPwService iPwService;

    @Resource
    private ITransactionalOperateService iTransactionalOperateService;

    private static final Pattern REG1 = Pattern.compile("(\\w{5,6})"
            + "/(\\d{2}[A-Z]{3}\\d{2})"
            + "([A-Z])"
            + "([A-Z]{3})"
            + "([#-])?.+");
    /**
     * OCA1284/02FEB23YPEK
     */
    private static final Pattern REG2 = Pattern.compile("^O(\\w{5,6})"
            + "/(\\d{2}[A-Z]{3}\\d{2})"
            + "([A-Z])"
            + "([A-Z]{3})$");

    /**
     * 所有选项
     */
    private static final Pattern REG9 = Pattern.compile("BAG|PSM|BLND|BAG|CHD\\d{1}?|DEAF|O(.+)?|M1|F1|INF1|INF|UM(-?\\d{1,4})?|SPML|STCR|CNIN|WCHC|WCHR"
            + "|WCHS|WCBD|WCBW|WCMP|WCOB|BSCT|PETC"
            + "|(\\d{1,3}/\\d{1,3})|(BT((/\\d{6})+))|(T/\\w{2}/\\d{6})"
            + "|EXBG|EXPC|INAD|ADSR|EXST|CBBG|COUR|DIPL|FF");

    @Override
    public List<HbpwCmdDto> parsePw(String cmd) {
        List<HbpwCmdDto> hbpwCmdDtoList = new ArrayList<>();
        String[] split = cmd.split(":");
        String param = split[1];
        if (ReUtil.isMatch(REG1, param)) {
            List<String> allGroups = ReUtil.getAllGroups(REG1, param);
            String fltNo = allGroups.get(1);
            String fltDate = allGroups.get(2);
            String cabinClass = allGroups.get(3);
            String dst = allGroups.get(4);
            // #或者-
            String str = null;
            String process = allGroups.get(5);
            int subIndex = 0;
            if (StrUtil.isNotBlank(process)) {
                subIndex = param.indexOf(process);
                // 从操作符开始截取
                str = param.substring(subIndex + 1);
            } else {
                subIndex = param.indexOf(dst);
                // 从操作符开始截取
                str = param.substring(subIndex + 3);
            }
            //是否执行pw
            boolean isExecPw = false;
            // 3,UM;4,BLAD
            String[] strArray = str.split(";");
            for (String str1 : strArray) {
                HbpwCmdDto hbpaCmdDto = new HbpwCmdDto();
                String[] psgList = str1.split(",");
                List<String> optionList = new ArrayList<>();
                for (int i = 0; i < psgList.length; i++) {
                    if (i == 0) {
                        hbpaCmdDto.setHbnbNo(Integer.parseInt(psgList[i]));
                    } else {
                        String option = psgList[i];
                        if (ReUtil.isMatch(REG2, option) || "O".equals(option)) {
                            List<String> lcGroup = ReUtil.getAllGroups(REG2, option);
                            isExecPw = true;
                            if (CollectionUtil.isNotEmpty(lcGroup)) {
                                HbpwCmdDto lcCmdDto = new HbpwCmdDto();
                                lcCmdDto.setFltNo(lcGroup.get(1));
                                lcCmdDto.setFltDate(lcGroup.get(2));
                                lcCmdDto.setCabinClass(lcGroup.get(3));
                                lcCmdDto.setDst(lcGroup.get(4));
                                hbpaCmdDto.setLcCmdDto(lcCmdDto);
                            }

                        } else {
                            optionList.add(option);
                        }
                    }
                }
                hbpaCmdDto.setFltNo(fltNo);
                hbpaCmdDto.setFltDate(fltDate);
                hbpaCmdDto.setCabinClass(cabinClass);
                hbpaCmdDto.setDst(dst);
                hbpaCmdDto.setAdvance("#".equals(process));
                hbpaCmdDto.setOptionList(optionList);
                hbpaCmdDto.setExecPw(isExecPw);
                hbpwCmdDtoList.add(hbpaCmdDto);
            }
        }
        return hbpwCmdDtoList;
    }

    @Override
    @CachePut(key = "'hbpwResult' + #memoryData.memoryDataId")
    public List<PwResultDto> handle(MemoryData memoryData, List<HbpwCmdDto> hbpwCmdDtoList) throws UnifiedResultException {
        PdInfoDto pdInfoDto = iCkiCacheService.getPdSbyInfo(memoryData);
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
            if (ObjectUtil.isEmpty(pdInfoDto)) {
                pdInfoDto = iCkiCacheService.getPrCache(memoryData);
                if (ObjectUtil.isEmpty(pdInfoDto)) {
                    pdInfoDto = iCkiCacheService.getHbprCache(memoryData);
                }
            }
        }
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        List<PwResultDto> pwResultDtoList = getPdData(hbpwCmdDtoList, pdInfoDto);
        if (CollUtil.isEmpty(pwResultDtoList)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        iTransactionalOperateService.executePw(pwResultDtoList);
        return pwResultDtoList;
    }

    @Override
    public void savePwForPrint(List<PwResultDto> pwResultDtoList) {
        iPwService.savePwForPrint(pwResultDtoList, "HBPW");
    }

    /**
     * Title：handfcy <br>
     * description：字符串fcy（F012C030Y255）进行加减 <br>
     *
     * @param fcyStr     原始字符串
     * @param cabinClass 舱位
     * @param num        数量
     * @param manipulate true为减，其他为加
     * @return <br>
     * <AUTHOR> <br>
     * @date 2022/08/11 <br>
     */
    private String handleFcy(String fcyStr, String cabinClass, Integer num, boolean manipulate) {
        String[] fcyStrs = fcyStr.split("/");
        StringBuffer sb = new StringBuffer();
        List<String> list = CollectionUtil.toList(fcyStrs);
        for (int i = 0; i < list.size(); i++) {
            String fcy = list.get(i);
            if (fcy.length() >= 2) {
                Object tempCabinClass = fcy.substring(0, 1);
                Object tempNum = fcy.substring(1);
                Integer fcyNum = 0;
                if (ObjectUtil.isNotEmpty(tempCabinClass) && ObjectUtil.isNotEmpty(tempNum)) {
                    int numLeng = tempNum.toString().length();
                    fcyNum = Integer.parseInt(tempNum.toString());
                    if (tempCabinClass.equals(cabinClass)) {
                        // true为减，其他为加
                        if (manipulate) {
                            if (fcyNum != 0) {
                                fcyNum = fcyNum - num;
                            }
                        } else {
                            fcyNum = fcyNum + num;
                        }
                    }
                    sb.append(tempCabinClass).append(String.format("%0" + numLeng + "d", fcyNum));
                    if (i < list.size() - 1) {
                        sb.append("/");
                    }
                }
            } else {
                return null;
            }
        }
        return sb.toString();
    }

    private void checkOption(List<PwResultDto> pwResultDtoList) throws UnifiedResultException {
        for (PwResultDto pwResultDto : pwResultDtoList) {
            // 检查选择项
            List<String> optionList = pwResultDto.getOptionList();
            if (CollectionUtil.isEmpty(optionList)) {
                return;
            }
            if (pwResultDto.isExecPw()) {
                return;
            }
            boolean isWchc = false;
            boolean isWchs = false;
            boolean isWchr = false;
            for (String option : optionList) {
                // 如果输入的选择有O标识，提示“OUTBOUND DELETE UNABLE”
                if ("O".equals(option)) {
                    throw new UnifiedResultException(Constant.OUTBOUND_DELETE_UNABLE);
                } else if (!ReUtil.isMatch(REG9, option)) {
                    // 如果选择项不正确，如“AAAA”,选择项不正确，提示“ITEM”
                    log.error("选项不正确");
                    throw new UnifiedResultException(Constant.ITEM);
                } else if ("WCHC".equals(option)) {
                    isWchc = true;
                } else if ("WCHS".equals(option)) {
                    isWchs = true;
                } else if ("WCHR".equals(option)) {
                    isWchr = true;
                }
            }
            // 如果输入的选择项同时存在WCHC,WCHS,WCHR，提示“DUP IS FOUND ”
            if (isWchc && isWchs && isWchr) {
                log.error("WCHC,WCHS,WCHR同时存在");
                throw new UnifiedResultException(Constant.DUP_IS_FOUND);
            }
        }
    }

    /**
     * Title：getPdData <br>
     * description：pd中获取旅客信息 <br>
     *
     * @param hbpwCmdDtoList
     * @param pdInfoDto
     * @return
     * @throws UnifiedResultException <br>
     * <AUTHOR> <br>
     * @date 2022/08/16 <br>
     */
    private List<PwResultDto> getPdData(List<HbpwCmdDto> hbpwCmdDtoList, PdInfoDto pdInfoDto) throws UnifiedResultException {
        //构建pwresult基础数据
        List<PwResultDto> pwrList = bulidPwResultDto(hbpwCmdDtoList, pdInfoDto);
        //旅客是多段的情况处理
        return iPwService.handlePsg(pwrList, Constant.HBPW);
    }

    /**
     * @param hbpwCmdDtoList
     * @param pdInfoDto
     * @return
     * @throws UnifiedResultException <br>
     * @title：bulidPwResultDto <br>
     * @description：pd对象转为候pw对象 <br>
     * <AUTHOR> <br>
     * @date 2023/01/29 <br>
     */
    private List<PwResultDto> bulidPwResultDto(List<HbpwCmdDto> hbpwCmdDtoList, PdInfoDto pdInfoDto) throws UnifiedResultException {
        List<PwResultDto> pwrList = new ArrayList<>();
        for (HbpwCmdDto hbpwCmdDto : hbpwCmdDtoList) {
            int hbnbNo = hbpwCmdDto.getHbnbNo();
            PdNmDto pdNmDto = null;
            if (pdInfoDto != null) {
                List<PdNmDto> nmList = pdInfoDto.getNms();
                if (CollectionUtil.isNotEmpty(nmList)) {
                    for (PdNmDto nmDto : nmList) {
                        String psgNum = nmDto.getPsgNum();
                        if (StrUtil.isNotBlank(psgNum)) {
                            if (hbnbNo == Integer.parseInt(psgNum)) {
                                pdNmDto = nmDto;
                                break;
                            }
                        }
                    }
                }
            }
            // 如果是旅客序号不在旅客列表内提示“NUMBER ERROR”。
            if (pdNmDto == null) {
                log.error("序号不在PD指令范围内");
                throw new UnifiedResultException(Constant.NUMBER_ERROR);
            }
            PwResultDto prd = new PwResultDto();
            prd.setOptionList(hbpwCmdDto.getOptionList());
            // 构建航班信息
            prd.setAirlineCode(hbpwCmdDto.getFltNo().substring(0, 2));
            prd.setPlaneType(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[0] : null);
            prd.setPlaneVersion(StrUtil.isNotEmpty(pdInfoDto.getPlaneType()) ? pdInfoDto.getPlaneType().split("/")[1] : null);
            // 构建旅客信息数据
            prd.setQueryName(pdNmDto.getQueryName());
            prd.setQueryName(StrUtil.fill(prd.getQueryName(), ' ', 13, false));
            prd.setPnrNmId(pdNmDto.getPnrNmId());
            prd.setIsCnin(pdNmDto.getIsCnin());
            prd.setName(pdNmDto.getName());
            prd.setFltNo(hbpwCmdDto.getFltNo());
            prd.setFltDate(DateUtils.com2ymd(hbpwCmdDto.getFltDate()));
            prd.setOrg(pdNmDto.getOrg());
            prd.setSellCabin(pdNmDto.getSellCabin());
            prd.setCabinClass(pdNmDto.getCabinClass());
            prd.setDst(hbpwCmdDto.getDst());
            prd.setAdvance(hbpwCmdDto.isAdvance());
            prd.setExecPw(hbpwCmdDto.isExecPw());
            prd.setLcCmdDto(hbpwCmdDto.getLcCmdDto());
            // 获取旅客PNRID
            MnjxPnrNm mnjxPnrNm = iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, pdNmDto.getPnrNmId()).one();
            if (mnjxPnrNm == null) {
                log.error("mnjxPnrNm对象为空");
                throw new UnifiedResultException(Constant.ERROR);
            }
            String pnrId = mnjxPnrNm.getPnrId();
            if (StrUtil.isBlank(pnrId)) {
                log.error("pnrId为空");
                throw new UnifiedResultException(Constant.ERROR);
            }
            prd.setMnjxPnrNm(mnjxPnrNm);
            pwrList.add(prd);
        }
        return pwrList;
    }
}
