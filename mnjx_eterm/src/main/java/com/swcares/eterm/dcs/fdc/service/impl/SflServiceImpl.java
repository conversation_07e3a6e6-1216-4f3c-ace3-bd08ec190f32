package com.swcares.eterm.dcs.fdc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxAirline;
import com.swcares.entity.MnjxFlight;
import com.swcares.entity.MnjxTcard;
import com.swcares.eterm.dcs.fdc.dto.SflDto;
import com.swcares.eterm.dcs.fdc.service.ISflService;
import com.swcares.service.IMnjxAirlineService;
import com.swcares.service.IMnjxFlightService;
import com.swcares.service.IMnjxTcardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.eterm.sendAgent.service.fdc
 * Description：SFL
 * date 2020-06-2020/6/16 15:21:34
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@Service
public class SflServiceImpl implements ISflService {

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    /**
     * 指令必须指定航司2字码
     * SFL:CA  SFL:3U
     */
    private static final String REG = "SFL:([0-9A-Za-z]{2})";

    /**
     * 指令必须指定航班号 3U8888、3U8888T
     */
    private static final String REG2 = "SFL:([0-9A-Za-z]{5,7})";

    @Override
    public Object handle(String cmd) {
        //1、验证指令格式
        if (!cmd.matches(REG) && !cmd.matches(REG2)) {
            return Constant.FORMAT;
        }
        // 航空公司
        if (cmd.matches(REG)) {
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG), cmd);
            // 获取CND数据
            String fltCode = allGroups.get(1);
            return this.retrieveFlightByAirCode(fltCode, Constant.FLIGHT_STATUS_STAGED);
        } else if (cmd.matches(REG2)) {
            // 航班号
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG2), cmd);
            String flightNo = allGroups.get(1);
            return retrieveByFlightNo(flightNo, Constant.FLIGHT_STATUS_STAGED);
        }
        return Constant.FORMAT;
    }

    @Override
    public UnifiedResult retrieveFlightByAirCode(String airCode, String flightStatus) {
        // 构建数据传输对象
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setArgs(Arrays.asList("A", airCode).toArray());
        MnjxAirline airline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineCode, airCode).one();
        if (ObjectUtil.isEmpty(airline)) {
            return unifiedResult;
        }
        List<MnjxFlight> flightList = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightStatus, flightStatus)
                .eq(MnjxFlight::getAirlineId, airline.getAirlineId())
                .list();
        List<String> list = flightList.stream().map(MnjxFlight::getFlightNo).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            List<String> flights = flightPage(list);
            unifiedResult.setResults(flights.toArray());
        }
        return unifiedResult;
    }

    @Override
    public UnifiedResult retrieveByFlightNo(String flightNo, String flightStatus) {
        UnifiedResult unifiedResult = new UnifiedResult();
        // 航班公司
        String fltCode = flightNo.substring(0, 2);
        unifiedResult.setArgs(Arrays.asList("F", fltCode, flightNo).toArray());
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .eq(MnjxFlight::getFlightStatus, flightStatus)
                .one();
        if (ObjectUtil.isEmpty(flight)) {
            return unifiedResult;
        }
        List<MnjxTcard> tcards = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).list();
        if (CollUtil.isEmpty(tcards)) {
            return unifiedResult;
        }
        List<SflDto> sfls = tcards.stream().map(k -> {
            SflDto sflDto = new SflDto();
            sflDto.setFlightNo(flightNo);
            sflDto.setCycle(k.getCycle());
            sflDto.setStartDate(DateUtils.ymd2Com(DateUtils.date2ymd(k.getStartDate())));
            sflDto.setEndDate(DateUtils.ymd2Com(DateUtils.date2ymd(k.getEndDate())));
            return sflDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(sfls)) {
            unifiedResult.setResults(sfls.toArray());
        }
        return unifiedResult;
    }

    /**
     * 手动分页
     *
     * @param list list
     * @return 手动分页
     */
    private List<String> flightPage(List<String> list) {
        // 使用分页处理好数据
        int pageSize = 8;
        int sp = 0;
        int total = (list.size() + pageSize - 1) / pageSize;
        List<String> flights = new ArrayList<>();
        for (int i = 0; i < total; i++) {
            String flight = list.stream().skip(sp).limit(pageSize).collect(Collectors.joining("  "));
            flights.add(flight);
            sp += pageSize;
        }
        return flights;
    }
}
