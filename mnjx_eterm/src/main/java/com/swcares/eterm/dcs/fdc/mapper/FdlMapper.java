package com.swcares.eterm.dcs.fdc.mapper;

import com.swcares.eterm.dcs.fdc.dto.FdlDto;
import com.swcares.eterm.dcs.fdc.dto.FdlSection;
import com.swcares.eterm.dcs.fdc.dto.FdlViewDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface FdlMapper {

    /**
     * retrieveNms
     *
     * @param fdlDto fdlDto
     * @return retrieveNms
     */
    List<FdlViewDto> retrieveNms(FdlDto fdlDto);

    /**
     * retrieveSections
     *
     * @param fltDate fltDate
     * @param set     set
     * @return retrieveSections
     */
    List<FdlSection> retrieveSections(@Param("fltDate") String fltDate, @Param("fltNos") Set<String> set);

    /**
     * retrieveSectionNms
     *
     * @param fdlDto fdlDto
     * @return retrieveSectionNms
     */
    List<FdlViewDto> retrieveSectionNms(FdlDto fdlDto);

    /**
     * retrieveAllSections
     *
     * @param fdlDto fdlDto
     * @return retrieveAllSections
     */
    List<FdlViewDto> retrieveAllSections(FdlDto fdlDto);
}
