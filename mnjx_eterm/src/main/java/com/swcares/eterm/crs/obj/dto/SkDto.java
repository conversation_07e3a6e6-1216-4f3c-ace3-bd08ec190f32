package com.swcares.eterm.crs.obj.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-09 13:58:45
 */
@Data
public class SkDto {
    /**
     * 指令标识 SK/DS
     */
    private String cmdParam;

    /**
     * 选项 PAE 默认P
     */
    private String option = "P";

    /**
     * 城市对
     */
    private String cityPair;

    /**
     * 出发城市
     */
    private List<String> orgIdList;

    /**
     * 到达城市
     */
    private List<String> dstIdList;

    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 航班日期的前三天
     */
    private String beforeFltDate;
    /**
     * 航班日期的前三天
     */
    private String afterFltDate;
    /**
     * 航班日期的前三天
     */
    private String beforeFltDateCom;
    /**
     * 航班日期的前三天
     */
    private String afterFltDateCom;
    /**
     * 起飞时间
     */
    private String offTime;

    /**
     * 航司
     */
    private String airlineCode;

    /**
     * 经停标识  D N
     */
    private String stop;
    /**
     * 经停/直达标识
     * D 直达 显示DIRECT ONLY
     * N 无经停航班 显示 NON-STOPS ONLY
     */
    private String stopDetail;
    /**
     * 舱位 座位等级
     */
    private String seatLevel;

}
