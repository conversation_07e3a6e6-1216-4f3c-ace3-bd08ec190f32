/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.crs.obj.dto;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DsgSectionDto {
    private String tcardId;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 起始站
     */
    private String org;
    /**
     * 到达站
     */
    private String dst;
    /**
     * 起飞时间
     */
    private String estimateOff;
    /**
     * 跨天
     */
    private String estimateOffChange;
    /**
     * 到达时间
     */
    private String estimateArr;
    /**
     * 到达跨天
     */
    private String estimateArrChange;
    /**
     * 机型
     */
    private String planeModelType;
    /**
     * 航节号
     */
    private String sectionNo;
    /**
     * 是否是尾航节
     */
    private String isLastSection;

    private String mealCode;
    private String planSectionId;
}
