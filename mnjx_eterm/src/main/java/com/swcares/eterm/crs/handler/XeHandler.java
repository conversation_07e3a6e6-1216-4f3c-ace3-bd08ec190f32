package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.obj.dto.XeDto;
import com.swcares.eterm.crs.service.IXeService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "XE", shorthand = true)
public class XeHandler implements Handler {

    @Resource
    private IXeService iXeService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        XeDto xeDto = iXeService.parseXe(cmd);
        String backStr = iXeService.handleXe(xeDto);
        return StrUtil.isEmpty(backStr)?iXeService.recall():backStr;
    }
}
