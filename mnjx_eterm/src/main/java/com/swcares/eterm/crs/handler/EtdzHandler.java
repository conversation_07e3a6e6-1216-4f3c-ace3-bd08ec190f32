package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ListUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxAirline;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IEtdzService;
import com.swcares.obj.dto.EtdzDto;
import com.swcares.obj.vo.EtdzVo;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 指令ETDZ验证
 *
 * <AUTHOR>
 *  2022-07-05 14:29:25
 */
@Slf4j
@OperateType(action = "ETDZ", template = "/crs/etdz.jf", shorthand = true)
public class EtdzHandler implements Handler {

    /**
     * ETDZ：打票机序号 [/旅客序号] [,出票航司][,出票选项]
     * 0) ETDZ:1    使用1号打票机打印所有旅客的票
     * 1) ETDZ:1/P1,INF    仅打印第一名旅客携带的婴儿客票
     * 2) ETDZ:1/P1,ADL    仅打印第一名旅客的客票
     * 3) ETDZ:1/P1,CA,ADL 仅打印第一名旅客的客票,以国航为结算出票
     * 4) ETDZ:1/P1,CA,INF 仅打印第一名旅客的携带的婴儿客票,且以国航为结算出票
     * 5) ETDZ:1/P1-P5,CA,INF 打印第一名至第五名旅客的携带的婴儿客票,且以国航为结算出票
     * 6) ETDZ:1/P1-P5,ADL 打印第一名至第五名旅客的客票
     * 7) ETDZ:1,INF 打印该PNR中所有没有出票的婴儿客票
     * 8) ETDZ:1,CA,ADL 打印该PNR中所有没有出票的成人客票,以国航为结算出票
     */
    private static final Pattern ETDZ_PATTERN = Pattern.compile("ETDZ:(\\w{1,3})(/(([Pp](\\d*))(-([Pp](\\d*)))?))?([,\\w]*)");

    @Resource
    private IEtdzService iEtdzService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        long startMillis1 = System.currentTimeMillis();
        //参数解析
        EtdzDto etdzDto = this.parseEtdz(cmd);
        // 要返回的结果容器
        EtdzVo etdzVo = iEtdzService.handle(etdzDto);
        long endMillis2 = System.currentTimeMillis();
        log.info(StrUtil.format("MemoryDataId[{}], 执行完整个ETDZ出票返回数据到handle, 花费时间：{} 毫秒", MemoryDataUtils.getMemoryData().getMemoryDataId(), endMillis2 - startMillis1));

        unifiedResult.setResults(ListUtils.toList(etdzVo).toArray());
        return unifiedResult;
    }

    /**
     * 参数解析
     *
     * @param cmd 指令
     * @return 参数对象
     * @throws UnifiedResultException 统一异常
     */
    private EtdzDto parseEtdz(String cmd) throws UnifiedResultException {
        if (!ReUtil.isMatch(ETDZ_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> allGroups = ReUtil.getAllGroups(ETDZ_PATTERN, cmd, false);
        String printNo = allGroups.get(0);
        String psgNo1 = allGroups.get(3);
        String psgNo2 = allGroups.get(6);
        String airlineCode = StrUtil.EMPTY;
        String option = StrUtil.EMPTY;
        //航司和出票选项处理
        String temp = allGroups.get(8).replaceFirst(StrUtil.COMMA, StrUtil.EMPTY);
        if (StrUtil.isNotEmpty(temp)) {
            List<String> split = StrUtils.split(temp, StrUtils.COMMA);
            for (String s : split) {
                switch (s.length()) {
                    case Constant.TWO:
                        airlineCode = s;
                        break;
                    case Constant.THREE:
                        option = s;
                        break;
                    default:
                        throw new UnifiedResultException(Constant.FORMAT);
                }
            }
            if (StrUtil.isNotEmpty(airlineCode)) {
                //查询航司是否存在
                MnjxAirline mnjxAirline = iEtdzService.retrieveAirline(airlineCode);
                if (ObjectUtil.isEmpty(mnjxAirline)) {
                    throw new UnifiedResultException(Constant.AIRLINE);
                }
            }
            if (StrUtil.isNotEmpty(option) && !StrUtil.equalsAny(option, Constant.ADL, Constant.INF)) {
                throw new UnifiedResultException(Constant.ETDZ_OPTION);
            }
        }
        EtdzDto etdzDto = new EtdzDto();
        etdzDto.setPrintNo(printNo);
        etdzDto.setPsgNoStart(psgNo1);
        etdzDto.setPsgNoEnd(psgNo2);
        etdzDto.setAirlineCode(airlineCode);
        etdzDto.setOption(option);
        etdzDto.setCmd(cmd);
        return etdzDto;
    }
}
