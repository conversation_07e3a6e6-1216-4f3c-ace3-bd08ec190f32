package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.obj.dto.RmkDto;
import com.swcares.eterm.crs.service.IRmkService;

import javax.annotation.Resource;

/**
 * 处理CT指令
 * 手工加入备注组的格式
 * >RMK：自由格式文本 / 旅客标识
 *
 * <AUTHOR>
 */
@OperateType(action = "RMK", shorthand = true)
public class RmkHandler implements Handler {

    @Resource
    private IRmkService iRmkService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        //参数解析
        RmkDto rmkDto = iRmkService.parseRmk(cmd);
        //业务处理
        iRmkService.handle(rmkDto);
        //数据返回
        return iRmkService.recall();
    }
}
