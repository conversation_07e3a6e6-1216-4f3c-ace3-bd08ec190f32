package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxPnrSeg;
import com.swcares.eterm.crs.service.IEsService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.AbstractPnrDto;
import com.swcares.obj.dto.PnrSegDto;
import com.swcares.obj.dto.PnrSsrDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/17 14:09
 */
@Slf4j
@Service
public class EsServiceImpl implements IEsService {

    @Resource
    private IPnrManageService iPnrManageService;

    @Override
    public void handleEs() throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
        MemoryDataPnr tmpMemoryDataPnr = memoryData.getTmpMemoryDataPnr();
        // 内存中没有同时存在两份pnr，报错NO PNR
        if (ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr()) || ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr().getPnrId()) || ObjectUtil.isEmpty(tmpMemoryDataPnr.getMnjxPnr()) || ObjectUtil.isEmpty(tmpMemoryDataPnr.getMnjxPnr().getPnrId())) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        // 当两份PNR是通过SP指令构成的，报错
        if (StrUtil.isNotEmpty(memoryDataPnr.getMnjxPnr().getPnrCrs()) && StrUtil.isEmpty(tmpMemoryDataPnr.getMnjxPnr().getPnrCrs())) {
            throw new UnifiedResultException(Constant.NEED_EOT);
        }
        this.validateEs(tmpMemoryDataPnr, memoryDataPnr);
        this.esPnr(tmpMemoryDataPnr, memoryDataPnr);
    }

    @Override
    public String recall() {
        return iPnrManageService.recall(iPnrManageService.getCurrentControlledPnr());
    }

    private void validateEs(MemoryDataPnr esMemoryDataPnr, MemoryDataPnr newMemoryDataPnr) throws UnifiedResultException {
        // 指定合并的部分没有航段组报错NO SEGMENT
        if (CollUtil.isEmpty(newMemoryDataPnr.getPnrSegDtos()) || newMemoryDataPnr.getPnrSegDtos().stream().allMatch(AbstractPnrDto::isXe)) {
            throw new UnifiedResultException(Constant.NO_SEGMENT);
        }

        // ES的航段组和已有航段组完全相同，报错DUPLICATE SEGMENT
        List<PnrSegDto> esSegDtoList = esMemoryDataPnr.getPnrSegDtos().stream()
                .filter(s -> !s.isXe())
                .collect(Collectors.toList());
        List<PnrSegDto> newSegDtoList = newMemoryDataPnr.getPnrSegDtos().stream()
                .filter(s -> !s.isXe())
                .collect(Collectors.toList());
        List<String> esSegInfoList = esSegDtoList.stream()
                .map(s -> StrUtil.format("{} {} {}{}", s.getMnjxPnrSeg().getFlightNo(), s.getMnjxPnrSeg().getFlightDate(), s.getMnjxPnrSeg().getOrg(), s.getMnjxPnrSeg().getDst()))
                .sorted()
                .collect(Collectors.toList());
        List<String> newSegInfoList = newSegDtoList.stream()
                .map(s -> StrUtil.format("{} {} {}{}", s.getMnjxPnrSeg().getFlightNo(), s.getMnjxPnrSeg().getFlightDate(), s.getMnjxPnrSeg().getOrg(), s.getMnjxPnrSeg().getDst()))
                .sorted()
                .collect(Collectors.toList());
        if (newSegInfoList.stream().anyMatch(esSegInfoList::contains)) {
            throw new UnifiedResultException(Constant.DUP_SEG);
        }

        // ES的航段组座位数和已有航段组座位数不同，报错SEATS
        List<PnrSegDto> noSaEsList = esSegDtoList.stream()
                .filter(s -> !Constant.SA.equals(s.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        List<PnrSegDto> noSaNewList = newSegDtoList.stream()
                .filter(s -> !Constant.SA.equals(s.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(noSaNewList) && noSaEsList.get(0).getMnjxPnrSeg().getSeatNumber().intValue() != noSaNewList.get(0).getMnjxPnrSeg().getSeatNumber().intValue()) {
            throw new UnifiedResultException(Constant.SEATS);
        }

        // ES的航段组和已出票的PNR合并，报错The simulation system does not support
        if (CollUtil.isNotEmpty(esMemoryDataPnr.getPnrNmTnDtos())) {
            throw new UnifiedResultException("The simulation system does not support");
        }
    }

    private void esPnr(MemoryDataPnr esMemoryDataPnr, MemoryDataPnr newMemoryDataPnr) {
        List<PnrSegDto> esSegDtos = esMemoryDataPnr.getPnrSegDtos().stream()
                .filter(s -> !s.isXe())
                .collect(Collectors.toList());
        List<PnrSegDto> newSegDtos = newMemoryDataPnr.getPnrSegDtos().stream()
                .filter(s -> !s.isXe())
                .collect(Collectors.toList());
        // 将新航段的序号都设置成0，用于区别已有的航段组
        newSegDtos.forEach(s -> s.getMnjxPnrSeg().setPnrSegNo(0));
        // 将新航段列表插入到原航段组中，并更新原航段组和关联的PNR项的segNo。
        // i:如果新航段是SA，判断新航段的出发机场和原航段的到达机场：
        // 1.判断新航段的出发机场和原航段的到达机场，如果匹配，插入到原航段的下一个位置；
        // 2.如果全都不匹配，判断新航段的到达机场和原第一个航段的出发机场，如果匹配，该SA插入到第一个位置；如果不匹配，插入到最后一个位置。
        // ii：如果新航段不是SA，判断起飞日期时间，按时间先后插入。如果日期时间完全相等，插入到下一个位置。
        for (PnrSegDto newSegDto : newSegDtos) {
            MnjxPnrSeg newSeg = newSegDto.getMnjxPnrSeg();
            newSeg.setPnrId(esMemoryDataPnr.getMnjxPnr().getPnrId());
            Iterator<PnrSegDto> iterator = esSegDtos.iterator();
            int esIndex = 0;
            boolean isInsert = false;
            while (iterator.hasNext()) {
                esIndex++;
                PnrSegDto esSegDto = iterator.next();
                MnjxPnrSeg esSeg = esSegDto.getMnjxPnrSeg();
                if (Constant.SA.equals(newSeg.getPnrSegType())) {
                    String dst = esSeg.getDst();
                    String org = newSeg.getOrg();
                    if (dst.equals(org)) {
                        esSegDtos.add(esIndex, newSegDto);
                        isInsert = true;
                        break;
                    }
                } else {
                    if (Constant.SA.equals(esSeg.getPnrSegType())) {
                        continue;
                    }
                    Date thisDate = DateUtil.parseDateTime(StrUtil.format("{} {}:{}:00", esSeg.getFlightDate(), esSeg.getEstimateOff().substring(0, 2), esSeg.getEstimateOff().substring(2)));
                    Date newDate = DateUtil.parseDateTime(StrUtil.format("{} {}:{}:00", newSeg.getFlightDate(), newSeg.getEstimateOff().substring(0, 2), newSeg.getEstimateOff().substring(2)));
                    if (newDate.compareTo(thisDate) >= 0) {
                        esSegDtos.add(esIndex, newSegDto);
                    } else {
                        esSegDtos.add(0, newSegDto);
                    }
                    isInsert = true;
                    break;
                }
            }
            if (!isInsert) {
                if (newSeg.getDst().equals(esSegDtos.get(0).getMnjxPnrSeg().getOrg())) {
                    esSegDtos.add(0, newSegDto);
                } else {
                    esSegDtos.add(esSegDtos.size(), newSegDto);
                }
            }
        }
        // 对关联的航段组序号的其他PNR项重新设置新序号
        int newSegNo = 1;
        int pnrIndex = esSegDtos.stream()
                .filter(s -> s.getMnjxPnrSeg().getPnrSegNo() != 0)
                .sorted(Comparator.comparing(PnrSegDto::getPnrIndex))
                .collect(Collectors.toList())
                .get(0)
                .getPnrIndex();
        for (PnrSegDto pnrSegDto : esSegDtos) {
            MnjxPnrSeg pnrSeg = pnrSegDto.getMnjxPnrSeg();
            int originalSegNo = pnrSeg.getPnrSegNo();
            List<PnrSsrDto> ssrDtoList = esMemoryDataPnr.getPnrNmDtos().stream()
                    .filter(n -> !n.isXe() && StrUtil.isEmpty(n.getUpdateMark()))
                    .flatMap(n -> n.getPnrSsrDtos().stream())
                    .filter(s -> !s.isXe() && ObjectUtil.isNotEmpty(s.getMnjxNmSsr().getPnrSegNo()) && s.getMnjxNmSsr().getPnrSegNo() == originalSegNo && !s.isChange())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ssrDtoList)) {
                int finalNewSegNo = newSegNo;
                ssrDtoList.forEach(s -> {
                    s.getMnjxNmSsr().setPnrSegNo(finalNewSegNo);
                    s.setChange(true);
                });
            }
            pnrSeg.setPnrSegNo(newSegNo);
            newSegNo++;
            pnrSegDto.setChange(true);
            pnrSegDto.setPnrIndex(pnrIndex);
            pnrSeg.setPnrIndex(pnrIndex);
            pnrIndex++;
        }
        esMemoryDataPnr.getMnjxPnr().setMaxIndex(esMemoryDataPnr.getMnjxPnr().getMaxIndex() + newSegDtos.size());
        esMemoryDataPnr.setPnrSegDtos(esSegDtos);
        newMemoryDataPnr.clearPnr();
    }
}
