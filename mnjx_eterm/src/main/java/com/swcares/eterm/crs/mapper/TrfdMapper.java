package com.swcares.eterm.crs.mapper;

import com.swcares.eterm.crs.obj.dto.TrfdZdto;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface TrfdMapper {

    /**
     * 获取pnrId
     *
     * @param pnrNmId 旅客id
     * @param nmXnId  婴儿id
     * @return 获取pnrId
     */
    String retrievePnrId(@Param("pnrNmId") String pnrNmId, @Param("nmXnId") String nmXnId);


    /**
     * 获取退票旅客信息
     *
     * @param pnrNmId 旅客id
     * @param nmXnId  婴儿id
     * @return 获取退票旅客信息
     */
    TrfdZdto retrievePsgInfo(@Param("pnrNmId") String pnrNmId, @Param("nmXnId") String nmXnId);
}
