package com.swcares.eterm.crs.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-09 16:17:07
 */
public interface SkMapper {

    /**
     * 查询mnjx_tcard_section表筛选 符合条件的tcard id，出发机场列表和到达机场列表的所有组合
     *
     * @param orgIdList 出发航站
     * @param dstIdList 到达航站
     * @return 出发机场列表和到达机场列表的所有组合
     */
    List<String> retrieveTcardIdsByCityPair(@Param("orgIdList") List<String> orgIdList, @Param("dstIdList") List<String> dstIdList);
}
