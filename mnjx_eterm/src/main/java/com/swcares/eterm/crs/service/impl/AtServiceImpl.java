package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.crs.service.IAtService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.PnrSegDto;
import com.swcares.service.IPnrCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 功能：@指令处理service
 *
 * <AUTHOR>
 * @date 2022/6/15
 */
@Service
@Slf4j
public class AtServiceImpl implements IAtService {

    private static final Pattern AT_PATTERN = Pattern.compile("[@\\\\](:)?([A-Z]+)?");

    @Resource
    private IPnrManageService iPnrManageService;

    @Resource
    private IPnrCommandService iPnrCommandService;

    @Override
    public void parseAt(String cmd) throws UnifiedResultException {
        // 检查指令输入
        if (ReUtil.isMatch(AT_PATTERN, cmd)) {
            List<String> allGroups = ReUtil.getAllGroups(AT_PATTERN, cmd);
            String option = allGroups.get(2);
            // 如果输入了参数，则参数必须为K。I暂时不实现
            if (StrUtil.isNotEmpty(option) && !option.equals(Constant.AT_K)) {
                throw new UnifiedResultException(Constant.OPTION);
            }
            MemoryData memoryData = MemoryDataUtils.getMemoryData();
            MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
            if (Constant.AT_K.equals(option)) {
                memoryDataPnr.setByAtK(true);
            } else {
                memoryDataPnr.setByAtK(false);
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }

    @Override
    public void handleAt(String cmd) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataPnr controlledPnr = iPnrManageService.getCurrentControlledPnr();
        MnjxSi mnjxSi = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi();
        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();

        // 判断另外一份PNR，如果是SP，当前被控PNR是tmp，没有PNR编码，需要对两份PNR都执行封口。ES的当前被控PNR是tmp，存在PNR编码，只对当前被控的PNR进行封口
        MemoryDataPnr tmpMemoryDataPnr = memoryData.getTmpMemoryDataPnr();
        MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
        if (StrUtil.isEmpty(tmpMemoryDataPnr.getMnjxPnr().getPnrCrs()) && controlledPnr.equals(tmpMemoryDataPnr)) {
            iPnrCommandService.at(memoryDataPnr, mnjxOffice, mnjxSi);
        }
        // 封口当前被控PNR
        iPnrCommandService.at(controlledPnr, mnjxOffice, mnjxSi);
    }

    @Override
    public UnifiedResult recall() {
        UnifiedResult unifiedResult = new UnifiedResult();
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        String crs = mnjxPnr.getPnrCrs();
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        pnrSegDtos = pnrSegDtos.stream()
                .filter(s -> !"SA".equals(s.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        List<String> segInfos = new ArrayList<>();
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            if (!pnrSegDto.isXe()) {
                String flightDate = mnjxPnrSeg.getFlightDate();
                String week = DateUtils.ymd2WeekEn(flightDate).substring(0, 2);
                flightDate = DateUtils.ymd2Com(flightDate);
                String segInfo = StrUtil.format("{}{}  {} {}{}  {}{} {}{}   {} {}", StrUtil.isNotEmpty(pnrSegDto.getCarrierFlight()) ? "*" : " ", mnjxPnrSeg.getFlightNo(), mnjxPnrSeg.getSellCabin(), week, flightDate,
                        mnjxPnrSeg.getOrg(), mnjxPnrSeg.getDst(), mnjxPnrSeg.getActionCode(), mnjxPnrSeg.getSeatNumber(), mnjxPnrSeg.getEstimateOff(), mnjxPnrSeg.getEstimateArr());
                segInfos.add(segInfo);
            }
        }
        unifiedResult.setResults(new Object[]{crs, segInfos});
        // 回显对象构建完后清空内存PNR。@k不清空
        if (!memoryDataPnr.isByAtK()) {
            MemoryData memoryData = MemoryDataUtils.getMemoryData();
            memoryData.getMemoryDataPnr().clearPnr();
            memoryData.getTmpMemoryDataPnr().clearPnr();
        }
        return unifiedResult;
    }
}
