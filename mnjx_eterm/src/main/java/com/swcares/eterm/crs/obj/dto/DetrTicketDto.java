package com.swcares.eterm.crs.obj.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022/7/28-16:48
 */
@Data
public class DetrTicketDto {
    /**
     * 票号
     */
    private String ticketNo;
    /**
     * 旅客姓名
     */
    private String adlName;
    /**
     * 婴儿姓名
     */
    private String xnName;
    private String xnBirthday;
    private String psgIndex;
    /**
     * 出发地
     */
    private String org;
    /**
     * 到达地
     */
    private String dst;
    /**
     * 票面状态1
     */
    private String ticketStatus1;
    /**
     * 票面状态1
     */
    private String ticketStatus2;
    /**
     * 旅游代码
     */
    private String tourCode;
    /**
     * 出票航司
     */
    private String issuedAirline;
    /**
     * 出票航司英文名
     */
    private String issuedAirlineEn;

    /**
     * pnrId
     */
    private String pnrId;
    private String pnrNmId;
    private String nmXnId;

    /**
     * 行程票价
     */
    private String fare;
    /**
     * 支付方式
     */
    private String payType;
    /**
     * 票面基础
     */
    private String ticketType;
    /**
     * 基建
     */
    private String taxCn;
    /**
     * 燃油
     */
    private String taxYq;
    /**
     * 总票价
     */
    private String totalPrice;
    private String fcInputValue;

    //*************************************************************************************************************
    /**
     * 参数 F
     */
    /**
     * 成人的身份信息
     */
    private String ssrFoid;
    /**
     * 婴儿的身份信息，年龄
     */
    private String xnInfo;
    //*************************************************************************************************************
    /**
     * 参数H
     */
    /**
     * 出票工作号所属OFFICE的IATA编码
     */
    private String iataOffice;
    /**
     * 出票日期
     */
    private String issuedDate;
    /**
     * 出票时间
     */
    private String issuedTime;
    /**
     * 出票工作号
     */
    private String issuedSiNo;
    /**
     * 出票OFFICE的英文名称
     */
    private String officeEn;
    /**
     * 出票OFFICE号
     */
    private String officeNo;
    /**
     * 出票时用的打票机的序号
     */
    private String printNo;

    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 兼容多航段的情况
     */

    List<DetrflightDto> flightAndFoidList;

    /**
     * 改签信息
     */
    private String oi;

    /**
     * oc
     */
    private String oc;

    /**
     * EI
     */
    private String ei;

    /**
     * 旧票面信息
     */
    private String oldTicketNo;

    private String s1Id;

    private String s2Id;

    private boolean fromEtkd;

    /**
     * 联票
     */
    private String couponTicketNo = StrUtil.EMPTY;
}
