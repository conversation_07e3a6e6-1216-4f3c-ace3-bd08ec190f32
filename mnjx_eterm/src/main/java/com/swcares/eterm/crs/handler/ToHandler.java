package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IToService;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * DI指令验证
 * 指令格式
 * TO:打票机序号
 *
 * <AUTHOR>
 */
@OperateType(action = "TO", shorthand = true, predicate = false, template = "/crs/TO.jf")
public class ToHandler implements Handler {

    private static final String REG_FMT_TO = "TO[:|\\s](\\d{1,3})";

    @Resource
    private IToService iToService;

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        // 1、指令格式验证
        if (!ReUtil.isMatch(REG_FMT_TO, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 2、获取指令传输对象
        unifiedResult.setArgs(ReUtil.getAllGroups(Pattern.compile(REG_FMT_TO), cmd).toArray());
        // 3、查询业务数据
        iToService.handle(unifiedResult);
        return unifiedResult;

    }
}
