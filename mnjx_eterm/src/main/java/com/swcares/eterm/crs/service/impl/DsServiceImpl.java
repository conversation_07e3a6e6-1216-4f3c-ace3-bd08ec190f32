package com.swcares.eterm.crs.service.impl;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.crs.obj.vo.SkVo;
import com.swcares.eterm.crs.obj.dto.SkDto;
import com.swcares.eterm.crs.service.IDsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 处理SK指令 service 航班时刻显示
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DsServiceImpl implements IDsService {
    @Resource
    private SkServiceImpl skServiceImpl;

    /**
     * 指令解析
     *
     * @param cmd 指令
     * @return 参数对象
     */
    @Override
    public SkDto parseDs(String cmd) throws UnifiedResultException {
        return skServiceImpl.parseSk(cmd);
    }


    /**
     * sk业务处理
     *
     * @param skDto sk查询参数对象
     */
    @Override
    public List<SkVo> handle(SkDto skDto) throws UnifiedResultException {
        return skServiceImpl.handle(skDto);
    }

}
