package com.swcares.eterm.crs.obj.vo;

import com.swcares.core.util.Constant;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/9
 */
@Data
public class DsgSectionVo {

    private String org;

    private String dst;
    /**
     * 机场三字码
     */
    private String airportCode;
    private String offTime;

    private String arrTime;

    private String planeModel;

    private String mealCode;

    /**
     * 经停点数量，当输入参数有城市对或者通过PNR获取才会统计
     */
    private int stopPoint = 0;

    /**
     * 备注，当输入参数有城市对或者通过PNR获取才会显示，当前需求始终设置为0
     */
    private String remark = Constant.STR_ZERO;

    /**
     * 中转经停时间（分钟）
     */
    private String stopTime;

    /**
     * 总飞行时间，只显示在最后一个航站信息
     */
    private String totalFlyTime;

    /**
     * 总飞行距离，只显示在最后一个航段信息
     */
    private String totalFlyDistance="0";
}
