package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxPnr;
import com.swcares.eterm.base.handler.Handler;
import lombok.extern.slf4j.Slf4j;

/**
 * 处理IG指令
 *
 * <AUTHOR>
 */
@OperateType(action = "IG")
@Slf4j
public class IgHandler implements Handler {

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        memoryData.getMemoryDataHandlerResult().getPnrRecallRecords().clear();
        // 正常控制的PNR
        MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
        // 合并或分离的临时PNR
        MemoryDataPnr tmpMemoryDataPnr = memoryData.getTmpMemoryDataPnr();
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        MnjxPnr tmpPnr = tmpMemoryDataPnr.getMnjxPnr();
        // 临时PNR为空的时候，释放正常控制的PNR
        if (ObjectUtil.isEmpty(tmpPnr.getPnrId())) {
            if (ObjectUtil.isEmpty(mnjxPnr.getPnrId())) {
                return Constant.NO_PNR;
            } else {
                if (memoryDataPnr.isPnrChanged() && StrUtil.isNotEmpty(mnjxPnr.getPnrCrs())) {
                    String pnrCrs = mnjxPnr.getPnrCrs();
                    memoryDataPnr.clearPnr();
                    return StrUtil.format("\u0010RT:{} IGNORED", pnrCrs);
                } else {
                    memoryDataPnr.clearPnr();
                    return "";
                }
            }
        }
        // 先释放临时PNR
        else {
            tmpMemoryDataPnr.clearPnr();
            memoryDataPnr.clearPnr();
            return "";
        }
    }
}
