package com.swcares.eterm.crs.handler;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IFdService;

import javax.annotation.Resource;

/**
 * 处理FD指令
 *
 * <AUTHOR>
 */
@OperateType(action = "FD", template = "/crs/fd.jf", fullScreen = true)
public class FdHandler implements Handler {

    @Resource
    public IFdService iFdService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        UnifiedResult unifiedResult = new UnifiedResult();
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        unifiedResult.setResults(ListUtils.toList(iFdService.handle(memoryData, cmd)).toArray());
        return unifiedResult;
    }
}
