package com.swcares.eterm.crs.mapper;

import com.swcares.entity.MnjxNmSsr;
import com.swcares.entity.MnjxPnr;
import com.swcares.eterm.crs.obj.dto.DetrTicketDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2022-07-29 16:06:20
 */
public interface DetrMapper {


    /**
     * 查询票价相关的信息
     *
     * @param ticketNo 票号
     * @return 票价相关信息
     */
    DetrTicketDto retrieveTicketInfo(@Param("ticketNo") String ticketNo);


    /**
     * 查询出票相关的工作号信息
     *
     * @param issuedSiId 工作号id
     * @return 返回工作号，部门号相关信息
     */
    DetrTicketDto retrieveSiInfo(@Param("issuedSiId") String issuedSiId);


    /**
     * 返回结果
     *
     * @param pnrIds   pnr的id列表
     * @param nmIdList 成人旅客id 或者pnrId
     * @param param    参数项
     * @return 查询旅客姓名，身份信息，出票信息
     */
    List<DetrTicketDto> retrieveSsrAndTicket(@Param("pnrIds") List<String> pnrIds, @Param("nmIdList") List<String> nmIdList, @Param("param") String param);

    /**
     * 查询免费行李重量
     *
     * @param sellCabin  销售舱位
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return 舱位和免费行李重量
     */
    String retrieveCnd(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin);

    /**
     * 通过ssr项提取所有的个人头上的SSR项
     *
     * @param ssrInfo ssr项
     * @return ssr项
     */
    List<MnjxNmSsr> retrieveMnjxPnrSsr(@Param("ssrInfo") String ssrInfo);

    MnjxPnr retrievePnrDateById(@Param("pnrId") String pnrId,@Param("startDate")  String startDate,@Param("endDate") String endDate);
}
