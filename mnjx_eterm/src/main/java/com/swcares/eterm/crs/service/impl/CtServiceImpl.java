package com.swcares.eterm.crs.service.impl;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import com.swcares.eterm.crs.service.ICtService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.CtDto;
import com.swcares.service.IPnrCommandService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class CtServiceImpl implements ICtService {

    @Resource
    private IPnrManageService iPnrManageService;

    @Resource
    private IPnrCommandService iPnrCommandService;

    @Override
    public void handle(CtDto ctDto) throws UnifiedResultException {
        iPnrManageService.validateBeforeEs();
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();
        MnjxOffice mnjxOffice = MemoryDataUtils.getMemoryData().getMnjxOffice();
        MnjxSi mnjxSi = MemoryDataUtils.getMemoryData().getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi();
        iPnrCommandService.ct(memoryDataPnr, ctDto, mnjxOffice, mnjxSi);
    }
}
