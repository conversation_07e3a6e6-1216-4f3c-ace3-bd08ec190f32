package com.swcares.eterm.crs.obj.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 上票，卸票类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumTnType {
    /**
     * 上票
     */
    X("X"),
    /**
     * 卸票
     */
    D("D");

    @Getter
    private final String describe;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param describe 中文描述
     * @return 具体的枚举类型
     */
    public static EnumTnType of(String describe) {
        return Arrays.stream(EnumTnType.values()).filter(enumTnType -> enumTnType.getDescribe().equalsIgnoreCase(describe)).findAny().orElse(null);
    }
}
