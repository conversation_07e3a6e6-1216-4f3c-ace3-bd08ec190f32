package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.ISsrService;
import com.swcares.obj.dto.SsrDto;

import javax.annotation.Resource;

/**
 * 处理SSR指令
 *
 * <AUTHOR>
 */
@OperateType(action = "SSR")
public class SsrHandler implements Handler {

    @Resource
    private ISsrService iSsrService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        SsrDto ssrDto = iSsrService.parseSsr(cmd);
        iSsrService.handleSsr(ssrDto);
        return iSsrService.recall();
    }
}
