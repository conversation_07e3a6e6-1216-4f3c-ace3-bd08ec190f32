package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxNmFp;
import com.swcares.entity.MnjxPnrFp;
import com.swcares.entity.MnjxPnrNm;
import com.swcares.eterm.crs.obj.dto.FpDto;
import com.swcares.eterm.crs.service.IFpService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.PnrFpDto;
import com.swcares.obj.dto.PnrNmDto;
import com.swcares.obj.dto.PnrNmFpDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/14
 */
@Slf4j
@Service
public class FpServiceImpl implements IFpService {

    @Resource
    private IPnrManageService iPnrManageService;

    /**
     * FP:CASH,CNY
     * FP:CASH, CNY
     * FP:CASH,CNY/P1
     * FP:IN/CASH,CNY
     * FP:IN/CASH,CNY/P1
     */
    private static final String REG = "FP[:|\\s](/?IN/)?([A-Z]{4}),\\s*([A-Z]{3})(/P([0-9]{1,2}))?";

    /**
     * CASH
     */
    private static final String REG_PAY = "CASH";

    /**
     * CNY
     */
    private static final String REG_CURRENCY = "CNY";

    @Override
    public String handle(FpDto fpDto) throws UnifiedResultException {
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();
        // 果当前无活动PNR,则新建生成一个空白PNR
        if (ObjectUtils.isNull(memoryDataPnr) || ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            iPnrManageService.createNewPnr();
        }

        // 是否是婴儿 1婴儿 0成人
        Integer isBaby = fpDto.isIn() ? 1 : 0;
        if (StrUtil.isNotEmpty(fpDto.getPId())) {
            int psgId = Integer.parseInt(fpDto.getPId());
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getPnrIndex() == psgId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrNmDtos)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            // 什么鬼
            PnrNmDto pnrNmDto = pnrNmDtos.get(0);
            List<PnrNmFpDto> pnrNmFpDtos = pnrNmDto.getPnrNmFpDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxNmFp().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmFpDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrFp().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrFpDtos)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
            PnrNmFpDto pnrNmFpDto = this.buildPnrNmFp(mnjxPnrNm.getPnrNmId(), fpDto);
            pnrNmDto.getPnrNmFpDtos().add(pnrNmFpDto);
        } else {
            List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrFp().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrFpDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            List<PnrNmDto> pnrNmList = memoryDataPnr.getPnrNmDtos().stream()
                    .filter(k -> !k.isXe())
                    .collect(Collectors.toList());
            for (PnrNmDto pnrNmDto : pnrNmList) {
                List<PnrNmFpDto> pnrNmFpDtos = pnrNmDto.getPnrNmFpDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> k.getMnjxNmFp().getIsBaby().equals(isBaby))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(pnrNmFpDtos)) {
                    throw new UnifiedResultException(Constant.PSGR_ID);
                }
            }
            PnrFpDto pnrFpDto = this.buildPnrFp(memoryDataPnr, fpDto);
            memoryDataPnr.getPnrFpDtos().add(pnrFpDto);
        }
        return iPnrManageService.recall(memoryDataPnr);
    }

    @Override
    public FpDto parseArgs(String cmd) throws UnifiedResultException {
        FpDto fpDto = new FpDto();
        // 是否有婴儿
        boolean isIn = false;
        // 支付方式
        String payType;
        // 货币代码
        String currencyCode;
        // 旅客编号
        String pId;
        if (cmd.matches(REG)) {
            List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG), cmd);
            if (StrUtil.isNotEmpty(groups.get(1))) {
                isIn = true;
            }
            payType = groups.get(2);
            currencyCode = groups.get(3);
            pId = groups.get(5);
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 支付方式为必填项，目前仅支持CASH
        if (!payType.matches(REG_PAY)) {
            throw new UnifiedResultException(Constant.CODE);
        }
        // 货币代码为必填项，目前仅支持CNY
        if (!currencyCode.matches(REG_CURRENCY)) {
            throw new UnifiedResultException(Constant.CURRENCY);
        }
        fpDto.setIn(isIn);
        fpDto.setPayType(payType);
        fpDto.setCurrencyCode(currencyCode);
        fpDto.setPId(pId);
        return fpDto;
    }

    /**
     * 构建PnrFp
     *
     * @param memoryDataPnr 内存对象
     * @param fpDto         参数对象
     * @return 返回对象
     */
    private PnrFpDto buildPnrFp(MemoryDataPnr memoryDataPnr, FpDto fpDto) {
        MnjxPnrFp mnjxPnrFp = new MnjxPnrFp();
        mnjxPnrFp.setPnrId(memoryDataPnr.getMnjxPnr().getPnrId());
        mnjxPnrFp.setIsBaby(fpDto.isIn() ? 1 : 0);
        mnjxPnrFp.setPayType(fpDto.getPayType());
        mnjxPnrFp.setCurrencyType(fpDto.getCurrencyCode());
        String content = StrUtils.format("{},{}", fpDto.getPayType(), fpDto.getCurrencyCode());
        if (fpDto.isIn()) {
            content = StrUtils.addPrefixIfNot(content, "FP/IN/");
        } else {
            content = StrUtils.addPrefixIfNot(content, "FP/");
        }
        mnjxPnrFp.setInputValue(content);
        return new PnrFpDto(mnjxPnrFp);
    }


    /**
     * 构建 pnrNmFp
     * @param nmId NmId
     * @param fpDto fp
     * @return PnrNmFpDto
     */
    private PnrNmFpDto buildPnrNmFp(String nmId, FpDto fpDto) {
        MnjxNmFp mnjxNmFp = new MnjxNmFp();
        mnjxNmFp.setPnrNmId(nmId);
        mnjxNmFp.setIsBaby(fpDto.isIn() ? 1 : 0);
        mnjxNmFp.setPayType(fpDto.getPayType());
        mnjxNmFp.setCurrencyType(fpDto.getCurrencyCode());
        String content = StrUtils.format("{},{}", fpDto.getPayType(), fpDto.getCurrencyCode());
        content = StrUtils.addPrefixByCond(fpDto.isIn(), content, "IN/");
        mnjxNmFp.setInputValue(StrUtils.format("FP/{}/P{}", content, fpDto.getPId()));
        return new PnrNmFpDto(mnjxNmFp);
    }
}
