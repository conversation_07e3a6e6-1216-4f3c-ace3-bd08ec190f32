package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.crs.mapper.MentMapper;
import com.swcares.eterm.crs.service.IMentService;
import com.swcares.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.eterm.crs.service.impl.MentServiceImpl <br>
 * description：MentServiceImpl <br>
 *
 * <AUTHOR> <br>
 * date 2022/01/13 <br>
 * @version v1.0 <br>
 */
@Service
public class MentServiceImpl implements IMentService {

    @Resource
    private MentMapper mentMapper;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxMentService iMnjxMentService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxNmService;

    @Resource
    private IMnjxNmFnService iMnjxFnService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxNmSsrService iMnjxSsrService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxAirportService iMnjxAirportcodeService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxCndService mnjxCndService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    private static final Pattern REG_FOID = Pattern.compile("SSR\\sFOID\\s[A-Z]{2}\\s\\w{3}\\s([\\w]+)/\\w{2,}");

    @Override
    public List<Map<String, Object>> dealCmd(String crsPnr, String ticketNo) throws UnifiedResultException {
        List<Map<String, Object>> list = new ArrayList<>();
        if (StrUtil.isBlank(crsPnr)) {
            //票号不存在
            MnjxPnrNmTicket ticket = iMnjxPnrNmTicketService.lambdaQuery().eq(MnjxPnrNmTicket::getTicketNo, ticketNo).one();
            if (ObjectUtil.isNull(ticket)) {
                throw new UnifiedResultException(Constant.TKT_NOT_FUND);
            }
            List<MnjxPnrNmTicket> allTickets;
            MnjxPnrNm nm = null;
            MnjxNmXn nmXn = null;
            //查出所有的票
            if (Constant.TICKET_STATUS_EXCHANGED.equals(ticket.getTicketStatus1())) {
                StringBuffer ticketNoBf = new StringBuffer(ticketNo);
                BigDecimal decTicket = new BigDecimal(ticketNo);
                StringBuffer insertBf = ticketNoBf.insert(3, "-");
                MnjxPnrRecord recordTicket = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrType, "TN").eq(MnjxPnrRecord::getChangeMark, "X")
                        .like(MnjxPnrRecord::getInputValue, insertBf.toString()).one();
                StringBuffer insertSubBf;
                if (ObjectUtil.isNull(recordTicket)) {
                    BigDecimal subtract = decTicket.subtract(new BigDecimal("1"));
                    insertSubBf = new StringBuffer(subtract.toString()).insert(3, "-");
                    recordTicket = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrType, "TN").eq(MnjxPnrRecord::getChangeMark, "X")
                            .like(MnjxPnrRecord::getInputValue, insertSubBf.toString()).one();
                }
                if (recordTicket.getInputValue().contains(Constant.ID_CARD)) {
                    throw new UnifiedResultException(Constant.TICKET_NUMBER + "," + Constant.INF);
                }
                nm = iMnjxNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, recordTicket.getPnrNmId()).one();
                List<MnjxPnrNmTn> nmTns = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getPnrNmId, recordTicket.getPnrNmId()).list();
                List<String> nmTnIds = nmTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                allTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getPnrNmTnId, nmTnIds).list();
                List<MnjxPnrRecord> ticketRecords = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrNmId, recordTicket.getPnrNmId()).eq(MnjxPnrRecord::getChangeMark, "X")
                        .eq(MnjxPnrRecord::getPnrType, "TN").list();
                if (CollUtil.isNotEmpty(ticketRecords)) {
                    List<String> recordTicketNos = new ArrayList<>();
                    for (MnjxPnrRecord mnjxPnrRecord : ticketRecords) {
                        String recordTicketNo = mnjxPnrRecord.getInputValue().substring(mnjxPnrRecord.getInputValue().indexOf("/") + 1, mnjxPnrRecord.getInputValue().lastIndexOf("/")).replaceAll("-", "");
                        recordTicketNos.add(recordTicketNo);
                    }
                    List<MnjxPnrNmTicket> recordTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getTicketNo, recordTicketNos).list();
                    allTickets.addAll(recordTickets);
                    List<MnjxPnrNmTicket> sortTickets = allTickets.stream().sorted(Comparator.comparing(MnjxPnrNmTicket::getTicketNo)).collect(Collectors.toList());
                    int size = allTickets.size() - ticketRecords.size();
                    allTickets.clear();
                    for (int i = 0; i < size; i++) {
                        allTickets.add(sortTickets.get(i));
                    }
                }
            } else {
                List<String> tnIds;
                MnjxPnrNmTn tn = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getTnId, ticket.getPnrNmTnId()).one();
                //客票为婴儿票报错
                if (StrUtil.isNotBlank(tn.getNmXnId())) {
//                    throw new UnifiedResultException(Constant.TICKET_NUMBER + "," + Constant.INF);
                    List<MnjxPnrNmTn> xnTns = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getNmXnId, tn.getNmXnId()).list();
                    tnIds = xnTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                    nmXn = iMnjxNmXnService.lambdaQuery().eq(MnjxNmXn::getNmXnId, tn.getNmXnId()).one();
                } else {
                    List<MnjxPnrNmTn> nmTns = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getPnrNmId, tn.getPnrNmId()).list();
                    tnIds = nmTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                    nm = iMnjxNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, tn.getPnrNmId()).one();
                }
                allTickets = iMnjxPnrNmTicketService.lambdaQuery().in(CollUtil.isNotEmpty(tnIds), MnjxPnrNmTicket::getPnrNmTnId, tnIds).list();
            }
            allTickets = allTickets.stream().sorted(Comparator.comparing(MnjxPnrNmTicket::getTicketNo)).collect(Collectors.toList());
            List<String> allNos = allTickets.stream().map(MnjxPnrNmTicket::getTicketNo).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(allNos)) {
                List<MnjxMent> dbMents = iMnjxMentService.lambdaQuery().in(MnjxMent::getPnrId, allNos).list();
                if (CollUtil.isNotEmpty(dbMents)) {
                    throw new UnifiedResultException(Constant.HAS_PRINTED);
                }
            }
            //判断票是否符合打票条件
            for (MnjxPnrNmTicket mnjxPnrNmTicket : allTickets) {
                if (Constant.TICKET_STATUS_EXCHANGED.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                    StringBuffer ticketNoBf = new StringBuffer(mnjxPnrNmTicket.getTicketNo());
                    BigDecimal decTicket = new BigDecimal(mnjxPnrNmTicket.getTicketNo());
                    StringBuffer insertBf = ticketNoBf.insert(3, "-");
                    MnjxPnrRecord recordTicket = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrType, "TN").eq(MnjxPnrRecord::getChangeMark, "X")
                            .like(MnjxPnrRecord::getInputValue, insertBf.toString()).one();
                    StringBuffer insertSubBf;
                    if (ObjectUtil.isNull(recordTicket)) {
                        BigDecimal subtract = decTicket.subtract(new BigDecimal("1"));
                        insertSubBf = new StringBuffer(subtract.toString()).insert(3, "-");
                        recordTicket = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrType, "TN").eq(MnjxPnrRecord::getChangeMark, "X")
                                .like(MnjxPnrRecord::getInputValue, insertSubBf.toString()).one();
                    }
                    //客票存在未使用状态
                    if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                        if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                DateUtil.offset(DateUtil.parse(recordTicket.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                            throw new UnifiedResultException(Constant.TIME_EXPIRED);
                        }
                    }
                } else {
                    MnjxPnrNmTn ticketTn = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getTnId, mnjxPnrNmTicket.getPnrNmTnId()).one();
                    MnjxPnrNm pnrNm = iMnjxNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, ticketTn.getPnrNmId()).one();
                    //操作人员与客票出票OFFICE号验证
                    MnjxSi si = iMnjxSiService.lambdaQuery().eq(MnjxSi::getSiId, ticketTn.getIssuedSiId()).one();
                    MnjxOffice dbOffice = iMnjxOfficeService.lambdaQuery().eq(MnjxOffice::getOfficeId, si.getOfficeId()).one();
                    MnjxOffice siOffice = MemoryDataUtils.getMemoryData().getMnjxOffice();
                    if (!siOffice.getOfficeNo().equals(dbOffice.getOfficeNo())) {
                        throw new UnifiedResultException(Constant.HAVE_NO_AUTHORITY);
                    }
                    //客票状态为REFUND或SUSPENDED报错
                    if (StrUtil.isNotBlank(mnjxPnrNmTicket.getTicketStatus1()) &&
                            (Constant.TICKET_STATUS_SUSPENDED.equals(mnjxPnrNmTicket.getTicketStatus1()) || Constant.REFUNDED.equals(mnjxPnrNmTicket.getTicketStatus1()))) {
                        throw new UnifiedResultException(Constant.CHECK_TKT_STATUS);
                    }
                    if (StrUtil.isNotBlank(mnjxPnrNmTicket.getTicketStatus2()) &&
                            (Constant.TICKET_STATUS_SUSPENDED.equals(mnjxPnrNmTicket.getTicketStatus2()) || Constant.REFUNDED.equals(mnjxPnrNmTicket.getTicketStatus2()))) {
                        throw new UnifiedResultException(Constant.CHECK_TKT_STATUS);
                    }
                    //客票行程单有效期进行校验
                    if (Constant.TICKET_STATUS_USED_OR_FLOWN.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                        //客票已使用
                        if (StrUtil.isNotBlank(mnjxPnrNmTicket.getTicketStatus2())) {
                            if (Constant.TICKET_STATUS_USED_OR_FLOWN.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                                MnjxPnrSeg twoSeg = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS2Id()).one();
                                MnjxPsgCki twoCki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, pnrNm.getPnrNmId()).eq(MnjxPsgCki::getPnrSegNo, twoSeg.getPnrSegNo()).one();
                                MnjxPsgOperateRecord twoBab = iMnjxPsgOperateRecordService.lambdaQuery()
                                        .eq(MnjxPsgOperateRecord::getPsgCkiId, twoCki.getPsgCkiId())
                                        .eq(MnjxPsgOperateRecord::getOperateType, "BAB")
                                        .orderByDesc(MnjxPsgOperateRecord::getOperateTime)
                                        .list()
                                        .get(0);
                                if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                        DateUtil.offset(twoBab.getOperateTime(), DateField.DAY_OF_YEAR, 7).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                                    throw new UnifiedResultException(Constant.TIME_EXPIRED);
                                }
                            }
                        } else {
                            MnjxPnrSeg oneSeg = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS1Id()).one();
                            MnjxPsgCki oneCki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, pnrNm.getPnrNmId()).eq(MnjxPsgCki::getPnrSegNo, oneSeg.getPnrSegNo()).one();
                            MnjxPsgOperateRecord oneBab = iMnjxPsgOperateRecordService.lambdaQuery().eq(MnjxPsgOperateRecord::getPsgCkiId, oneCki.getPsgCkiId()).eq(MnjxPsgOperateRecord::getOperateType, "BAB").one();
                            if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                    DateUtil.offset(oneBab.getOperateTime(), DateField.DAY_OF_YEAR, 7).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                                throw new UnifiedResultException(Constant.TIME_EXPIRED);
                            }
                        }
                    } else {
                        //客票存在未使用状态
                        if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                            if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                    DateUtil.offset(DateUtil.parse(ticketTn.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                                throw new UnifiedResultException(Constant.TIME_EXPIRED);
                            }
                        }
                        if (StrUtil.isNotBlank(mnjxPnrNmTicket.getTicketStatus2())) {
                            if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(mnjxPnrNmTicket.getTicketStatus2())) {
                                if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                        DateUtil.offset(DateUtil.parse(ticketTn.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                                    throw new UnifiedResultException(Constant.TIME_EXPIRED);
                                }
                            }
                        }
                    }
                }
            }
            List<MnjxPnrSeg> segmentList = new ArrayList<>();
            for (MnjxPnrNmTicket mnjxPnrNmTicket : allTickets) {
                MnjxPnrSeg one = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS1Id()).one();
                if (ObjectUtil.isNull(one)) {
                    MnjxPnrSeg mnjxPnrSeg = new MnjxPnrSeg();
                    mnjxPnrSeg.setPnrSegId(mnjxPnrNmTicket.getS1Id());
                    segmentList.add(mnjxPnrSeg);
                } else {
                    segmentList.add(one);
                }
                if (StrUtil.isNotBlank(mnjxPnrNmTicket.getS2Id())) {
                    MnjxPnrSeg two = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS2Id()).one();
                    if (ObjectUtil.isNull(two)) {
                        MnjxPnrSeg mnjxPnrSeg = new MnjxPnrSeg();
                        mnjxPnrSeg.setPnrSegId(mnjxPnrNmTicket.getS2Id());
                        segmentList.add(mnjxPnrSeg);
                    } else {
                        segmentList.add(two);
                    }
                }
            }
            String psgName;
            if (ObjectUtil.isNotEmpty(nmXn)) {
                //查询婴儿的pnr
                nm = iMnjxNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, nmXn.getPnrNmId()).one();
                psgName = StrUtil.format("{} INF({})", nmXn.getXnCname(), DateUtils.ym2Com(nmXn.getXnBirthday()));
            } else {
                String psgType = nm.getPsgType();
                if (Constant.STR_ONE.equals(psgType)) {
                    psgName = StrUtil.format("{} CHD", nm.getName());
                } else {
                    psgName = nm.getName();
                }
                MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery()
                        .eq(MnjxPnrNmUm::getPnrNmId, nm.getPnrNmId())
                        .one();
                if (ObjectUtil.isNotEmpty(nmUm)) {
                    psgName = StrUtil.format("{}(UM{})", nm.getName(), nmUm.getUmAge());
                }
            }
            MnjxPnr pnr = iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrId, nm.getPnrId()).one();
            String bookOffice = pnr.getCreateOfficeNo();
            String patType = "AD";
            MnjxNmSsr ssr = iMnjxSsrService.lambdaQuery().eq(MnjxNmSsr::getPnrNmId, nm.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "FOID").one();
            String ssrInfo;
            if (ObjectUtil.isNotNull(ssr)) {
                ssrInfo = ssr.getSsrInfo();
            } else {
                ssrInfo = "";
            }
            MnjxOffice office = iMnjxOfficeService.lambdaQuery().eq(MnjxOffice::getOfficeNo, bookOffice).one();
            Map<String, Object> map = new HashMap<>(1024);
            List<MnjxPnrRecord> eiRecords = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrId, pnr.getPnrId()).eq(MnjxPnrRecord::getPnrType, "EI").list();
            if (CollUtil.isNotEmpty(eiRecords)) {
                map.put("eiInfo", eiRecords.get(0).getInputValue());
            } else {
                List<MnjxPnrRecord> nmEiRecords = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrId, pnr.getPnrId()).eq(MnjxPnrRecord::getPnrType, "NM EI").list();
                if (CollUtil.isNotEmpty(nmEiRecords)) {
                    map.put("eiInfo", nmEiRecords.get(0).getInputValue().subSequence(0, nmEiRecords.get(0).getInputValue().indexOf("/")));
                }
            }
            map.put("allTickets", allTickets);
            map.put("psgName", psgName);
            map.put("officeNo", office.getOfficeNo());
            //ssr有可能是空
            String idNumber = StrUtil.EMPTY;
            if (StrUtil.isNotEmpty(ssrInfo) && ReUtil.isMatch(REG_FOID, ssrInfo)) {
                idNumber = ReUtil.get(REG_FOID, ssrInfo, 1);
                if (StrUtil.isNotBlank(idNumber) && idNumber.startsWith("NI")) {
                    if (ObjectUtil.isNotEmpty(nmXn)) {
                        if (idNumber.startsWith("NI")) {
                            idNumber = idNumber.replace("NI", "(INFANT)");
                        }
                    }
                }
            }
            map.put("IDNO", idNumber);
            map.put("pnrICs", pnr.getPnrIcs());
            String maxNum = getMaxNum();
            if (StrUtil.isBlank(maxNum)) {
                map.put("serialNumber", "0000000001");
            } else {
                BigDecimal bigDecimal = new BigDecimal(maxNum);
                String fill = StrUtil.fill(bigDecimal.add(new BigDecimal("1")).toString(), '0', 10, true);
                map.put("serialNumber", fill);
            }
            if (Constant.OFFICE_TYPE_AGENT.equals(office.getOfficeType())) {
                MnjxAgent agent = iMnjxAgentService.lambdaQuery().eq(MnjxAgent::getAgentId, office.getOrgId()).one();
                map.put("agentCode", agent.getAgentIata());
                map.put("issudeBy", agent.getAgentCname());
            } else if (Constant.OFFICE_TYPE_AIRPORT.equals(office.getOfficeType())) {
                MnjxAirport airport = iMnjxAirportcodeService.lambdaQuery().eq(MnjxAirport::getAirportId, office.getOrgId()).one();
                map.put("agentCode", "");
                map.put("issudeBy", airport.getAirportCname());
            } else if (Constant.OFFICE_TYPE_AIRLINE.equals(office.getOfficeType())) {
                MnjxAirline airline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineId, office.getOrgId()).one();
                map.put("agentCode", "");
                map.put("issudeBy", airline.getAirlineFullName());
            }
            map.put("segSize", segmentList.size());
            for (int i = 0; i < segmentList.size(); i++) {
                if (StrUtil.isBlank(segmentList.get(i).getPnrId())) {
                    MnjxPnrRecord segRecord = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getChangeMark, "X").eq(MnjxPnrRecord::getPnrSegId, segmentList.get(i).getPnrSegId())
                            .eq(MnjxPnrRecord::getPnrType, "SEG").one();
                    String[] values = segRecord.getInputValue().trim().split(" ");
                    map.put("fltNo" + i, values[0]);
                    map.put("fltDate" + i, DateUtils.com2ymd(values[2].substring(2)));
                    map.put("time" + i, values[6]);
                    String org = values[3].substring(0, 3);
                    String dst = values[3].substring(3);
                    map.put("dst" + i, dst);
                    MnjxAirport arrAirport = iMnjxAirportcodeService.lambdaQuery()
                            .eq(MnjxAirport::getAirportCode, dst).one();
                    MnjxCity arrCity = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityId, arrAirport.getCityId()).one();
                    map.put("dstName" + i, arrCity.getCityCname());
                    if (i == 0) {
                        map.put("org", org);
                        MnjxAirport offAirport = iMnjxAirportcodeService.lambdaQuery()
                                .eq(MnjxAirport::getAirportCode, org).one();
                        MnjxCity offCity = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityId, offAirport.getCityId()).one();
                        map.put("orgName", offCity.getCityCname());
                    }
                    String cabinClass = getCabinClass(values[0], values[1]);
                    if (StrUtil.isNotBlank(cabinClass)) {
                        map.put("cabinClass" + i, values[1]);
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            map.put("CABINKG" + i, "0K");
                        } else {
                            map.put("CABINKG" + i, getCndCabinWeight(values[0], cabinClass) + "K");
                        }
                        List<MnjxNmSsr> chdList = iMnjxSsrService.lambdaQuery()
                                .eq(MnjxNmSsr::getSsrType, "CHLD")
                                .eq(MnjxNmSsr::getPnrNmId, nm.getPnrNmId())
                                .list();
                        if (CollUtil.isEmpty(chdList)) {
                            map.put("fareBasis" + i, cabinClass + values[1]);
                        } else {
                            map.put("fareBasis" + i, cabinClass + "CH");
                        }
                    }
                } else {
                    if (i == 0) {
                        map.put("org", segmentList.get(i).getOrg());
                        MnjxAirport offAirport = iMnjxAirportcodeService.lambdaQuery()
                                .eq(MnjxAirport::getAirportCode, segmentList.get(i).getOrg()).one();
                        MnjxCity offCity = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityId, offAirport.getCityId()).one();
                        map.put("orgName", offCity.getCityCname());
                    }
                    map.put("dst" + i, segmentList.get(i).getDst());
                    MnjxAirport arrAirport = iMnjxAirportcodeService.lambdaQuery()
                            .eq(MnjxAirport::getAirportCode, segmentList.get(i).getDst()).one();
                    MnjxCity arrCity = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityId, arrAirport.getCityId()).one();
                    map.put("dstName" + i, arrCity.getCityCname());
                    map.put("fltNo" + i, segmentList.get(i).getFlightNo());
                    map.put("fltDate" + i, segmentList.get(i).getFlightDate());
                    List<MnjxPlanSection> planSections = getMatchPlanSections(segmentList.get(i).getFlightNo(), segmentList.get(i).getFlightDate(), segmentList.get(i).getOrg(), segmentList.get(i).getDst());
                    map.put("time" + i, planSections.get(planSections.size() - 1).getEstimateOff());
                    MnjxPsgCki cki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, nm.getPnrNmId()).eq(MnjxPsgCki::getPnrSegNo, segmentList.get(i).getPnrSegNo()).one();
                    map.put("cabinClass" + i, cki.getSellCabin());
                    if (ObjectUtil.isNotEmpty(nmXn)) {
                        map.put("CABINKG" + i, "0K");
                    } else {
                        map.put("CABINKG" + i, getCndCabinWeight(segmentList.get(i).getFlightNo(), cki.getCabinClass()) + "K");
                    }

                    MnjxNmSsr chldSsr = iMnjxSsrService.lambdaQuery().eq(MnjxNmSsr::getSsrType, "CHLD").eq(MnjxNmSsr::getPnrNmId, nm.getPnrNmId())
                            .one();
                    if (ObjectUtil.isNull(chldSsr)) {
                        map.put("fareBasis" + i, cki.getCabinClass() + cki.getSellCabin());
                    } else {
                        map.put("fareBasis" + i, cki.getCabinClass() + "CH");
                    }
                }
            }
            if (allTickets.size() > 1) {
                map.put("ticketNo", allTickets.get(0).getTicketNo() + allTickets.get(allTickets.size() - 1).getTicketNo().substring(allTickets.get(allTickets.size() - 1).getTicketNo().length() - 3));
            } else {
                map.put("ticketNo", ticketNo);
            }
            MnjxNmFn fn = null;
            MnjxPnrFn pnrFn = null;
            MnjxPnrRecord fnRecord = null;
            if (Constant.TICKET_STATUS_EXCHANGED.equals(ticket.getTicketStatus1())) {
                if (ObjectUtil.isNull(fn) && ObjectUtil.isNull(pnrFn)) {
                    List<MnjxPnrRecord> fnRecords = iMnjxPnrRecordService.lambdaQuery().in(MnjxPnrRecord::getPnrType, "FN", "NM FN").eq(MnjxPnrRecord::getPnrId, pnr.getPnrId()).list();
                    if (CollUtil.isNotEmpty(fnRecords)) {
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            List<MnjxPnrRecord> xnRecords = fnRecords.stream().filter(f -> f.getInputValue().contains("/IN/")).collect(Collectors.toList());
                            fnRecord = xnRecords.get(0);
                        } else {
                            List<MnjxPnrRecord> pnrRecords = fnRecords.stream().filter(f -> !f.getInputValue().contains("/IN/")).collect(Collectors.toList());
                            fnRecord = pnrRecords.get(0);
                        }
                    }
                }
            } else {
                List<MnjxNmFn> fnList = iMnjxFnService.lambdaQuery().eq(MnjxNmFn::getPnrNmId, nm.getPnrNmId()).list();
                if (CollectionUtil.isEmpty(fnList)) {
                    List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery().eq(MnjxPnrFn::getPnrId, pnr.getPnrId()).list();
                    if (CollUtil.isNotEmpty(pnrFnList)) {
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            List<MnjxPnrFn> xnFns = pnrFnList.stream().filter(f -> "IN".equals(f.getPatType())).collect(Collectors.toList());
                            pnrFn = xnFns.get(0);
                        } else {
                            List<MnjxPnrFn> xnFns = pnrFnList.stream().filter(f -> !"IN".equals(f.getPatType())).collect(Collectors.toList());
                            pnrFn = xnFns.get(0);
                        }
                    }
                } else {
                    // 暂时只处理一个fn，如果nmId查不到的，用pnrId查应该是每个旅客都是同一个fn

                    if (ObjectUtil.isNotEmpty(nmXn)) {
                        List<MnjxNmFn> xnFns = fnList.stream().filter(f -> "IN".equals(f.getPatType())).collect(Collectors.toList());
                        fn = xnFns.get(0);
                    } else {
                        List<MnjxNmFn> nmFns = fnList.stream().filter(f -> !"IN".equals(f.getPatType())).collect(Collectors.toList());
                        fn = nmFns.get(0);
                    }

                    if (ObjectUtil.isNull(fn)) {
                        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery().eq(MnjxPnrFn::getPnrId, pnr.getPnrId()).eq(MnjxPnrFn::getPatType, patType).list();
                        if (CollUtil.isNotEmpty(pnrFnList)) {
                            if (ObjectUtil.isNotEmpty(nmXn)) {
                                List<MnjxPnrFn> xnFns = pnrFnList.stream().filter(f -> "IN".equals(f.getPatType())).collect(Collectors.toList());
                                pnrFn = xnFns.get(0);
                            } else {
                                List<MnjxPnrFn> nmFns = pnrFnList.stream().filter(f -> !"IN".equals(f.getPatType())).collect(Collectors.toList());
                                pnrFn = nmFns.get(0);
                            }

                        }
                    }
                }
                if (ObjectUtil.isNull(fn) && ObjectUtil.isNull(pnrFn)) {
                    List<MnjxPnrRecord> fnRecords = iMnjxPnrRecordService.lambdaQuery().in(MnjxPnrRecord::getPnrType, "FN", "NM FN").eq(MnjxPnrRecord::getPnrId, pnr.getPnrId()).list();
                    if (CollUtil.isNotEmpty(fnRecords)) {
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            List<MnjxPnrRecord> xnFns = fnRecords.stream().filter(f -> f.getInputValue().contains("/IN/")).collect(Collectors.toList());
                            fnRecord = xnFns.get(0);
                        } else {
                            List<MnjxPnrRecord> nmFns = fnRecords.stream().filter(f -> !f.getInputValue().contains("/IN/")).collect(Collectors.toList());
                            fnRecord = nmFns.get(0);
                        }
                    }
                }
            }
            if (ObjectUtil.isNotNull(fn)) {
                map.put("fcny", fn.getFCurrency() + fn.getFPrice());
                map.put("cnyCn", fn.getTCnCurrency() + fn.getTCnPrice());
                map.put("cnyYq", fn.getTYqCurrency() + fn.getTYqPrice());
                map.put("acny", fn.getACurrency() + fn.getAPrice());
            } else if (ObjectUtil.isNotNull(pnrFn)) {
                map.put("fcny", pnrFn.getFCurrency() + pnrFn.getFPrice());
                map.put("cnyCn", pnrFn.getTCnCurrency() + pnrFn.getTCnPrice());
                map.put("cnyYq", pnrFn.getTYqCurrency() + pnrFn.getTYqPrice());
                map.put("acny", pnrFn.getACurrency() + pnrFn.getAPrice());
            } else if (ObjectUtil.isNotNull(fnRecord)) {
                String[] values = fnRecord.getInputValue().split("/");
                for (String value : values) {
                    if (value.contains("FCNY")) {
                        map.put("fcny", value.substring(1));
                        continue;
                    }
                    if (value.contains("ACNY")) {
                        map.put("acny", value.substring(1));
                        continue;
                    }
                    if (value.length() > 3) {
                        String tailStr = value.substring(value.length() - 2);
                        if ("CN".equals(tailStr)) {
                            map.put("cnyCn", value.substring(1, value.length() - 2));
                        }
                        if ("YQ".equals(tailStr)) {
                            map.put("cnyYq", value.substring(1, value.length() - 2));
                        }
                    }
                }
            }
            list.add(map);
        }
        return list;
    }


    @Override
    public void persistMent(List<MnjxMent> saveList) {
        iMnjxMentService.saveBatch(saveList);
    }


    @Override
    public MnjxMent constructMent(String id, String mentDate, String invoiceNo, String workNum, String pnrId) {
        MnjxMent mnjxMent = new MnjxMent();
        mnjxMent.setIdBgt(id);
        mnjxMent.setPnrId(pnrId);
        mnjxMent.setMentDate(mentDate);
        mnjxMent.setInvoiceNo(invoiceNo);
        mnjxMent.setWorkNum(workNum);
        return mnjxMent;
    }

    private List<MnjxPlanSection> getMatchPlanSections(String flightNo, String flightDate, String orgCode,
                                                       String dstCode) {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, flightNo).one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery().eq(MnjxPlanFlight::getFlightDate, flightDate)
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId()).one();
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, orgCode.substring(0, 3)).one();
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery().eq(MnjxAirport::getAirportCode, dstCode).one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection).orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff).list();
        boolean findFirstAirport = false;
        List<MnjxPlanSection> thisPsgPlanSectionList = new ArrayList<>();
        for (MnjxPlanSection mnjxPlanSection : planSectionList) {
            // 输入的航段是多航段中的某一个航段，或者是单航段
            if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId())
                    && dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                thisPsgPlanSectionList.add(mnjxPlanSection);
            }
            // 输入的航段是多航段中多个航段的组合
            else {
                // 出发
                if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId())) {
                    findFirstAirport = true;
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                }
                // 如果都没有匹配到，说明是中间航段，也需要把开舱数据加进去进行筛选
                else if (findFirstAirport) {
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                    if (dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                        findFirstAirport = false;
                    }
                }
            }
        }
        return thisPsgPlanSectionList;
    }

    private int getCndCabinWeight(String flightNo, String cabinClass) {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, flightNo).one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxCnd cnd = mnjxCndService.lambdaQuery().eq(MnjxCnd::getCndId, tcard.getCndId()).one();
        int weight;
        if (cabinClass.equals(cnd.getFirstCabinClass())) {
            weight = cnd.getFirstWeight();
        } else if (cabinClass.equals(cnd.getSecondCabinClass())) {
            weight = cnd.getSecondWeight();
        } else if (cabinClass.equals(cnd.getThirdCabinClass())) {
            weight = cnd.getThirdWeight();
        } else if (cabinClass.equals(cnd.getFourthCabinClass())) {
            weight = cnd.getFourthWeight();
        } else {
            weight = cnd.getFifthWeight();
        }
        return weight;
    }

    private String getCabinClass(String flightNo, String sellCabin) {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, flightNo).one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxCnd cnd = mnjxCndService.lambdaQuery().eq(MnjxCnd::getCndId, tcard.getCndId()).one();
        //获取升舱的舱等
        if (StrUtil.isNotBlank(cnd.getFirstSellCabin()) && cnd.getFirstSellCabin().contains(sellCabin)) {
            return cnd.getFirstCabinClass();
        }
        if (StrUtil.isNotBlank(cnd.getSecondSellCabin()) && cnd.getSecondSellCabin().contains(sellCabin)) {
            return cnd.getSecondCabinClass();
        }
        if (StrUtil.isNotBlank(cnd.getThirdSellCabin()) && cnd.getThirdSellCabin().contains(sellCabin)) {
            return cnd.getThirdCabinClass();
        }
        if (StrUtil.isNotBlank(cnd.getFourthSellCabin()) && cnd.getFourthSellCabin().contains(sellCabin)) {
            return cnd.getFourthCabinClass();
        }
        if (StrUtil.isNotBlank(cnd.getFifthSellCabin()) && cnd.getFifthSellCabin().contains(sellCabin)) {
            return cnd.getFifthCabinClass();
        }
        return null;
    }

    private String getMaxNum() {
        return mentMapper.queryMaxStr();
    }
}
