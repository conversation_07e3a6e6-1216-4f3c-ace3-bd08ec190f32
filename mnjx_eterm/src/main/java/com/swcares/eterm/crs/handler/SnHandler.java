package com.swcares.eterm.crs.handler;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.SnDto;
import com.swcares.eterm.crs.service.ISnService;
import javax.annotation.Resource;

/**
 * 不定义航段组，就是open航段
 *
 * <AUTHOR>
 */
@OperateType(action = "SN")
public class SnHandler implements Handler {
    @Resource
    private ISnService iSnService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        SnDto snDto = new SnDto();
        MemoryDataPnr memoryDataPnr = MemoryDataUtils.getMemoryData().getMemoryDataPnr();
        iSnService.handle(memoryDataPnr,snDto);
    	return StrUtils.EMPTY;
    }
}
