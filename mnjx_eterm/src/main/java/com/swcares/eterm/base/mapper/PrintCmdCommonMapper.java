package com.swcares.eterm.base.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PrintCmdCommonMapper {

    /**
     * 判断票号范围是否输入
     *
     * @param printNo   printNo
     * @param officeNum officeNum
     * @return 判断票号范围是否输入
     */
    @MapKey("printer_no")
    List<Map<String, Object>> retrieveTicketLimitsIsInput(@Param("printNo") String printNo, @Param("officeNum") String officeNum);


    /**
     * 判断打票机是否上票
     *
     * @param printNo   printNo
     * @param officeNum officeNum
     * @return 判断打票机是否上票
     */
    @MapKey("ticket_start")
    List<Map<String, Object>> retrieveTicketLimitsIsLoaded(@Param("printNo") String printNo, @Param("officeNum") String officeNum);
}
