import org.junit.jupiter.api.Test;

import java.nio.ByteBuffer;

import static com.swcares.core.util.ArrayUtils.*;

public class ArrayUtilsTest {
    @Test
    public void testIntToBytes() {
        int c=968523,d=-65423;
        byte[] ans=intToBytes(d);
        intPrint(d);
        for(int i=0;i<4;i++)
            bytePrint(ans[i]);

        System.out.println(Integer.toBinaryString(d));
        System.out.println(Integer.toUnsignedString(d));
    }

    @Test
    public void testBytesToInt() {
        int c=968523,d=-65423;
        byte[] ans=intToBytes(c);
        intPrint(c);
        for(int i=0;i<4;i++)
            bytePrint(ans[i]);

        int e=bytesToInt(ans);
        System.out.println(e);
    }

    @Test
    public void test(){
        System.out.println(ByteBuffer.wrap(new byte[]{0, -92}).getShort());
    }

    @Test
    public void insert(){
        byte[] a = new byte[]{1,2,3,4};

    }
}
