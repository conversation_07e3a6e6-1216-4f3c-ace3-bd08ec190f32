import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.channel.embedded.EmbeddedChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
public class ChannelTest {
    @Test
    public void test() {
        EmbeddedChannel embeddedChannel = new EmbeddedChannel(
                new LoggingHandler(LogLevel.DEBUG),
                new LengthFieldBasedFrameDecoder(2048, 2, 2, -4, 0),
                new LoggingHandler(LogLevel.DEBUG)
        );

        ByteBuf byteBuf = ByteBufAllocator.DEFAULT.buffer().writeBytes(new byte[]{
                1, 0, 0, -84, 0, 0, 0, 1, 57, 81, 112, 2, 27, 11, 32, 32, 0, 15, 30, 83, 83, 32, 51, 85, 56, 56, 56, 56, 47, 89, 47, 49, 52, 79, 67, 84, 47, 80, 69, 75, 83, 72, 65, 47, 78, 78, 47, 49, 13, 78, 77, 32, 49, 67, 97, 114, 101, 116, 97, 107, 101, 114, 47, 84, 114, 105, 120, 13, 83, 83, 82, 32, 70, 79, 73, 68, 32, 51, 85, 32, 72, 75, 47, 78, 73, 52, 52, 48, 51, 48, 56, 49, 57, 57, 51, 49, 48, 48, 55, 54, 51, 55, 56, 47, 80, 49, 13, 67, 84, 58, 49, 51, 54, 48, 56, 48, 51, 53, 54, 53, 49, 47, 80, 49, 13, 79, 83, 73, 58, 51, 85, 32, 67, 84, 67, 84, 32, 49, 51, 53, 51, 51, 50, 51, 50, 51, 51, 53, 47, 65, 78, 32, 89, 85, 69, 13, 84, 75, 58, 84, 76, 47, 50, 49, 48, 48, 47, 46, 47, 83, 72, 65
        });
        log.info("长度:{}", byteBuf.getShort(2));
        embeddedChannel.writeInbound(byteBuf);
    }
}
