import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;

/**
 * 修改文件的后缀
 */
public class FileRenameSub {
    public static void main(String[] args) throws IOException {
        Path path = Paths.get("E:\\workspace_java_IntelliJ\\Ultimate_Edition_work\\STS\\mnjx_eterm\\src\\main\\resources\\templates\\cmn\\help");
        System.out.println(path);
        Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (file.toString().endsWith(".ftl")) {
                    String targetFile = file.toString().replace(".ftl", ".jf");
                    Files.copy(file, Paths.get(targetFile));
                    Files.delete(file);
                }
                return super.visitFile(file, attrs);
            }
        });
    }
}
