package com.swcares.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxBroadcastStatus;
import com.swcares.entity.MnjxBroadcastTemplate;
import com.swcares.mapper.TemplateConfigMapper;
import com.swcares.obj.dto.TemplateConfigDto;
import com.swcares.obj.vo.AudioVo;
import com.swcares.obj.vo.FlightVo;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15 16:05
 */
@Slf4j
@Service
public class TemplateConfigServiceImpl implements ITemplateConfigService {

    @Resource
    private ICommonService iCommonService;

    @Resource
    private IMnjxBroadcastTemplateService iMnjxBroadcastTemplateService;

    @Resource
    private IMnjxBroadcastStatusService iMnjxBroadcastStatusService;

    @Resource
    private IMnjxFlightScreenService iMnjxFlightScreenService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private TemplateConfigMapper templateConfigMapper;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Override
    public void createTemplate(TemplateConfigDto templateConfigDto) throws UnifiedResultException {
        MnjxBroadcastTemplate dbTemplate = iMnjxBroadcastTemplateService.lambdaQuery()
                .eq(MnjxBroadcastTemplate::getTemplateNo, templateConfigDto.getTemplateNo())
                .one();
        if (ObjectUtil.isNotEmpty(dbTemplate)) {
            throw new UnifiedResultException("编号已存在");
        }
        MnjxBroadcastTemplate template = new MnjxBroadcastTemplate();
        BeanUtil.copyProperties(templateConfigDto, template);
        iMnjxBroadcastTemplateService.save(template);
    }

    @Override
    public List<MnjxBroadcastTemplate> retrieveTemplate() throws UnifiedResultException {
        return iMnjxBroadcastTemplateService.list();
    }

    @Override
    public void updateTemplate(MnjxBroadcastTemplate mnjxBroadcastTemplate) throws UnifiedResultException {
        MnjxBroadcastTemplate dbTemplate = iMnjxBroadcastTemplateService.lambdaQuery()
                .eq(MnjxBroadcastTemplate::getTemplateNo, mnjxBroadcastTemplate.getTemplateNo())
                .one();
        if (ObjectUtil.isNotEmpty(dbTemplate)) {
            throw new UnifiedResultException("编号已存在");
        }
        iMnjxBroadcastTemplateService.updateById(mnjxBroadcastTemplate);
    }

    @Override
    public void deleteTemplate(String templateId) throws UnifiedResultException {
        MnjxBroadcastTemplate template = iMnjxBroadcastTemplateService.getById(templateId);
        iMnjxBroadcastTemplateService.removeById(templateId);
        iMnjxBroadcastStatusService.lambdaUpdate()
                .eq(MnjxBroadcastStatus::getTemplateNo, template.getTemplateNo())
                .remove();
    }

    @Override
    public List<AudioVo> generateTemplateToAudioData(String templateId) throws UnifiedResultException {
        List<AudioVo> audioVoList = new ArrayList<>();
        List<FlightVo> flightVoList = iCommonService.constructTemplateFlightVo(null, null, true);
        if (CollUtil.isEmpty(flightVoList)) {
            throw new UnifiedResultException("无航班数据");
        }

        boolean beUpdated = false;
        List<MnjxBroadcastStatus> broadcastStatusList = iMnjxBroadcastStatusService.list();
        List<MnjxBroadcastStatus> newBroadcastStatusList = new ArrayList<>();
        List<MnjxBroadcastTemplate> templateList = iMnjxBroadcastTemplateService.list();
        for (FlightVo flightVo : flightVoList) {
            // 没有传ID表示生成除了登机口的所有模板的语音文件
            if (StrUtil.isEmpty(templateId)) {
                for (MnjxBroadcastTemplate template : templateList) {
                    if (!"5".equals(template.getTemplateNo())) {
                        iCommonService.generateAudioVo(audioVoList, template, flightVo);
                        if (broadcastStatusList.stream().noneMatch(b -> flightVo.getFlightNo().equals(b.getFlightNo()))) {
                            MnjxBroadcastStatus broadcastStatus = new MnjxBroadcastStatus();
                            broadcastStatus.setTemplateNo(template.getTemplateNo());
                            broadcastStatus.setStatus(Constant.STR_ONE);
                            broadcastStatus.setFlightNo(flightVo.getFlightNo());
                            broadcastStatus.setFlightDate(DateUtil.today());
                            newBroadcastStatusList.add(broadcastStatus);
                        } else {
                            broadcastStatusList.forEach(b -> {
                                b.setFlightDate(DateUtil.today());
                                b.setStatus(Constant.STR_ONE);
                            });
                            beUpdated = true;
                        }
                    }
                }
            } else {
                MnjxBroadcastTemplate template = iMnjxBroadcastTemplateService.getById(templateId);
                iCommonService.generateAudioVo(audioVoList, template, flightVo);
                if (broadcastStatusList.stream().noneMatch(b -> flightVo.getFlightNo().equals(b.getFlightNo()))) {
                    MnjxBroadcastStatus broadcastStatus = new MnjxBroadcastStatus();
                    broadcastStatus.setTemplateNo(template.getTemplateNo());
                    broadcastStatus.setStatus(Constant.STR_ONE);
                    broadcastStatus.setFlightNo(flightVo.getFlightNo());
                    broadcastStatus.setFlightDate(DateUtil.today());
                    newBroadcastStatusList.add(broadcastStatus);
                } else {
                    broadcastStatusList.forEach(b -> {
                        b.setFlightDate(DateUtil.today());
                        b.setStatus(Constant.STR_ONE);
                    });
                    beUpdated = true;
                }
            }
        }
        if (CollUtil.isNotEmpty(newBroadcastStatusList)) {
            iMnjxBroadcastStatusService.saveBatch(newBroadcastStatusList);
        }
        if (beUpdated) {
            iMnjxBroadcastStatusService.updateBatchById(broadcastStatusList);
        }
        return audioVoList;
    }
}
