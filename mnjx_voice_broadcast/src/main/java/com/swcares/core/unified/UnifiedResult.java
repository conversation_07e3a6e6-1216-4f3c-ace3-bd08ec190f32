package com.swcares.core.unified;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * 统一返回结果
 * 创建这么多的方法就是在后面使用的使用方便一点
 *
 * <AUTHOR>
 */
@ApiModel("统一返回结果")
@Data
@Builder
public class UnifiedResult<T> implements Serializable {
    /**
     * 返回代码
     * 200：成功
     * 500：服务器内部错误
     * 400：程序发生异常
     * 403: 没有响应权限
     */
    @ApiModelProperty(value = "code", notes = "返回代码")
    private int code;
    /**
     * 上面的代码本身不能让人很清楚的知道发生了什么什么问题
     * 所以用message来表示具体的说明
     * 取值只能是对应上面code的集中取值
     */
    @ApiModelProperty(value = "message", notes = "状态码说明")
    private String message;
    /**
     * 消息返回的的具体时间
     */
    @ApiModelProperty(value = "timestamp", notes = "消息返回的的具体时间")
    private String timestamp;
    /**
     * 需要返回的消息体
     */
    @ApiModelProperty(value = "data", notes = "需要返回的数据")
    private T data;


    public static UnifiedResult ok() {
        return UnifiedResult.ok("成功");
    }

    public static UnifiedResult ok(String msg) {
        return UnifiedResult.ok(msg, null);
    }

    public static <T> UnifiedResult ok(String msg, T data) {
        return UnifiedResult.ok(HttpStatus.HTTP_OK, msg, data);
    }

    public static <T> UnifiedResult ok(int code, String msg, T data) {
        return UnifiedResult.builder().code(code).message(msg).timestamp(DateUtil.now()).data(data).build();
    }

    public static UnifiedResult fail(String msg) {
        return UnifiedResult.fail(HttpStatus.HTTP_BAD_REQUEST, msg);
    }

    public static UnifiedResult fail(int code, String msg) {
        return UnifiedResult.fail(code, msg, null);
    }

    public static  <T> UnifiedResult fail(int code, String msg, T data) {
        return UnifiedResult.builder().code(code).message(msg).timestamp(DateUtil.now()).data(data).build();
    }

    /**
     * 打印数据
     *
     * @param response      返回对象
     * @param unifiedResult 需要输出的内容
     */
    public static void writeJson(HttpServletResponse response, UnifiedResult unifiedResult) {
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, StrUtil.format("{},{}", HttpMethod.POST.name(), HttpMethod.GET.name()));
        ServletUtil.write(response,JSONUtil.toJsonStr(unifiedResult), ContentType.JSON.getValue());
    }
}
