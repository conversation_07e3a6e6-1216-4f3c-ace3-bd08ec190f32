package com.swcares.mapper;

import com.swcares.entity.MnjxFlight;
import com.swcares.obj.vo.FlightVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/16 14:25
 */
public interface TemplateConfigMapper {

    List<MnjxFlight> retrieveShaOffFlight();

    /**
     * retrieveFlightNo
     *
     * @param flightNo flightNo
     * @param org      org
     * @param fltDate  fltDate
     * @return retrieveFltNoAndSection
     */
    List<FlightVo> retrieveFlightNo(@Param("flightNo") String flightNo, @Param("org") String org, @Param("fltDate") String fltDate);
}
