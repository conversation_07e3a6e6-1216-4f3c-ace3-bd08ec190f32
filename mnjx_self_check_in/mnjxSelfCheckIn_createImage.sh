#!/bin/bash
chmod 744 ./entrypoint.sh
echo "删除mnjx_self_check_in镜像"
docker rmi -f $(docker images "*/*/mnjx_self_check_in" -aq)
echo "使用本地环境打包镜像"
# 使用系统时间作为版本号
currentDateTime=$(date "+%Y%m%d%H%M%S")
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_self_check_in:latest .
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_self_check_in:"${currentDateTime}" .
echo "登录harbor服务器"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "推送镜像到远程的harbor"
docker push harbor.kaiya.com:30443/sts/mnjx_self_check_in:latest
docker push harbor.kaiya.com:30443/sts/mnjx_self_check_in:"${currentDateTime}"
