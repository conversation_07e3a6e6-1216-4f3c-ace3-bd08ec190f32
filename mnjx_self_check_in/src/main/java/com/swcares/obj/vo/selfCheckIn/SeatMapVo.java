package com.swcares.obj.vo.selfCheckIn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 14:28
 */
@Data
@ApiModel("座位图数据")
public class SeatMapVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "舱等")
    private String cabinClass;

    @ApiModelProperty(value = "最大X坐标")
    private Integer maxX;

    @ApiModelProperty(value = "最大Y坐标")
    private Integer maxY;

    @ApiModelProperty(value = "需要屏蔽的列")
    private List<Integer> removeXList = new ArrayList<>();

    @ApiModelProperty(value = "座位列表数据")
    private List<SeatVo> seatVoList = new ArrayList<>();
}
