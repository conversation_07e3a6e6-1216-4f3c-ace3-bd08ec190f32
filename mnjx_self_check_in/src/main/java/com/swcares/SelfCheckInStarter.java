package com.swcares;

import com.swcares.core.util.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2024/10/18 14:30
 */
@Slf4j
@SpringBootApplication
@EnableTransactionManagement
@EnableCaching
public class SelfCheckInStarter {
    public static void main(String[] args) {
        SpringApplication.run(SelfCheckInStarter.class, args);
        log.info(StrUtils.format("swagger api 地址:  http://{}:{}/mnjx_self_check_in/swagger-ui/index.html", "localhost", "8359"));
    }
}
