package com.swcares.mapper;

import com.swcares.entity.MnjxPlanFlight;
import com.swcares.entity.MnjxTcardSection;
import com.swcares.obj.vo.PassengerTripVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 10:38
 */
public interface SelfCheckInMapper {

    List<PassengerTripVo> retrievePassengerTripByIdCard(@Param("idCard") String idCard, @Param("today") String today);

    MnjxPlanFlight retrievePlanFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    List<MnjxTcardSection> retrieveTcardSection(@Param("flightNo") String flightNo);
}
