chmod 744 ./entrypoint.sh
echo "删除麒麟kylin_mnjx_self_check_in镜像"
docker rmi -f $(docker images "*/*/kylin_mnjx_self_check_in" -aq)
echo "使用本地环境打包镜像"
# 使用系统时间作为版本号
currentDateTime=$(date "+%Y%m%d%H%M%S")
docker build -f ./kylin_Dockerfile -t harbor.kaiya.com:30443/sts/kylin_mnjx_self_check_in:latest .
docker build -f ./kylin_Dockerfile -t harbor.kaiya.com:30443/sts/kylin_mnjx_self_check_in:"${currentDateTime}" .
echo "构建进行完成，版本：${currentDateTime}"
echo "登录harbor服务器"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "推送镜像到远程的harbor"
docker push harbor.kaiya.com:30443/sts/kylin_mnjx_self_check_in:latest
docker push harbor.kaiya.com:30443/sts/kylin_mnjx_self_check_in:"${currentDateTime}"
echo "推送镜像到远程的harbor完成，版本：${currentDateTime}"
