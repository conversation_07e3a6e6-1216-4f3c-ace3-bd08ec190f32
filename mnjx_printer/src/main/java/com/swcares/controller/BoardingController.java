package com.swcares.controller;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.service.IBoardingService;
import com.swcares.vo.BoardingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 登机牌打印
 *
 * <AUTHOR>
 */
@Api(tags = "登机牌打印")
@Slf4j
@RestController
@RequestMapping("/boarding")
public class BoardingController {
    @Resource
    private IBoardingService iBoardingService;

    @ApiOperation("登机牌打印")
    @PostMapping("/print")
    public void print(@RequestBody BoardingVo boardingVo) throws UnifiedResultException {
        iBoardingService.print(boardingVo);
    }
}
