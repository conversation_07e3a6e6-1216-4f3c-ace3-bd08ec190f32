package com.swcares.service.impl.enpson;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.print.CreatePrintXls;
import com.swcares.core.print.PrintXls;
import com.swcares.core.print.PrinterConfiguration;
import com.swcares.service.IItineraryService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.ItineraryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EnpsonItineraryServiceImpl extends BasePrintServiceImpl<ItineraryVo> implements IItineraryService {

    @Resource
    private PrinterConfiguration printerConfiguration;

    @Override
    protected String generatePrintData(ItineraryVo itineraryVo) {
        return null;
    }

    @Override
    public String printEnpson(Map<String, Object> data) {
        log.info("收到ment指令：{}", data.toString());
        String inPath = "\\templet\\241x93mm行程单模板1.xls";
        if ("A4".equals(printerConfiguration.getItineraryType())) {
            inPath = "\\templet\\travel_itinerary.xls";
        } else if ("1".equals(printerConfiguration.getOnlyPrintData())) {
            if ("0".equals(printerConfiguration.getDirection())) {
                inPath = "\\templet\\24.3x9.31\\241x93mm行程单模板-数据-反向.xls";
            } else {
                inPath = "\\templet\\24.3x9.31\\241x93mm行程单模板-数据.xls";
            }
        }

        List<Map<String, Object>> listMap = (List<Map<String, Object>>) data.get("data");
        for (int j = 0; j < listMap.size(); j++) {
            String outPath = StrUtil.format("{}{}.xls", ".\\new_travel_itinerary", j);
            Map<String, Object> map = listMap.get(j);
            try {
                int segSize = Integer.parseInt(map.get("segSize").toString());
                for (int i = 0; i < segSize; i++) {
                    if (i == 0) {
                        map.put("ORG", map.get("orgName" + i) + "  " + map.get("org" + i));
                    }
                    map.put("DST" + i, map.get("dstName" + i) + "  " + map.get("dst" + i));
                    String fltNo = (String) map.get("fltNo" + i);
                    // VOID表示当前处理的SA航段组
                    if (!"VOID".equals(fltNo)) {
                        map.put("CABINCLASS" + i, CharSequenceUtil.emptyIfNull(map.get("cabinClass" + i).toString()));
                        map.put("FLTDATE" + i, CharSequenceUtil.emptyIfNull(map.get("fltDate" + i).toString()));
                        map.put("TIME" + i, CharSequenceUtil.emptyIfNull(map.get("time" + i).toString()));
                        map.put("FAREBASIS" + i, CharSequenceUtil.emptyIfNull(map.get("fareBasis" + i).toString()));
                        map.put("FLTNO" + i, fltNo.substring(2));
                        map.put("CARRIER" + i, fltNo.substring(0, 2));
                    } else {
                        map.put("FLTNO" + i, fltNo);
                        map.put("CARRIER" + i, "");
                    }
                }
                map.put("DST" + segSize, "VOID");
                map.put("SERIALNUMBER", map.get("serialNumber"));
                map.put("CARRIER" + segSize, "VOID");
                map.put("AGENTCODE", map.get("agentCode"));
                map.put("OFFICENO", map.get("officeNo"));
                map.put("EIINFO", map.get("eiInfo"));
                map.put("PNRICS", map.get("pnrICs"));
                map.put("ISSUDEBY", map.get("issudeBy"));
                map.put("TICKETNO", map.get("totalTicketNo"));
                map.put("PSGNAME", map.get("psgName").toString());
                String fcny = this.handDot((String) map.get("fcny"));
                if ("CNY0.00".equals(fcny)) {
                    map.put("FCNY", "CNY0.00");
                    map.put("CNYCN", "CN0.00");
                    map.put("CNYYQ", "YQ0.00");
                    map.put("ACNY", "CNY0.00");
                } else {
                    map.put("FCNY", fcny);
                    String cnyCn = (String) map.get("cnyCn");
                    String cn = cnyCn.replace("CNY", "");
                    if ("0.00".equals(cn)) {
                        cnyCn = "CN EXEMPT";
                    } else {
                        cnyCn = "CN" + cn;
                    }
                    String cnyYq = (String) map.get("cnyYq");
                    String yq = cnyYq.replace("CNY", "");
                    if ("0.00".equals(yq) || "EXEMPTYQ".equals(yq)) {
                        cnyYq = "YQ EXEMPT";
                    } else {
                        cnyYq = "YQ" + yq;
                    }
                    String acny = this.handDot((String) map.get("acny"));
                    map.put("CNYCN", cnyCn);
                    map.put("CNYYQ", cnyYq);
                    map.put("ACNY", acny);
                }
                map.put("DATEOFISSUE", DateUtil.today());
                CreatePrintXls.newExcel(inPath, outPath, map, printerConfiguration);
                PrintXls.exec(outPath, printerConfiguration, j);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "OK";
    }

    /**
     * Title：handDot <br>
     * description：字符串取消后面的多个.后面的字符，然后加.0 <br>
     *
     * @param strDot
     * @return <br>
     * author：zhaokan <br>
     * date：2020/11/16 <br>
     */
    private String handDot(String strDot) {
        if (StrUtil.isNotEmpty(strDot)) {
            String[] temp = strDot.split("\\.");
            return temp[0] + ".00";
        }
        return strDot;
    }
}
