package com.swcares.service.impl.new_beiyang;

import com.alibaba.fastjson.JSON;
import com.swcares.service.IBoardingService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.BoardingVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(prefix = "printer", name = "category", havingValue = "BTP6800X")
public class NewBeiyangBoardingServiceImpl extends BasePrintServiceImpl<BoardingVo> implements IBoardingService {

    @Override
    protected String generatePrintData(BoardingVo vo) {
        return JSON.toJSONString(vo);
    }
}
