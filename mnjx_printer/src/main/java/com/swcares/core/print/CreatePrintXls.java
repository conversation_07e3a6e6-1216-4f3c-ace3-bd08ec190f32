package com.swcares.core.print;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.nio.file.Files;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ClassName：com.kaiya.mnjx.printSocket.xls.CreatePrintXls <br>
 * description：创建打印xls文件 <br>
 *
 * <AUTHOR> <br>
 * date 2020/11/15 <br>
 * @version v1.0 <br>
 */
public class CreatePrintXls {

    private static final Pattern PATTERN = Pattern.compile("\\{(.+?)}", Pattern.CASE_INSENSITIVE);

    /**
     * Title：newExcel <br>
     * description：根据模板创建待打印的excel文件 <br>
     *
     * @param inPath  模板文件路径
     * @param outPath 新文件路径
     * @param params  打印数据
     * @throws IOException <br>
     *                     author：zhaokan <br>
     *                     date：2020/11/15 <br>
     */
    public static void newExcel(String inPath, String outPath, Map<String, Object> params, PrinterConfiguration printerConfiguration) throws IOException {
        InputStream is = ResourceUtil.getStream(inPath);
        Workbook wb = WorkbookFactory.create(is);
        // 获取Excel的工作表sheet，下标从0开始。
        Sheet sheet = wb.getSheetAt(0);
        // 将整个工作表打印在一页（缩放）
        sheet.setAutobreaks(true);
        // 获取Excel的行数
        int trLength = sheet.getPhysicalNumberOfRows();
        for (int i = 0; i < trLength; i++) {
            // 获取Excel的行，下标从0开始
            Row row = sheet.getRow(i);
            // 若行为空，则遍历下一行
            if (row == null) {
                continue;
            }
            int minColIx = row.getFirstCellNum();
            int maxColIx = row.getLastCellNum();
            for (int colIx = minColIx; colIx < maxColIx; colIx++) {
                // 获取指定单元格，单元格从左到右下标从0开始
                Cell cell = row.getCell(colIx);
                if (ObjectUtil.isEmpty(cell)) {
                    continue;
                }
                String runText = cell.getStringCellValue();
                if ("".equals(runText)) {
                    continue;
                }
                System.out.println(cell);
                Matcher matcher = matcher(runText);
                if (matcher.find()) {
                    while ((matcher = matcher(runText)).find()) {
                        Object obj = params.get(matcher.group(1));
                        String text = "";
                        if (obj != null) {
                            text = String.valueOf(obj);
                        }
                        runText = matcher.replaceFirst(text);
                    }
                    CellStyle cellStyle = cell.getCellStyle();
                    Font font = wb.createFont();
                    font.setBold(false);
                    font.setFontHeightInPoints((short) printerConfiguration.getFontSize());
                    font.setFontName("微软雅黑");
                    cellStyle.setFont(font);
                    cell.setCellStyle(cellStyle);
                    cell.setCellValue(runText);
                }
            }
        }
        // A4进行一些设定
        if ("A4".equals(printerConfiguration.getItineraryType())) {
            sheet.setZoom(100);
            PrintSetup print = sheet.getPrintSetup();
            // 打印方向，true：横向，false：纵向(默认)
            print.setLandscape(true);
            // 设置a4纸
            print.setPaperSize((short) 9);
        }
        OutputStream out = Files.newOutputStream(new File(outPath).toPath());
        wb.write(out);
        is.close();
        out.close();
        System.out.println("根据模板创建excel成功");
    }

    /**
     * 正则匹配字符串
     *
     * @param str 输入的字符串
     * @return 匹配器
     */
    private static Matcher matcher(String str) {
        return PATTERN.matcher(str);
    }
}
