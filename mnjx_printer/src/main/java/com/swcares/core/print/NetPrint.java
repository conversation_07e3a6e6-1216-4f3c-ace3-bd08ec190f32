package com.swcares.core.print;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.net.Socket;

/**
 * COM口打印
 *
 * <AUTHOR>
 */
@Slf4j
public class NetPrint {

    private NetPrint() {
    }

    public static void transmitData(String tData) {
        log.info("进入网络打印");
        Socket socket = null;
        try {
            byte[] data = ArrayUtil.addAll(new byte[]{0x2}, tData.getBytes(CharsetUtil.GBK), new byte[]{0x3});
            socket = new Socket("192.168.13.100", 9004);
            OutputStream os = socket.getOutputStream();
            os.write(data);
            os.close();
            log.info("网络打印完成");
        } catch (Exception e) {
            log.error("打印发生错误，错误原因：{}", e.getMessage());
            e.printStackTrace();
        } finally {
            if (socket != null) {
                try {
                    socket.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
