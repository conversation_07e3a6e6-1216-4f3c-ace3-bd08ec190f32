package com.swcares.core.protocol.atp;

import java.util.List;

/**
 * 登机牌PEC的载体
 * <p>
 * 一张登机牌在逻辑上被划分成了“长×宽＝72×18”（或者说“横向×纵向＝72×18”）的一张网格。
 * 登机牌的纵向分为18个单位，编号从A到R，每个单位实际大小为2.5毫米（mm）。
 * 登机牌的横向分为72个单位，编号从01到72，每个单位的实际大小为4.5毫米。其中编号“52”跨越大小牌分割线，不被使用。
 * <p>
 * eg:
 * #0101110112011301210122012301310132013301410142014301B
 * #0520E01111123113211F#0620F32B#0728D01V#0803H01G50G63120221023302B
 * #0903#1005H03G52G65120521053305B#1103#1406#1506#1705
 * #1807L01J28J53J66133011023202B#1904M19124043022111R
 * #2003J01J35J50J63131031052205B#2104M34410542054308R#2204N51N64J41B#2304F#2420#2511
 * #2608#2708#2804O12B#2915O16B#3017#3110#3203221541154315B#3301J45121331133313B
 * #3517#3603J11121831183318B#3910#4007#4105G01J20F50F63120621063306B
 * #4205G03J22F52F65120821083308B#4319O24B#4419Q49#4519O12B#4601#5020#5301#5503#5604
 * #5710R37B#6040#61B7R121231#6212#6312I11W#6410P37B#6515O37N
 * #6606R15#6706R25#6806#6920#7006#7199#7720#
 *
 * <AUTHOR>
 */
public class AtpPecPayload {
    List<AtpPecPayloadItem> atpPecPayloadItems;
}
