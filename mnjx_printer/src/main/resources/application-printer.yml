# ===== 打印机类型设置  ===== #
printer:
  # 打印机的类型，目前只支持:Honeywell、IER400、新北洋BTP6800X、新北洋BT080。参数：Honeywell、IER、BTP6800X、BT080、Hailian、DongXing
  category: BTP6800X
  # 打印机使用的接口类型 取值参考：PrintType。COM、USB、NET
  type: USB

  # ================================新北洋6800X的配置信息========================================
  # 使用新北洋6800X型号USB连接时，指定登机牌打印数据构建
  newBeiyangBoarding: 海南航空
  # 使用新北洋6800X型号USB连接时，指定行李牌打印数据构建
  newBeiyangLuggage: 中国航信
  # 登机牌打印机信息
  boardingPrinterInfo: BTP-6800X2(U) 1
  # 行李牌打印机信息
  luggagePrinterInfo: BTP-6800X2(U) 2
  # 登机牌打印机序列号信息（12位）(公司新北洋登机牌序列号220509200043)
  boardingPrinterSerial: 220509200043
  # 行李牌打印机序列号信息（12位）（公司新北洋行李条序列号220509200002）
  luggagePrinterSerial: 220509200002

  # ================================其他打印机串口连接绑定了固定串口的配置信息===============================
  # 打印机连接模式，0：不需要指定串口号，1：需要指定串口号
  # 注意：根据学校实际情况，在打包打印机程序前进行修改，同时需要修改下面2个串口号参数，并且上面接口类型需要使用COM类型
  printerConnectionModel: 0
  # 指定登机牌打印机串口号
  cpCom: COM5
  # 指定行李牌打印机串口号
  btpCom: COM6

  # ================================行程单打印方式===============================
  # 使用A4纸打印或者使用241x93mm三等分纸打印：A4;241x93
  itineraryType: 241x93
  # 使用三等分纸打印时是否只打印数据。0否，1是
  onlyPrintData: 1
  # 只打印数据时打印数据的方向。0反向，1正向
  direction: 0
  # 字体大小（根据实际打印效果调试）
  fontSize: 9
