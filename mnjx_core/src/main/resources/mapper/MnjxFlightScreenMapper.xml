<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxFlightScreenMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxFlightScreen">
        <id column="flight_screen_id" property="flightScreenId" />
        <result column="flight_no" property="flightNo" />
        <result column="gate" property="gate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        flight_screen_id, flight_no, gate
    </sql>

</mapper>
