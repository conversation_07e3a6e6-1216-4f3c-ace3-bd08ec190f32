<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxConfigLuggageMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxConfigLuggage">
        <result column="config_luggage_id" property="configLuggageId" />
        <result column="max_bags" property="maxBags" />
        <result column="singleton_bagges_weight" property="singletonBaggesWeight" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        config_luggage_id, max_bags, singleton_bagges_weight
    </sql>

</mapper>
