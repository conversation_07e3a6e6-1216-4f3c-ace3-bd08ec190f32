<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrNmTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrNmTicket">
        <id column="nm_ticket_id" property="nmTicketId"/>
        <result column="pnr_nm_tn_id" property="pnrNmTnId"/>
        <result column="ticket_no" property="ticketNo"/>
        <result column="ticket_status_1" property="ticketStatus1"/>
        <result column="ticket_status_2" property="ticketStatus2"/>
        <result column="tour_code" property="tourCode"/>
        <result column="receipt_print" property="receiptPrint"/>
        <result column="s1_id" property="s1Id"/>
        <result column="s2_id" property="s2Id"/>
        <result column="input_value" property="inputValue"/>
        <result column="hbnb_1" property="hbnb1"/>
        <result column="hbnb_2" property="hbnb2"/>
        <result column="is_et" property="isEt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nm_ticket_id, pnr_nm_tn_id, ticket_no, ticket_status_1, ticket_status_2, tour_code, receipt_print, s1_id, s2_id, input_value,hbnb_1,hbnb_2,is_et
    </sql>

</mapper>
