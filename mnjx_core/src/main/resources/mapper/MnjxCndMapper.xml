<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxCndMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxCnd">
        <id column="cnd_id" property="cndId"/>
        <result column="plane_model_id" property="planeModelId"/>
        <result column="cnd_no" property="cndNo"/>
        <result column="layout" property="layout"/>
        <result column="layout_source" property="layoutSource"/>
        <result column="first_cabin_class" property="firstCabinClass"/>
        <result column="first_sell_cabin" property="firstSellCabin"/>
        <result column="first_seats" property="firstSeats"/>
        <result column="first_weight" property="firstWeight"/>
        <result column="second_cabin_class" property="secondCabinClass"/>
        <result column="second_sell_cabin" property="secondSellCabin"/>
        <result column="second_seats" property="secondSeats"/>
        <result column="second_weight" property="secondWeight"/>
        <result column="third_cabin_class" property="thirdCabinClass"/>
        <result column="third_sell_cabin" property="thirdSellCabin"/>
        <result column="third_seats" property="thirdSeats"/>
        <result column="third_weight" property="thirdWeight"/>
        <result column="fourth_cabin_class" property="fourthCabinClass"/>
        <result column="fourth_sell_cabin" property="fourthSellCabin"/>
        <result column="fourth_seats" property="fourthSeats"/>
        <result column="fourth_weight" property="fourthWeight"/>
        <result column="fifth_cabin_class" property="fifthCabinClass"/>
        <result column="fifth_sell_cabin" property="fifthSellCabin"/>
        <result column="fifth_seats" property="fifthSeats"/>
        <result column="fifth_weight" property="fifthWeight"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        cnd_id, plane_model_id, cnd_no, layout, layout_source, first_cabin_class, first_sell_cabin, first_seats, first_weight,
        second_cabin_class, second_sell_cabin, second_seats, second_weight, third_cabin_class, third_sell_cabin, third_seats, third_weight,
        fourth_cabin_class, fourth_sell_cabin, fourth_seats, fourth_weight, fifth_cabin_class, fifth_sell_cabin, fifth_seats, fifth_weight
    </sql>

</mapper>
