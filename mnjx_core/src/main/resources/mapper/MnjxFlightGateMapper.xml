<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxFlightGateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxFlightGate">
        <id column="flight_gate_id" property="flightGateId" />
        <result column="flight_no" property="flightNo" />
        <result column="gate_no" property="gateNo" />
        <result column="delay_time" property="delayTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        flight_gate_id, flight_no, gate_no, delay_time
    </sql>

</mapper>
