<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxAllowedIpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxAllowedIp">
        <id column="allowed_ip_id" property="allowedIpId" />
        <result column="office_id" property="officeId" />
        <result column="allowed_ip" property="allowedIp" />
        <result column="user_name" property="userName" />
        <result column="status" property="status" />
        <result column="expiration_date" property="expirationDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        allowed_ip_id, office_id, allowed_ip, user_name, status, expiration_date
    </sql>

</mapper>
