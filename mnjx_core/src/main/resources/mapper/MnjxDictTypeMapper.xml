<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxDictTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxDictType">
        <id column="dict_type_id" property="dictTypeId"/>
        <result column="dict_type_key" property="dictTypeKey"/>
        <result column="dict_type_name" property="dictTypeName"/>
        <result column="dict_type_status" property="dictTypeStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        dict_type_id, dict_type_key, dict_type_name,dict_type_status
    </sql>

</mapper>
