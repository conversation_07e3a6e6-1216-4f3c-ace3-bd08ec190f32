<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxSeatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxSeat">
        <id column="seat_id" property="seatId"/>
        <result column="open_cabin_id" property="openCabinId"/>
        <result column="plan_section_id" property="planSectionId"/>
        <result column="seat_no" property="seatNo"/>
        <result column="seat_status" property="seatStatus"/>
        <result column="seat_status_old" property="seatStatusOld"/>
        <result column="seat_row" property="seatRow"/>
        <result column="seat_column" property="seatColumn"/>
        <result column="seat_x" property="seatX"/>
        <result column="seat_y" property="seatY"/>
        <result column="is_window" property="isWindow"/>
        <result column="is_aisle" property="isAisle"/>
        <result column="is_forward" property="isForward"/>
        <result column="is_after" property="isAfter"/>
        <result column="is_right" property="isRight"/>
        <result column="is_left" property="isLeft"/>
        <result column="seat_status_old_two" property="seatStatusOldTwo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
		seat_id, open_cabin_id, plan_section_id, seat_no, seat_status, seat_status_old, seat_row, seat_column, seat_x,
		seat_y, is_window, is_aisle, is_forward, is_after, is_right, is_left, seat_status_old_two
    </sql>


</mapper>
