<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxNmFnMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxNmFn">
        <id column="nm_fn_id" property="nmFnId" />
        <result column="pnr_nm_id" property="pnrNmId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="is_baby" property="isBaby" />
        <result column="f_currency" property="fCurrency" />
        <result column="f_price" property="fPrice" />
        <result column="s_currency" property="sCurrency" />
        <result column="s_price" property="sPrice" />
        <result column="c_rate" property="cRate" />
        <result column="x_currency" property="xCurrency" />
        <result column="x_price" property="xPrice" />
        <result column="t_cn_currency" property="tCnCurrency" />
        <result column="t_cn_price" property="tCnPrice" />
        <result column="t_yq_currency" property="tYqCurrency" />
        <result column="t_yq_price" property="tYqPrice" />
        <result column="a_currency" property="aCurrency" />
        <result column="a_price" property="aPrice" />
        <result column="pat_type" property="patType" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nm_fn_id, pnr_nm_id, pnr_index, f_currency, f_price, s_currency, s_price, c_rate, x_currency, x_price, t_cn_currency, t_cn_price, t_yq_currency, t_yq_price, a_currency, a_price, pat_type, input_value
    </sql>

</mapper>
