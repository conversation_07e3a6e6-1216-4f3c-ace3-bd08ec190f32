<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxAgentAirlineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxAgentAirline">
        <id column="agent_airline_id" property="agentAirlineId"/>
        <result column="agent_id" property="agentId"/>
        <result column="airline_id" property="airlineId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        agent_airline_id, agent_id, airline_id
    </sql>

</mapper>
