<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxCityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxCity">
        <id column="city_id" property="cityId"/>
        <result column="country_id" property="countryId"/>
        <result column="state_id" property="stateId"/>
        <result column="city_cname" property="cityCname"/>
        <result column="city_ename" property="cityEname"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_abbr" property="cityAbbr"/>
        <result column="city_type" property="cityType"/>
        <result column="city_status" property="cityStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        city_id, country_id, state_id, city_cname, city_ename, city_code, city_abbr, city_type, city_status
    </sql>

</mapper>
