<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrNmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrNm">
        <id column="pnr_nm_id" property="pnrNmId"/>
        <result column="pnr_id" property="pnrId"/>
        <result column="pnr_index" property="pnrIndex"/>
        <result column="psg_index" property="psgIndex"/>
        <result column="psg_type" property="psgType"/>
        <result column="name" property="name"/>
        <result column="query_name" property="queryName"/>
        <result column="sex" property="sex"/>
        <result column="is_cnin" property="isCnin"/>
        <result column="is_ctc" property="isCtc"/>
        <result column="is_id" property="isId"/>
        <result column="id_reason" property="idReason"/>
        <result column="input_value" property="inputValue"/>
        <result column="psg_ind" property="psgInd"/>
        <result column="create_at_no" property="createAtNo"/>
        <result column="change_at_no" property="changeAtNo"/>
        <result column="change_type" property="changeType"/>
        <result column="pnr_gn_id" property="pnrGnId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_nm_id, pnr_id, pnr_index, psg_index, psg_type, name, query_name, sex, is_cnin, is_ctc, is_id, id_reason, input_value, psg_ind, create_at_no, change_at_no, change_type, pnr_gn_id
    </sql>

</mapper>
