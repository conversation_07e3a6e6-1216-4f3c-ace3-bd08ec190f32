<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrSegMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrSeg">
        <id column="pnr_seg_id" property="pnrSegId" />
        <result column="pnr_id" property="pnrId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="pnr_seg_no" property="pnrSegNo" />
        <result column="flight_no" property="flightNo" />
        <result column="cabin_class" property="cabinClass" />
        <result column="sell_cabin" property="sellCabin" />
        <result column="org" property="org" />
        <result column="dst" property="dst" />
        <result column="action_code" property="actionCode" />
        <result column="seat_number" property="seatNumber" />
        <result column="estimate_off" property="estimateOff" />
        <result column="estimate_arr" property="estimateArr" />
        <result column="share_flight_no" property="shareFlightNo" />
        <result column="pnr_seg_type" property="pnrSegType" />
        <result column="plane_version" property="planeVersion" />
        <result column="recreation" property="recreation" />
        <result column="meal" property="meal" />
        <result column="terminal" property="terminal" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_seg_id, pnr_id, pnr_index, pnr_seg_no, flight_no, cabin_class, sell_cabin, org, dst, action_code, seat_number, estimate_off, estimate_arr, share_flight_no,
pnr_seg_type,plane_version,recreation,meal,terminal,input_value
    </sql>

</mapper>
