package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * PNR全局FP记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_fp")
@ApiModel(value="MnjxPnrFp对象", description="PNR全局FP记录表")
public class MnjxPnrFp extends Model<MnjxPnrFp> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_fp_id", type = IdType.ASSIGN_ID)
    private String pnrFpId;

    @ApiModelProperty(value = "PNR_ID")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "是否婴儿FN记录 0：否 1：是")
    @TableField("is_baby")
    private Integer isBaby;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "支付方式")
    @TableField("pay_type")
    private String payType;

    @ApiModelProperty(value = "货币类型")
    @TableField("currency_type")
    private String currencyType;

    @ApiModelProperty(value = "类型：AD CH IN GM JC")
    @TableField("pat_type")
    private String patType;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.pnrFpId;
    }

}
