package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 改期规则
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_rescheduling_rules")
@ApiModel(value = "MnjxReschedulingRules对象", description = "改期规则")
public class MnjxReschedulingRules extends Model<MnjxReschedulingRules> {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "舱等")
    @TableField("cabin_class")
    private String cabinClass;

    @ApiModelProperty(value = "销售舱位")
    @TableField("sell_cabin")
    private String sellCabin;

    @ApiModelProperty(value = "航班起飞前2小时内折扣")
    @TableField("within_two_hours_discount")
    private Double withinTwoHoursDiscount;

    @ApiModelProperty(value = "航班起飞前2小时（含2）折扣")
    @TableField("two_including_discount")
    private Double twoIncludingDiscount;

    @ApiModelProperty(value = "航班起飞前24小时（含24）折扣")
    @TableField("twenty_four_including_discount")
    private Double twentyFourIncludingDiscount;

    @ApiModelProperty(value = "航班起飞前48小时（含48）折扣")
    @TableField("forty_eight_including_discount")
    private Double fortyEightIncludingDiscount;

    @ApiModelProperty(value = "航班起飞前72小时（含72）折扣")
    @TableField("seventy_two_including_discount")
    private Double seventyTwoIncludingDiscount;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
