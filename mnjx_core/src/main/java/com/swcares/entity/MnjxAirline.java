package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.swcares.core.validator.ValueTypeConstraint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_airline")
public class MnjxAirline extends Model<MnjxAirline> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", hidden = true)
    @TableId(value = "airline_id", type = IdType.ASSIGN_ID)
    @Pattern(regexp = "(\\d+)?", message = "只能是数字")
    private String airlineId;

    @ApiModelProperty(value = "国家表主键ID")
    @TableField("country_id")
    @NotBlank(message = "国家代码不能为空")
    private String countryId;

    @ApiModelProperty(value = "航空公司全称", example = "中国国际航空公司")
    @TableField("airline_full_name")
    @NotBlank(message = "航空公司全称不能为空")
    @Size(max =32, message = "航空公司全称长度最大32")
    private String airlineFullName;

    @ApiModelProperty(value = "航空公司简称 国航")
    @TableField("airline_short_name")
    private String airlineShortName;

    @ApiModelProperty(value = "航空公司英文 Air China Limited")
    @TableField("airline_ename")
    private String airlineEname;

    @ApiModelProperty(value = "二字码 CA")
    @TableField("airline_code")
    @NotBlank(message = "航空二字码不能为空")
    @Size(max = 4, message = "航空二字码长度不合法，最大长度为4位")
    @Pattern(regexp = "[A-Za-z1-9]{2,4}", message = "航司二字码格式不合法")
    private String airlineCode;

    @ApiModelProperty(value = "结算码 786")
    @TableField("airline_settlement_code")
    @NotBlank(message = "结算码不能为空")
    @Size(max = 4, message = "结算码长度不合法，最大长度为4位")
    @Pattern(regexp = "\\d{1,4}", message = "结算码格式不合法")
    private String airlineSettlementCode;

    @ApiModelProperty(value = "联系人")
    @TableField("airline_contact_name")
    private String airlineContactName;

    @ApiModelProperty(value = "联系方式")
    @TableField("airline_contact_phone")
    private String airlineContactPhone;

    @ApiModelProperty(value = "地址")
    @TableField("airline_contact_address")
    private String airlineContactAddress;

    @ApiModelProperty(value = "状态 1,启用 0,停用")
    @TableField("airline_status")
    @ValueTypeConstraint(message = "状态格式不正确")
    private String airlineStatus = "1";

    @ApiModelProperty(value = "航空公司折扣（用于计算票价）", example = "0.5")
    @TableField("airline_code_discount")
    @NotNull(message = "航空公司折扣不为空")
    @DecimalMin(value = "0.00", message = "折扣错误，只能为正数")
    private BigDecimal airlineCodeDiscount;

    @ApiModelProperty(value = "航空公司三字码 中国国际航空-CA/CCA 三字码为CCA")
    @TableField("airline_three_code")
    @NotBlank(message = "航空公司三字码不能为空")
    @Size(max = 4, message = "航空三字码长度不合法，最大长度为4位")
    @Pattern(regexp = "[A-Za-z1-9]{2,4}", message = "航空公司三字码格式不合法")
    private String airlineThreeCode;

    @Override
    protected Serializable pkVal() {
        return this.airlineId;
    }

}
