package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_tk")
public class MnjxPnrTk extends Model<MnjxPnrTk> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_tk_id", type = IdType.ASSIGN_ID)
    private String pnrTkId;

    @ApiModelProperty(value = "PNR ID")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "TK类型")
    @TableField("pnr_tk_type")
    private String pnrTkType;

    @ApiModelProperty(value = "计划出票日期，形如1400")
    @TableField("plan_etdz_date")
    private String planEtdzDate;

    @ApiModelProperty(value = "计划出票时间,形如1400")
    @TableField("plan_etdz_time")
    private String planEtdzTime;

    @ApiModelProperty(value = "出票部门")
    @TableField("etdz_office")
    private String etdzOffice;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.pnrTkId;
    }

}
