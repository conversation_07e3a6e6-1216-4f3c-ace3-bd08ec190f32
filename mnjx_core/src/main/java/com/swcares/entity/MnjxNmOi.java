package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_oi")
public class MnjxNmOi extends Model<MnjxNmOi> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "nm_oi_id", type = IdType.ASSIGN_ID)
    private String nmOiId;

    @ApiModelProperty(value = "姓名ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "OI信息")
    @TableField("oi_info")
    private String oiInfo;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "旧票号")
    @TableField("old_ticket_no")
    private String oldTicketNo;


    @Override
    protected Serializable pkVal() {
        return this.nmOiId;
    }

}
