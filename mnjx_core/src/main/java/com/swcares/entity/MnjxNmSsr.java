package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_ssr")
public class MnjxNmSsr extends Model<MnjxNmSsr> {
    @ApiModelProperty(value = "ID")
    @TableId(value = "nm_ssr_id", type = IdType.ASSIGN_ID)
    private String nmSsrId;

    @ApiModelProperty(value = "旅客ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "航段ID")
    @TableField("pnr_seg_no")
    private Integer pnrSegNo;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "SSR类型")
    @TableField("ssr_type")
    private String ssrType;

    @ApiModelProperty(value = "行动代码:HK NN RR")
    @TableField("action_code")
    private String actionCode;

    @ApiModelProperty(value = "城市对")
    @TableField("org_dst")
    private String orgDst;

    @ApiModelProperty(value = "自由格式文本")
    @TableField("ssr_info")
    private String ssrInfo;

    @ApiModelProperty(value = "航空公司代码")
    @TableField("airline_code")
    private String airlineCode;

    @ApiModelProperty(value = "航班日期")
    @TableField("flt_date")
    private String fltDate;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.nmSsrId;
    }

}
