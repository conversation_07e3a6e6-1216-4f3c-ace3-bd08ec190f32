package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_ct")
public class MnjxNmCt extends Model<MnjxNmCt> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_ct_id", type = IdType.ASSIGN_ID)
    private String pnrCtId;

    @ApiModelProperty(value = "PNR_ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "城市三字码")
    @TableField("city_code")
    private String cityCode;

    @ApiModelProperty(value = "自由文本")
    @TableField("ct_text")
    private String ctText;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;

    @Override
    protected Serializable pkVal() {
        return this.pnrCtId;
    }

}
