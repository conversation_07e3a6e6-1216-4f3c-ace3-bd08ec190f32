package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022-07-07 11:14:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_nm_tn")
@ApiModel(value = "MnjxPnrNmTn对象", description = "")
public class MnjxPnrNmTn extends Model<MnjxPnrNmTn> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "tn_id", type = IdType.ASSIGN_ID)
    private String tnId;

    @ApiModelProperty(value = "成人或儿童旅客ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "婴儿旅客ID")
    @TableField("nm_xn_id")
    private String nmXnId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private int pnrIndex;

    @ApiModelProperty(value = "出票所用打票机ID")
    @TableField("printer_id")
    private String printerId;

    @ApiModelProperty(value = "出票日期时间")
    @TableField("issued_time")
    private String issuedTime;

    @ApiModelProperty(value = "出票人员SI-ID")
    @TableField("issued_si_id")
    private String issuedSiId;

    @ApiModelProperty(value = "出票航司二字码")
    @TableField("issued_airline")
    private String issuedAirline;

    @ApiModelProperty(value = "本次出票的起始票号")
    @TableField("ticket_no_start")
    private String ticketNoStart;

    @ApiModelProperty(value = "本次出票的结束票号")
    @TableField("ticket_no_end")
    private String ticketNoEnd;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;

    @Override
    protected Serializable pkVal() {
        return this.tnId;
    }

}
