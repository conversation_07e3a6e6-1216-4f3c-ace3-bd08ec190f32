package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_rmk")
public class MnjxPnrRmk extends Model<MnjxPnrRmk> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_rmk_id", type = IdType.ASSIGN_ID)
    private String pnrRmkId;

    @ApiModelProperty(value = "PNR ID")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "PNR序号")
    @TableField(value = "pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "备注类型名")
    @TableField("rmk_name")
    private String rmkName;

    @ApiModelProperty(value = "备注内容")
    @TableField("rmk_info")
    private String rmkInfo;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;

    @Override
    protected Serializable pkVal() {
        return this.pnrRmkId;
    }

}
