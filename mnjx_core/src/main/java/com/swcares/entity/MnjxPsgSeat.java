package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_psg_seat")
public class MnjxPsgSeat extends Model<MnjxPsgSeat> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "psg_seat_id", type = IdType.ASSIGN_ID)
    private String psgSeatId;

    @ApiModelProperty(value = "旅客ID")
    @TableField("psg_cki_id")
    private String psgCkiId;

    @TableField("before_seat")
    private String beforeSeat;

    @ApiModelProperty(value = "旅客座位")
    @TableField(value = "psg_seat", updateStrategy = FieldStrategy.IGNORED)
    private String psgSeat;

    @ApiModelProperty(value = "额外占座座位号")
    @TableField("seat_exst")
    private String seatExst;

    @ApiModelProperty(value = "座位状态")
    @TableField(value = "seat_status", updateStrategy = FieldStrategy.IGNORED)
    private String seatStatus;

    @ApiModelProperty(value = "客户偏好")
    @TableField("seat_preference")
    private String seatPreference;

    @Override
    protected Serializable pkVal() {
        return this.psgSeatId;
    }

}
