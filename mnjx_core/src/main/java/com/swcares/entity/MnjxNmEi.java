package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_ei")
@ApiModel(value="MnjxNmEi对象", description="")
public class MnjxNmEi extends Model<MnjxNmEi> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "nm_ei_id", type = IdType.ASSIGN_ID)
    private String nmEiId;

    @ApiModelProperty(value = "旅客ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "签注内容")
    @TableField("ei_info")
    private String eiInfo;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.nmEiId;
    }

}
