package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * T-CARD表T2行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_tcard_section")
@ApiModel(value="MnjxTcardSection对象", description="T-CARD表T2行记录表")
public class MnjxTcardSection extends Model<MnjxTcardSection> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "tcard_section_id", type = IdType.ASSIGN_ID)
    private String tcardSectionId;

    @ApiModelProperty(value = "航班ID")
    @TableField("tcard_id")
    private String tcardId;

    @ApiModelProperty(value = "航节号")
    @TableField("section_no")
    private Integer sectionNo;

    @ApiModelProperty(value = "是否尾部航节：0 否 1是")
    @TableField("is_last_section")
    private String isLastSection;

    @ApiModelProperty(value = "机场代码")
    @TableField("airport_id")
    private String airportId;

    @ApiModelProperty(value = "DEPT离港时间")
    @TableField("dep_time")
    private String depTime;

    @ApiModelProperty(value = "ARR进港时间")
    @TableField("arr_time")
    private String arrTime;

    @ApiModelProperty(value = "进港日期变更：值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值")
    @TableField("arr_date_change")
    private String arrDateChange;

    @ApiModelProperty(value = "离港日期变更：	值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值		离港日期变更：	值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值")
    @TableField("off_date_change")
    private String offDateChange;

    @ApiModelProperty(value = "BRD登机时间")
    @TableField("brd_time")
    private String brdTime;

    @ApiModelProperty(value = "登机时间日期变更")
    @TableField("brd_date_change")
    private String brdDateChange;


    @Override
    protected Serializable pkVal() {
        return this.tcardSectionId;
    }

}
