package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_action_record")
public class MnjxActionRecord extends Model<MnjxActionRecord> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "action_record_id", type = IdType.ASSIGN_ID)
    private String actionRecordId;

    @ApiModelProperty(value = "office主键Id")
    @NotBlank(message = "office主键Id不能为空")
    @TableField("office_id")
    private String officeId;

    @ApiModelProperty(value = "OFFICE号所属机构ID，根据OFFICE_TYPE分别关联到agent表，airline表，airport表")
    @NotBlank(message = "OFFICE号所属机构ID不能为空")
    @TableField("org_id")
    private String orgId;

    @ApiModelProperty(value = "SI工作号 ID")
    @TableField("si_id")
    private String siId;

    @ApiModelProperty(value = "指令执行记录")
    @TableField("action")
    private String action;

    @ApiModelProperty(value = "请求IP")
    @TableField("ip_address")
    private String ipAddress;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;
}
