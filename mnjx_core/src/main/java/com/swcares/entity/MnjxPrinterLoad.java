package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 打票机上票及卸票操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_printer_load")
@ApiModel(value = "MnjxPrinterLoad对象", description = "打票机上票及卸票操作记录表")
public class MnjxPrinterLoad extends Model<MnjxPrinterLoad> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "printer_load_id", type = IdType.ASSIGN_ID)
    private String printerLoadId;

    @ApiModelProperty(value = "上票者工作号ID")
    @TableField("loader_si_id")
    private String loaderSiId;

    @ApiModelProperty(value = "上票时间")
    @TableField("load_date")
    private Date loadDate;

    @ApiModelProperty(value = "操作目标打票机ID")
    @TableField("printer_id")
    private String printerId;

    @ApiModelProperty(value = "起始票号")
    @TableField("ticket_start")
    private Long ticketStart;

    @ApiModelProperty(value = "终止票号")
    @TableField("ticket_end")
    private Long ticketEnd;

    @ApiModelProperty(value = "卸票者工作号ID")
    @TableField("unloader_si_id")
    private String unloaderSiId;

    @ApiModelProperty(value = "卸票时间")
    @TableField("unloader_date")
    private Date unloaderDate;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
