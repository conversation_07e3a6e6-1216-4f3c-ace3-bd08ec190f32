package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_sp_info")
@ApiModel(value="MnjxSpInfo对象", description="")
public class MnjxSpInfo extends Model<MnjxSpInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sp_info_id", type = IdType.ASSIGN_ID)
    private String spInfoId;

    @TableField("pnr_id")
    private String pnrId;

    @TableField("sp_to_crs_pnr")
    private String spToCrsPnr;

    @TableField("sp_from_crs_pnr")
    private String spFromCrsPnr;

    @TableField("at_no")
    private String atNo;


    @Override
    protected Serializable pkVal() {
        return this.spInfoId;
    }

}
