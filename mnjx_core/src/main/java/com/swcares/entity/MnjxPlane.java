package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_plane")
public class MnjxPlane extends Model<MnjxPlane> {
    @ApiModelProperty(value = "ID")
    @TableId(value = "plane_id", type = IdType.ASSIGN_ID)
    private String planeId;

    @ApiModelProperty(value = "飞机号")
    @TableField("plane_no")
    @Size(max = 6, message = "飞机号长度不合法，最大长度为6位")
    private String planeNo;

    @ApiModelProperty(value = "航空公司ID")
    @TableField("airline_id")
    private String airlineId;

    @ApiModelProperty(value = "机型表ID")
    @TableField("plane_model_id")
    private String planeModelId;

    @ApiModelProperty(value = "飞机状态：1运营中/0退出运营")
    @TableField("is_use")
    @NotBlank(message = "飞机状态不能为空")
    private String isUse;

    @ApiModelProperty(value = "采购日期")
    @TableField("purchase_date")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @NotNull(message = "采购日期不能为空")
    private LocalDate purchaseDate;

    @ApiModelProperty(value = "cnd布局表主键ID")
    @TableField("cnd_id")
    private String cndId;

    @Override
    protected Serializable pkVal() {
        return this.planeId;
    }

}
