package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户偏好设置旅客信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sgui_user_preference_passenger_info")
@ApiModel(value="SguiUserPreferencePassengerInfo对象", description="用户偏好设置旅客信息表")
public class SguiUserPreferencePassengerInfo extends Model<SguiUserPreferencePassengerInfo> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "旅客信息ID")
    @TableId(value = "passenger_info_id", type = IdType.ASSIGN_ID)
    private String passengerInfoId;

    @ApiModelProperty(value = "偏好设置ID")
    @TableField("preference_id")
    private String preferenceId;

    @ApiModelProperty(value = "旅客国籍")
    @TableField("passenger_nationality")
    private String passengerNationality;

    @ApiModelProperty(value = "签证签发国")
    @TableField("visa_issue_country")
    private String visaIssueCountry;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;


    @Override
    protected Serializable pkVal() {
        return this.passengerInfoId;
    }

}
