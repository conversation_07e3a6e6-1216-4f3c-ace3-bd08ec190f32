package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户偏好设置检查提示表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sgui_user_preference_check_tip")
@ApiModel(value="SguiUserPreferenceCheckTip对象", description="用户偏好设置检查提示表")
public class SguiUserPreferenceCheckTip extends Model<SguiUserPreferenceCheckTip> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "检查提示ID")
    @TableId(value = "check_tip_id", type = IdType.ASSIGN_ID)
    private String checkTipId;

    @ApiModelProperty(value = "偏好设置ID")
    @TableField("preference_id")
    private String preferenceId;

    @ApiModelProperty(value = "是否检查城市名称")
    @TableField("city_name")
    private Boolean cityName;

    @ApiModelProperty(value = "是否检查机场名称")
    @TableField("airport_name")
    private Boolean airportName;

    @ApiModelProperty(value = "是否检查旅客信息")
    @TableField("passenger_information")
    private Boolean passengerInformation;

    @ApiModelProperty(value = "是否检查含税价格")
    @TableField("price_including_tax")
    private Boolean priceIncludingTax;

    @ApiModelProperty(value = "是否检查航司名称")
    @TableField("airline_name_check_switch")
    private Boolean airlineNameCheckSwitch;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;


    @Override
    protected Serializable pkVal() {
        return this.checkTipId;
    }

}
