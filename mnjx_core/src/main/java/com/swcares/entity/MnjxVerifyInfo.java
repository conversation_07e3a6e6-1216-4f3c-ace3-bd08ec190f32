package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_verify_info")
public class MnjxVerifyInfo extends Model<MnjxVerifyInfo> {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID")
	@TableId(value = "verify_id", type = IdType.ASSIGN_ID)
	private String verifyId;

	@ApiModelProperty(value = "psgCkiId")
	@TableField("psg_cki_id")
	private String psgCkiId;
	
	@ApiModelProperty(value = "相似度")
	@TableField("similarity")
	private String similarity;
	
	@ApiModelProperty(value = "是否验证通过")
	@TableField("validat")
	private String validat;
	
	@ApiModelProperty(value = "验证时间")
	@TableField("verify_time")
	private String verifyTime;
}
