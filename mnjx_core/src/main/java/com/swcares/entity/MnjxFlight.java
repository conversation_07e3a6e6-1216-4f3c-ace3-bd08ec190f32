package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_flight")
public class MnjxFlight extends Model<MnjxFlight> {

    @ApiModelProperty(value = "航班ID")
    @TableId(value = "flight_id", type = IdType.ASSIGN_ID)
    private String flightId;

    @ApiModelProperty(value = "航空公司ID")
    @TableField("airline_id")
    private String airlineId;

    @ApiModelProperty(value = "航班号")
    @TableField("flight_no")
    private String flightNo;

    @ApiModelProperty(value = "航班状态")
    @TableField("flight_status")
    private String flightStatus;

    @ApiModelProperty(value = "电子票航班标识")
    @TableField("is_e")
    private String isE = "E";

    @ApiModelProperty(value = "可利用航班标识")
    @TableField("is_a")
    private String isA;

    @ApiModelProperty(value = "取消标识")
    @TableField("is_x")
    private String isX;

    @ApiModelProperty(value = "手工候补标识")
    @TableField("is_w")
    private String isW;

    @ApiModelProperty(value = "允许ASR舱位")
    @TableField("allow_asr")
    private String allowAsr;
    
    @ApiModelProperty(value = "承运航班号")
    @TableField("carrier_flight")
    private String carrierFlight;
    
    @ApiModelProperty(value = "共享航班状态")
    @TableField("share_state")
	private String shareState;


    @Override
    protected Serializable pkVal() {
        return this.flightId;
    }

}
