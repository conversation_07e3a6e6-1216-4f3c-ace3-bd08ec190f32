package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 行李属性配置表，为PA接收旅客提供行李件数和每件行李最大重量限制
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_config_luggage")
@ApiModel(value = "MnjxConfigLuggage对象", description = "行李属性配置表，为PA接收旅客提供行李件数和每件行李最大重量限制")
public class MnjxConfigLuggage extends Model<MnjxConfigLuggage> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "config_luggage_id", type = IdType.ASSIGN_ID)
    private String configLuggageId;

    @ApiModelProperty(value = "最大支持行李件数：40件")
    @TableField("max_bags")
    private Integer maxBags;

    @ApiModelProperty(value = "单件行李重量最大值：50KG")
    @TableField("singleton_bagges_weight")
    private Integer singletonBaggesWeight;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
