package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_ex_luggage")
@ApiModel(value="MnjxExLuggage对象", description="")
public class MnjxExLuggage extends Model<MnjxExLuggage> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ex_luggage_id", type = IdType.ASSIGN_ID)
    private String exLuggageId;

    @TableField("psg_cki_id")
    private String psgCkiId;

    @TableField("weight")
    private Integer weight;

    @ApiModelProperty(value = "EXBG、EXPC	EXBG最多只能存在一条数据	EXPC可以存在多条")
    @TableField("ex_type")
    private String exType;

    @TableField("lcm")
    private Integer lcm;


    @Override
    protected Serializable pkVal() {
        return this.exLuggageId;
    }

}
