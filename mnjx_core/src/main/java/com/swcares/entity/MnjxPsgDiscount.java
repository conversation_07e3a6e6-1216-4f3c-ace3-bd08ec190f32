package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_psg_discount")
public class MnjxPsgDiscount extends Model<MnjxPsgDiscount> {
    @ApiModelProperty(value = "ID")
    @TableId(value = "psg_discount_id", type = IdType.ASSIGN_ID)
    private String psgDiscountId;

    @TableField("airline_code")
    private String airlineCode;

    @TableField("adult_discount")
    private BigDecimal adultDiscount;

    @TableField("jc_discount")
    private BigDecimal jcDiscount;

    @TableField("gm_discount")
    private BigDecimal gmDiscount;

    @TableField("chd_discount")
    private BigDecimal chdDiscount;

    @TableField("inf_discount")
    private BigDecimal infDiscount;


    @Override
    protected Serializable pkVal() {
        return this.psgDiscountId;
    }

}
