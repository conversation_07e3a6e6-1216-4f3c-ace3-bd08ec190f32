package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户偏好设置航司CTCT表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sgui_user_preference_airline_ctct")
@ApiModel(value="SguiUserPreferenceAirlineCtct对象", description="用户偏好设置航司CTCT表")
public class SguiUserPreferenceAirlineCtct extends Model<SguiUserPreferenceAirlineCtct> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "偏好设置ID")
    @TableField("preference_id")
    private String preferenceId;

    @ApiModelProperty(value = "航司代码")
    @TableField("airline")
    private String airline;

    @ApiModelProperty(value = "CTCT值")
    @TableField("ctct")
    private String ctct;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
