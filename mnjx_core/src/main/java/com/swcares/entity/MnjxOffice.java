package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021-07-30
 */
@ToString
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_office")
public class MnjxOffice extends Model<MnjxOffice> {

    @ApiModelProperty(value = "officeId")
    @TableId(value = "office_id", type = IdType.ASSIGN_ID)
    private String officeId;

    @ApiModelProperty(value = "OFFICE号")
    @NotBlank(message = "OFFICE号不能为空")
    @Size(max = 8, message = "输入的office号超过最大长度8")
    @Pattern(regexp = "[A-Za-z\\d]{1,8}",message = "office号只能为1-8位大小写字母或数字")
    @TableField("office_no")
    private String officeNo;

    @ApiModelProperty(value = "机构类型 0:代理人 , 1:机场 , 2:航空公司")
    @NotBlank(message = "机构类型不能为空")
    @TableField("office_type")
    private String officeType;

    @ApiModelProperty(value = "OFFICE号所属机构主键ID")
    @NotBlank(message = "OFFICE号所属机构主键ID不能为空")
    @TableField("org_id")
    private String orgId;

    @ApiModelProperty(value = "状态 1:启用 , 0:停用")
    @NotBlank(message = "状态不能为空")
    @TableField("office_status")
    private String officeStatus;

    @ApiModelProperty(value = "OFFICE号密码")
//    @NotBlank(message = "OFFICE号密码不能为空")
    @TableField("office_password")
    private String officePassword;

    @Override
    protected Serializable pkVal() {
        return this.officeId;
    }

}
