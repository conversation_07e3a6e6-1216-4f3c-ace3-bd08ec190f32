package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_ticket_operate_record")
@ApiModel(value = "MnjxTicketOperateRecord对象", description = "")
public class MnjxTicketOperateRecord extends Model<MnjxTicketOperateRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ticket_operate_record_id", type = IdType.ASSIGN_ID)
    private String ticketOperateRecordId;
    @ApiModelProperty(value = "航司结算码")
    @TableField("settlement_code")
    private String settlementCode;

    @ApiModelProperty(value = "票号")
    @TableField("ticket_no")
    private String ticketNo;


    @ApiModelProperty(value = "该客票第一航段组客票使用状态")
    @TableField("ticket_status_1")
    private String ticketStatus1;

    @ApiModelProperty(value = "该客票第二航段组客票使用状态")
    @TableField("ticket_status_2")
    private String ticketStatus2;

    @ApiModelProperty(value = "工作号")
    @TableField("si_no")
    private String siNo;

    @ApiModelProperty(value = "退票单号")
    @TableField("refund_no")
    private String refundNo;

    @ApiModelProperty(value = "操作时间")
    @TableField("operate_time")
    private String operateTime;


    @Override
    protected Serializable pkVal() {
        return this.ticketOperateRecordId;
    }

}
