package com.swcares.core.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class Constant {
    /**
     * =====================数字常量区域 start======================
     * 常量0
     */
    public static final int ZERO = 0;
    public static final String STR_ZERO = String.valueOf(ZERO);
    public static final char C_ZERO = '0';
    /**
     * 常量1
     */
    public static final int ONE = 1;
    public static final String STR_ONE = String.valueOf(ONE);
    /**
     * 常量2
     */
    public static final int TWO = 2;
    public static final String STR_TWO = String.valueOf(TWO);

    /**
     * 常量3
     */
    public static final int THREE = 3;
    public static final int MINUS_THREE = -THREE;
    public static final String STR_THREE = String.valueOf(THREE);
    /**
     * 常量4
     */
    public static final int FOUR = 4;

    public static final String STR_FOUR = String.valueOf(FOUR);
    /**
     * 常量6
     */
    public static final int SIX = 6;
    /**
     * 常量10
     */
    public static final int TEN = 10;
    /**
     * 常量11
     */
    public static final int ELEVEN = 11;
    /**
     * 常量12
     */
    public static final int TWELVE = 12;

    /**
     * 常量17
     */
    public static final int FOURTEEN = 14;
    public static final String STR_TWELVE = String.valueOf(TWELVE);

    /**
     * 常量17
     */
    public static final int SEVENTEEN = 17;
    /**
     * 常量30
     */
    public static final int THIRTY = 30;
    /**
     * 常量40
     */
    public static final int FORTY = 40;

    public static final int TWENTY_FOUR = 24;
    public static final int FIFTY_NINE = 59;
    /**
     * 常量60
     */
    public static final int ONT_SECOND = 60;
    /**
     * 常量100
     */
    public static final int ONE_HUNDRED = 100;

    /**
     * 常量一天的时间戳
     */
    public static final long DAY_SECOND_1000 = 86400000L;

    /**
     * 常量一分钟的毫秒数
     */
    public static final int ONE_SECOND_1000 = 60000;

    /**
     * 飞机类型：J喷气式飞机 B空中客机 H直升机 T货机
     */
    public static final String J = "J";
    public static final String B = "B";
    public static final String H = "H";
    public static final String T = "T";
    /**
     * 娱乐标识
     */
    public static final String E = "E";
    public static final String P = "P";
    public static final String A = "A";
    /**
     * 字母
     */
    public static final String Z = "Z";
    /**
     * PW
     */
    public static final String PW = "PW";
    /**
     * HBPW
     */
    public static final String HBPW = "HBPW";

    /**
     * ,符号
     */
    public static final String COMMA = ",";

    /**
     * -符号
     */
    public static final String CROSSBAR = "-";
    /**
     * -符号
     */
    public static final String ADD_SYMBOL = "+";
    /**
     * /符号
     */
    public static final String SLASH = "/";
    /**
     * 类型不存在
     */
    public static final String NO_TYPE = "类型不存在";

    /**
     * 指令格式输入不正确
     */
    public static final String FORMAT = "FORMAT";
    /**
     * 指令执行成功
     */
    public static final String ACCEPTED = "ACCEPTED";
    /**
     * 选择项输入错误
     */
    public static final String OPTION = "OPTION";
    /**
     * 指令执行异常
     */
    public static final String FUNCTION = "FUNCTION";
    /**
     * 舱位等级输入错误或不存在
     */
    public static final String CLASS = "CLASS";
    /**
     * SK/DS 指令
     * 航班日期输入错误
     */
    public static final String DATE = "DATE";
    /**
     * 输入了错误的终端名称
     */
    public static final String DEVICE = "DEVICE";
    public static final String DEVICE_TYPE = "DEVICE TYPE";

    /**
     * 航班号输入错误
     */
    public static final String FLT_NBR = "FLT NBR";
    /**
     * 航班号输入错误
     */
    public static final String FLIGHT_NUMBER = "FLIGHT NUMBER";
    /**
     * 选择项输入错误或不存在
     */
    public static final String ITEM = "ITEM";
    /**
     * BT后要输入行李牌号、行李号输入错误
     */
    public static final String BAG_TAG = "BAGTAG";
    /**
     * 1.PR指令：输入的旅客序列号不正确
     * 2.SEI指令：输入的序号错误
     * 3.CND指令：机型、版本不存在
     */
    public static final String ENTRY_NBR = "ENTRY NBR";
    /**
     * 旅客姓名输入不正确
     */
    public static final String NAMES = "NAMES";
    /**
     * 旅客姓名输入不正确
     */
    public static final String BIRTH = "BIRTH ERROR";
    /**
     * `
     * 航班上输入的旅客不存在
     */
    public static final String NO_RECORD = "NO RECORD";
    /**
     * 旅客未接收
     */
    public static final String NOT_ACCEPTED_PASSENGERS = "NOT ACCEPTED PASSENGERS";
    /**
     * 输入的旅客序列号不正确
     */
    public static final String NUMBER = "NUMBER";
    /**
     * 指令使用级别错误
     */
    public static final String AUTHORITY = "AUTHORITY";
    /**
     * 行李选项输入错误
     */
    public static final String BAG = "BAG";
    /**
     * 行李不一致
     */
    public static final String DELETE_ONE_BAG_ONCE_OR_ALL = "DELETE ONE BAG ONCE OR ALL ";
    /**
     * 儿童数量输入错误
     */
    public static final String CHILD_COUNT_CONFLICT = "CHILD COUNT CONFLICT";
    /**
     * 输入的目的地错误
     */
    public static final String DESTINATION = "DESTINATION";
    /**
     * 输入信息重复
     */
    public static final String DUP = "DUP";
    /**
     * 航班已经完全关闭
     */
    public static final String FLIGHT_CLOSED = "FLIGHT CLOSED";
    /**
     * 航班已经初始化关闭
     */
    public static final String FLIGHT_CLOSED_FOR_INIT = "FLIGHT CLOSED FOR INIT";
    /**
     * 航班已经中间关闭
     */
    public static final String FLIGHT_CLOSED_FOR_LDP = "FLIGHT CLOSED FOR LDP";
    /**
     * 航班已经取消
     */
    public static final String FLT_CANCELED = "FLT CANCELED";

    /**
     * 航班状态OP
     */
    public static final String FLT_OPEN = "ACCEPTED/AIRPORT REOPENED";

    /**
     * 航班状态CI
     */
    public static final String FLT_INITIALLY_CLOSED = "FLT INITIALLY CLOSED";
    /**
     * 航班已经关闭
     */
    public static final String FLT_CLOSED = "FLT CLOSED";
    public static final String CL = "CL";

    public static final String CI = "CI";

    public static final String CC = "CC";

    public static final String CI_FAILED = "CI FAILED";

    public static final String NO_CI = "NO CI";

    public static final String NO_CC = "NO CC";

    public static final String NO_CCL = "NO CCL";

    public static final String FLT_OPEN_FAILED = "FLT OPEN FAILED";

    public static final String FLIGHT_OPEN = "FLIGHT OPEN";


    public static final String CCL_SUCCESS = "ACCEPTED/AIRPORT CLOSED FOR LDP";


    public static final String CCL_REOPENED_SUCCESS = "ACCEPTED/AIRPORT REOPENED FOR LDP";

    public static final String CCL_FAILED = "CCL FAILED";

    public static final String CCL_REOPEN_FAILED = "CCL REOPEN FAILED";

    public static final String CC_SUCCESS = "ACCEPTED/AIRPORT CLOSED";

    public static final String CC_FAILED = "CC FAILED";

    public static final String CC_REOPEN_FAILED = "CC REOPEN FAILED";
    /**
     * 航班需要初始化
     */
    public static final String NEED_INITIALIZE = "NEED INITIALIZE";
    /**
     * 航班上一节点已经关闭
     */
    public static final String PRE_FLT_CLOSED = "PRE_FLT CLOSE";
    /**
     * 航班被保护
     */
    public static final String PROT_COV = "PROT COV";
    /**
     * PA指令输入了错误的旅客范围
     */
    public static final String GROUP = "GROUP";
    /**
     * 输入航段错误
     */
    public static final String ILLEGAL_SEGMENT = "ILLEGAL SEGMENT";
    /**
     * 座位状态是X
     */
    public static final String ALREADY_DONE = "ALREADY DONE";
    /**
     * 序号不正确
     */
    public static final String ELE_NBR = "ELE NBR";
    /**
     * 座位安排错误
     */
    public static final String SEATS = "SEATS";
    /**
     * 旅客已经值
     */
    public static final String PAX_ALREADY_CHICKED_IN = "PAX ALREADY CHICKED-IN";
    /**
     * 输入的航空公司错误或者航空公司数据不存在
     * 1.EX指令
     * 2.cnd指令
     */
    public static final String AIRLINE = "AIRLINE";
    /**
     * 航空公司错误
     */
    public static final String AIRLINE_CODE = "AIRLINE CODE";
    /**
     * 票联标识有误
     */
    public static final String CHECK_COUPON = "CHECK COUPON";
    /**
     * 票号有误
     */
    public static final String CHECK_TKT_NUM = "CHECK TKT NUM";
    /**
     * 票号有误
     */
    public static final String CAN_NOT_REFUND = "CAN NOT PTINT NEW REFUND  \r\n提取的退票单不存在";
    /**
     * 票号状态不匹配
     */
    public static final String STATUS_NOT_ALLOWED = "STATUS CHANGE NOT ALLOWED";
    /**
     * 联票数量不匹配
     */
    public static final String CHECK_CONJUNCTION_NUM = "CHECK_CONJUNCTION_NO_NUM";
    /**
     * 无人陪伴儿童年龄输入错误
     */
    public static final String UM_AGE = "UM AGE";
    /**
     * 没有可删除的行李项
     */
    public static final String NO_BAGS = "NO BAGS";

    /**
     * 没有缺省航班
     */
    public static final String NO_DEFAULT_FLIGHT = "NO DEFAULT FLIGHT";
    /**
     * 机场代码输入错误
     */
    public static final String AIRPORT = "AIRPORT";
    /**
     * 输入城市对错误
     */
    public static final String CITY_PAIR = "CITY PAIR";
    /**
     * 航班号错误
     */
    public static final String FLIGHT = "FLIGHT";
    /**
     * 密码输入错误
     */
    public static final String PROT_SET = "PROT SET";
    /**
     * 有未完成PNR，在退出前必须完成，或放弃
     */
    public static final String PENDING = "PENDING";
    /**
     * 未退出打票机的控制，退出后即可
     */
    public static final String TICKET_PRINTER_IN_USE = "TICKET PRINTER IN USE";
    /**
     * 输入姓名超长
     */
    public static final String NAME_LENGTH = "NAME LENGTH";
    /**
     * 部门代码不正确
     */
    public static final String OFFICE = "OFFICE";
    /**
     * 输入时间不正确
     */
    public static final String TIME = "TIME";
    /**
     * 不能
     */
    public static final String UNABLE = "UNABLE";
    /**
     * FT指令，指令输入正确，系统没有行动(说明当前设置的航班已经为缺省航班)
     */
    public static final String NO_ACTION_INDICATED = "NO ACTION INDICATED";
    /**
     * 城市为起点城市
     */
    public static final String ORIGIN = "ORIGIN";
    /**
     * 登机口错误
     */
    public static final String GATE = "GATE";
    /**
     * 登机口错误
     */
    public static final String STATUS = "STATUS";
    /**
     * 不能给未到旅客加减行李
     */
    public static final String NO_PRESENT_PAX_CONFLICT = "NO PRESENT PAX CONFLICT";
    /**
     * 只能携带一个婴儿
     */
    public static final String ONLY_ONE = "ONLY ONE INFANT ALLOWED PER PASSENGER";

    /**
     * 证件号类型
     */
    public static final String FOID_TYPE_NI = "NI";

    public static final String STATUS_CONFLICT = "旅客状态冲突";

    /**
     * 验证上一条指令是否是PD，RL，RN，FB，FSN，PR，PA，PU，否则的话报错：NO PSR LIST
     */
    public static final String NO_PSR_LIST = "NO PSR LIST";


    /**
     * 输入的城市code错误
     */
    public static final String CITY_CODE_ERR_IN = "CITY CODE ERROR ON INPUT";

    public static final String AV = "AV";
    public static final String SEGMENT = "SEGMENT";
    public static final String SCH_NBR = "**SCH NBR**";
    public static final String FLT_NUMBER = "FLT NUMBER";
    public static final String RR = "RR";
    public static final String HK = "HK";
    public static final String NO_SEATS = "NO SEATS";
    public static final String NOSE = "NO SE";
    public static final String ACTION = "ACTION";
    public static final String LENGTH = "LENGTH";
    public static final String PSGR_ID = "PSGR ID";
    public static final String SEGMENT_ELEMENT_MISSING = "SEGMENT ELEMENT MISSING";
    public static final String GROUPS = "GROUPS";
    public static final String NAME_PRESENT = "NAME PRESENT";
    public static final String DUP_ID = "DUP ID";
    public static final String SIZE = "SIZE";
    /**
     * PNR中的航段不连续
     */
    public static final String CONTINUITY = "CONTINUITY";
    public static final String NO_PNR = "NO PNR";
    public static final String INVALID = "INVALID";
    public static final String INVALID_SSR = "INVALID SSR TYPE";
    public static final String CODE = "CODE";
    public static final String PNR = "PNR";
    public static final String COMPARTMENT = "COMPARTMENT";
    public static final String AMOUNT = "AMOUNT";
    public static final String SEAT_CONFLICT = "SEAT CONFLICT";
    public static final String CABIN_CLASS_ERROR = "CABIN CLASS ERROR";
    public static final String CHECK_BLNK_CODE = "CHECK BLNK CODE";
    public static final String PASSENGER_NO_ET = "PASSENGER NO ET,NOT ALLOW TO CHECK IN";

    /**
     * 无国际航班
     */
    public static final String NO_INTERNATIONAL_FLT = "NO INTERNATIONAL FLT";
    /**
     * 旅客未值机
     */
    public static final String PAX_STATUS_CONFLICT = "PAX STATUS CONFLICT";
    /**
     * 提示使用XEPNR指令直接取消PNR
     */
    public static final String USE_XE_PNR = "USE XE PNR";
    public static final Object TICKET_PRINTER_NOT_EXIST = "TICKET PRINTER NOT EXIST";
    /**
     * 指令格式输入不正确
     */
    public static final String CHECK_FORMAT = "请检查输入格式";
    /**
     * 航班不生效
     */
    public static final String FLT_DATE = "FLT/DATE";
    /**
     * 航节为空或者没有该航班
     */
    public static final String FESTIVAL_IS_EMPTY = "FESTIVAL IS EMPTY";
    /**
     * 常量Y
     */
    public static final String COMMON_Y = "Y";
    /**
     * 常量N
     */
    public static final String COMMON_N = "N";
    /**
     * 常量M
     */
    public static final String COMMON_M = "M";
    /**
     * 常量K
     */
    public static final String COMMON_K = "K";
    /**
     * 常量T
     */
    public static final String COMMON_T = "T";

    /**
     * 常量D
     */
    public static final String TCARD_CYCLE_D = "D";
    /**
     * 常量X
     */
    public static final String TCARD_CYCLE_X = "X";
    /**
     * 旅客未值机
     */
    public static final String NACC = "NACC";
    /**
     * 旅客已值机
     */
    public static final String ACC = "ACC";
    public static final String MOD = "MOD";
    /**
     * 旅客为候补旅客
     */
    public static final String SB = "SB";

    public static final String SE = "SE";
    /**
     * 旅客已删除
     */
    public static final String DL = "DL";

    /**
     * 旅客已登机
     */
    public static final String GT = "GT";
    /**
     * 系统没有该飞机号
     */
    public static final String AIRCRAFT = "AIRCRAFT";

    /**
     * 数据错误。
     */
    public static final String DATA_ERROR = "DATA ERROR";

    /**
     * 需要根据上一个指令结果来查询数据
     */
    public static final String NO_DISPLAY = "NO DISPLAY";

    public static final String NO_DISPLAY_SYMBOL = "**NO DISPLAY**";


    public static final String TICKET_STATUS_OPEN_FOR_USE = "OPEN FOR USE";
    public static final String TICKET_STATUS_VOID = "VOID";
    public static final String TICKET_STATUS_REFOUND = "REFUNDED";
    /**
     * 系统错误
     */
    public static final String ERROR = "ERROR";
    public static final String NO_SSR = "NO SSR";
    public static final String DELETE_SUCCESS = "删除成功";
    public static final String DELETE_FAIL = "删除失败";
    public static final String UPDATE_SUCCESS = "更新成功";
    public static final String UPDATE_FAIL = "更新失败";
    public static final String CREATE_SUCCESS = "新增成功";
    public static final String CREATE_FAIL = "新增失败";
    public static final String RETRIEVE_FAIL = "没有查询到数据";
    public static final String IMPORT_SUCCESS = "导入成功";
    public static final String IMPORT_FAIL = "导入失败";
    public static final String TAKE_EFFECT_SUCCESS = "生效成功";
    public static final String TAKE_EFFECT_FAIL = "生效失败";
    public static final String AUTHORIZATION_SUCCESS = "授权成功";
    public static final String AUTHORIZATION_FAIL = "授权失败";

    /**
     * 过渡区航班
     */
    public static final String FLIGHT_STATUS_STAGED = "STAGED";
    /**
     * 正常航班
     */
    public static final String FLIGHT_STATUS_MASTER = "MASTER";
    public static final String PLANE_MODEL_NOT_EXIST = "Model does not exist";
    public static final String PLANE_NO_EXIST = "The aircraft number already exists";
    public static final String CND_NO_NOT_EXIST = "CND The layout number does not exist";
    public static final String CND_NO_EXIST = "CND号已存在";
    public static final String CREW_NO_NOT_EXIST = "The crew number does not exist";
    public static final String PLANE_NOT_FIND = "未找到此飞机";
    public static final String SEAT_MODEL_NOT_FIND = "未找到此座位模型";
    public static final String AIRLINE_NOT_FIND = "未找到此航空公司";
    public static final String FREQUENTER_CARD_EXIST = "已经存在此常客卡号了";
    public static final String FREQUENTER_CERTIFICATE_NOT_EXIST = "已经存在此证件号的常客了";

    /**
     * OFFICE的启用和禁用状态的默认值
     */
    public static final String OFFICE_ENABLE_STATUS = "1";
    public static final String OFFICE_DISABLE_STATUS = "0";
    /**
     * OFFICE所属机构分类 0:代理人 , 1:机场 , 2:航空公司
     */
    public static final String OFFICE_TYPE_AGENT = "0";
    public static final String OFFICE_TYPE_AIRPORT = "1";
    public static final String OFFICE_TYPE_AIRLINE = "2";
    public static final String OFFICE_TYPE_IS_NOT_EMPTY = "office所属机构类型不能为空";
    public static final String OFFICE_TYPE_ERROR = "请输入正确的机构类型";
    public static final String OFFICE_ORG_ID_IS_NOT_EXIST = "该机构ID不存在";
    public static final String OFFICE_PIRMARY_ID_IS_NOT_EMPTY = "OFFICE主键不能为空";
    public static final String OFFICE_NO_IS_NOT_EMPTY = "officeNo不能为空";
    public static final String OFFICE_NO_EXIST = "officeNo已经存在了";
    public static final String OFFICE_STATUS_IS_NOT_EMPTY = "office状态不能为空";
    public static final String OFFICE_STATUS_ERROR = "请输入正确的office状态";
    public static final String OFFICE_IS_IN_USE_BY_SI = "操作失败，该office正在被工作号使用中，不能停用或删除";
    public static final String OFFICE_IS_IN_USE_BY_PRINTER = "操作失败，该office正在被打票机使用中，不能停用或删除";
    public static final String OFFICE_IS_IN_USE_BY_TICKETLIMITS = "操作失败，该office正在被票号范围使用中，不能停用或删除";
    public static final String OFFICE_IS_IN_ENABLE = "该office还在启用中，不能直接删除";
    public static final String OFFICE_ORG_STATUS_DISABLE = "该机构状态为禁用，不能添加";
    public static final String OFFICE_DISABLE = "停用";
    public static final String OFFICE_ENABLE = "启用";
    public static final String OFFICE_PASSWORD_ERROR = "密码只能包含6-15位大小写字母、数字、特殊符号";

    public static final String ORDER_EXIST = "指令名已存在,请勿重复添加";
    public static final String ORDER_TYPE_ERROR = "指令类型不存在";
    public static final String ORDER_ID_IS_NOT_EMPTY = "指令ID不能为空";
    public static final String ORDER_ID_IS_NOT_EXIST = "指令ID不存在";
    public static final String ORDER_TYPE_IS_NOT_EMPTY = "指令类型不能为空";
    public static final String ORDER_NAME_IS_NOT_EMPTY = "指令名不能为空";
    public static final String PARAM_TYPE_HEADER = "header";
    public static final String PARAM_TYPE_BODY = "body";
    public static final String PARAM_TYPE_PATH = "path";
    public static final String PARAM_TYPE_QUERY = "query";
    public static final String PARAM_TYPE_FORM = "form";

    public static final boolean FALSE = false;
    public static final String SI = "SI";
    /**
     * 半屏行数12行
     */
    public static final int HALF_SCREEN = 11;
    /**
     * 全屏行数
     */
    public static final int FULL_SCREEN = 22;

    public static final String SI_USER_GRP = "USER GRP";
    public static final String SI_SI_DISABLE = "SI DISABLE";
    public static final String OFFICE_USER_PASSWORD_ERROR = "OFFICE 用户名或者密码错误";
    public static final String OFFICE_STATUS_DISABLE = "OFFICE 已经停用";
    public static final String FLIGHT_STATUS_ACTIVE = "ACTIVE";
    public static final String FLIGHT_STATUS_CANCEL = "CANCEL";
    public static final String FLIGHT_STATUS_DELETE = "DELETE";
    public static final String TCARD_CYCLE_ERROR = "航班周期错误：指定必须在3位以内。例如：123/X12/D得格式";

    /**
     * =======================值机状态 start===============================
     * 值机状态:
     * CC :值机完全关闭
     * CI :值机初始关闭
     * CL :值机中间关闭
     * OP :值机开放
     * PC :航班被保护
     * XX :航班取消
     * DL :航班删除，从航班表操作传值
     */
    public static final String CK_STATUS_CC = "CC";
    public static final String CK_STATUS_CI = "CI";
    public static final String CK_STATUS_CL = "CL";
    public static final String CK_STATUS_OP = "OP";
    public static final String CK_STATUS_PC = "PC";
    public static final String CK_STATUS_XX = "XX";
    public static final String CK_STATUS_DL = "DL";
    public static final String CK_STATUS_EC = "EC";

    public static final String BOARD_STATUS_BCL = "BCL";
    public static final String FIGHT_NOT_FIND = "Flight information could not be found";
    public static final String CABIN_STATUS_OPEN = "OPEN";
    public static final String CMD_NOT_SUPPORT = "UNKNOWN COMMAND";
    public static final String DICT_CODE_EXIST = "字典值已经存在，不不要添加";
    public static final String OFFICE_NOT_EXIST = "请确认机构是否存在";
    public static final String SEG_TYPE_SD = "SD";
    public static final String FLIGHT_ERROR = "航班问题";
    public static final String CNY = "CNY";
    public static final String TOTAL = "TOTAL";
    public static final String FIELD_ID = "FIELD ID";
    public static final String CASH_COLLECTION = "CASH COLLECTION (SCNY???.) LOST";
    public static final String COMMISSION = "COMMISSION (C??.??) LOST";
    public static final String DUP_CN_YQ = "DUP CN/YQ";
    public static final String YQ = "YQ";
    /**
     * RMK指令自由文本的长度
     */
    public static final int RMK_LEN_LIMIT = 108;
    /**
     * 登录的提示信息
     */
    public static final String SI_SIGNED_IN_OTHER_STATION = "SI SIGNED IN OTHER STATION";
    /**
     * 参数A：显示PNR的当前内容，如果是团体PNR不显示NM；
     */
    public static final String RT_ARG_A = "A";
    /**
     * 有效航段的最大数量
     */
    public static final long MAX_VALID_SEGMENT_NO = 4;
    public static final String PASSENGER_NOT_FIND = "旅客未找到（自定义错误）";
    public static final String RT_PARAM_A = "A";
    /**
     * 运价模式
     */
    public static final String PAT_MODE_A = "A";
    public static final String BOOKING_SEAT_SUCCESS = "订座成功";
    public static final String BOOKING_SEAT_FAIL = "订座失败";
    public static final String NO = "否";
    public static final String PASSENGER_TYPE_ADULT = "ADULT";
    public static final String PASSENGER_TYPE_CHILD = "CHILD";
    public static final String PASSENGER_TYPE_INFANT = "INFANT";

    public static final String KEY = "key";
    public static final Object USE_F5_SAVE_EXIT = "ET USING F5 KEY TO SAVE/EXIT";
    public static final String STOCK = "STOCK";
    public static final String ACTIVE = "ACTIVE";
    /**
     * 主舱位错误
     */
    public static final String CABIN_ERROR = "CABIN ERROR";
    /**
     * 提示旅客已登机，需先拉下
     */
    public static final String BOARDING_PSG = "BOARDING PSG";
    public static final String CHECK_TICKET = "CHECK TICKET";
    public static final String ONLY_TAXES_CN_YQ_OC_ARE_ALLOWED = "ONLY TAXES CN/YQ/OC ARE ALLOWED";
    /**
     * 国内航空公司代码
     */
    public static final String[] AIRLINES = {
            "3U", "8L", "9C", "9D", "9H", "A6", "AD", "BK", "CA", "CN",
            "CZ", "DR", "DZ", "EU", "FM", "FU", "G5", "GJ", "GS", "GT",
            "GX", "GY", "HO", "HU", "JD", "JD", "JR", "KN", "KY", "LT",
            "MF", "MU", "NS", "OQ", "PN", "QW", "RY", "SC", "TV", "UQ",
            "Y8", "ZH"
    };
    public static final String[] FLTSTATUS = {"S", "O", "C", "I", "N", "CL"};
    /**
     * PSM类型
     */
    public static final String[] PSM_TYPES = {
            "UMNR", "BLND", "STCR", "WCHR", "WCOB", "WCMP", "WCHS", "WCHC", "WCBW", "WCBD", "DEAF", "JMP", "STCR", "PSM",
            "MOML", "CHML", "AVML", "BBML", "DBML", "VGML", "BLML", "VOML", "GFML", "HFML", "LPML", "NBML", "ORML",
            "FPML", "HNML", "KSML", "LCML", "LFML", "LSML", "NLML", "RVML", "SFML", "TDML", "VJML", "VLML", "PRML"
    };
    /**
     * SSR类型
     */
    public static final Map<String, String> SSR_MAP = new HashMap<>();
    /**
     * 特殊餐食对照中文
     */
    public static final Map<String, String> SPML_CN_MAP = new HashMap<>();
    /**
     * 特殊餐食
     */
    public static final Map<String, String> SPML_MAP = new HashMap<>();
    /**
     * 舱位等级
     * CABIN_CLASS_F 头等舱
     * CABIN_CLASS_C 商务舱
     * CABIN_CLASS_Y 经济舱
     */
    public static final String CABIN_CLASS_F = "F";
    public static final String CABIN_CLASS_C = "C";
    public static final String CABIN_CLASS_Y = "Y";
    public static final String CABIN_INIT_LENTH = "2";
    public static final String SEGMENT_SEAT = "[0-9]{1,3}";
    /**
     * 值机数据还原存储类型
     */
    public static final String INSERT = "I";
    public static final String DELETE = "D";
    public static final String UPDATE = "U";
    /**
     * 还原操作需要用到的实体名
     */
    public static final String MNJX_PNR_NM = "MnjxPnrNm";
    public static final String MNJX_PNR = "MnjxPnr";
    public static final String MNJX_NM_SSR = "MnjxNmSsr";
    public static final String MNJX_PSG_CKI = "MnjxPsgCki";
    public static final String MNJX_PSG_SEAT = "MnjxPsgSeat";
    public static final String MNJX_PSG_OPERATE_RECORD = "MnjxPsgOperateRecord";
    public static final String MNJX_PSG_CKI_OPTION = "MnjxPsgCkiOption";
    public static final String MNJX_NM_XN = "MnjxNmXn";
    public static final String MNJX_SEAT = "MnjxSeat";
    public static final String MNJX_LUGGAGE = "MnjxLuggage";
    public static final String MNJX_PNR_CT = "MnjxPnrCt";
    public static final String MNJX_PNR_GN = "MnjxPnrGn";
    public static final String MNJX_PLAN_SECTION = "MnjxPlanSection";
    public static final String MNJX_PNR_NM_TICKET = "MnjxPnrNmTicket";
    public static final String MNJX_FREQUENTER = "MnjxFrequenter";
    public static final String MNJX_PNR_NM_UM = "MnjxPnrNmUm";
    public static final String MNJX_NM_OSI = "MnjxNmOsi";
    public static final String MNJX_PNR_RECORD = "MnjxPnrRecord";
    public static final String MNJX_EX_LUGGAGE = "MnjxExLuggage";
    public static final String MNJX_UNPACK_INFO = "MnjxUnpackInfo";
    public static final String MNJX_PNR_NM_TN = "MnjxPnrNmTn";
    public static final String MNJX_PRINTER = "MnjxPrinter";
    /**
     * 机构类型标识:  0:代理人 ， 1：机场 ， 2：航司
     */
    public static final String ORG_AGENT = "0";
    public static final String ORG_AIRPORT = "1";
    public static final String ORG_AIRLINE = "2";
    /**
     * 政采权限标识 ： 1：有  ， 0：无
     */
    public static final Integer IS_GP = 1;
    public static final Integer IS_NOT_GP = 0;
    /**
     * 航班计划取消结果
     */
    public static final String CANCEL_SUCCESS = "取消航班计划成功";
    public static final String CANCEL_FAIL = "取消航班计划失败";
    /**
     * 航班计划初始化结果
     */
    public static final String INITIAL_SUCCESS = "初始化成功";
    public static final String INITIAL_FAIL = "初始化失败";
    /**
     * 取消航班计划初始化结果
     */
    public static final String CANCEL_INITIAL_SUCCESS = "取消初始化成功";
    public static final String CANCEL_INITIAL_FAIL = "取消初始化失败";
    public static final String NO_OFFICE = "部门号错误";
    public static final String ADULT_TYPE = "0";
    public static final String CHILD_TYPE = "1";
    public static final String UM_TYPE = "2";
    public static final String INF_TYPE = "3";
    public static final String VIP_TYPE = "VIP";
    public static final String UPDATE_PLANE_FAIL = "修改飞机号失败，检查是否存在飞机号";
    public static final String NO_PLAN_FLIGHT = "未找到该航班计划";
    public static final String DISCOUNT_LESS_THAN_ZERO = "折扣率错误，不能出现负数";
    /**
     * CNTD指令提示输入英文
     */
    public static final String PLS_INPUT_ENGLISH = "PLS INPUT IN ENGLISH NAME";
    public static final String TOO_MANY_ITEMS_OF_RESULT = "TOO MANY ITEMS OF RESULT";
    /**
     * CNTD指令提示无记录
     */
    public static final String NO_INFO = "NO INFO";
    /**
     * DDI指令TKT统一回显
     */
    public static final String BSP = "BSP";
    /**
     * 票证使用状态：IU：in use 正在使用中
     */
    public static final String IU = "IU";
    /**
     * CND ID重复
     */
    public static final String CND_ID_REPEAT = "CND_ID重复";
    /**
     * XC指令提示
     */
    public static final String INPUT = "INPUT";
    /**
     * EX指令：没有查询到对应机型
     */
    public static final String NIL = "NIL";
    /**
     * CND指令：没有查询到飞机座位布局表数据
     */
    public static final String NOT_CND_LIST = "没有查询到飞机座位布局表数据";
    /**
     * FI指令：城市或机场名输入不正确
     * ACRT指令查询部门号不存在的情况（航信的回显）
     */
    public static final String CITY = "CITY";

    //*********************************eterm 指令**************************************************************
    /**
     * 没有查询到该部门号下的工作号信息
     */
    public static final String NO_OFFICE_SI = "NO FIND SI INFO";
    /**
     * 票号范围错误
     */
    public static final String TKT_NBR = "TKT NBR";
    /**
     * 票号范围不在已有票号范围内
     */
    public static final String NOT_WITHIN_LIMIT = "TICKETS NOT ISSUED TO OFFICE";
    /**
     * 已被使用
     */
    public static final String BE_USED = "TICKETS HAVE BEEN USED OR JUST IN USE BY OTHER";
    /**
     * 没有打票机
     */
    public static final String NO_PRINTER = "NO PRINTER";
    /**
     * 上票失败
     */
    public static final String VOTE_ON_FAIL = "VOTE ON FAILED";
    /**
     * 销售舱位有重复
     */
    public static final String SELL_CABIN_REPEAT = "销售舱位存在重复字母";
    /**
     *
     */
    public static final String DESTINATION_CITY = "DESTINATION CITY";
    public static final String ROW = "ROW";
    public static final String CMD_BF = "BF";
    public static final String BF_IG = "IG";
    public static final String BF_DL = "DL";
    /**
     * av查询排序 P --  显示结果按照起飞时间先后顺序排列
     */
    public static final String AV_OPTION_P = "P";
    /**
     * av查询排序 A --  显示结果按照到达时间先后顺序排列
     * ACRT指令：标识工作号是否活跃也是用的A字符
     */
    public static final String AV_OPTION_A = "A";
    /**
     * av查询排序 E --  显示结果按照飞行时间由短到长排列
     */
    public static final String AV_OPTION_E = "E";
    /**
     * SK/DS/av指令：查询经停 D 直达
     */
    public static final String AV_STOP_D = "D";
    /**
     * av查询经停 D 直达
     */
    public static final String AV_STOP_D_DETAIL = "DIRECT ONLY";
    /**
     * SK/DS/av指令 查询经停 N 无经停航班
     */
    public static final String AV_STOP_N = "N";
    /**
     * av查询经停 N 无经停航班
     */
    public static final String AV_STOP_N_DETAIL = "NON-STOPS ONLY";
    /**
     * av查询结果座位数大于9显示A
     */
    public static final String SEAT_A = "A";
    /**
     * av查询结果座位数等于0显示S
     */
    public static final String SEAT_S = "S";
    /**
     * av ra没有先执行av，或已执行过一次ra
     */
    public static final String AV_RA_FAIL = "**TXN SEQUENCE**";
    /**
     * av 城市对或 av ra没有结果
     */
    public static final String AV_NORMAL_NO_RESULT = "**NO ROUTING**";
    /**
     * av 航班号没有结果
     */
    public static final String AV_FLIGHT_NO_RESULT = "**NO-OP**";
    /**
     * ff 未检索到该日航班飞行计划记录
     * FI指令：查询仅返回当天日期前三天，至未来的航班信息，再更早的航班信息查询返回：NO-OP
     */
    public static final String NO_OP = "NO-OP";
    /**
     * IT指令由于本系统不支持国际航班查询，固统一返回错误信息
     */
    public static final String IT_NOT_AN_OAG_LEG_AVAIL_FLIGHT = "**NOT AN OAG LEG-AVAIL FLIGHT**";
    /**
     * SK/DS指令
     */
    public static final String NO_ROUTING = "**NO ROUTING**";
    /**
     * CNTD选项错误提示
     */
    public static final String SUBFUNCTION = "SUBFUNCTION";
    /**
     * 中文正则
     */
    public static final String CHINESE_REG = "[\\u4e00-\\u9fa5]+";
    /**
     * 日期转换错误
     */
    public static final String INVALID_DATE = "INVALID DATE - CANNOT BE PROPERLY CONVERTED";
    /**
     * NM输入的姓名组合错误
     */
    public static final String ERROR_MIX_NAME = "NO FIRST NAME ALLOWED WITH CHINESE NAMES";
    /**
     * NM CHD标识
     */
    public static final String CHD = "CHD";
    /**
     * 同姓多名输入数量不匹配
     */
    public static final String NM_COUNT_ERROR = "SEATS/INITIALS/SLASHES";
    /**
     * DATE
     */
    public static final String DATE_ERROR = "DATE";
    /**
     * XN添加婴儿所选的旅客不是成人时
     */
    public static final String PSG_TYPE_ERROR = "PSG NOT AN ADULT";
    /**
     * CND使用不能修改
     */
    public static final String CND_USED = "CND已被使用，无法修改";
    /**
     * NM指令：被修改的姓名组标识
     */
    public static final String BE_UPDATED = "BE UPDATED";
    /**
     * NM指令：修改后的姓名组标识
     */
    public static final String TO_UPDATE = "TO UPDATE";
    /**
     * 飞机状态 1使用状态 0未使用状态 （1运营中  0退出运营）
     */
    public static final String IS_USE = "1";
    public static final String UN_USE = "0";
    public static final String UNABLE_TO_SELL = "UNABLE TO SELL.PLEASE CHECK THE AVAILABILITY WITH \"AV\" AGAIN";
    public static final String SD_UNABLE_TO_SELL = "UNABLE TO SELL.PLEASE CHECK THE AVAILAVILITY WITH \"AV\" AGAIN";
    public static final String CHECK_TKT_TIME = "CHECK TKT TIME";
    public static final String FLT_BOOKING_LIMITED = "FLT BOOKING LIMITED,PLS USE ONE TRANSMIT TO BUILD A WHOLE PNR";
    /**
     * 修改姓名标识
     */
    public static final String NM_CHANGE_TYPE = "C";
    /**
     * 删除标识
     */
    public static final String DELETE_TYPE = "X";
    /**
     * FU指令：格式中没输入选项报错
     */
    public static final String NO_CHANGE = "NO CHANGE";
    /**
     * FU指令
     * 无效航班
     */
    public static final String INVALID_FLT = "INVALID FLT";
    /**
     * FU指令
     * 航班保护状态 PC EC
     */
    public static final String PC = "PC";
    public static final String EC = "EC";
    public static final String OP = "OP";
    /**
     * FU指令
     * 没有航节信息
     */
    public static final String NOT_PLAN_SECTION = "NOT PLAN SECTION";
    /**
     * CT
     */
    public static final String INVALID_CHARACTER = "INVALID CHARACTER";
    /**
     * CT
     */
    public static final String ELEM_SIZE = "ELEM SIZE";
    /**
     * 不支持的SSR FOID 类型
     */
    public static final String INVALID_FOID = "INVALID FOID";
    /**
     * 行动代码
     */
    public static final String ACTION_CODE = "ACTION CODE";
    public static final String ACTION_CODE_HK = "HK";
    public static final String ACTION_CODE_NN = "NN";
    public static final String ACTION_CODE_DK = "DK";
    public static final String ACTION_CODE_KK = "KK";
    public static final String ACTION_CODE_HN = "HN";
    public static final String ACTION_CODE_DR = "DR";
    public static final String ACTION_CODE_KL = "KL";
    public static final String ACTION_CODE_RR = "RR";
    public static final String ACTION_CODE_XX = "XX";
    /**
     * SSR 类型 FOID
     */
    public static final String SSR_TYPE_FOID = "FOID";
    /**
     * SSR 类型 INFT
     */
    public static final String SSR_TYPE_INFT = "INFT";
    /**
     * SSR 类型 UMNR
     */
    public static final String SSR_TYPE_UMNR = "UMNR";
    /**
     * SSR 类型 SPML
     */
    public static final String SSR_TYPE_SPML = "SPML";
    /**
     * SSR 类型 FQTV
     */
    public static final String SSR_TYPE_FQTV = "FQTV";
    /**
     * SSR 类型 MEDA
     */
    public static final String SSR_TYPE_MEDA = "MEDA";
    /**
     * SSR FOID的3种输入
     */
    public static final String CERTIFICATE_TYPE_NI = "NI";
    public static final String CERTIFICATE_TYPE_PP = "PP";
    public static final String CERTIFICATE_TYPE_ID = "ID";
    /**
     * SSR INFT 婴儿姓名格式错误，没有/
     */
    public static final String INFT_NAME = "INFANT NAME";
    /**
     * 没有XN项
     */
    public static final String NO_XN = "NO XN";
    /**
     * 生日格式错误
     */
    public static final String BIRTHDAY_ERROR = "BIRTHDAY(DDMMMYY),PLS";
    /**
     * SSR 类型 CHLD
     */
    public static final String SSR_TYPE_CHLD = "CHLD";
    /**
     * 没有CHD标识
     */
    public static final String NOT_CHD = "NOT CHD";
    /**
     * UMNR必须是自动添加，不允许手动添加
     */
    public static final String UMNR_MUST_BE_AUTO = "UMNR REQUEST MUST BE AUTO-FORMAT";
    /**
     * 不允许输入自由文本
     */
    public static final String CAN_NOT_INPUT_FREETEXT = "TEXT NOT ALLOWED";
    /**
     * 不允许输入2个餐食信息
     */
    public static final String ONLY_ONE_MEAL = "DOUBLE MEAL REQUEST IS NOT ALLOWED";
    /**
     * SSR类型不存在
     */
    public static final String SERVICE_TYPE = "SERVICE TYPE";
    /**
     * NM添加时人数大于团队旅客数
     */
    public static final String SEATS_NAMES = "SEATS/NAMES";
    /**
     * 有删除未提交的姓名或团队
     */
    public static final String NAME_CANCELLATION = "NAME CANCELLATION";
    /**
     * PNR未提交状态 OP
     */
    public static final String PNR_OP = "OP";
    /**
     * PNR已提交状态 CO
     */
    public static final String PNR_CO = "CO";
    /**
     * PNR已删除状态 DEL
     */
    public static final String PNR_DEL = "DEL";
    public static final String SS_DATE_ERROR = "请一次完成PNR并封口(@或\\或DZ指令)";
    /**
     * PNR已被控制，不能修改其他项
     */
    public static final String PNR_IN_CONTROL = "SIMULTANEOUS MODIFICATION - REENTER MODIFICATIONS";
    /**
     * 如果10分钟内未封口就自动将该PNR标记为释放状态，用户此时再来封口，报错
     */
    public static final String PNR_MAX_TIME = "MAX TIME FOR EOT - IGNORE PNR AND RESTART";
    /**
     * 封口参数K
     */
    public static final String AT_K = "K";
    /**
     * 封口时检查NAME
     */
    public static final String CHECK_NAME = "CHECK NAME";
    /**
     * 封口时检查航段组
     */
    public static final String CHECK_SEG = "CHECK SEGMENT";
    /**
     * OSI CTCT或CTCM信息组数量少于1
     */
    public static final String PNR_NO_CT_CM = "PLEASE INPUT OSI (AIRLINE) CTCT OR OSI (AIRLINE) CTCM";
    /**
     * OSI CTCT
     */
    public static final String OSI_CTCT = "CTCT";
    /**
     * OSI CTCM
     */
    public static final String OSI_CTCM = "CTCM";
    /**
     * CT信息组数量小于1
     */
    public static final String PNR_NO_CT = "CHECK CONTACT AGENCY CTC";
    /**
     * CT信息组数量大于1，但是CT 'T' 信息组数量少于1
     */
    public static final String PNR_NO_CT_T = "CHECK AGENCY CTC";
    /**
     * 如果没有关联的SSR INFT信息组
     */
    public static final String PNR_INFT_MISSING = "SSR INFT MISSING";
    /**
     * 多航段时空间不连续
     */
    public static final String CHECK_CONTINUITY = "CHECK CONTINUITY";
    /**
     * 转机时间小于0
     */
    public static final String CHECK_CONNECTION = "CHECK CONNECTION";
    /**
     * TK类型 TL
     */
    public static final String TK_TYPE_TL = "TL";
    /**
     * AV返回日期错误
     */
    public static final String AV_DATE = "**DATE**";
    /**
     * 如果PNR组无GN信息行且存在任意非婴儿旅客无关联的SSR FOID身份信息
     */
    public static final String NO_VALID_FOID = "PLEASE INPUT VALID IDENTITY INFORMATION";
    /**
     * 需要封口或取消
     */
    public static final String NEED_EOT = "NEED EOT -- 需要封口(@)或还原(IG)";
    /**
     * ETDZ指令出票：票已经出完的提示
     */
    public static final String PNR_TICKETED = "PNR TICKETED";
    /**
     * 打票机已上票号范围不合规
     */
    public static final String PRINTER_TICKET_LIMITS_ERROR = "PRINTER TICKET LIMITS ERROR";
    /**
     * ETDZ指令：打票机与si工作号未建立控制连接提示
     */
    public static final String INACTIVE = "INACTIVE";
    /**
     * 打票机状态 UP
     */
    public static final String UP = "UP";
    /**
     * ETDZ指令：判断打票机状态错误的提示
     */
    public static final String DEVICE_STATUS = "DEVICE STATUS";
    /**
     * 打票机属性
     */
    public static final String TAT_ET = "TAT/ET";
    /**
     * ETDZ指令：打票机属性不正确提示
     */
    public static final String DEVICE_ATTRIBUTE = "DEVICE ATTRIBUTE";
    /**
     * 打票机输入状态错误提示
     */
    public static final String INPUT_INACTIVE = "INPUT INACTIVE";
    /**
     * ETDZ指令调用at封口指令
     */
    public static final String AT = "@";
    /**
     * 有效航段数要小于或者等于5
     */
    public static final String SEG_SIZE_ERROR = "ERROR,SEGMENTS <=5 FOR DOMESTIC TICKETS";
    /**
     * ETDZ指令：检查XN项INFT信息不全提示
     */
    public static final String XN_INFT_ERROR = "PLEASE INPUT SSR INFT AND NEED AIRLINE CONFIRM IT";

    public static final String COLON = ":";

    /**
     * NM项
     */
    public static final String PNR_NM = "NM";
    /**
     * SEG项
     */
    public static final String PNR_SEG = "SEG";
    /**
     * OSI项
     */
    public static final String PNR_OSI = "OSI";
    /**
     * NM OSI项
     */
    public static final String PNR_NM_OSI = "NM OSI";
    public static final String NM_OSI = "NMOSI";
    /**
     * CT项
     */
    public static final String PNR_CT = "CT";
    /**
     * NM CT项
     */
    public static final String PNR_NM_CT = "NM CT";
    public static final String NM_CT = "NMCT";
    /**
     * TK项
     */
    public static final String PNR_TK = "TK";
    /**
     * TK指令类型 TL/T
     */
    public static final String PNR_TK_T = "T";
    /**
     * RMK项
     */
    public static final String PNR_RMK = "RMK";
    /**
     * NM RMK项
     */
    public static final String PNR_NM_RMK = "NM RMK";
    public static final String NM_RMK = "NMRMK";
    /**
     * GN项
     */
    public static final String PNR_GN = "GN";
    /**
     * FC项
     */
    public static final String PNR_FC = "FC";
    /**
     * NM FC项
     */
    public static final String PNR_NM_FC = "NM FC";
    public static final String RT_NM_FC = "NMFC";
    /**
     * SSR项
     */
    public static final String PNR_SSR = "SSR";
    /**
     * FN项
     */
    public static final String PNR_FN = "FN";
    /**
     * NM FN项
     */
    public static final String PNR_NM_FN = "NM FN";
    public static final String NM_FN = "NMFN";
    /**
     * EI项
     */
    public static final String PNR_EI = "EI";
    /**
     * NM EI
     */
    public static final String PNR_NM_EI = "NM EI";
    public static final String NM_EI = "NMEI";
    /**
     * OI项
     */
    public static final String PNR_OI = "OI";
    /**
     * TN项
     */
    public static final String PNR_TN = "TN";
    /**
     * XN项
     */
    public static final String PNR_XN = "XN";
    /**
     * FP项
     */
    public static final String PNR_FP = "FP";
    /**
     * NM FP项
     */
    public static final String PNR_NM_FP = "NM FP";
    public static final String NM_FP = "NMFP";
    /**
     * TC项
     */
    public static final String PNR_TC = "TC";
    /**
     * FC
     */
    public static final String DECIMAL = "DECIMAL";
    /**
     * FC
     */
    public static final String FC_TYPE = "FC TYPE";
    /**
     * FC
     */
    public static final String ITINERARY_DOES_NOT_MATCH_FC = "ITINERARY DOES NOT MATCH FC";
    /**
     * FC
     */
    public static final String AIRLINE_CODE_DOES_NOT_MATCH_ITINERARY = "AIRLINE CODE DOES NOT MATCH ITINERARY";
    public static final String CURRENCY = "CURRENCY";
    /**
     * TK指令：本系统仅支持自动出票，TK/T项只能由ETDZ指令自动生成，不接受手动输入，参数是T报错
     */
    public static final String MANUAL_TICKET_NOT_ALLOWED = "MANUAL TICKET NOT ALLOWED";
    /**
     * 要求PNR只允许存在一项TK
     */
    public static final String DUP_TK = "DUP TK";
    /**
     * ETDZ指令：判断Fn项
     */
    public static final String INCOMPLETE_PNR_FN = "INCOMPLETE PNR/FN";
    /**
     * ETDZ指令：判断婴儿的Fn项
     */
    public static final String INCOMPLETE_PNR_FN_IN = "INCOMPLETE PNR/FN/IN";
    /**
     * ETDZ指令：判断Fn项 FN/IN项反查XN项校验
     */
    public static final String INCOMPLETE_PNR_XN = "INCOMPLETE PNR/XN";
    /**
     * ETDZ指令：判断婴儿的Fn项 FN/IN项反查XN项校验
     */
    public static final String INCOMPLETE_PNR_XN_PN = "INCOMPLETE PNR/XN/PN";
    /**
     * ETDZ指令：判断FC项
     */
    public static final String INCOMPLETE_PNR_FC = "INCOMPLETE PNR/FC";
    /**
     * ETDZ指令：判断婴儿的FC项
     */
    public static final String INCOMPLETE_PNR_FC_IN = "INCOMPLETE PNR/FC/IN";
    /**
     * ETDZ指令：判断FP项
     */
    public static final String INCOMPLETE_PNR_FP = "INCOMPLETE PNR/FP";
    /**
     * ETDZ指令：判断婴儿的FP项
     */
    public static final String INCOMPLETE_PNR_FP_IN = "INCOMPLETE PNR/FP/IN";
    /***
     * RMK类型编码：军警残记录行
     */
    public static final String RMK_TYPE_GMJC = "GMJC";
    public static final String GM = "**GM";
    public static final String JC = "**JC";
    /***
     /***
     * RMK类型编码：自由文本记录行
     */
    public static final String RMK_TYPE_FREE = "FREE";
    /**
     * 成人标识
     */
    public static final String ADL = "ADL";
    public static final String INF = "INF";
    public static final String ETDZ_OPTION = "出票选项不正确";
    /**
     * pat指令
     */
    public static final String PAT_OPTION_ERROR = "PAT选项错误";
    public static final String NO_SEGMENT = "NO SEGMENT";
    /**
     * 有未封口的新增姓名或修改姓名
     */
    public static final String NAME_CHANGE = "NAME CHANGE";
    /**
     * 离港团名-组成字符
     */
    public static final char[] LETTERS = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
    public static final String SEG_EXPIRED = "航段过期";
    public static final String GN_ISSUE_TIME = "TKT TIME RESTRICTION  DZ NOT ALLOWED";
    /**
     * 字符串拼接
     */
    public static final String TWO_STR_MIX_SPLIT_SLASH = "{}/{}";
    /**
     * SY S选项
     */
    public static final String SY_S = "S";
    /**
     * SY Z选项
     */
    public static final String SY_Z = "Z";
    /**
     * SY E选项
     */
    public static final String SY_E = "E";
    /**
     * SY T选项
     */
    public static final String SY_T = "T";
    /**
     * SY B选项
     */
    public static final String SY_B = "B";
    /**
     * SY A选项
     */
    public static final String SY_A = "A";
    /**
     * 字母
     */
    public static final String LETTER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    /**
     * 超过剩余座位数
     */
    public static final String OVER_SEATS = "OVER SEATS";
    /**
     * CABIN
     */
    public static final String CABIN = "CABIN";
    /**
     * BN
     */
    public static final String BN = "BN";
    /**
     * SN
     */
    public static final String SN = "SN";
    /**
     * RL
     */
    public static final String RL = "RL";
    /**
     * TN
     */
    public static final String TN = "TN";
    /**
     * CN
     */
    public static final String CN = "CN";
    /**
     * NI
     */
    public static final String NI = "NI";
    /**
     * PP
     */
    public static final String PP = "PP";
    /**
     * ID
     */
    public static final String ID = "ID";
    /**
     * NM
     */
    public static final String NM = "NM";
    public static final String TICKET_NUMBER = "TICKET NUMBER";
    /**
     * DETR 查询出票office的参数项
     */
    public static final String K = "K";
    public static final String NOT_TICKET_NO = "ET TICKET NUMBER IS NOT EXIST";
    public static final String FC = "FC";
    public static final String NM_FC = "NM FC";
    public static final String X = "X";
    public static final String OTHER = ".";
    public static final String F = "F";
    public static final String Y = "Y";
    public static final String TOO_TICKET = "TOO MANY TICKET ENTRY";
    public static final String TICKET_NUMBER_NOT_FOUND = "TICKET NUMBER NOT FOUND";
    /**
     * ddi 电子客票的授权航司展示字符# （空格为拼接时的分割字符）
     */
    public static final String WELL_NUMBER = "# ";
    /**
     * PA输入序号没在PD列表内
     */
    public static final String NUMBER_ERROR = "NUMBER ERROR";
    /**
     * 当前日期大于起飞时间
     */
    public static final String LOCATION_TRAVEL_TIME_ZERO = "LOCATION TRAVEL TIME 0";
    public static final String DUP_IS_FOUND = "DUP IS FOUND";
    public static final String ITEM_CONFLICT = "ITEM CONFLICT";
    public static final String WEIGHT_NOT_ALLOWED = "WEIGHT NOT ALLOWED";
    public static final String MAXIMUM_BAGTAGS_EXCEEDED = "MAXIMUM BAGTAGS EXCEEDED";
    public static final String BAGGAGE_WEIGHT = "***PLEASE CHECK THE BAGGAGE WEIGHT INPUT!*** ";
    public static final String LENFTH_TOO_LONG = "LENFTH TOO LONG";
    public static final String STCR_NOT_ALLOWED = "STCR NOT ALLOWED";
    public static final String NOT_PRESENT_PAX_CONFLICT = "NOT PRESENT PAX CONFLICT ";
    /**
     * PA 接收的旅客不是O联程旅客
     */
    public static final String TRANSFER_FLIGHT_NBR = "TRANSFER FLIGHT NBR";
    /**
     * PA 数字和旅客数不匹配
     */
    public static final String GENDER_CONFLICT = "GENDER CONFLICT";
    public static final String NAME_REQUIRED = "NAME REQUIRED";
    public static final String CHILD_INFANT = "CHILD CAN NOT BE ACCEPTED WITH INFANT";
    /**
     * SNR座位与旅客舱位不匹配
     */
    public static final String SEATING_CONFLICT = "SEATING CONFLICT";
    public static final String UNABLE_TO_RESERVE_SEATS = "UNABLE TO RESERVE SEATS";
    /**
     * PA 婴儿数量不能超过1
     */
    public static final String ONLY_ONE_INF = "ONLY ONE INFANT CAN BE ADDED AT A TIME";
    /**
     * 无陪旅客
     */
    public static final String UM = "UM";
    /**
     * AGE
     */
    public static final String AGE = "AGE";
    /**
     * PA 如果选择项同时含有CHD，INF并且指令是按照CHD,INF顺序时，提示“CHILD CAN NOT BE ACCEPTED WITH INFANT ”。
     */
    public static final String CHD_CAN_NOT_WITH_INF = "CHILD CAN NOT BE ACCEPTED WITH INFANT";
    /**
     * PA 接收INF，该旅客没有携带婴儿时 INFANT COUNT CONFLICT
     */
    public static final String INF_COUNT_CONFLICT = "INFANT COUNT CONFLICT";
    /**
     * PA 接收输入指令不允许同时存在 1-3 和 ;
     */
    public static final String RANGE_PSGR = "CANNOT INPUT RANGE WITH INDIVIDUAL PSGR NBRS";
    /**
     * PA 接收旅客序号超过31
     */
    public static final String EXCESS_SURNAMES = "EXCESS SURNAMES";
    /**
     * PA 接收旅客数不能多余40个
     */
    public static final String TOO_MANY_PASSENGERS = "TOO MANY PASSENGERS";
    /**
     * PA 接收行李件数超过最大值
     */
    public static final String OVER_MAX_BAG = "MAXIMUM BAGTAGS EXCEEDED";
    /**
     * PA 接收行李重量超过最大值
     */
    public static final String OVER_MAX_BAG_WEIGHT = "***PLEASE CHECK THE BAGGAGE WEIGHT INPUT!***";
    /**
     * 票状态 CHECKED IN
     */
    public static final String CHECKED_IN = "CHECKED IN";
    /**
     * 座位状态 ：可利用座位
     */
    public static final String ASTERISK = "*";
    /**
     * 座位状态 ：为其他航段保留的座位
     */
    public static final String SEAT_STATUS_O = "O";
    /**
     * 座位状态 ：为本航段保留的座位
     */
    public static final String SEAT_STATUS_A = "A";
    /**
     * 座位状态 ：锁定（不可利用）座位
     */
    public static final String SEAT_STATUS_X = "X";
    /**
     * 座位状态 ：最后可利用座位，*用完后才可以用
     */
    public static final String SEAT_STATUS_C = "C";
    /**
     * 座位状态 ：已有旅客占用
     */
    public static final String SEAT_STATUS_POINT = ".";
    /**
     * 座位状态 ：VIP留座
     */
    public static final String SEAT_STATUS_D = "D";
    /**
     * 座位状态 ：ASR订座名单中保留座
     */
    public static final String SEAT_STATUS_V = "V";
    /**
     * 座位状态 ：可利用的无伴儿童座位
     */
    public static final String SEAT_STATUS_U = "U";
    /**
     * 座位状态 ：可利用的摇篮座位
     */
    public static final String SEAT_STATUS_B = "B";
    /**
     * 座位状态 ：团体留座
     */
    public static final String SEAT_STATUS_R = "R";
    /**
     * 座位状态 ：靠背不可移动的座位
     */
    public static final String SEAT_STATUS_SLASH = "/";
    /**
     * 座位状态 ：RS指令保留的团体座位
     */
    public static final String SEAT_STATUS_G = "G";
    /**
     * 座位状态 ：为未到旅客保留座位
     */
    public static final String SEAT_STATUS_P = "P";
    /**
     * 座位状态 ：转港占用（锁定）区
     */
    public static final String SEAT_STATUS_T = "T";

    /**
     * RA指令：释放所有类型的保留座位（P或R或D）
     */
    public static final String SEAT_TYPE_ALL = "ALL";

    /**
     * RA指令：释放所有团体留座(R)
     */
    public static final String SEAT_TYPE_RES = "RES";

    /**
     * RA指令：释放所有要客留座(D)
     */
    public static final String SEAT_TYPE_ASR = "ASR";

    /**
     * RA指令：释放所有值机留座(P)
     */
    public static final String SEAT_TYPE_CRS = "CRS";

    /**
     * 删除座位数错误
     */
    public static final String CONFLICT = "CONFLICT";
    /**
     * XE SSR CHLD判断FC FN
     */
    public static final String PLS_DEL_FN_FC = "PLS DELETE FN/FC FIRST";
    /**
     * XE PNR@ 返回
     */
    public static final String PNR_WAS_CANCELLED = "THIS PNR WAS ENTIPRELY CANCELLED";
    public static final String PNR_CHECKED_CAN_NOT_CANCELLED = "THIS PNR HAS BEEN CHECKED CAN NOT BE CANCELLED";
    public static final String ONE_OR_MORE_ET_RELEASED = "ONE OR MORE ET RECORDS RELEASED";
    public static final String PNR_CANCELLED = "PNR CANCELLED";
    public static final String PNR_NEED_ES = "NEED ES";
    public static final String PNR_NOT_ALLOWED_TO_XE = "NOT ALLOWED TO XE PSPT/FOID";
    public static final String PNR_DEL_SSR_TKNE_FIRST = "儿童旅客如需删除儿童标识 请先删除SSR TKNE";
    /**
     * 行李要么全部删除，要么只能删除一件
     */
    public static final String ALL_BAG_DELETE = "ONE BAG ONE TIME OR ALL";
    public static final String NO_FRAV = "**NO FRAV**";
    /**
     * 表示旅客已经拉下
     */
    public static final String PASSENGER_ALREADY_DELETED = "PASSENGER ALREADY DELETED";
    /**
     * 选择项不在当前旅客已有选择项范围内
     */
    public static final String ITEM_NOT_FOUND = "ITEM NOT FOUND";
    public static final String GENDER_DELETION_REQUIRES_PAX_DELETION = "GENDER DELETION REQUIRES PAX DELETION";
    /**
     * 表示不能删除出港联程航班
     */
    public static final String OUTBOUND_DELETE_UNABLE = "OUTBOUND DELETE UNABLE";
    public static final String BAG_TAG_MISMATCH = "BAG/TAG MISMATCH";
    public static final String CHECK_BAG_WEIGHT = "CHECK BAG WEIGHT";
    public static final String BAGGAGE_DESTINATION_CONFLICT = "BAGGAGE DESTINATION CONFLICT";
    /**
     * x行李重量是否匹配，不匹配报错
     */
    public static final String THE_ACTUAL_WEIGHT_IS = "THE ACTUAL WEIGHT IS";
    /**
     * 需要行李牌
     */
    public static final String BAGTAG = "BAGTAG";
    public static final String ELECTRONIC_TICKETING_UPDATE_NOT_ALLOWED = "ELECTRONIC TICKETING UPDATE NOT ALLOWED";
    /**
     * 行李目的地不正确
     */
    public static final String BAG_TAG_DESTINATION = "BAGTAG DESTINATION";
    /**
     * 轮椅类型只允许存在一个
     */
    public static final String DUP_WHEELCHAIR = "WHEELCHAIR HAS BEEN EXISTENCE, PLEASE PW FIRST";
    /**
     * 在AEC换飞机才能换cnd号，否则就报错
     */
    public static final String PLS_USE_STANDARD_AEC_PROCEDURE_FOR_INITIALIZED_FLIGHT = "PLS USE STANDARD AEC PROCEDURE FOR INITIALIZED FLIGHT";
    /**
     * 支付方式为婴儿
     */
    public static final String IN = "IN";
    /**
     * 支付方式为儿童
     */
    public static final String CH = "CH";
    /**
     * 婴儿的税费显示
     */
    public static final String EXEMPT = "EXEMPT";
    /**
     * 旅客已登机
     */
    public static final String PAX_ALREADY_BOARDED = "PAX ALREADY BOARDED";
    public static final String INVALID_BN = "INVALID BN";
    public static final String NO_INF = "NO INF";

    /**
     * 特餐不存在的情况
     */
    public static final String CHECK_INPUT_TEXT = "CHECK INPUT TEXT";
    public static final String SSR_TYPE_BLND = "BLND";
    public static final String SSR_TYPE_MOML = "MOML";
    public static final String SSR_TYPE_DEAF = "DEAF";
    public static final String SSR_TYPE_WCHR = "WCHR";
    public static final String SSR_TYPE_WCHS = "WCHS";
    public static final String SSR_TYPE_WCHC = "WCHC";
    public static final String SSR_TYPE_WCBD = "WCBD";
    public static final String SSR_TYPE_VJML = "VJML";
    public static final String RMK_TYPE_ICS = "ICS";
    public static final String RMK_TYPE_SP = "SP";
    public static final String RMK_TYPE_TJ = "TJ";
    public static final String RMK_TYPE_TR = "TR";
    public static final String RMK_TYPE_CMSA = "CMSA";
    public static final String TICKET_STATUS_CHECK_IN = "CHECK_IN";
    public static final String TICKET_STATUS_LIFT_OR_BOARDED = "LIFT/BOARDED";
    public static final String TICKET_STATUS_USED_OR_FLOWN = "USED/FLOWN";
    public static final String TICKET_STATUS_SUSPENDED = "SUSPENDED";
    public static final String TICKET_STATUS_EXCHANGED = "EXCHANGED";
    public static final String TICKET_STATUS_AIRPORT_CNTL = "AIRPORT CNTL";
    public static final String TICKET_STATUS_CPN_NOTE = "CPN NOTE";
    public static final String TICKET_STATUS_FIM_EXCH = "FIM EXCH";
    /**
     * 配餐数据错误
     */
    public static final String CLASS_GROUP = " CLASS-GROUP";
    public static final String SSR_FREE_TEXT = "TAKECARE";
    public static final int FIVE = 5;

    /**
     * 身份证号正则
     * 2 12345 19 93 12 31 111 x
     */
    public static final Pattern CERTIFICATE_PATTERN = Pattern.compile("^[1-9]\\d{5}((18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31))\\d{3}[0-9Xx]$");

    /**
     * FOID类型验证
     * FOID 3U HK/NI210321199312121313/P1
     */
    public static final Pattern FOID_PATTERN = Pattern.compile("^FOID\\s" +
            "([A-Z0-9]{2})\\s" +
            "([A-Z]+)(/)?([A-Z]{2})(\\w+)(/P(\\d+))?$");
    public static final byte ONE_HUNDRED_AND_TWENTY_EIGHT = (byte) 128;
    public static final int EIGHTEEN = 18;
    /**
     * 输入的票号不对，只输入了前两位，结算码没有输入完整
     */
    public static final String TIKET_NUMBER_AIRLINE_CODE = "TIKET NUMBER - AIRLINE CODE";
    /**
     * 退票，打票机不匹配或者不存在
     */
    public static final String Z_OPTIN_MUST_USE_ET_DEVICE = "Z OPTIN MUST USE ET DEVICE";
    /**
     * 退票，票号不存在
     */
    public static final String TKT_NOT_FUND = "TKT# NOT FUND";
    public static final String TKT_NOT_OPEN_FOR_USE_PLEASE_CHECK_TKT = "TKT NOT OPEN FOR USE,PLEASE CHECK TKT";
    /**
     * 没找到pnr
     */
    public static final String NOT_MATCHED_PNR = "NOT MATCHED PNR";
    /**
     * PNR状态:已删除状态DEL
     */
    public static final String DEL = "DEL";
    public static final String REFUND_SECTOR_IS_NOT_EXIST_MANUAL_REFUND_REQUIRED = "REFUND SECTOR IS NOT EXIST MANUAL REFUND REQUIRED";
    public static final String TKNE = "TKNE";
    public static final String TKT_NOT_ET = "TKT# NOT ET";
    public static final String NOT_EXIST = "NOT EXIST";
    public static final String REFUNDED = "REFUNDED";
    public static final String D = "D";
    public static final String OPEN = "OPEN";
    public static final String PSG_ID = "PSG ID";
    public static final String NOMATCH = "NOMATCH";
    public static final String SEAT_UNAVAILABLE = "SEAT UNAVAILABLE";
    public static final String SEAT_OCCUPIED = "SEAT OCCUPIED";
    public static final String SEAT_RESERVED = "SEAT RESERVED";
    public static final String BLOCKED_SEAT = "BLOCKED SEAT";
    public static final String REFD = "REFD";
    public static final String CLASS_GROUP_SPECIFICATION_REQUIRED = "CLASS-GROUP SPECIFICATION REQUIRED";
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;

    public static final int NINE = 9;
    public static final String NOT_A_HOST_FLIGHT = "NOT A HOST FLIGHT";
    public static final int TWENTY = 26;
    public static final int NINETY_NINE = 99;
    public static final int FIVE_HUNDRED_AND_TWELVE = 512;
    public static final int FORTY_EIGHT = 48;
    public static final int FIVE_HUNDRED_AND_ELEVEN = 511;
    public static final String CKI_STATUS_SB = "SB";
    public static final String CMD_HBJC = "HBJC";
    public static final String CMD_JC = "JC";
    public static final String CMD_PN = "PN";
    public static final String CMD_PB = "PB";
    public static final String CMD_PF = "PF";
    public static final String CMD_PL = "PL";
    public static final String CMD_PG = "PG";
    public static final String CMD_SY = "SY";
    public static final String CMD_BAB = "BAB";
    public static final String CMD_BDB = "BDB";
    public static final String CMD_PW = "PW";
    public static final String CMD_HBPA = "HBPA";
    public static final String PARAM_ABC = "ABC";
    public static final String PARAM_B = "B";
    public static final String CMD_FB = "FB";
    public static final String CMD_FSN = "FSN";
    public static final String PARAM_O = "O";
    public static final String CMD_RL = "RL";
    public static final String CMD_RN = "RN";
    public static final String CMD_PR = "PR";
    public static final String CMD_ETKD = "ETKD";
    public static final String CKI_STATUS_ACC = "ACC";
    public static final String CKI_STATUS_GT = "GT";
    public static final String CKI_STATUS_DL = "DL";
    public static final String CKI_STATUS_NACC = "NACC";
    public static final int NINETEEN = 19;

    /**
     * 初始化状态为已初始化的航班
     */
    public static final String I = "I";
    public static final String S = "S";
    public static final String O = "O";
    public static final String C = "C";
    public static final String N = "N";
    public static final String L = "L";
    public static final String R = "R";
    public static final String V = "V";

    public static final String TEXEMPTCN = "TEXEMPTCN";
    public static final String TEXEMPTYQ = "TEXEMPTYQ";
    public static final String INFT = "INFT";
    public static final String FORMAT_ERROR = "FORMAT ERROR";
    public static final String SSR_TYPE_JMP = "JMP";
    public static final String SSR_TYPE_STCR = "STCR";
    public static final String SSR_TYPE_PSM = "PSM";
    public static final String HB_TYPE_NREC = "NREC";
    public static final String SSR_TYPE_MSG = "MSG";
    public static final String SSR_TYPE_PIL = "PIL";
    public static final String SSR_TYPE_PETC = "PETC";
    public static final String SSR_TYPE_BSCT = "BSCT";
    public static final String CMD_NM = "NM";
    public static final String CMD_NNM = "NNM";
    public static final String OSI_INPUT = "PLEASE INPUT OSI CTCT/CTCM FOR OLD PNR PSG";
    public static final String SEGMENT_ERROR = "SEGMENT ERROR";
    public static final String NO_PREVIOUS_BOARDING_PASS_ISSUED = "NO PREVIOUS BOARDING PASS ISSUED";
    public static final String RETRIEVE_SUCCESS = "查询成功";
    public static final String PASSENGER_HAS_NO_BAGS = "PASSENGER HAS NO BAGS";
    public static final String BAGS_NOT_ALLOWED_FOR_POOLED_DONOR = "BAGS NOT ALLOWED FOR POOLED DONOR";
    public static final String CHECK_EXST_WEIGHT = "CHECK EXST WEIGHT";

    /**
     * SSR类型
     */
    private static final String[] SSR_TYPES = {
            "ADTK", "AVIH", "BIKE", "BIRS", "BLND", "BSCT", "BULK", "TWOV", "UMNR", "TKTL",
            "CBBG", "CKIN", "COUR", "DEAF", "DEPA", "DEPU", "DOCA", "DOCO", "STCR", "TKNA",
            "DOCS", "EXST", "FOID", "FQTR", "FQTS", "FQTU", "FQTV", "FRAG", "SMSW", "SPEQ",
            "GMJC", "GPST", "GRPF", "GRPS", "INFT", "LANG", "FRAV", "TKNE", "TKNM", "TKNR",
            "MAAS", "MEDA", "MODL", "NSSA", "NSSB", "NSST", "NSSW", "SMSA", "SMSB", "SMST",
            "OTHS", "PCTC", "PETC", "PSPT", "RLOC", "RQST", "SEAT", "SEMN", "SLPR",
            "WCBD", "WCBW", "WCHC", "WCHR", "WCHS", "WCMP", "WCOB", "WEAP", "XBAG", "CHLD",
            "MOML", "CHML", "AVML", "BBML", "DBML", "VGML", "BLML", "FPML", "HNML", "KSML",
            "LCML", "LFML", "LSML", "NLML", "RVML", "SFML", "TDML", "VJML", "VLML", "VOML",
            "GFML", "HFML", "LPML", "NBML", "ORML", "PRML"
    };
    public static final String PLEASE_RELEASE_THE_SEGMENT_BOOKINT_BEFORE_REFUND_THE_TICKET = "PLEASE RELEASE THE SEGMENT BOOKINT BEFORE REFUND THE TICKET";
    /**
     * 退票航段显示
     */
    public static final String COUPON_NO1 = "1200";
    public static final String COUPON_NO = "1000";
    public static final String HAVE_NO_AUTHORITY_TO_REFUND_OTHEROFFICE_TICKET = "Have No Authority to Refund OtherOffice Ticket";
    public static final String GROUP_PNR_MANUAL_REFUND_REQUIRED = "GROUP PNR,Manual refund required.";
    public static final String REISSUED_TICKET_HAS_BEEN_PARTIALLY_USED_MANUAL_REFUND_REQUIRED = "Reissued Ticket has been partially used. Manual refund required.";


    static {
        // 特别餐
        SPML_MAP.put("SPML", "SPML");
        SSR_MAP.put("SPML", "SPML");
        SPML_CN_MAP.put("SPML", "特别餐");
        // 亚洲素餐
        SPML_MAP.put("AVML", "AVML");
        SSR_MAP.put("AVML", "AVML");
        SPML_CN_MAP.put("AVML", "亚洲素餐");
        // 婴儿餐
        SPML_MAP.put("BBML", "BBML");
        SSR_MAP.put("BBML", "BBML");
        SPML_CN_MAP.put("BBML", "婴儿餐");
        // 胃溃疡餐
        SPML_MAP.put("BLML", "BLML");
        SSR_MAP.put("BLML", "BLML");
        SPML_CN_MAP.put("BLML", "胃溃疡餐");
        // 小孩餐
        SPML_MAP.put("CHML", "CHML");
        SSR_MAP.put("CHML", "CHML");
        SPML_CN_MAP.put("CHML", "小孩餐");
        // 糖尿病餐
        SPML_MAP.put("DBML", "DBML");
        SSR_MAP.put("DBML", "DBML");
        SPML_CN_MAP.put("DBML", "糖尿病餐");
        // 鲜水果餐
        SPML_MAP.put("FPML", "FPML");
        SSR_MAP.put("FPML", "FPML");
        SPML_CN_MAP.put("FPML", "鲜水果餐");
        // 无谷类餐
        SPML_MAP.put("GFML", "GFML");
        SSR_MAP.put("GFML", "GFML");
        SPML_CN_MAP.put("GFML", "无谷类餐");
        // 高纤维餐
        SPML_MAP.put("HFML", "HFML");
        SSR_MAP.put("HFML", "HFML");
        SPML_CN_MAP.put("HFML", "高纤维餐");
        // 印度餐
        SPML_MAP.put("HNML", "HNML");
        SSR_MAP.put("HNML", "HNML");
        SPML_CN_MAP.put("HNML", "印度餐");
        // 以色列餐
        SPML_MAP.put("KSML", "KSML");
        SSR_MAP.put("KSML", "KSML");
        SPML_CN_MAP.put("KSML", "以色列餐");
        // 低热能餐
        SPML_MAP.put("LCML", "LCML");
        SSR_MAP.put("LCML", "LCML");
        SPML_CN_MAP.put("LCML", "低热能餐");
        // 低脂肪餐
        SPML_MAP.put("LFML", "LFML");
        SSR_MAP.put("LFML", "LFML");
        SPML_CN_MAP.put("LFML", "低脂肪餐");
        // 低蛋白餐
        SPML_MAP.put("LPML", "LPML");
        SSR_MAP.put("LPML", "LPML");
        SPML_CN_MAP.put("LPML", "低蛋白餐");
        // 低盐餐
        SPML_MAP.put("LSML", "LSML");
        SSR_MAP.put("LSML", "LSML");
        SPML_CN_MAP.put("LSML", "低盐餐");
        // 穆斯林餐
        SPML_MAP.put("MOML", "MOML");
        SSR_MAP.put("MOML", "MOML");
        SPML_CN_MAP.put("MOML", "穆斯林餐");
        // 无牛肉餐
        SPML_MAP.put("NBML", "NBML");
        SSR_MAP.put("NBML", "NBML");
        SPML_CN_MAP.put("NBML", "无牛肉餐");
        // 无乳糖餐
        SPML_MAP.put("NLML", "NLML");
        SSR_MAP.put("NLML", "NLML");
        SPML_CN_MAP.put("NLML", "无乳糖餐");
        // 东方餐
        SPML_MAP.put("ORML", "ORML");
        SSR_MAP.put("ORML", "ORML");
        SPML_CN_MAP.put("ORML", "东方餐");
        // 低嘌呤餐
        SPML_MAP.put("PRML", "PRML");
        SSR_MAP.put("PRML", "PRML");
        SPML_CN_MAP.put("PRML", "低嘌呤餐");
        // 生食素餐
        SPML_MAP.put("RVML", "RVML");
        SSR_MAP.put("RVML", "RVML");
        SPML_CN_MAP.put("RVML", "生食素餐");
        // 海鲜餐
        SPML_MAP.put("SFML", "SFML");
        SSR_MAP.put("SFML", "SFML");
        SPML_CN_MAP.put("SFML", "海鲜餐");
        // 素餐(无奶油的)
        SPML_MAP.put("VGML", "VGML");
        SSR_MAP.put("VGML", "VGML");
        SPML_CN_MAP.put("VGML", "素餐(无奶油的)");
        // 素餐
        SPML_MAP.put("VJML", "VJML");
        SSR_MAP.put("VJML", "VJML");
        SPML_CN_MAP.put("VJML", "素食申请");
        // 西方素食申请
        SPML_MAP.put("VLML", "VLML");
        SSR_MAP.put("VLML", "VLML");
        SPML_CN_MAP.put("VLML", "西方素食申请");
        // 东方素食申请
        SPML_MAP.put("VOML", "VOML");
        SSR_MAP.put("VOML", "VOML");
        SPML_CN_MAP.put("VOML", "东方素食申请");
        // 初学走路的孩子的餐食
        SPML_MAP.put("TDML", "TDML");
        SSR_MAP.put("TDML", "TDML");
        SPML_CN_MAP.put("TDML", "初学走路的孩子的餐食");

        SSR_MAP.put("TKNE", "TKNE");
        SSR_MAP.put("FOID", "FOID");
        SSR_MAP.put("WCHC", "WCHC");
        SSR_MAP.put("WCHR", "WCHR");
        SSR_MAP.put("WCHS", "WCHS");
        SSR_MAP.put("WCBD", "WCBD");
        SSR_MAP.put("WCBW", "WCBW");
        SSR_MAP.put("WCMP", "WCMP");
        SSR_MAP.put("WCOB", "WCOB");
        SSR_MAP.put("FQTV", "FQTV");
        SSR_MAP.put("BSCT", "BSCT");
        SSR_MAP.put("CHLD", "CHLD");
        SSR_MAP.put("UMNR", "UMNR");
        SSR_MAP.put("INFT", "INFT");
        SSR_MAP.put("VIP", "VIP");
        // 聋哑
        SSR_MAP.put("DEAF", "DEAF");
        // 盲人
        SSR_MAP.put("BLND", "BLND");
        // 升舱
        SSR_MAP.put("UPG", "UPG");
        // 降舱
        SSR_MAP.put("DNG", "DNG");
        SSR_MAP.put("PSM", "PSM");
        SSR_MAP.put("MSG", "MSG");
        SSR_MAP.put("PIL", "PIL");
        // 折椅
        SSR_MAP.put("JMP", "JMP");
        // 担架
        SSR_MAP.put("STCR", "STCR");
        // 拒绝入境
        SSR_MAP.put("INAD", "INAD");
        SSR_MAP.put("DEPA", "DEPA");
        SSR_MAP.put("GMJC", "GMJC");
        SSR_MAP.put("DEPU", "DEPU");
        SSR_MAP.put("TWOV", "TWOV");
        SSR_MAP.put("AVIH", "AVIH");
        SSR_MAP.put("PETC", "PETC");
        SSR_MAP.put("SLPR", "SLPR");
        SSR_MAP.put("WEAP", "WEAP");
        SSR_MAP.put("MEDA", "MEDA");
    }

    /**
     * 检查SSR类型
     *
     * @param airCode 航空公司代码
     * @return 类型结果
     */
    public static boolean checkIsExistsSsrType(String airCode) {
        boolean res = false;
        for (String str : SSR_TYPES) {
            if (str.equalsIgnoreCase(airCode.trim())) {
                res = true;
                break;
            }
        }
        return res;
    }

    /**
     * 检查航空公司代码 是否存在
     *
     * @param airCode 航空公司代码
     * @return 是否存在
     */
    public static boolean checkIsExistsAirCode(String airCode) {
        boolean res = false;
        for (String str : AIRLINES) {
            if (str.equalsIgnoreCase(airCode.trim())) {
                res = true;
                break;
            }
        }
        return res;
    }


    /**
     * TRI
     * 客票状态不为OPEN FOR USE
     */
    public static final String SEGMENT_STATUS_OF_01D_TICKET_IS_INCALID = "SEGMENT STATUS OF 01D TICKET IS INCALID";

    /**
     * TRI
     * 不是同一个office操作
     */
    public static final String HAVE_NO_AUTHORITY = "Have No Authority";

    /**
     * TRI
     */
    public static final String SYSTEM_ERROR = "SYSTEM ERROR";

    /**
     * 航段判断
     */
    public static final String NEW_TKT_AND_OLD_TKT_SHOULD_BE_CONSISTENT_FOR_REVA = "NEW TKT AND OLD TKT SHOULD BE CONSISTENT FOR REVA";

    /**
     * 团队
     */
    public static final String NOT_SUPPORT_GROUP = "NOT SUPPORT GROUP";

    /**
     * 不允许更换承运人，请手工处理
     */
    public static final String CARRIER_CHANGE_IS_NOT_ALLOWED = "不允许更换承运人,请手工处理.";

    /**
     * 多名旅客未输入PSGR ID
     */
    public static final String MULTIPLE_PASSENGER_NEED_PSGR_ID = "MULTIPLE PASSENGER NEED PSGR ID";

    /**
     * 输入的旅客id不存在
     */
    public static final String PSGR_ID_ERROR_OR_NAME_NOT_FUND = "PSGR ID ERROR OR NAME NOT FUND";

    /**
     *
     */
    public static final String NO_ELIGIBLE_NEW_FREIGHT_RATE = "没有符合条件的新运价,请手工处理";

    public static final String NO_DATA = "NO DATA";

    public static final String TOO_MANY_FLIGHTS = "TOO MANY FLIGHTS TO DISPLAY, PLEASE USE A SMALLER TIME RANGE";

    /**
     * 已退票
     */
    public static final String TICKET_REFUNDED = "TICKET REFUNDED";

    /**
     * 逾重行李EXBG
     */
    public static final String EX_EXBG = "EXBG";

    /**
     * 逾重行李EXPC
     */
    public static final String EX_EXPC = "EXPC";

    /**
     * 降舱
     */
    public static final String DNG = "DNG";

    /**
     * 升舱
     */
    public static final String UPG = "UPG";

    /**
     * 升降舱错误提示
     */
    public static final String INVALID_UPG_DNG = "INVALID UPG/DNG";

    public static final String MODIFICATION_WOULD_CAUSE_STANDBYING_URES = "MODIFICATION WOULD CAUSE STANDBYING URES";
    public static final String MODIFICATION_WOULD_CAUSE_STANDBYING_WTL = "MODIFICATION WOULD CAUSE STANDBYING WTL";

    public static final String GS_NOT_AVAILABLE = "GS NOT AVAILABLE";

    public static final String DL_GS_NOT_AVAILABLE = "ET PROCESSING IN PROGRESS\r\n\r\n\r\nGS NOT AVAILABLE";

    public static final String BAGS_NOT_ALLOWED_IF_PAX_TO_BE_STANDBIED = "BAGS NOT ALLOWED IF PAX TO BE STANDBIED";

    public static final String MODIFICATION_NOT_POSSIBLE = "MODIFICATION NOT POSSIBLE";

    public static final String BAGTAG_REPEATED = "BAGTAG REPEATED";

    public static final String SO = "SO";

    /**
     * 航班不存在
     */
    public static final String FLIGHT_NOT_FOUND = "FLIGHT NOT FOUND";
    public static final String NO_SCHEDULE = "NO SCHEDULE";
    public static final String PARAM_ERROE = "指令参数错误";

    /**
     * BSCT第一个数字必须为1
     */
    public static final String ONLY_ONE_BSCT = "ONLY ONE INFANT CAN BE ADDED AT A TIME";

    /**
     * PETC第一个数字必须小于2
     */
    public static final String ONLY_TWO_PETC = "ONLY TWO PETC CAN BE ADDED AT A TIME";

    public static final String WEIGHT = "WEIGHT";

    public static final String SEATS_NOT_ENOUGH = "SEATS NOT ENOUGH";

    /**
     * 免费改期、升舱
     */
    public static final String TNKE = "TNKE";

    /**
     * 收费改期、升舱
     */
    public static final String OI = "OI";

    public static final String STANDBYING_STL = "MODIFICATION WOULD CAUSE STANDBYING STL";

    public static final String ACCEPTANCE_REJECTED = "ACCEPTANCE REJECTED";

    /**
     * 打票机没有票再执行卸票的提示
     */
    public static final String CHECK_INPUT_FORMAT = "请检查输入格式";

    /**
     * CC 关闭的时间不能在航班起飞时间之前
     */
    public static final String CC_TIME_MUST_BE_LATER_THAN_ED_TIME = "CC TIME MUST BE LATER THAN ED TIME";

    /**
     * 电子客票号错误
     */
    public static final String TKT_NUMBER_ERROR = "TKT NUMBER ERROR";

    /**
     * 免费改期标识
     */
    public static final String AUTOMATIC = "AUTOMATIC";

    /**
     * 登机牌打印模式开关
     */
    public static final String CONFIG_TYPE_BOARDING_PRINT_CONFIG = "BOARDING_PRINT_MODEL";

    /**
     * 特定航班的部分舱等在生效时设定状态为C。0关闭，1打开
     */
    public static final String CABIN_STATUS_C_CONFIG = "CABIN_STATUS_C";

    /**
     * 特定航班的部分舱等在生效时设定状态为X。0关闭，1打开
     */
    public static final String CABIN_STATUS_X_CONFIG = "CABIN_STATUS_X";

    /**
     * 记录用户操作生效状态。0关闭，1打开
     */
    public static final String ACTION_RECORD = "ACTION_RECORD";

    /**
     * 生效航班开舱时指定的销售舱位状态
     */
    public static final String[] SELL_CABIN_STATUS = {"L", "Q"};

    /**
     * 生效航班开舱时指定设置X或者C状态的航班
     */
    public static final String[] X_C_FLIGHT_NO = {"ZH9326", "CZ3622", "HU7341", "CA4555", "ZH9388", "CA4502", "MU5863", "CA1352", "CA1322"};
    public static final String[] WEATHER = {"阴 OverCast", "晴 Sunny", "多云 Cloudy"};
    public static final String[] WIND_POWER = {"--", "3-4级5.5-7.9 M/S"};
    public static final String[] WIND_DIRECTION = {"东北风 Northeasterly", "东南风 Southeast"};
    public static final String[] CITYS = {"衢州", "库车", "嘉峪关", "景德镇", "衡阳", "铜仁", "汉中", "通辽", "梧州", "长沙", "格尔木", "九江", "克拉玛依", "阿克苏", "安阳", "喀什", "安康", "大同", "延安", "杭州", "赣州", "昭通", "黑河", "井冈山", "和田", "芒市", "哈密", "长治", "哈尔滨", "西昌", "林芝", "伊宁", "东营", "梅县", "沙市", "朝阳", "广汉", "潍坊", "无锡", "三亚", "万州", "新源", "怀化", "泉州", "满洲里", "大理", "昆明", "塔城", "迪庆", "邯郸", "元谋", "济宁", "连城", "昌都", "大足", "厦门", "鞍山", "通化", "康定", "黎平", "鄂尔多斯", "腾冲", "高雄", "成都", "上海", "大连", "武汉", "青岛", "乌鲁木齐", "济南", "吕梁", "稻城", "文山", "攀枝花", "三明", "临沧", "巴中", "重庆", "北京", "岳阳", "遵义", "张掖", "乌兰浩特", "中卫", "上饶", "南宁", "天津", "沈阳", "贵阳", "温州", "福州", "太原", "宁波", "南昌", "长春", "郑州", "兰州", "合肥", "香港", "珠海", "烟台", "石家庄", "银川", "汕头", "呼和浩特", "拉萨", "延吉", "宜昌", "丽江", "绵阳", "西宁", "武夷山", "常州", "苏州", "湛江", "张家界", "黄山", "佳木斯", "锦州", "包头", "北海", "南通", "义乌", "威海", "扬州", "南充", "常德", "安顺", "徐州", "连云港", "牡丹江", "盐城", "海口", "敦煌", "蚌埠", "恩施", "襄樊", "宜宾", "丹东", "齐齐哈尔", "安庆", "保山", "洛阳", "泸州", "库尔勒", "柳州", "赤峰", "澳门", "临沂", "庆阳", "海拉尔", "黄岩", "榆林", "阜阳", "西双版纳", "九寨沟", "舟山", "吉安", "酒泉", "南阳", "阿城", "安吉", "安陆", "阿里", "阿拉善左", "阿尔山", "阿拉善右", "阿图什", "北安", "布尔津", "白城", "保定", "北戴河", "宝鸡", "毕节", "北流", "博乐", "北宁", "白山", "巴彦淖尔", "百色", "本溪", "滨州", "沧源", "亳州", "赤壁", "承德", "昌都地区", "武隆", "长葛", "巢湖", "澄海", "从化", "昌吉", "常宁", "常熟", "楚雄", "慈溪", "潮阳", "沧州", "潮州", "郴州", "池州", "崇州", "滁州", "大安", "大丰", "登封", "东莞", "敦化", "丹江口", "都江堰", "德令哈", "大庆", "迪庆州", "东台", "且末", "东兴", "祁连", "定西", "东阳", "五大连池", "都匀", "大冶", "丹阳", "当阳", "德阳", "儋州", "德州", "邓州", "额济纳旗", "二连浩特", "峨眉山", "恩平", "鄂州", "福安", "丰城", "防城港", "福鼎", "奉化", "阜康", "福清", "佛山", "抚顺", "阜新", "抚远", "番禺", "富阳", "抚州", "广安", "藁城", "固原", "贵港", "个旧", "高明", "桂平", "广水", "贵溪", "高邮", "夏河", "巩义", "果洛", "广元", "广州", "甘孜州", "淮安", "鹤壁", "淮北", "海城", "韩城", "汉川", "河池", "晖春", "海东", "花都", "华甸", "海东地区", "鹤岗", "黄冈", "洪湖", "黄骅", "海林", "虎林", "呼伦贝尔", "海门", "侯马", "海宁", "淮南", "海南州", "黄南州", "鹤山", "衡水", "黄石", "河源", "霍林郭勒", "华阴", "惠阳", "湖州", "化州", "花土沟", "惠州", "合作", "集安", "邹城", "漠河", "建德", "江都", "蛟河", "界首", "金华", "景洪", "九华山", "晋江", "靖江", "即墨", "江门", "荆门", "集宁", "胶南", "建瓯", "句容", "吉首", "江山", "金坛", "鸡西", "加格达奇", "嘉兴", "济源", "建阳", "江阴", "江油", "姜堰", "嘉义", "建三江", "胶州", "焦作", "晋中", "荆州", "开封", "凯里", "开平", "昆山", "奎屯", "开原", "开远", "临安", "六安", "灵宝", "乐昌", "聊城", "罗定", "娄底", "陆丰", "廊坊", "临汾", "漯河", "临海", "凌海", "老河口", "澜沧", "临江", "龙井", "龙口", "六盘水", "乐清", "龙泉", "庐山", "乐山", "离石", "丽水", "莱芜", "潞西", "兰溪", "临夏", "莱阳", "溧阳", "荔波", "涟源", "辽阳", "辽源", "莱州", "阆中", "雷州", "连州", "林州", "林芝地区", "中国马祖", "中国马公", "马鞍山", "麻城", "梅河口", "茂名", "米泉", "眉山", "密山", "孟州", "绵竹", "南安", "宁德", "宁安", "宁国", "南海", "内江", "宁蒗", "南平", "那曲", "那曲地区", "南雄", "平度", "平顶山", "蓬莱", "平湖", "盘锦", "平凉", "普宁", "莆田", "凭祥", "萍乡", "濮阳", "彭州", "邳州", "启东", "千岛湖", "曲阜", "琼海", "潜江", "曲靖", "邛崃", "七台河", "沁阳", "清远", "庆阳地区", "钦州", "青州", "瑞安", "荣城", "若羌", "如皋", "仁怀", "日喀则", "瑞丽", "任丘", "乳山", "日照", "汝州", "顺德", "绥芬河", "韶关", "四会", "绥化", "石河子", "莎车", "商洛", "舒兰", "思茅", "三门峡", "山南", "遂宁", "山南地区", "神农架林区", "四平", "商丘", "三水", "石狮", "石首", "汕尾", "邵武", "绍兴", "上虞", "邵阳", "十堰", "松原", "双鸭山", "尚志", "深圳", "嵊州", "朔州", "松滋", "随州", "泰安", "太仓", "天长", "铜川", "铁力", "铁岭", "同里", "铜陵", "吐鲁番", "中国台南", "天门", "台山", "唐山", "天水", "通什", "泰兴", "桐乡", "中国台中", "泰州", "通洲", "乌兰察布", "文昌", "文登", "乌海", "芜湖", "吴江", "武进", "温蛉", "温岭", "万宁", "渭南", "巫山", "武威", "乌兰特中旗", "武穴", "五指山", "项城", "兴城", "许昌", "宣城", "孝感", "香格里拉", "新会", "锡林浩特", "新密", "咸宁", "宿迁", "仙桃", "湘潭", "邢台", "新乡", "新沂", "新余", "信宜", "兴义", "宿州", "雅安", "阳春", "伊春", "雁荡山", "云浮", "永安", "阳朔", "阳江", "营口", "永康", "玉林", "玉门", "阳泉", "偃师", "玉树州", "鹰潭", "宜兴", "玉溪", "益阳", "荥阳", "兖州", "扬中", "仪征", "宜州", "永州", "禹州", "淄博", "自贡", "遵化", "株洲", "镇江", "诸暨", "张家港", "张家口", "周口", "扎兰屯", "驻马店", "漳平", "肇庆", "黔江", "中山", "钟祥", "资兴", "枣阳", "资阳", "枣庄", "漳州", "周庄", "株州", "涿州", "大兴安岭", "菏泽", "吉林", "南京", "台北", "西安", "宜春", "金门", "望安", "秦皇岛", "恒春", "金昌", "五台山", "运城", "阿勒泰", "桂林", "阿坝州"};

    /**
     * DM
     */
    public static final String DM = "DM";

    /**
     * 2小时
     */
    public static final int TWO_HOUR = 2;

    /**
     * 24小时
     */
    public static final int TWENTY_FOUR_HOUR = 24;

    /**
     * 48小时
     */
    public static final int FORTY_EIGHT_HOUR = 48;

    /**
     * 72小时
     */
    public static final int SEVENTY_TWO_HOUR = 72;

    /**
     * TEXEMPTOC
     */
    public static final String TEXEMPTOC = "TEXEMPTOC";

    /**
     * 电子客票没有查询到
     */
    public static final String ET_NOT_FOUND = "ET PASSENGER DATA NOT FOUND";

    public static final String DATE_FORMAT = "DATE MUST BE DDMMMYY";

    public static final String COUPON_STATUS_CODE_INVALID = "COUPON STATUS CODE INVALID";
    public static final String TICKET_SUSPENDED = "TICKET SUSPENDED";

    public static final String FD_L = "L";

    public static final String SA = "SA";

    public static final String ONE_SPACE = " ";

    public static final String SS_DATE_CODE = "yyyy-MM-dd HHmm";

    public static final String CMD_PA = "PA";

    public static final String CMD_PU = "PU";

    public static final String SEMICOLON = ";";

    public static final String RT_PARAM_C = "C";

    public static final String RT_PARAM_CN = "CN";

    public static final String RT_PARAM_NC = "NC";

    public static final String CHECK_TKT_STATUS = "TKT STATUS,PLEASE CHECK TKT";

    public static final String TIME_EXPIRED = "The Itinerary Receipt has expired";

    public static final String HAS_PRINTED = "The  Itinerary Receipt has been printed";

    public static final String SEI_CODE = "SEI";

    public static final String SEM_CODE = "SEM";

    public static final String FDL_CODE = "FDL";

    public static final String CMD_PD = "PD";

    public static final String HBPU_CODE = "HBPU";

    public static final String STAR_SLASH = "*/";

    public static final int MAX_HOU = 23;

    public static final int THIRTEEN = 13;

    public static final String DATA = "data";

    public static final String OK = "ok";

    public static final String ID_CARD = "/IN/";

    public static final String ITEM_SHOULD_PRECEDE_O_ITEM = "ITEM SHOULD PRECEDE O-ITEM";

    public static final String GRP = "GRP";

    public static final String NGRP = "NGRP";

    public static final String HBNB = "HBNB";

    public static final String ARRIVAL_TYPE = "I";

    public static final String DEPARTURE_TYPE = "O";

    public static final String BAG_TYPE = "BAG/ALL";

    public static final String ABC = "ABC";

    public static final String PARAM_SBY = "SBY";

    public static final String HBPU_CNIN_TYPE = ",CNIN/";

    public static final int LINE_NUM = 80;

    public static final String CMD_HBPR = "HBPR";

    public static final String OUTBOUND_CONFLICT = "OUTBOUND CONFLICT";

    public static final String UNABLE_TRANSFER = "UNABLE - PAX WILL BE STANDBYED ON TRANSFER FLIGHT";

    public static final String ALREADY_ACCEPTED = "TRANSFER FLIGHT/ALREADY ACCEPTED";

    public static final String NEED_SORT_CODE = "AV;SK;DS";

    public static final String NOT_SHARED_FLIGHT = "NOT SHARED FLIGHT";

    /**
     * 常客卡号输入错误
     */
    public static final String INVALID_PROFILE_NUMBER = "INVALID PROFILE NUMBER";

    /**
     * 常客卡等级输入错误
     */
    public static final String FFP_CODE_ERROR = "FFP TOP TIER CODE INPUT ERROR";

    public static final String EXIST_FQTV = "同一旅客同一航段只允许输入一个常客信息";

    public static final String PROFILE_NOT_FOUND = "PROFILE NOT FOUND";

    public static final String FREQUENT_FLYER_PRIORITY = "FREQUENT FLYER PRIORITY";

    public static final String CHECK_FREQUENT_FLYER_ID = "CHECK FREQUENT FLYER ID";

    public static final String OSCBCT_RR = "ONLY SEGMENT CAN BE CHANGED TO RR";

    public static final String ANOTHER_UPDATE_ALREADY_ACTIVE = "ANOTHER UPDATE ALREADY ACTIVE";

    public static final String DATA_REQUIRED = "PASSPORT DOCUMENT DATA REQUIRED";
    public static final String NON_GROUP = "NON-GROUP";

    public static final String PAT_EXIST = "PLS DELETE AUTOMATIC FARE QUOTE FN/FC/TC FIRST";

    public static final String INPUT_SSR_INFT_CONFIRM = "PLEASE INPUT SSR INFT AND NEED AIRLINE CONFIRM IT";

    public static final String DUP_SEG = "DUPLICATE SEGMENT";

    public static final String RE_QUOTE = "PLS DELETE AUTO FN/FC/TC/EI AND RE-QUOTE";

    public static final String COUNT_REQUIRED = "COUNT REQUIRED";

    public static final String NO_BAG_DATA_TO_UPDATE = "NO BAG DATA TO UPDATE";

    public static final String WEIGHT_REQUIRED = "WEIGHT REQUIRED";

    public static final String AVIH_COUNT_CONFLICT = "AVIH COUNT CONFLICT";

    public static final int ADULT_WEIGHT = 75;

    public static final int CHILD_WEIGHT = 38;

    public static final int INFANT_WEIGHT = 10;

    public static final List<String> PNR_COMMAND_LIST = new ArrayList<>();

    static {
        PNR_COMMAND_LIST.add("SD");
        PNR_COMMAND_LIST.add("SS");
        PNR_COMMAND_LIST.add("NM");
        PNR_COMMAND_LIST.add("GN");
        PNR_COMMAND_LIST.add("SSR");
        PNR_COMMAND_LIST.add("OSI");
        PNR_COMMAND_LIST.add("RR");
        PNR_COMMAND_LIST.add("RMK");
        PNR_COMMAND_LIST.add("SFC");
        PNR_COMMAND_LIST.add("RFC");
        PNR_COMMAND_LIST.add("FP");
        PNR_COMMAND_LIST.add("FC");
        PNR_COMMAND_LIST.add("FN");
        PNR_COMMAND_LIST.add("RMK");
        PNR_COMMAND_LIST.add("CT");
        PNR_COMMAND_LIST.add("XN");
        PNR_COMMAND_LIST.add("TK");
        PNR_COMMAND_LIST.add("XE");
        PNR_COMMAND_LIST.add("X");
        PNR_COMMAND_LIST.add("RT");
        PNR_COMMAND_LIST.add("EI");
        PNR_COMMAND_LIST.add("OI");
        PNR_COMMAND_LIST.add("ES");
        PNR_COMMAND_LIST.add("SP");
        PNR_COMMAND_LIST.add("@");
        PNR_COMMAND_LIST.add("SA");
    }
}

