package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [航空公司授权查询返回结果对象]
 *
 * <AUTHOR>
 */
@Data
@Api(tags = "航空公司授权查询返回结果对象")
public class AgentAirlineVo {

    @ApiModelProperty(value = "代理人ID")
    private String agentId;

    @ApiModelProperty(value = "航司ID")
    private String airlineId;

    @ApiModelProperty(value = "航空公司二字码")
    private String airlineCode;

    @ApiModelProperty(value = "航空公司中文名")
    private String airlineFullName;

}
