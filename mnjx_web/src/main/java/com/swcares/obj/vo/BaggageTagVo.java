package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-10-11
 */
@Api(tags = "行李牌数据")
@Data
public class BaggageTagVo {

    @ApiModelProperty("机场三字码")
    private String airportCity;

    @ApiModelProperty("航司二字码")
    private String airCode;

    @ApiModelProperty("飞机号")
    private String planeNo;

    @ApiModelProperty("到达地三字码")
    private String dst;

    @ApiModelProperty("航班号")
    private String fltNo;

    @ApiModelProperty("航班日期")
    private String fltDate;

    @ApiModelProperty("姓名拼音")
    private String yName;

    @ApiModelProperty("行李总数")
    private String bagNo;

    @ApiModelProperty("行李总重量")
    private String bagWeight;

    @ApiModelProperty("部门号")
    private String officeNumber;

    @ApiModelProperty("登机号")
    private String aboardNo;
}
