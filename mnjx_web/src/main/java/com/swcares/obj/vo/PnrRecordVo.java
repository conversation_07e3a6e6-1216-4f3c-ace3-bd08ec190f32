package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * description：订座记录管理列表对象
 *
 * @author：zhaokan
 * @date：2021/10/14
 */
@Api(tags = "订座记录管理列表对象")
@Data
public class PnrRecordVo {

	@ApiModelProperty(value = "crs pnr 编码")
	private String pnrCrs;

	@ApiModelProperty(value = "航班号")
	private String flightNo;

	@ApiModelProperty(value = "航班日期")
	private String flightDate;

	@ApiModelProperty(value = "PNR创建时间")
	private String createTime;

}
