package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * [操作记录查询返回对象]
 *
 * <AUTHOR>
 */
@Data
@Api(tags = "操作记录查询返回列表")
public class ActionRecordResultListVo {

    @ApiModelProperty(value = "office号")
    private String officeNo;

    @ApiModelProperty(value = "OFFICE号所属机构ID，根据OFFICE_TYPE分别关联到agent表，airline表，airport表")
    private String cname;

    @ApiModelProperty(value = "siNo")
    private String siNo;

    @ApiModelProperty(value = "级别:11,41,81,91,101")
    private String levelCode;

    @ApiModelProperty(value = "级别描述")
    private String levelRemark;

    @ApiModelProperty(value = "指令执行记录")
    private String action;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "修改时间")
    private String updateTime;
}
