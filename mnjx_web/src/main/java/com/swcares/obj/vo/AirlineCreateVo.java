package com.swcares.obj.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AirlineCreateVo {
    @ExcelProperty(value = "所属国际", order = 1)
    private String countryIso;

    @ExcelProperty(value = "航空公司中文全称", order = 2)
    private String airlineFullName;

    @ExcelProperty(value = "航空公司中文简称", order = 3)
    private String airlineShortName;

    @ExcelProperty(value = "航空公司英文名", order = 4)
    private String airlineEname;

    @ExcelProperty(value = "航空公司二字码", order = 5)
    private String airlineCode;

    @ExcelProperty(value = "航空公司结算码", order = 6)
    private String airlineSettlementCode;

    @ExcelProperty(value = "航空公司联系人", order = 7)
    private String airlineContactName;

    @ExcelProperty(value = "航空公司联系人电话", order = 8)
    private String airlineContactPhone;

    @ExcelProperty(value = "航空公司注册地址", order = 9)
    private String airlineContactAddress;

    @ExcelProperty(value = "数据状态", order = 10)
    private String airlineStatus = "1";
}
