package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Data
@Api(tags = "工作号修改前的查询")
public class JobUpdateVo {
    @ApiModelProperty(value = "机构ID",example="1494139283630333953")
    private String organizationId;

    @ApiModelProperty(value = "机构名称",example="成都订座")
    private String organizationName;

    @ApiModelProperty(value = "年级ID",example = "")
    private String gradeId;

    @ApiModelProperty(value="年级名称",example="2022")
    private String gradeName;

    @ApiModelProperty(value="专业ID",example="")
    private String majorId;

    @ApiModelProperty(value="专业名称",example="计算机")
    private String majorName;

    @ApiModelProperty(value="班级id",example="")
    private String classId;

    @ApiModelProperty(value="班级名称",example="1班")
    private String className;

    @ApiModelProperty(value="角色，0,学生 1,教师",example="0")
    private String accountRole;

    @ApiModelProperty(value="工作号")
    private String jobNo;

    @ApiModelProperty(value="学生或老师姓名",example="")
    private String accountName;

    @ApiModelProperty(value="身份证号",example="")
    private String accountIdCard;

    @ApiModelProperty(value="性别, 0,男 1，女",example="0")
    private String accountSex;

    @ApiModelProperty(value="学号",example="")
    private String accountSerialNo;

    @ApiModelProperty(value="手机号",example="")
    private String accountPhone;

}
