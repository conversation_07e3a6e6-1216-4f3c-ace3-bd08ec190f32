package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 【查询参数】
 *
 * <AUTHOR> by yaodan
 * 2021/8/19-11:22
 */
@Api(tags = "机场查询参数")
@Data
public class AirportQueryVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机场三字码  ", name = "airportCode", example = "CTU")
    private String airportCode;
    @ApiModelProperty(value = "城市三字码  ", name = "cityId", example = "1493424179859927058")
    private String cityId;
    @ApiModelProperty(value = "机场ID  ", name = "airportId", example = "1493493314010734594")
    private String airportId;

}
