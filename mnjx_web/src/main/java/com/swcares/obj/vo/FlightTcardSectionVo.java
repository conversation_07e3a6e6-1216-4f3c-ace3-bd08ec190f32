package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR> by yaodan
 */
@Data
public class FlightTcardSectionVo {
	@ApiModelProperty(value = "TCard航节Id")
	private String tcardSectionId;

	@ApiModelProperty(value = "TCardId")
	private String tcardId;

	@ApiModelProperty(value = "航节号", hidden = true)
	private Integer sectionNo;

	@ApiModelProperty(value = "是否尾部航节：0 否 1是", hidden = true)
	private String isLastSection;

	@ApiModelProperty(value = "机场代码ID")
	private String airportId;

	@ApiModelProperty(value = "机场代码")
	private String airportCode;

	@ApiModelProperty(value = "DEPT离港时间")
	private String depTime;

	@ApiModelProperty(value = "ARR进港时间")
	private String arrTime;

	@ApiModelProperty(value = "进港日期变更：值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值")
	private String arrDateChange;

	@ApiModelProperty(value = "离港日期变更：值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值" +
			"离港日期变更：值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值")
	private String offDateChange;

	@ApiModelProperty(value = "BRD登机时间")
	private String brdTime;

	@ApiModelProperty(value = "登机时间日期变更")
	private String brdDateChange;

}
