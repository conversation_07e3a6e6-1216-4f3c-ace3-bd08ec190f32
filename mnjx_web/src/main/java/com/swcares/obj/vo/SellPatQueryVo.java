package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel("销售运价分页列表请求对象")
@Data
public class SellPatQueryVo {
    @NotNull(message = "当前页码不能为空")
    @ApiModelProperty("当前页码")
    private Integer current;
    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty("每页条数")
    private Integer size;
    @ApiModelProperty("航空公司")
    private String airCode;
    @ApiModelProperty("票价等级")
    private String ticketLevel;
    @ApiModelProperty("起飞城市")
    private String org;
    @ApiModelProperty("到达城市")
    private String dst;

}
