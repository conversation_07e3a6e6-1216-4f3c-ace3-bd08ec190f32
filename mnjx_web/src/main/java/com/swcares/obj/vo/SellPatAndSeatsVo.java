package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 开舱返回数据的舱等价格
 *
 * <AUTHOR>
 * @date 2022/4/2
 */
@Api(tags = "开舱返回数据的舱等价格")
@Data
public class SellPatAndSeatsVo {

    @ApiModelProperty(value = "票价等级")
    private String ticketLevel;

    @ApiModelProperty(value = "总座位数")
    private Integer seatTotal;

    @ApiModelProperty(value = "可用座位数")
    private Integer seatAvailable;

    @ApiModelProperty(value = "销售舱位价格")
    private Integer sellCabinPrice;
}
