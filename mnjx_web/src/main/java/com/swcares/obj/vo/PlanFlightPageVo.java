package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("航班计划分页列表返回对象")
public class PlanFlightPageVo {

    @ApiModelProperty(value = "表主键ID")
    private String planFlightId;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "飞机号")
    private String planeNo;

    @ApiModelProperty(value = "MASTER ACTIVE CANCEL DELETE")
    private String flightStatus;

    @ApiModelProperty(value = "ASR舱位")
    private String allowAsr;

    @ApiModelProperty(value = "航班是否初始化")
    private String isFlightInitial;

    @ApiModelProperty(value = "国内/国际")
    private String tType;

    @ApiModelProperty(value = "TCARD表ID")
    private String tcardId;

    @ApiModelProperty(value = "cnd号")
    private String cndNo;

    @ApiModelProperty(value = "机型")
    private String planeModelType;

    @ApiModelProperty(value = "版本号")
    private String planeModelVersion;

    @ApiModelProperty(value = "夜航")
    private String isN;

    @ApiModelProperty(value = "航站列表")
    private List<PlanFlightAirportVo> planFlightAirportVoList;

}
