package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CndRetrieveVo {

    @ApiModelProperty(value = "CNDID")
    private String cndId;

    @ApiModelProperty(value = "CND号")
    private String cndNo;

    @ApiModelProperty(value = "机型", example = "A320")
    private String planeModelType;

    @ApiModelProperty(value = "版本号", example = "V3A5X8Y2")
    private String planeModelVersion;

    @ApiModelProperty(value = "状态：0 停用 1 启用", example = "0")
    private String status;

    @ApiModelProperty(value = "运营布局", example = "F10Y12")
    private String planeCabinLayout;
}
