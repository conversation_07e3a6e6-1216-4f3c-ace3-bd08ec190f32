package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/18 10:21 上午
 * 概要
 */
@Data
public class EffectiveFlightVo {

    @ApiModelProperty(value = "周期星期几")
    @NotNull(message = "周期不能为空")
    private Integer[] cycle;
    @ApiModelProperty(value = "周数")
    @NotNull(message = "周数不能为空")
    private Integer weeks;
    @ApiModelProperty(value = "航班ID")
    @NotNull(message = "航班号不能为空")
    private String flightId;
}
