package com.swcares.obj.vo;

import com.swcares.entity.MnjxCountry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 【返回结果】
 *
 * <AUTHOR> by yaodan
 * 2021/8/24-16:36
 */
@Api(tags = "国家返回的数据")
@Data
@EqualsAndHashCode(callSuper = true)
public class CountryVo extends MnjxCountry {

    @ApiModelProperty(value = "洲际代码 ", example = "AN")
    private String continentCode;
}
