package com.swcares.obj.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue;


/**
 * <AUTHOR>
 */
@Api(tags="航节搜索传输对象")
@Data
public class SectionCreateVo {
    @ApiModelProperty(value = "ID")
    private String sectionId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航节号")
    @NotNull(message = "航节号不能为空")
    private Integer sectionNo;

    @ApiModelProperty(value = "是否尾部航节")
    private Integer isLastSection;

    @ApiModelProperty(value = "机场代码")
    @NotBlank(message = "机场代码不能为空")
    private String airportCode;

    @ApiModelProperty(value = "飞机停靠位置")
    private String stopPosition;

    @ApiModelProperty(value = "距离")
    @JSONField(serialzeFeatures = WriteMapNullValue)
    private String distance;

    @ApiModelProperty(value = "飞行时间")
    @JSONField(serialzeFeatures = WriteMapNullValue)
    private String flyTime;

    @ApiModelProperty(value = "计划进港时间")
    private String estimateOff;

    @ApiModelProperty(value = "计划离港时间")
    private String estimateArr;

    @ApiModelProperty(value = "进港日期变更")
    private String arrDateChange;

    @ApiModelProperty(value = "离港日期变更")
    private String offDateChange;

    @ApiModelProperty(value = "餐食代码")
    private String mealCode ="M";

    @ApiModelProperty(value = "配餐机场")
    private String mealAirport;

    @ApiModelProperty(value = "娱乐标识")
    private String isRecreation ="E";
}
