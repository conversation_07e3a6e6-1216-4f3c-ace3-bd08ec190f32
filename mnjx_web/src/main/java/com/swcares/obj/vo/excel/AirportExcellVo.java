package com.swcares.obj.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AirportExcellVo {
    @ExcelProperty(value = "城市三字码", order = 1)
    private String cityCode;
    @ExcelProperty(value = "机场中文名", order = 2)
    private String airportCname;
    @ExcelProperty(value = "机场英文名", order = 3)
    private String airportEname;
    @ExcelProperty(value = "机场三字码", order = 4)
    private String airportCode;
    @ExcelProperty(value = "数据记录状态", order = 5)
    private String airportStatus;
    @ExcelProperty(value = "备注", order = 6)
    private String remark;
}
