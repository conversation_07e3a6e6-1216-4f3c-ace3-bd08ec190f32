package com.swcares.obj.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.core.util.Constant;
import com.swcares.core.validator.PropConstraint;
import com.swcares.service.ICndService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CndCreateVo {

    @ApiModelProperty(value = "ID")
    @PropConstraint(service = ICndService.class,
            method = "retrieveById",
            isExist = false,
            message = Constant.CND_ID_REPEAT)
    private String cndId;

    @ApiModelProperty(value = "机型ID")
    @NotBlank(message = "机型版本号不能为空")
    @Pattern(regexp = "\\d+", message = "机型版本号不存在")
    private String planeModelId;

    @ApiModelProperty(value = "CND号")
    @NotBlank(message = "cnd号不能空")
    @Pattern(regexp = "\\d+", message = "CND表号格式错误（数字）")
    @PropConstraint(service = ICndService.class,
            method = "retrieveByCndNo",
            isExist = false,
            message = Constant.CND_NO_EXIST)
    private String cndNo;

    @ApiModelProperty(value = "飞机布局")
    @TableField("layout")
    @NotBlank(message = "cnd布局不能为空")
    @Pattern(regexp = "[A-Za-z](\\d)+(/[A-Za-z](\\d)+)*", message = "飞机布局格式不正确")
    private String layout;

    @ApiModelProperty(value = "飞机源图")
    @NotBlank(message = "飞机源图不能为空")
    @Pattern(regexp = "[A-Za-z0-9\\[=*.:\\s]+", message = "飞机源图格式不正确")
    private String layoutSource;

    @ApiModelProperty(value = "一等舱")
    @Pattern(regexp = "([A-Za-z]+)?", message = "一等舱格式错误")
    private String firstCabinClass;

    @ApiModelProperty(value = "一等舱销售舱位")
    @Pattern(regexp = "([A-Za-z]+)?", message = "一等舱销售舱位格式错误")
    private String firstSellCabin;

    @ApiModelProperty(value = "一等舱座位数")
    private Integer firstSeats;

    @ApiModelProperty(value = "一等舱可托运行李重量")
    private Integer firstWeight;

    @ApiModelProperty(value = "一等舱折扣")
    private BigDecimal firstDiscount;

    @ApiModelProperty(value = "二等舱")
    @Pattern(regexp = "([A-Za-z]+)?", message = "二等舱格式错误")
    private String secondCabinClass;

    @ApiModelProperty(value = "二等舱销售舱位")
    @Pattern(regexp = "([A-Za-z]+)?", message = "二等舱销售舱位格式错误")
    private String secondSellCabin;

    @ApiModelProperty(value = "二等舱座位数")
    private Integer secondSeats;

    @ApiModelProperty(value = "二等舱可托运行李重量")
    private Integer secondWeight;

    @ApiModelProperty(value = "二等舱折扣")
    private BigDecimal secondDiscount;

    @ApiModelProperty(value = "三等舱")
    @Pattern(regexp = "([A-Za-z]+)?", message = "三等舱格式错误")
    @TableField("third_cabin_class")
    private String thirdCabinClass;

    @ApiModelProperty(value = "三等舱销售舱位")
    @Pattern(regexp = "([A-Za-z]+)?", message = "三等舱销售舱位格式错误")
    private String thirdSellCabin;

    @ApiModelProperty(value = "三等舱座位数")
    private Integer thirdSeats;

    @ApiModelProperty(value = "三等舱可托运行李重量")
    private Integer thirdWeight;

    @ApiModelProperty(value = "三等舱折扣")
    private BigDecimal thirdDiscount;

    @ApiModelProperty(value = "四等舱")
    @Pattern(regexp = "([A-Za-z]+)?", message = "四等舱格式错误")
    private String fourthCabinClass;

    @ApiModelProperty(value = "四等舱销售舱位")
    @Pattern(regexp = "([A-Za-z]+)?", message = "四等舱销售舱位格式错误")
    private String fourthSellCabin;

    @ApiModelProperty(value = "四等舱座位数")
    private Integer fourthSeats;

    @ApiModelProperty(value = "四等舱可托运行李重量")
    private Integer fourthWeight;

    @ApiModelProperty(value = "四等舱折扣")
    private BigDecimal fourthDiscount;

    @ApiModelProperty(value = "五等舱")
    @Pattern(regexp = "([A-Za-z]+)?", message = "五等舱格式错误")
    private String fifthCabinClass;

    @ApiModelProperty(value = "五等舱销售舱位")
    @Pattern(regexp = "([A-Za-z]+)?", message = "五等舱销售舱位格式错误")
    private String fifthSellCabin;

    @ApiModelProperty(value = "五等舱座位数")
    private Integer fifthSeats;

    @ApiModelProperty(value = "五等舱可托运行李重量")
    private Integer fifthWeight;

    @ApiModelProperty(value = "五等舱折扣")
    private BigDecimal fifthDiscount;

    @ApiModelProperty(value = "状态:0 停用 1启用")
    private String status;

}
