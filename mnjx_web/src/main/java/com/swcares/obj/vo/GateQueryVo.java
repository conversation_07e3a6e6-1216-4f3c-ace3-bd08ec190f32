package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [查询参数]
 *
 * <AUTHOR> by yaodan
 * 2021/8/25-15:00
 */
@Api(tags = "登机口查询参数")
@Data
public class GateQueryVo {
    @ApiModelProperty(value = "机场代码 id", example = "1493493314010734594")
    private String airportId;

    @ApiModelProperty(value = "机场区域", example = "T1")
    private String gateArea;

    @ApiModelProperty(value = "机场分区", example = "B")
    private String gateZone;

    @ApiModelProperty(value = "登机口", example = "35")
    private String gateNo;
}
