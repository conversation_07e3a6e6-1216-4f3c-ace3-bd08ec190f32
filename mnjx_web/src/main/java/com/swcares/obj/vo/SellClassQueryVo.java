package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel("销售舱等查询请求对象")
@Data
@EqualsAndHashCode(callSuper = false)
public class SellClassQueryVo extends PageQueryVo {

    @ApiModelProperty(value = "编号")
    private String sellClassId;

    @ApiModelProperty(value = "销售舱等")
    private String cabinClass;

    @ApiModelProperty(value = "舱等折扣")
    private BigDecimal cabinClassDiscount;

}
