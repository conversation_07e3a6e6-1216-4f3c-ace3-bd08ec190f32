package com.swcares.obj.dto;

import com.swcares.core.util.Constant;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import lombok.Data;

import java.util.List;
import java.util.Vector;

/**
 * 旅客预定
 *
 * <AUTHOR>
 */
@Data
public class PassengerBookDto {
    /**
     * 航段数据
     */
    List<PassengerFlightDto> passengerFlightDtos = new Vector<>(Constant.FIVE);
    /**
     * 联系号码
     */
    private String phone;
    /**
     * 团队名
     */
    private String groupName = StrUtils.EMPTY;
    /**
     * 旅客列表
     */
    private List<PassengerDetailsDto> passengerDetailsDtos;
    /**
     * 出票部门
     */
    private MnjxOffice mnjxOffice;
    /**
     * 出票工作号
     */
    private MnjxSi mnjxSi;
}
