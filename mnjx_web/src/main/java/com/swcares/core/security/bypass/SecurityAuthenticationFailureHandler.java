package com.swcares.core.security.bypass;

import com.swcares.core.exception.IpBlockedException;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.utils.IpUtils;
import com.swcares.service.impl.IpBlockService;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 登录失败的处理方式
 *
 * <AUTHOR>
 */
@Component
public class SecurityAuthenticationFailureHandler implements AuthenticationFailureHandler {

    @Resource
    private IpBlockService ipBlockService;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException {
        // 获取IP
        String clientIp = IpUtils.getClientIp(request);
        // 如果IP被封禁，返回封禁信息
        if (exception instanceof IpBlockedException) {
            UnifiedResult.writeJson(response, UnifiedResult.fail("尝试次数过多，该IP已被封禁，请稍后再试"));
            return;
        }
        // 增加登录失败次数
        ipBlockService.incrementFailureCount(clientIp);
        // 返回登录失败信息
        UnifiedResult.writeJson(response, UnifiedResult.fail("用户名或者密码错误，登录失败了！！！"));
    }
}
