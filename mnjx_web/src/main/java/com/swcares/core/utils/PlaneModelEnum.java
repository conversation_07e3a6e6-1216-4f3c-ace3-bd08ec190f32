package com.swcares.core.utils;

import com.swcares.core.util.StrUtils;

import java.util.EnumMap;
import java.util.Map;

/**
 * 飞机机型版本
 *
 * <AUTHOR>
 */
public enum PlaneModelEnum {
    //飞机类型：J，喷气式飞机 B，空中客机 H，直升机 T，货机

    /**
     * 飞机类型
     */
    J("J", "喷气式飞机"),
    B("B", "B-空中客机"),
    H("H", "H-直升机"),
    T("T", "T-货机"),
    /**
     * 是否有氧舱
     */
    ZERO("0", "是"),
    ONE("1", "否");

    /**
     * 定义飞机类型
     *
     * @return 定义飞机类型
     */
    public static Map<PlaneModelEnum, String> getKind() {
        Map<PlaneModelEnum, String> objectObjectHashMap = new EnumMap<>(PlaneModelEnum.class);
        objectObjectHashMap.put(PlaneModelEnum.J, PlaneModelEnum.J.value);
        objectObjectHashMap.put(PlaneModelEnum.B, PlaneModelEnum.B.value);
        objectObjectHashMap.put(PlaneModelEnum.H, PlaneModelEnum.H.value);
        objectObjectHashMap.put(PlaneModelEnum.T, PlaneModelEnum.T.value);
        return objectObjectHashMap;
    }

    PlaneModelEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private final String key;

    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    /**
     * 比较字段并转换相应的值
     *
     * @param key key
     * @return 比较字段并转换相应的值
     */
    public static String comparator(String key) {
        String typeValue = StrUtils.EMPTY;
        for (PlaneModelEnum element : PlaneModelEnum.values()) {
            if (element.getKey().equals(key)) {
                typeValue = element.getValue();
                break;
            }
        }
        return typeValue;
    }


}
