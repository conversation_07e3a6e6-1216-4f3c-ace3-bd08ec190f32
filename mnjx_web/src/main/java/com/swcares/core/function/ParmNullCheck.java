package com.swcares.core.function;

import cn.hutool.core.util.StrUtil;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 空值转换
 *
 * <AUTHOR>
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ParmNullCheck {
    /**
     * 是否开启null值转换
     **/
    boolean isOpen() default true;

    /**
     * 需要转换的值
     **/
    String value() default StrUtil.EMPTY;
}
