package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.vo.BaggageTagVo;
import com.swcares.obj.vo.BoardingPassVo;
import com.swcares.obj.vo.FlightSeatVo;

/**
 * <AUTHOR>
 * @date 2021-9-29
 */
public interface IEvaluationEtermService {

    /**
     * Description:[获取登机牌信息]
     *
     * @param admissionTicket 准考证号
     * @param idCard          演员身份证号
     * @param date            日期
     * @return
     * @
     * <AUTHOR>
     * @update 2021-9-30
     */
    BoardingPassVo getBoardingPass(String admissionTicket, String idCard, String date) ;

    /**
     * [获取航班座位图]
     *
     * @param idCard          身份证号
     * @param admissionTicket 准考证号
     * @param date            日期
     * @return 获取航班座位图
     * @date 2021-10-09 16:48:09
     * <AUTHOR>
     */
    FlightSeatVo queryFlightSeat(String idCard, String admissionTicket, String date);

    /**
     * Title: queryBaggageTag
     * Description: 获取行李牌数据<br>
     *
     * @param admissionTicket 准考证号
     * @param idCard          演员身份证号
     * @param date            日期
     * @return {@link BaggageTagVo}
     * @
     * <AUTHOR>
     * @date 2021-10-11 16:01
     */
    BaggageTagVo queryBaggageTag(String admissionTicket, String idCard, String date) ;

    /**
     * Title: queryLoginStatus
     * Description: 查询考生登录状态<br>
     *
     * @param admissionTicket 准考证号
     * @return {@link String}
     * @throws UnifiedResultException
     * <AUTHOR>
     * @date 2021/10/19 18:49
     */
    String queryLoginStatus(String admissionTicket) throws UnifiedResultException;
}
