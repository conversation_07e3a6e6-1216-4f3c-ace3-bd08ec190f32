package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.obj.vo.OpenCabinRetrieveVo;
import com.swcares.obj.vo.OpenCabinPageVo;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
public interface IOpenCabinService {

    /**
     * queryListByPage
     *
     * @param openCabinRetrieveVo openCabinQueryVo
     * @return queryListByPage
     */
    IPage<OpenCabinPageVo> retrievePage(OpenCabinRetrieveVo openCabinRetrieveVo);
}
