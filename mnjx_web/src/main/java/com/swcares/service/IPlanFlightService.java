package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.vo.PlanFlightNoDateVo;
import com.swcares.obj.vo.PlanFlightPageVo;
import com.swcares.obj.vo.PlanFlightRetrieveVo;
import com.swcares.obj.vo.PlanFlightUpdateVo;

import java.util.List;

/**
 * @author：wangchun
 */
public interface IPlanFlightService {

    /**
     * 航班飞行计划分页列表查询
     *
     * @param queryVo
     * @return
     */
    IPage<PlanFlightPageVo> retrievePage(PlanFlightRetrieveVo queryVo);

    /**
     * Title: updatePlanFlight
     * Description: 更新航班计划<br>
     *
     * @param planFlightUpdateVo planFlightUpdateVo
     * @throws UnifiedResultException 统一异常
     * <AUTHOR>
     * @date 2022/3/22 15:51
     */
    void updatePlanFlight(PlanFlightUpdateVo planFlightUpdateVo) throws UnifiedResultException;

    /**
     * Title: deleteById
     * Description: 通过ID删除航班计划<br>
     *
     * @param planFlightId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/3/22 11:04
     */
    boolean cancelPlanFlight(String planFlightId);

    /**
     * Title: initialPlanFlight
     * Description: 初始化航班计划<br>
     *
     * @param planFlightId planFlightId
     * @return {@link boolean}
     * @throws UnifiedResultException 统一异常
     * <AUTHOR>
     * @date 2022/3/22 11:16
     */
    boolean initialPlanFlight(String planFlightId) throws UnifiedResultException;

    /**
     * Title: cancelInitialPlanFlight
     * Description: 取消航班初始化<br>
     *
     * @param planFlightId planFlightId
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/3/29 10:17
     */
    boolean cancelInitialPlanFlight(String planFlightId);

    /**
     * 查询PlanFlightNoDateVo list
     *
     * @param date date
     * @return PlanFlightNoDateVo
     */
    List<PlanFlightNoDateVo> retrieveNoDate(String date);


    List<PlanFlightNoDateVo> retrieveNoDateList(List<String> dateList);
}
