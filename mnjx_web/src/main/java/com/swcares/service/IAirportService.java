package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.MnjxCity;
import com.swcares.obj.vo.excel.AirportExcellVo;
import com.swcares.obj.vo.AirportQueryVo;
import com.swcares.obj.vo.AirportVo;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/19-11:26
 */
public interface IAirportService {

    /**
     * 新增机场信息
     *
     * @param mnjxAirport mnjxAirport
     * @return 新增机场信息
     * @throws UnifiedResultException 异常
     */
    boolean createAirport(MnjxAirport mnjxAirport) throws UnifiedResultException;

    /**
     * 通过excell导入数据
     *
     * @param entities entities
     * @return 通过excell导入数据
     */
    List<AirportExcellVo> createEntityByExcell(List<MnjxAirport> entities);

    /**
     * 查询城市代码
     *
     * @return 查询城市代码
     */
    List<MnjxCity> retrieveCity();

    /**
     * Title: retrieveAllAirport
     * Description: 查询所有的机场
     *
     * @return {@link List<MnjxAirport>}
     * <AUTHOR>
     * @date 2022/4/1 14:29
     */
    List<MnjxAirport> retrieveAllAirport();

    /**
     * 条件加分页查询
     *
     * @param page           page
     * @param airportQueryVo airportQueryVo
     * @return 条件加分页查询
     */
    IPage<AirportVo> retrievePageByCond(IPage<AirportVo> page, AirportQueryVo airportQueryVo);

    /**
     * 根据id获取机场信息
     *
     * @param id id
     * @return 根据id获取机场信息
     */
    MnjxAirport retrieveById(String id);

    /**
     * 修改机场信息
     *
     * @param mnjxAirport 机场对象
     * @return 修改是否成功
     * @throws UnifiedResultException 异常对象
     */
    boolean updateAirport(MnjxAirport mnjxAirport) throws UnifiedResultException;

    /**
     * 修改机场状态
     * @param airportId 机场id
     * @param airportStatus 机场状态
     * @return 修改机场状态
     * @throws UnifiedResultException 统一异常
     */
    boolean updateAirportStatus(String airportId, String airportStatus) throws UnifiedResultException;

    /**
     * 批量删除机场信息
     * @param ids ids
     * @return 批量删除机场信息
     * @throws UnifiedResultException 统一异常
     */
    boolean deleteAirports(List<String> ids) throws UnifiedResultException;

    /**
     * vo转实体
     *
     * @param excelVos vo
     * @return vo转实体
     */
    List<MnjxAirport> vo2Entity(List<AirportExcellVo> excelVos);
}
