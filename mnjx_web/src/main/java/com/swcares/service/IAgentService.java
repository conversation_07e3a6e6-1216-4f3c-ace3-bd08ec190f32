package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxAgent;
import com.swcares.obj.vo.AgentAirlineVo;
import com.swcares.obj.vo.AgentQueryVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IAgentService {

    /***
     * 新增代理人
     * @param mnjxAgent mnjxAgent
     * @return 新增代理人
     * @throws UnifiedResultException 统一异常
     */
    boolean create(MnjxAgent mnjxAgent) throws UnifiedResultException;

    /**
     * 新增代理人航司授权信息
     *
     * @param agentId       代理人id
     * @param airlineIdList 授权的航司集合
     * @return 新增代理人航司授权信息
     */
    boolean creatAgentAirlines(String agentId, List<String> airlineIdList);

    /**
     * 条件加分页查询
     *
     * @param page         page
     * @param agentQueryVo bagQueryVo
     * @return 条件加分页查询
     */
    IPage<MnjxAgent> retrieveListByPage(IPage<MnjxAgent> page, AgentQueryVo agentQueryVo);

    /**
     * 查询代理人的航司授权信息
     *
     * @param agentId 代理人id
     * @return 代理人的航司授权信息
     */
    List<AgentAirlineVo> retrieveAgentAirline(String agentId);

    /**
     * 更新代理人
     *
     * @param mnjxAgent mnjxAgent
     * @return 更新代理人
     * @throws UnifiedResultException 统一异常
     */
    boolean updateById(MnjxAgent mnjxAgent) throws UnifiedResultException;

    /**
     * 通过ID更新状态
     *
     * @param id     id
     * @param status status
     * @return 通过ID更新状态
     * @throws UnifiedResultException 统一异常
     */
    boolean updateStatusById(String id, String status) throws UnifiedResultException;

    /**
     * 批量删除
     *
     * @param idList idList
     * @return 批量删除
     * @throws UnifiedResultException 统一异常
     */
    boolean deleteByIds(List<String> idList) throws UnifiedResultException;
}
