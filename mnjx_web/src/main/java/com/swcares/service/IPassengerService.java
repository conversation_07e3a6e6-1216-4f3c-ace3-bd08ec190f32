package com.swcares.service;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.name.ChineseNameDto;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.dto.AvDto;
import com.swcares.obj.dto.PassengerBookDto;
import com.swcares.obj.vo.AvVo;
import com.swcares.obj.vo.EtdzVo;
import com.swcares.obj.vo.excel.PassengerExcelVo;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 旅客管理服务
 *
 * <AUTHOR>
 */
public interface IPassengerService {

    /**
     * 航班旅客生成服务
     *
     * @param memoryDataPnr    PNR内存大对象
     * @param passengerBookDto 预定对象
     * @return 订座是否成功
     * @throws UnifiedResultException 统一异常
     */
    EtdzVo bookingSeatsForDto(MemoryDataPnr memoryDataPnr, PassengerBookDto passengerBookDto) throws UnifiedResultException;

    /**
     * 获取指定数量的旅客信息
     *
     * @param num 需要获取的数量
     * @return 获取指定数量的旅客信息
     */
    List<ChineseNameDto> retrieveChineseNames(int num);

    /**
     * 获取指令数量的电话号码
     *
     * @param num 需要获取的数量
     * @return 获取指令数量的电话号码
     */
    List<String> retrieveChineseMobiles(int num);

    /**
     * 查询航班数据
     *
     * @param memoryDataPnr PNR内存大对象
     * @param avDto         查询对象
     * @return 航班数据
     * @throws UnifiedResultException 统一异常
     */
    List<AvVo> av(MemoryDataPnr memoryDataPnr, AvDto avDto) throws UnifiedResultException;


    /**
     * 根据流数据预定航班座位
     *
     * @param allDatas   解析后的所有数据
     * @param flightDate 航班日期
     */
    void bookingSeatsForInputStream(Map<String, List<PassengerExcelVo>> allDatas, String flightDate);

    /**
     * 读取数据并且进行解析
     *
     * @param inputStream 文件输入流
     * @return 所有解析后的数据
     * @throws IOException io异常
     */
    Map<String, List<PassengerExcelVo>> preprocessingExcelData(InputStream inputStream) throws IOException;
}
