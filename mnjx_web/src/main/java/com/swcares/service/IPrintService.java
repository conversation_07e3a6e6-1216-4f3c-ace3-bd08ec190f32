package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;

import java.util.List;
import java.util.Map;

public interface IPrintService {

    /**
     * @param tktNoList
     * @return
     * @throws Exception <br>
     * @title：getPrintData <br>
     * @description：TODO(这里用一句话描述这个方法的作用) <br>
     * <AUTHOR> <br>
     * @date 2023/07/13 <br>
     */
    public List<Map<String, Object>> getPrintData(List<String> tktNoList) throws Exception;

    /**
     * @param tktNoList
     * @return
     * @throws Exception <br>
     * @title：getPrintData <br>
     * @description：TODO(这里用一句话描述这个方法的作用) <br>
     * <AUTHOR> <br>
     * @date 2023/07/13 <br>
     */
    public List<Map<String, Object>> getPreviewData(List<String> tktNoList) throws Exception;

    List<String> getTktNoByPnr(String pnr) throws UnifiedResultException;

    List<String> getBoardingPrintData(String key);

    List<String> getLuggagePrintData(String key, String bagNo);

    String getSavedForPrintDataList(String key) throws UnifiedResultException;
}
