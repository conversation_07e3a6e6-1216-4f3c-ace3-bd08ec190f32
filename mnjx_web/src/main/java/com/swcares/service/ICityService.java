package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxCity;
import com.swcares.obj.vo.excel.CityExcellVo;
import com.swcares.obj.vo.CityQueryVo;
import com.swcares.obj.vo.CityVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICityService {
    /**
     * 新增城市
     *
     * @param mnjxCity 数据对象
     * @return 新增是否成功
     * @throws UnifiedResultException 统一异常
     */
    boolean create(MnjxCity mnjxCity) throws UnifiedResultException;

    /**
     * 通过excel导入
     *
     * @param mnjxCities mnjxCities
     * @return 通过excel导入
     */
    List<CityExcellVo> createByExcel(List<MnjxCity> mnjxCities);

    /**
     * 通过id提取城市
     *
     * @param cityId id
     * @return 通过id提取城市
     */
    MnjxCity retrieveById(String cityId);

    /**
     * 分页+条件查询城市信息
     *
     * @param page        page
     * @param cityQueryVo cityQueryVo
     * @return 分页+条件查询城市信息
     */
    IPage<CityVo> retrievePageByCond(IPage<CityVo> page, CityQueryVo cityQueryVo);

    /**
     * 列表对象转换
     *
     * @param cityExcelVos cityExcelVos
     * @return 列表对象转换
     */
    List<MnjxCity> vo2Entity(List<CityExcellVo> cityExcelVos);


    /**
     * 更新城市信息
     *
     * @param mnjxCity 需要更新城市信息
     * @return 成功与否
     * @throws UnifiedResultException 统一异常
     */
    boolean update(MnjxCity mnjxCity) throws UnifiedResultException;

    /**
     * 修改状态
     *
     * @param cityId cityId
     * @param status status
     * @return 修改状态
     */
    boolean updateCityStatus(String cityId, String status);

    /**
     * 批量删除
     *
     * @param idList 城市id集合
     * @return 成功与否
     */
    String delete(List<String> idList) throws UnifiedResultException;


    void bindState(List<String> cityIds, String stateId) throws UnifiedResultException;

    void unbindState(String cityId);
}
