package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import com.swcares.entity.MnjxLevel;
import com.swcares.entity.MnjxSi;
import com.swcares.obj.vo.LevelOrderVo;
import com.swcares.obj.vo.OrderLevelTypeQueryVo;
import com.swcares.service.ILevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by 鱼仔
 * 2021/8/23-13:15
 * 全部的级别管理接口
 */
@Slf4j
@Api(tags = "级别管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/level")
public class LevelController {
    @Resource
    private ILevelService iLevelService;

    @ApiOperation("新增级别信息")
    @PostMapping("/creatLevel")
    public String creatLevel(@RequestBody @Valid MnjxLevel mnjxLevel) throws UnifiedResultException {
        boolean result = iLevelService.creatLevel(mnjxLevel);
        return result ? "添加成功" : "添加失败";
    }


    @ApiOperation("进行级别和指令绑定")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "levelOrderVos",
                    value = "指令名列表",
                    required = true,
                    dataTypeClass = LevelOrderVo.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })

    @PostMapping("/createBoundLevelOrder")
    public String createBoundLevelOrder(@RequestBody LevelOrderVo levelOrderVos) throws UnifiedResultException {
        return iLevelService.createBoundLevelOrder(levelOrderVos);
    }

    @ApiOperation("查询所有级别信息")
    @GetMapping("/retrieveList")
    public List<MnjxLevel> retrieveList() {
        return iLevelService.retrieveList();
    }

    @ApiOperation("根据级别号获取级别信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "levelCode",
                    value = "级别号",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = Integer.class
            )
    })
    @GetMapping("/retrieveLevelCode/{levelCode}")
    public MnjxLevel retrieveLevelCode(@PathVariable String levelCode) {
        return iLevelService.retrieveLevelCode(levelCode);
    }

    /**
     * 分页查询代理人的所有信息
     */
    @ApiOperation("分页查询级别信息")
    @GetMapping("/retrieveListByPage/{current}/{limit}")
    @PageCheck
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    name = "levelCode",
                    value = "登记",
                    paramType = Constant.PARAM_TYPE_QUERY,
                    dataTypeClass = String.class
            )
    })
    public IPage<MnjxLevel> retrieveListByPage(@PathVariable long current, @PathVariable long limit, String levelCode) {
        return iLevelService.retrieveListByPage(current, limit, levelCode);
    }

    @ApiOperation("根据id获取级别信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "levelId",
                    value = "级别id",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @GetMapping("/retrieveById/{levelId}")
    public MnjxLevel retrieveById(@PathVariable String levelId) {
        return iLevelService.retrieveById(levelId);
    }

    @ApiOperation("查询全部的指令集合以及该权限下的所有指令")
    @GetMapping("/retrieveOrder")
    public List<OrderLevelTypeQueryVo> retrieveOrder(String levelCode) throws UnifiedResultException {
        return iLevelService.retrieveOrder(levelCode);
    }

    @ApiOperation("修改级别信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxLevel",
                    value = "级别信息对象",
                    required = true,
                    dataTypeClass = MnjxLevel.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    @PostMapping("/update")
    public String update(@RequestBody @Valid MnjxLevel mnjxLevel) throws UnifiedResultException {
        return iLevelService.update(mnjxLevel);
    }

    /**
     * [根据id修改状态--0:启用1:停用]
     */
    @ApiOperation("修改级别状态")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ids",
                    value = "级别ID列表",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class
            )
    })
    @PostMapping("/updateStatusById")
    public String updateStatusById(@RequestBody List<String> levelIds) throws UnifiedResultException {
        List<MnjxLevel> list = iLevelService.retrieveList();
        List<MnjxLevel> mnjxLevels = list.stream().filter(mnjxLevel -> levelIds.contains(mnjxLevel.getLevelId())).collect(Collectors.toList());
        for (MnjxLevel mnjxLevel : mnjxLevels) {
            String levelId = mnjxLevel.getLevelId();
            List<MnjxSi> mnjxSi = iLevelService.retrieveSiByLevelId(levelId);
            if (ObjectUtils.isNotEmpty(mnjxSi)) {
                throw new UnifiedResultException("工作号使用中");
            }
            mnjxLevel.setLevelStatus(Constant.STR_ZERO.equals(mnjxLevel.getLevelStatus()) ? "1" : "0");
            iLevelService.updateStatusById(mnjxLevel);
        }
        return "修改成功";
    }

    /**
     * 根据id批量删除
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ids",
                    value = "级别ID列表",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class
            )
    })
    @DeleteMapping("/deleteRows")
    public String deleteRows(@RequestBody List<String> ids) throws UnifiedResultException {
        return iLevelService.deleteRows(ids);
    }
}
