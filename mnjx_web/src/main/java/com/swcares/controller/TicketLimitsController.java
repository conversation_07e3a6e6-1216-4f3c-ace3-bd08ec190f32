package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxTicketLimits;
import com.swcares.service.ITicketLimitsService;
import com.swcares.obj.vo.OfficeListVo;
import com.swcares.obj.vo.TicketLimitsRetrieveVo;
import com.swcares.obj.vo.TicketLimitsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * description：TicketLimitsController
 *
 * <AUTHOR>
 * @date 2022/03/30
 */
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping("/ticketLimits")
@Api(tags = "票号范围管理")
@RestController
public class TicketLimitsController {

    @Resource
    private ITicketLimitsService iTicketLimitsService;

    @ApiOperation("新增")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxTicketLimits",
                    value = "票设置对象",
                    paramType = Constant.PARAM_TYPE_BODY,
                    required = true,
                    dataTypeClass = MnjxTicketLimits.class
            )
    })
    @PostMapping("/create")
    public String create(@Valid @RequestBody MnjxTicketLimits mnjxTicketLimits) throws UnifiedResultException {
        boolean isOk = iTicketLimitsService.create(mnjxTicketLimits);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("下拉查询机构")
    @GetMapping("/retrieveOrgDropdownList")
    public List<OfficeListVo> retrieveOrgDropdownList(String officeType) {
        return iTicketLimitsService.retrieveOrgDropdownList(officeType);
    }

    @ApiOperation("下拉查询OFFICE号")
    @GetMapping("/retrieveOfficeNoDropdownList")
    public List<MnjxOffice> retrieveOfficeNoDropdownList(String officeType, String orgId){
        return iTicketLimitsService.retrieveOfficeNoDropdownList(officeType, orgId);
    }

    @ApiOperation("分页列表查询")
    @PostMapping("/retrieveListByPage/{current}/{limit}")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "查询条件",
                    name = "ticketLimitsRetrieveVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = TicketLimitsRetrieveVo.class
            )
    })
    @PageCheck
    public IPage<TicketLimitsVo> retrieveListByPage(@PathVariable long current,
                                                    @PathVariable long limit,
                                                    @RequestBody TicketLimitsRetrieveVo ticketLimitsRetrieveVo) {
        return iTicketLimitsService.retrieveListByPage(new Page<>(current, limit), ticketLimitsRetrieveVo);
    }

    @ApiOperation("修改")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxTicketLimits",
                    value = "票设置对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxTicketLimits.class
            )
    })
    @PutMapping("/update")
    public String update(@RequestBody MnjxTicketLimits mnjxTicketLimits) throws UnifiedResultException {
        boolean isOk = iTicketLimitsService.update(mnjxTicketLimits);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ticketLimitsId",
                    value = "票号范围主键",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @DeleteMapping("/deleteById/{ticketLimitsId}")
    public String deleteById(@PathVariable String ticketLimitsId) throws UnifiedResultException {
        boolean isOk = iTicketLimitsService.deleteById(ticketLimitsId);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}
