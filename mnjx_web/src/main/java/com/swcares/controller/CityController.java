package com.swcares.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxCity;
import com.swcares.obj.vo.excel.CityExcellVo;
import com.swcares.obj.vo.CityQueryVo;
import com.swcares.obj.vo.CityVo;
import com.swcares.service.ICityService;
import com.swcares.core.utils.ExcelUtils;
import com.swcares.core.utils.NetFileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@Api(tags = "城市管理")
@RestController()
@RequestMapping("/city")
@Slf4j
public class CityController {
    @Resource
    private ICityService iCityService;
    @Resource
    private CacheManager cacheManager;

    @ApiOperation("添加城市")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxCity",
                    value = "添加对象",
                    readOnly = true,
                    dataTypeClass = MnjxCity.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    @PostMapping("/create")
    public String create(@RequestBody @Validated MnjxCity mnjxCity) throws UnifiedResultException {
        boolean isOk = iCityService.create(mnjxCity);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("导入城市列表")
    @PostMapping("createByExcell")
    public String createByExcell(@RequestPart("file") MultipartFile file) throws IOException, UnifiedResultException {
        Cache cache = cacheManager.getCache(SecurityController.CACHE_WORM_REAR);
        //清理缓存
        cache.evict("failList");
        // 将excell中的数据全部转换为内存中的vo对象
        List<CityExcellVo> cityExcellVos = ExcelUtils.read(file, CityExcellVo.class);
        // 将vo对象全部转换为entity对象
        List<MnjxCity> mnjxCities = iCityService.vo2Entity(cityExcellVos);
        // 将所有数据都保存了
        List<CityExcellVo> allList = iCityService.createByExcel(mnjxCities);
        if (CollUtil.isEmpty(allList)) {
            return Constant.IMPORT_SUCCESS;
        } else {
            int failSize = (int) allList.stream().
                    filter(cityExcellVo -> StrUtil.isNotEmpty(cityExcellVo.getRemark())).count();
            int successSize = (int) allList.stream().
                    filter(cityExcellVo -> StrUtil.isEmpty(cityExcellVo.getRemark())).count();
            cache.put("failList", allList);
            throw new UnifiedResultException(StrFormatter.format("{}{}{}{}{}", Constant.IMPORT_SUCCESS, successSize, "条,", Constant.IMPORT_FAIL, failSize, "条数，数据可能已经存在"));
        }
    }

    @ApiOperation("通过ID获取具体的对象")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "cityId",
                    value = "城市ID",
                    readOnly = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    @GetMapping("/retrieveById/{cityId}")
    public MnjxCity retrieveById(@PathVariable String cityId) {
        return iCityService.retrieveById(cityId);
    }

    @ApiOperation("分页+条件查询城市信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "current",
                    value = "当前页码",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    name = "limit",
                    value = "每页记录数",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    name = "cityQueryVo",
                    value = "查询对象",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CityQueryVo.class
            )
    })
    @PostMapping("/retrievePageByCond/{current}/{limit}")
    @PageCheck
    public IPage<CityVo> retrievePageByCond(@PathVariable long current,
                                            @PathVariable long limit,
                                            @RequestBody CityQueryVo cityQueryVo) {
        return iCityService.retrievePageByCond(new Page<>(current, limit), cityQueryVo);
    }


    @ApiOperation("修改城市信息")
    @ApiImplicitParam(
            name = "mnjxCity",
            value = "城市信息对象",
            required = true,
            paramType = Constant.PARAM_TYPE_BODY,
            dataTypeClass = MnjxCity.class
    )
    @PutMapping("/updateCity")
    public String updateCity(@RequestBody @Validated MnjxCity mnjxCity) throws UnifiedResultException {
        boolean isOk = iCityService.update(mnjxCity);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation("修改城市信息--是否启用该城市")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "cityId",
                    value = "城市id",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class,
                    example = "1493493314010734594"
            ),
            @ApiImplicitParam(
                    name = "status",
                    value = "是否启用该城市 状态 0,启用 1,停用",
                    required = true,
                    paramType = Constant.PARAM_TYPE_QUERY,
                    dataTypeClass = String.class,
                    example = "1"
            )
    })
    @PutMapping("/updateCityStatus/{cityId}")
    public String updateCityStatus(@PathVariable String cityId, @RequestParam String status) throws UnifiedResultException {
        boolean isOk = iCityService.updateCityStatus(cityId, status);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteCitys")
    @ApiImplicitParam(
            name = "ids",
            value = "id列表",
            required = true,
            paramType = Constant.PARAM_TYPE_BODY,
            dataTypeClass = List.class
    )
    public String deleteCities(@RequestBody List<String> ids) throws UnifiedResultException {
        return iCityService.delete(ids);
    }

    @GetMapping(value = "/downloadFailFile")
    @ApiOperation("下载导入失败文件")
    public void downloadFailFile(HttpServletResponse response) throws Exception {
        Cache cache = cacheManager.getCache(SecurityController.CACHE_WORM_REAR);
        //处理失败的文件
        List<CityExcellVo> failList = (List<CityExcellVo>) cache.get("failList").get();
        if (CollUtil.isEmpty(failList)) {
            throw new UnifiedResultException("有导入失败数据才可以下载");
        }
        // 通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 合并单元格后的标题行，使用默认标题样式
        writer.merge(3, "导入失败，下列城市已经存在");
        writer.autoSizeColumnAll();
        //自定义标题别名
        writer.addHeaderAlias("cityCname", "城市中文名");
        writer.addHeaderAlias("cityCode", "城市代码");
        writer.addHeaderAlias("remark", "备注");
        List<CityExcellVo> rows = new ArrayList<>(failList);
        // 默认的，未添加alias的属性也会写出，如果想只写出加了别名的字段，可以调用此方法排除之
        writer.setOnlyAlias(true);
        writer.write(rows);
        String excelName = "CityFailTemp.xlsx";
        NetFileUtils.downloadHutool(response, excelName, writer);
    }

    @ApiOperation("城市绑定所属州")
    @PutMapping("/bindState")
    @ApiImplicitParam(
            name = "stateId",
            value = "州ID",
            required = true,
            paramType = Constant.PARAM_TYPE_QUERY,
            dataTypeClass = String.class
    )
    public UnifiedResult bindState(@RequestBody List<String> cityIds, @RequestParam String stateId) throws UnifiedResultException {
        iCityService.bindState(cityIds, stateId);
        return UnifiedResult.ok();
    }

    @ApiOperation("解除已绑定的州")
    @PutMapping("/unbindState")
    @ApiImplicitParam(
            name = "cityId",
            value = "城市ID",
            required = true,
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_QUERY
    )
    public UnifiedResult unbindState(@RequestParam String cityId) {
        iCityService.unbindState(cityId);
        return UnifiedResult.ok();
    }
}
