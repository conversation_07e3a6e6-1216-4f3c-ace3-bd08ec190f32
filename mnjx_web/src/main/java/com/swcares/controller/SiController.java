package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxLevel;
import com.swcares.entity.MnjxOffice;
import com.swcares.obj.vo.*;
import com.swcares.service.ISiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "工作号管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/si")
public class SiController {
    @Resource
    private ISiService isiService;

    @ApiOperation("新增工作号")
    @ApiImplicitParam(name = "siCreateVo",
            value = "工作号新增参数实体",
            required = true,
            paramType = Constant.PARAM_TYPE_BODY,
            dataTypeClass = SiCreateVo.class)
    @PostMapping("/create")
    public String create(@RequestBody @Valid SiCreateVo siCreateVo) throws UnifiedResultException {
        boolean isOk = isiService.create(siCreateVo);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("获取机构类型")
    @GetMapping("/retrieveOfficeType")
    public List<Map<String, String>> retrieveOfficeType() {
        return isiService.retrieveOfficeType();
    }

    @ApiOperation(value = "根据机构类型查找机构列表")
    @ApiImplicitParam(
            name = "officeType",
            value = "机构类型",
            required = true,
            paramType = Constant.PARAM_TYPE_PATH,
            dataTypeClass = String.class
    )
    @GetMapping("/retrieveOrganizationByOfficeType/{officeType}")
    public Set<OfficeListVo> retrieveOrganization(@PathVariable String officeType) throws UnifiedResultException {
        return isiService.retrieveOrganization(officeType);
    }

    @ApiOperation("根据机构查询工作号")
    @ApiImplicitParam(
            name = "orgId",
            value = "机构ID",
            required = true,
            paramType = Constant.PARAM_TYPE_PATH,
            dataTypeClass = String.class
    )
    @GetMapping("/retrieveOfficeNoByOrganization/{orgId}")
    public List<MnjxOffice> retrieveOfficeNoByOrganization(@PathVariable String orgId) {
        return isiService.retrieveOfficeNoByOrganization(orgId);
    }

    @ApiOperation("获取所有的工作号级别")
    @GetMapping("/retrieveLevelCode")
    public List<MnjxLevel> retrieveLevelCode() {
        return isiService.retrieveLevelCode();
    }

    @ApiOperation("分页查询工作号")
    @PostMapping("/retrieveListByPage/{current}/{size}")
    public IPage<SiReturnVo> retrieveListByPage(@PathVariable Long current,
                                                @PathVariable Long size,
                                                @RequestBody @Valid SiQueryVo siQueryVo) {
        return isiService.retrieveListByPage(current, size, siQueryVo);
    }

    @ApiOperation("修改工作号")
    @ApiImplicitParam(name = "SIUpdateVo",
            value = "工作号修改参数实体",
            required = true,
            paramType = Constant.PARAM_TYPE_BODY,
            dataTypeClass = SiUpdateVo.class)
    @PutMapping("/update")
    public String update(@RequestBody @Valid SiUpdateVo siUpdateVo) throws UnifiedResultException {
        Boolean result = isiService.update(siUpdateVo);
        return result ? Constant.UPDATE_SUCCESS : Constant.UPDATE_FAIL;
    }

    @ApiOperation("根据id修改工作号状态")
    @ApiImplicitParam(name = "id",
            value = "工作号ID",
            required = true,
            dataTypeClass = String.class)
    @PutMapping("/updateStatusById/{id}")
    public String updateStatusById(@PathVariable String id) throws UnifiedResultException {
        Boolean result = isiService.updateStatusById(id);
        return result ? Constant.UPDATE_SUCCESS : Constant.UPDATE_FAIL;
    }

    @ApiOperation("根据id删除工作号")
    @ApiImplicitParam(name = "id",
            value = "工作号ID",
            required = true,
            dataTypeClass = String.class)
    @DeleteMapping("/deleteById/{id}")
    public String deleteById(@PathVariable String id) throws UnifiedResultException {
        Boolean result = isiService.deleteById(id);
        return result ? Constant.DELETE_SUCCESS : Constant.DELETE_FAIL;
    }

}
