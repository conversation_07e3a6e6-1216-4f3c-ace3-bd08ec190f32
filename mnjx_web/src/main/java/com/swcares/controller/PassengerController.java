package com.swcares.controller;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.name.ChineseNameDto;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.BeanUtils;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import com.swcares.obj.dto.AvDto;
import com.swcares.obj.dto.PassengerBookDto;
import com.swcares.obj.vo.AvVo;
import com.swcares.obj.vo.EtdzVo;
import com.swcares.obj.vo.excel.PassengerExcelVo;
import com.swcares.obj.vo.PassengerReservationsPageVo;
import com.swcares.service.IPassengerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 旅客数据生成
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "旅客管理")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/passenger")
public class PassengerController {
    @Resource
    private IPassengerService iPassengerService;

    @ApiOperation(value = "新增旅客订座记录", notes = "旅客订座记录新增接口")
    @ApiImplicitParams(@ApiImplicitParam(value = "旅客订座记录输入项", name = "passengerRecordVo", required = true, dataTypeClass = PassengerReservationsPageVo.class, paramType = Constant.PARAM_TYPE_BODY))
    @PostMapping("/bookingSeats")
    public String bookingSeats(@RequestBody PassengerReservationsPageVo passengerReservationsPageVo) throws UnifiedResultException {
        PassengerBookDto passengerBookDto = new PassengerBookDto();
        BeanUtils.copyProperties(passengerReservationsPageVo, passengerBookDto, false);
        // 生成Pnr记录
        EtdzVo etdzVo = iPassengerService.bookingSeatsForDto(new MemoryDataPnr(), passengerBookDto);
        // 结果反馈
        if (ObjectUtils.isNotNull(etdzVo)) {
            return Constant.BOOKING_SEAT_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.BOOKING_SEAT_FAIL);
        }
    }

    @ApiOperation(value = "根据excell的文件生成航班的订座记录")
    @ApiImplicitParams({@ApiImplicitParam(name = "flightDate", value = "航班日期", paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = String.class, example = "2022-07-27")})
    @PostMapping("/bookingSeatsByXls/{flightDate}")
    public String bookingSeatsByXls(@RequestPart("file") MultipartFile file, @PathVariable("flightDate") String flightDate) throws IOException {
        // 获得文件的输入流
        InputStream inputStream = file.getInputStream();
        // 解析所有的数据，按照sheet的名称为key的格式进行存储
        Map<String, List<PassengerExcelVo>> sortedFinalAllDatas = iPassengerService.preprocessingExcelData(inputStream);
        // 使用流的方式生成
        iPassengerService.bookingSeatsForInputStream(sortedFinalAllDatas, flightDate);
        // 结果反馈
        return Constant.BOOKING_SEAT_SUCCESS;
    }

    @ApiOperation("查询航班信息(这个要和AV的逻辑一致)")
    @ApiImplicitParams({@ApiImplicitParam(name = "org", value = "出发机场三字码", required = true, paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = String.class, example = "CTU"), @ApiImplicitParam(name = "dst", value = "到达机场三字码", required = true, paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = String.class, example = "PEK"), @ApiImplicitParam(name = "flightDate", value = "航班日期", required = true, paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = String.class, example = "2022-07-27")})
    @GetMapping("/av/{org}/{dst}/{flightDate}")
    public List<AvVo> av(@PathVariable("org") String org, @PathVariable("dst") String dst, @PathVariable("flightDate") String flightDate) throws UnifiedResultException {
        AvDto avDto = new AvDto();
        // 出发城市
        avDto.setOrgCity(org);
        // 目标城市
        avDto.setDstCity(dst);
        // 航班日期
        avDto.setFlightDate(flightDate);
        // 查询航班数据
        return iPassengerService.av(new MemoryDataPnr(), avDto);
    }

    @ApiOperation("获取指定数量的旅客信息")
    @ApiImplicitParams(@ApiImplicitParam(value = "要生成多少个旅客数据", name = "num", required = true, paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = Integer.class))
    @GetMapping("/retrieveChineseNames/{num}")
    public List<ChineseNameDto> retrieveChineseNames(@PathVariable("num") int num) {
        return iPassengerService.retrieveChineseNames(num);
    }

    @ApiOperation("获取指定数量的电话号码")
    @ApiImplicitParams(@ApiImplicitParam(value = "要生成多少个电话号码", name = "num", required = true, paramType = Constant.PARAM_TYPE_PATH, dataTypeClass = Integer.class))
    @GetMapping("/retrieveChineseMobiles/{num}")
    public List<String> retrieveChineseMobiles(@PathVariable("num") int num) {
        return iPassengerService.retrieveChineseMobiles(num);
    }
}
