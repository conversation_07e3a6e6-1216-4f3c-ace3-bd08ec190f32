package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxLevelOrder;
import com.swcares.service.ILevelOrderService;
import com.swcares.service.IMnjxLevelOrderService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/23-13:15
 */
@Api(tags = "指令级别关系管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/levelOrder")
@Slf4j
public class LevelOrderController {
    @Resource
    private IMnjxLevelOrderService iMnjxLevelOrderService;
    @Resource
    private ILevelOrderService iLevelOrderService;

    @ApiOperation("新增指令信息")
    @PostMapping("/createLevelOrder")
    public String createLevelOrder(@RequestBody MnjxLevelOrder mnjxLevelOrder) throws UnifiedResultException {
        return iLevelOrderService.createLevelOrder(mnjxLevelOrder);
    }


    @ApiOperation("查询所有指令信息")
    @PostMapping("/retrieveList")
    public List<MnjxLevelOrder> retrieveList() {
        List<MnjxLevelOrder> list = iMnjxLevelOrderService.list();
        return list;
    }

    @ApiOperation("查询级别号对应的指令名称")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "levelCode",
                    value = "级别号",
                    required = true,
                    dataTypeClass = Integer.class,
                    paramType = Constant.PARAM_TYPE_PATH

            )
    })
    @GetMapping("/retrieveOrderByLevelCode/{levelCode}")
    public List<MnjxLevelOrder> retrieveOrderByLevelCode(@PathVariable Integer levelCode) {
        return iLevelOrderService.retrieveOrderByLevelCode(levelCode);
    }

    @ApiOperation("根据id获取指令级别信息")
    @GetMapping("/retrieveById/{id}")
    public MnjxLevelOrder retrieveById(@ApiParam(value = "指令级别id", required = true) @PathVariable String id) {
        return iMnjxLevelOrderService.getById(id);
    }


    @ApiOperation("修改指令级别信息")
    @PostMapping("/update")
    public String update(@ApiParam(value = "指令级别信息对象", required = true) @RequestBody MnjxLevelOrder mnjxLevelOrder) {
        boolean result = iMnjxLevelOrderService.updateById(mnjxLevelOrder);
        return result == true ? "修改成功" : "修改失败";
    }

    /**
     * 根据id批量删除
     *
     * @param idList
     * @return
     */
    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteRows")
    public String deleteRows(@ApiParam(value = "指令级别ID列表", required = true) @RequestBody List<String> idList) {
        boolean result = iMnjxLevelOrderService.removeByIds(idList);
        return result == true ? "删除成功" : "删除失败";
    }

}
