package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.util.Constant;
import com.swcares.obj.vo.ActionRecordQueryVo;
import com.swcares.obj.vo.ActionRecordResultListVo;
import com.swcares.obj.vo.ActionRecordResultVo;
import com.swcares.service.IActionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> by sxl
 * 2023/2/2-13:15
 */
@Api(tags = "用户操作记录")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@Slf4j
@RestController
@RequestMapping("/actionRecord")

public class ActionRecordController {

    @Resource
    IActionRecordService iActionRecordService;

    @ApiOperation("分页查询操作记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "current",
                    value = "当前页码",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class),
            @ApiImplicitParam(
                    name = "limit",
                    value = "每页记录数",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class),
            @ApiImplicitParam(
                    name = "actionRecordQueryVo",
                    value = "查询对象",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = ActionRecordQueryVo.class)
    })
    @PageCheck
    @PostMapping("/retrievePage/{current}/{limit}")
    public IPage<ActionRecordResultVo> retrievePage(@PathVariable long current,
                                                    @PathVariable long limit,
                                                    @RequestBody ActionRecordQueryVo actionRecordQueryVo) {
        return iActionRecordService.retrievePage(new Page<>(current, limit), actionRecordQueryVo);
    }

    @ApiOperation("分页查询操作记录列表详情")
    @PostMapping("/retrieveDetail")
    public List<ActionRecordResultListVo> retrieveDetail(@RequestBody ActionRecordResultVo actionRecordResultVo){
        return iActionRecordService.retrieveDetail(actionRecordResultVo);
    }

    @ApiOperation("操作记录详情下载")
    @PostMapping("/download")
    public void fileDownload(@RequestBody ActionRecordQueryVo actionRecordQueryVo, HttpServletRequest request, HttpServletResponse response){
        iActionRecordService.download(actionRecordQueryVo,request,response);
    }
}
