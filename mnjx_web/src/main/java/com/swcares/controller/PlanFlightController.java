package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.service.IPlanFlightService;
import com.swcares.obj.vo.PlanFlightPageVo;
import com.swcares.obj.vo.PlanFlightRetrieveVo;
import com.swcares.obj.vo.PlanFlightUpdateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * description：航班飞行计划管理
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
@Slf4j
@Api(tags = "航班计划管理")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/planFlight")
public class PlanFlightController {

    @Resource
    private IPlanFlightService iPlanFlightService;

    @ApiOperation("分页查询航班飞行计划信息")
    @PostMapping("/retrievePage")
    @PageCheck(model = 2, sizeName = "limit")
    public IPage<PlanFlightPageVo> retrievePage(@Valid @RequestBody PlanFlightRetrieveVo queryVo) {
        return iPlanFlightService.retrievePage(queryVo);
    }

    @ApiOperation("修改航班计划")
    @PutMapping("/updatePlanFlight")
    public String updatePlanFlight(@RequestBody PlanFlightUpdateVo planFlightUpdateVo) throws UnifiedResultException {
        iPlanFlightService.updatePlanFlight(planFlightUpdateVo);
        return Constant.UPDATE_SUCCESS;
    }

    @ApiOperation(value = "根据id初始化航班计划", notes = "根据id初始化航班计划")
    @GetMapping("/initialPlanFlight/{planFlightId}")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "航班计划ID",
                    name = "planFlightId",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    public String initialPlanFlight(@PathVariable String planFlightId) throws UnifiedResultException {
        boolean result = iPlanFlightService.initialPlanFlight(planFlightId);
        if (result) {
            return Constant.INITIAL_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.INITIAL_FAIL);
        }
    }

    @ApiOperation(value = "根据id取消航班初始化", notes = "根据id取消航班初始化")
    @GetMapping("/cancelInitialPlanFlight/{planFlightId}")
    @ApiImplicitParam(
            value = "航班计划ID",
            name = "planFlightId",
            required = true,
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_PATH
    )
    public String cancelInitialPlanFlight(@PathVariable String planFlightId) throws UnifiedResultException {
        boolean result = iPlanFlightService.cancelInitialPlanFlight(planFlightId);
        if (result) {
            return Constant.CANCEL_INITIAL_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CANCEL_INITIAL_FAIL);
        }
    }

    @ApiOperation(value = "根据id取消航班计划", notes = "根据id取消航班计划")
    @GetMapping("/cancelPlanFlight/{planFlightId}")
    @ApiImplicitParam(
            value = "航班计划ID",
            name = "planFlightId",
            required = true,
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_PATH
    )
    public String cancelPlanFlight(@PathVariable String planFlightId) throws UnifiedResultException {
        boolean result = iPlanFlightService.cancelPlanFlight(planFlightId);
        if (result) {
            return Constant.CANCEL_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CANCEL_FAIL);
        }
    }
}
