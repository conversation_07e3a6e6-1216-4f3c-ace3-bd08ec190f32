package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.service.IOpenCabinService;
import com.swcares.obj.vo.OpenCabinRetrieveVo;
import com.swcares.obj.vo.OpenCabinPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@Api(tags = "开舱")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/openCabin")
@Slf4j
public class OpenCabinController {

    @Resource
    private IOpenCabinService iOpenCabinService;

    @ApiOperation("分页查询所有开舱信息")
    @PostMapping("/retrievePage")
    @PageCheck(model = 2, sizeName = "limit")
    public IPage<OpenCabinPageVo> retrievePage(@Valid @RequestBody OpenCabinRetrieveVo openCabinRetrieveVo) {
        return iOpenCabinService.retrievePage(openCabinRetrieveVo);
    }
}
