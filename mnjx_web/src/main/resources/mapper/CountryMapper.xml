<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.CountryMapper">

    <select id="retrievePageByCond" resultType="com.swcares.obj.vo.CountryVo">
        SELECT
            mc.* ,
            mis.continent_code
        FROM
            mnjx_country mc
            LEFT JOIN mnjx_continent mis ON mis.continent_id = mc.continent_id
        <where>
            <if test="countryQueryVo.countryIso != null and countryQueryVo.countryIso != ''">
                mc.country_iso like concat('%',#{countryQueryVo.countryIso},'%')
            </if>
            <if test="countryQueryVo.countryCname != null and countryQueryVo.countryCname != ''">
                AND mc.country_cname like concat('%',#{countryQueryVo.countryCname},'%')
            </if>
            <if test="countryQueryVo.continentCode != null and countryQueryVo.continentCode != ''">
                AND mis.continent_code like concat('%',#{countryQueryVo.continentCode},'%')
            </if>
            <if test="countryQueryVo.countryStatus != null and countryQueryVo.countryStatus != ''">
                AND mc.country_status =#{countryQueryVo.countryStatus}
            </if>
        </where>
    </select>
</mapper>
