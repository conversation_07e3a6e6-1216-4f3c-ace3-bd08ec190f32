<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.StsJobMapper">
    <select id="retrieveShaFlight" resultType="com.swcares.obj.dto.FlightGroupDto">
        SELECT DISTINCT mf.flight_no,
                        mts.dep_time
        FROM mnjx_flight mf
                 INNER JOIN mnjx_tcard mt ON mt.flight_id = mf.flight_id
                 INNER JOIN mnjx_tcard_section mts ON mts.tcard_id = mt.tcard_id
                 INNER JOIN mnjx_airport ma ON ma.airport_id = mts.airport_id
        WHERE mts.section_no = '1'
          AND ma.airport_code = 'SHA'
        ORDER BY mts.dep_time
    </select>

    <select id="retrieveSegDate" resultType="java.lang.String">
        select DISTINCT flight_date
        from mnjx_pnr_seg
        where flight_date <![CDATA[<]]> #{selectDate}
        ORDER BY flight_date ASC
    </select>

</mapper>