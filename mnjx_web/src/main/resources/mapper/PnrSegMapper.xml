<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PnrSegMapper">

    <select id="selectFlt" resultType="com.swcares.obj.vo.FlightInfoVo">
        SELECT
        IFNULL(A.flight_no,'') AS FLT_NO,
        IFNULL(A.flight_date,'') AS FLT_DATE,
        IFNULL(CONCAT(A.ORG,A.DST),'') AS ORGDEST,
        IFNULL(B.ESTIMATE_OFF,'') AS PLAN_OFF_DATE,
        IFNULL(B1.ESTIMATE_ARR,'') AS PLAN_ON_DATE,
        CASE WHEN CAST(B1.ESTIMATE_ARR as SIGNED) <![CDATA[<]]> CONVERT(B.ESTIMATE_OFF, SIGNED)
        THEN CONVERT(B1.ESTIMATE_ARR, SIGNED) + 2400
        WHEN CONVERT(B1.ESTIMATE_ARR, SIGNED) <![CDATA[>=]]> CONVERT(B.ESTIMATE_OFF, SIGNED)
        THEN CONVERT(B1.ESTIMATE_ARR, SIGNED)
        END AS ADJUSTMENT_PLAN_ON_DATE,
        IFNULL(V.plane_model_type,'') AS PLANE_NO,
        IFNULL(B.PLAN_SECTION_ID,'') AS PLAN_SECTION_ID
        FROM
        mnjx_plan_flight A
        INNER JOIN mnjx_plan_section B
        ON B.plan_flight_id = A.plan_flight_id
        AND B.plan_section_no = 1
        INNER JOIN MNJX_PLAN_SECTION B1
        ON B1.plan_flight_id = A.plan_flight_id
        AND B1.plan_section_no = 2
        INNER JOIN mnjx_flight F
        ON F.flight_no = A.flight_no
        INNER JOIN MNJX_PLANE P
        ON P.plane_no = F.plane_no
        INNER JOIN mnjx_plane_model V
        ON V.plane_model_id = P.plane_model_id
        WHERE 1 = 1
        AND (F.flight_status <![CDATA[!=]]> 'CANCEL' AND F.flight_status <![CDATA[!=]]> 'DELETE')
        AND STR_TO_DATE(F.,'%Y-%m-%d') <![CDATA[>=]]> CURRENT_DATE
        <if test="org != null and org != ''">
            AND A.ORG = #{org}
        </if>
        <if test="dst != null and dst != ''">
            AND A.DST = #{dst}
        </if>
        <if test="fltDate != null and fltDate != ''">
            <if test="fltDate==nowDate">
                AND str_to_date(B.ESTIMATE_OFF,'%H%i%S') <![CDATA[>=]]> CURRENT_TIME()
                AND str_to_date(B1.ESTIMATE_ARR,'%H%i%S') <![CDATA[>=]]> CURRENT_TIME()
            </if>
            AND A.flight_date = #{fltDate}
        </if>
    </select>

</mapper>
