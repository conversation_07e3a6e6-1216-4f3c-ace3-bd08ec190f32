<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PlanFlightMapper">

    <select id="retrievePlanFlightPageList" resultType="com.swcares.obj.vo.PlanFlightPageVo">
        select distinct
        mpf.plan_flight_id ,
        mpf.flight_date ,
        mf.flight_no ,
        mpf.flight_status ,
        mpf.allow_asr ,
        mpf.is_flight_initial ,
        mt.t_type ,
        mt.tcard_id ,
        mpf.cnd_no ,
        mpm.plane_model_type ,
        mpm.plane_model_version ,
        mt.is_n
        from
        mnjx_plan_flight mpf
        left join mnjx_tcard mt on mpf.tcard_id = mt.tcard_id
        left join mnjx_flight mf on mt.flight_id = mf.flight_id
        left join mnjx_cnd mc on mt.cnd_id = mc.cnd_id
        left join mnjx_plane_model mpm on mc.plane_model_id = mpm.plane_model_id
        left join mnjx_tcard_section mts on mt.tcard_id = mts.tcard_id
        where mf.flight_status = 'MASTER'
        <if test="vo.flightDateStart != '' and vo.flightDateStart != null and vo.flightDateEnd != '' and vo.flightDateEnd != null">
            and mpf.flight_date <![CDATA[>=]]> #{vo.flightDateStart}
            and mpf.flight_date <![CDATA[<=]]> #{vo.flightDateEnd}
        </if>
        <if test="vo.flightNo != '' and vo.flightNo != null">
            and mf.flight_no = #{vo.flightNo}
        </if>
        <if test="vo.airlineCode != '' and vo.airlineCode != null">
            and mpf.airline_code = #{vo.airlineCode}
        </if>
        <if test="vo.flightStatus != '' and vo.flightStatus != null">
            and mpf.flight_status = #{vo.flightStatus}
        </if>
        <if test="tcardIdList != null and tcardIdList.size() != 0">
            and mts.tcard_id in
            <foreach collection="tcardIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by
        mf.flight_no ,
        mpf.flight_date desc
    </select>

    <select id="retrievePlanFlightAirportListByPlanFlightId" resultType="com.swcares.obj.vo.PlanFlightAirportVo">
        select
        mps.plan_flight_id ,
        (
        select
        ma.airport_code
        from
        mnjx_airport ma
        where
        ma.airport_id = mps.dep_apt_id) org ,
        (
        select
        ma.airport_code
        from
        mnjx_airport ma
        where
        ma.airport_id = mps.arr_apt_id) dst ,
        mps.estimate_off ,
        mps.estimate_off_change ,
        mps.estimate_arr ,
        mps.estimate_arr_change ,
        mps.estimate_boarding ,
        mps.estimate_boarding_change
        from
        mnjx_plan_section mps
        where 1 = 1
        <if test="planFlightIdList != null and planFlightIdList.size() > 0">
            and mps.plan_flight_id in
            <foreach collection="planFlightIdList" item="planFlightId" open="(" separator="," close=")">
                #{planFlightId}
            </foreach>
        </if>
        order by mps.is_last_section ,mps.estimate_off_change ,mps.estimate_off
    </select>

    <select id="retrieveTcardSectionPlanFlightId" resultType="com.swcares.obj.vo.TcardSectionPlanFlightIdVo">
        select
        mpf.plan_flight_id ,
        (
        select
        ma.airport_code
        from
        mnjx_airport ma
        where
        ma.airport_id = mts.airport_id) airport_code ,
        mts.*
        from
        mnjx_tcard_section mts
        left join mnjx_plan_flight mpf on
        mts.tcard_id = mpf.tcard_id
        where 1 = 1
        <if test="planFlightIdList != null and planFlightIdList.size() > 0">
            and mpf.plan_flight_id in
            <foreach collection="planFlightIdList" item="planFlightId" open="(" separator="," close=")">
                #{planFlightId}
            </foreach>
        </if>
        order by mts.section_no
    </select>

    <select id="retrieveNoDate" resultType="com.swcares.obj.vo.PlanFlightNoDateVo">
        SELECT mpf.plan_flight_id,
               mf.flight_no,
               mpf.flight_date
        FROM mnjx_plan_flight mpf
                 LEFT JOIN mnjx_tcard mt ON mt.tcard_id = mpf.tcard_id
                 LEFT JOIN mnjx_flight mf ON mf.flight_id = mt.flight_id
        WHERE mpf.flight_date <![CDATA[<]]> #{date}
    </select>
    <select id="retrieveNoDateList" resultType="com.swcares.obj.vo.PlanFlightNoDateVo">
        SELECT
        mpf.plan_flight_id,
        mf.flight_no,
        mpf.flight_date
        FROM
        mnjx_plan_flight mpf
        LEFT JOIN mnjx_tcard mt ON mt.tcard_id = mpf.tcard_id
        LEFT JOIN mnjx_flight mf ON mf.flight_id = mt.flight_id
        WHERE mpf.flight_date in
        <foreach collection="dateList" item="date" open="(" separator="," close=")">
            #{date}
        </foreach>
    </select>
</mapper>
