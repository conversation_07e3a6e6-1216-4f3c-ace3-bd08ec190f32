<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PassengerMapper">

    <select id="getMnjxOpenCabin" resultType="com.swcares.entity.MnjxOpenCabin">
        SELECT moc.*
        FROM mnjx_flight mf,
             mnjx_tcard mt,
             mnjx_plan_flight mpf,
             mnjx_plan_section mps,
             mnjx_airport ma_org,
             mnjx_airport ma_dst,
             mnjx_open_cabin moc
        WHERE mf.flight_id = mt.flight_id
          AND mt.tcard_id = mpf.tcard_id
          AND mpf.plan_flight_id = mps.plan_flight_id
          AND mps.dep_apt_id = ma_org.airport_id
          AND mps.arr_apt_id = ma_dst.airport_id
          AND mps.plan_section_id = moc.plan_section_id
          AND mf.flight_no = #{mnjxPnrSeg.flightNo}
          AND mpf.flight_date = #{mnjxPnrSeg.flightDate}
          AND ma_org.airport_code = #{mnjxPnrSeg.org}
          AND ma_dst.airport_code = #{mnjxPnrSeg.dst}
          AND moc.cabin_class = #{mnjxPnrSeg.cabinClass}
          AND moc.sell_cabin = #{mnjxPnrSeg.sellCabin}
    </select>
</mapper>
