<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.LuggageMapper">

    <sql id="retrieveBag">
        select
            ml.luggage_no,
            mpnt.ticket_no,
            mpn.name,
            mps.flight_no,
            mps.flight_date
        from
            mnjx_pnr_nm mpn
            left join mnjx_pnr_nm_ticket mpnt on mpn.pnr_nm_id = mpnt.pnr_nm_id
            left join mnjx_luggage ml on mpn.pnr_nm_id = ml.pnr_nm_id
            left join mnjx_pnr mp on mpn.pnr_id = mp.pnr_id
            left join mnjx_psg_cki mpc on mpn.pnr_nm_id = mpc.pnr_nm_id
            left join mnjx_pnr_seg mps on mp.pnr_id = mps.pnr_id
        where
            mps.pnr_seg_no = mpc.pnr_seg_no
    </sql>

    <select id="retrieveAllBagByLimit" resultType="com.swcares.obj.vo.LuggageVo">
        <include refid="retrieveBag"/>
    </select>

    <select id="retrieveBagByLimitAndCondition" resultType="com.swcares.obj.vo.LuggageVo"
            parameterType="com.swcares.obj.vo.LuggageRetrieveVo">
        <include refid="retrieveBag"/>
        <if test="luggageRetrieveVo.flightNo != null and luggageRetrieveVo.flightNo != ''">
            and instr(mps.flight_no, #{luggageRetrieveVo.flightNo}) > 0
        </if>

        <if test="luggageRetrieveVo.flightDate != null and luggageRetrieveVo.flightDate != ''">
            and instr(mps.flight_date, #{luggageRetrieveVo.flightDate}) > 0
        </if>

        <if test="luggageRetrieveVo.luggageNo != null and luggageRetrieveVo.luggageNo != ''">
            and instr(ml.luggage_no, #{luggageRetrieveVo.luggageNo}) > 0
        </if>

        <if test="luggageRetrieveVo.ticketNo != null and luggageRetrieveVo.ticketNo != ''">
            and instr(mpnt.ticket_no, #{luggageRetrieveVo.ticketNo}) > 0
        </if>
    </select>
</mapper>
