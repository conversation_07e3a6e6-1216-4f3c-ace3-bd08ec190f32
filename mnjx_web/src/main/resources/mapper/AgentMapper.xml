<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.AgentMapper">
    <select id="retrieveAgentAirline" resultType="com.swcares.obj.vo.AgentAirlineVo">
        SELECT
	        ma.airline_id,
	        ma.airline_code,
	        ma.airline_full_name,
	        maa.agent_id
        FROM
	        mnjx_airline ma
	        LEFT JOIN mnjx_agent_airline maa ON maa.airline_id = ma.airline_id
	            AND maa.agent_id = #{agentId}
	    where ma.airline_status = '1'
    </select>
</mapper>
