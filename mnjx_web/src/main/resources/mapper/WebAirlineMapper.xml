<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.WebAirlineMapper">
    <select id="idBgtToInteger" resultType="com.swcares.entity.MnjxAirline">
        select AIR_FULLNAME,
               AIR_SHORTNAME,
               AIR_ENAME,
               AIR_CODE,
               COUNT_CODE,
               COUNTRY_NO,
               NAME,
               PHONE_NUMBER,
               ADDRESS,
               STATUS,
               REMARKS,
               CONVERT(ID_BGT, UNSIGNED INTEGER) ID_BGT
        from mnjx_airline ${ew.customSqlSegment}
        order by ID_BGT
    </select>
</mapper>
