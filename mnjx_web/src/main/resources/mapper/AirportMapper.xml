<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.AirportMapper">

    <sql id="getAirportCity">
        SELECT DISTINCT ma.airport_id,
                        ma.airport_code,
                        ma.airport_cname,
                        ma.airport_ename,
                        ma.airport_status,
                        ma.city_id,
                        mc.city_cname,
                        mc.city_code,
                        ma.latitude,
                        ma.longitude
        FROM mnjx_airport ma
                 LEFT JOIN mnjx_city mc ON mc.city_id = ma.city_id
    </sql>

    <select id="getAirportAndCities" resultType="com.swcares.obj.vo.AirportVo">
        <include refid="getAirportCity"/>
        where 1 = 1

        <if test="airportQueryVo.airportCode != null and airportQueryVo.airportCode !=''">
            and ma.airport_code = #{airportQueryVo.airportCode}
        </if>
        <if test="airportQueryVo.cityId != null and airportQueryVo.cityId !=''">
            and ma.city_Id = #{airportQueryVo.cityId}
        </if>
        <if test="airportQueryVo.airportId != null and airportQueryVo.airportId !=''">
            and ma.airport_id = #{airportQueryVo.airportId}
        </if>
        order by ma.airport_code
    </select>

</mapper>
