import cn.hutool.core.date.DateTime;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MyTest {
    public static void main(String[] args) {
        DateTime startDateTime = DateUtils.date();
        DateTime endDateTime = DateUtils.offsetDay(startDateTime, 4);
        List<DateTime> collect = Stream
                // 获取迭代器
                .iterate(startDateTime, dateTime -> DateUtils.offsetDay(dateTime, Constant.ONE))
                // 到什么地方结束
                .limit(DateUtils.betweenDay(endDateTime, startDateTime, true) + Constant.ONE)
                .collect(Collectors.toList());

        collect.forEach(new Consumer<DateTime>() {
            @Override
            public void accept(DateTime dateTime) {
                System.out.println(dateTime);
            }
        });
    }
}
