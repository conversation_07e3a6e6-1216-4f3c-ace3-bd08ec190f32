#========================================================================================================
#说明
#当前SQL只是把生成的订座数据全部删除了, 但是实际更新了航班的舱位的座位数没有被释放
#
#========================================================================================================
SET FOREIGN_KEY_CHECKS = 0;
truncate table mnjx_pnr_record;
truncate table mnjx_pnr_at;
truncate table mnjx_pnr_tk;
truncate table mnjx_pnr_tc;
truncate table mnjx_pnr_seg;
truncate table mnjx_pnr_rmk;
truncate table mnjx_pnr_osi;
truncate table mnjx_pnr_nm_um;
truncate table mnjx_pnr_nm_ticket;
truncate table mnjx_pnr_nm_tn;
truncate table mnjx_pnr_gn;
truncate table mnjx_pnr_fp;
truncate table mnjx_pnr_fn;
truncate table mnjx_pnr_fc;
truncate table mnjx_pnr_ei;
truncate table mnjx_pnr_ct;
truncate table mnjx_nm_xn;
truncate table mnjx_nm_oi;
truncate table mnjx_nm_ssr;
truncate table mnjx_nm_ct;
truncate table mnjx_nm_ei;
truncate table mnjx_nm_fc;
truncate table mnjx_nm_fn;
truncate table mnjx_nm_fp;
truncate table mnjx_nm_osi;
truncate table mnjx_nm_rmk;
truncate table mnjx_pnr_nm_tn;
truncate table mnjx_luggage;
truncate table mnjx_luggage_carryon;
truncate table mnjx_ex_luggage;
truncate table mnjx_sp_info;
truncate table mnjx_pnr_nm;
truncate table mnjx_pnr;
truncate table mnjx_ticket_operate_record;
truncate table mnjx_refund_ticket;
truncate table mnjx_ticket_price;
truncate table mnjx_job_execute;
# 清理了所有的订座数据, 那打票机数据页要跟着走
update mnjx_printer mp set mp.last_ticket=null;
SET FOREIGN_KEY_CHECKS = 1;
