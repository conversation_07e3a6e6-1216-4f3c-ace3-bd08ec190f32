package com.swcares.core.type;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Repository;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Repository
@ConditionalOnProperty(prefix = "eterm.config", name = "runningMode", havingValue = "teach")
public @interface MysqlRepository {
}
