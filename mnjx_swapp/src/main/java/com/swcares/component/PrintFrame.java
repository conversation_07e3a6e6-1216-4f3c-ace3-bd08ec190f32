package com.swcares.component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import javax.imageio.ImageIO;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * description：PrintFrame <br>
 *
 * <AUTHOR> <br>
 * date 2023/06/30 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class PrintFrame {

    public static final String REG_1 = "^(\\d{3})-?(\\d{10})(-|/)?(\\d{2})?$";

    public static final String REG_2 = "^\\w{6}$";

    // 文本框
    JTextField textField = null;

    DefaultTableModel dtm = null;

    JTable table = null;

    JButton button1 = null;

    JButton button2 = null;

    JPanel j2 = null;
    JPanel j3 = null;

    public void initFrame() {

        JFrame frame = createJframe();

        textField = new PrintText().create(frame);
        // 标签
        new PrintLable().create(frame);
        // 下拉列表
        new PrintBox().create(frame);
        // 按钮
        button1 = new PrintButton().createButton1(frame);
        button2 = new PrintButton().createButton2(frame);
        button2.setEnabled(false);

        // 边界布局
        JPanel j1 = new JPanel();
        j1.setBorder(BorderFactory.createTitledBorder(""));
        j1.setBounds(0, 10, 300, 525);
        j1.setLayout(null);

        j2 = new JPanel();
        j2.setBorder(BorderFactory.createTitledBorder(""));
        j2.setBounds(310, 10, 875, 525);
        j2.setLayout(null);

        j3 = new JPanel();
        j3.setBorder(BorderFactory.createTitledBorder(""));
        j3.setBounds(15, 280, 240, 230);
        j3.setLayout(null);

        j1.add(j3);

        JLabel jLabe9 = new JLabel("票号列表");
        jLabe9.setFont(new Font("宋体", Font.PLAIN, 13));
        jLabe9.setBounds(2, 2, 150, 20);
        j3.add(jLabe9);

        // 创建指定列名和数据的表格
        dtm = new DefaultTableModel(10, 1);
        table = new JTable(dtm);
        table.setRowHeight(18);
        table.setBounds(1, 25, 239, 200);
        table.getColumnModel().getColumn(0).setPreferredWidth(200);
        // 创建显示表格的滚动面板
        JScrollPane scrollPane = new JScrollPane(table);
        j3.add(table);
        j3.add(scrollPane, BorderLayout.CENTER);
        j3.setVisible(false);

        frame.add(j1);
        frame.add(j2);

        // 菜单
        frame.setJMenuBar(new PrintMenu().createMenu());
        // 创建行程单
        button1.registerKeyboardAction(new f5Event(), KeyStroke.getKeyStroke(KeyEvent.VK_F5, 0), JComponent.WHEN_IN_FOCUSED_WINDOW);
        button1.addActionListener((e) -> {
            button1Event();
        });

        // 打印行程单
        button1.registerKeyboardAction(new f6Event(), KeyStroke.getKeyStroke(KeyEvent.VK_F6, 0), JComponent.WHEN_IN_FOCUSED_WINDOW);
        button2.addActionListener((e) -> {
            button2Event();
        });


        // 表格点击事件
        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 选中的行
                int rowIndex = table.getSelectedRow();
                String value = (String) dtm.getValueAt(rowIndex, 0);
                // 预览
                preview(j2, j3, dtm, value, false);
            }
        });

        frame.setVisible(true);
    }

    //f5快捷键事件
    class f5Event implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            button1Event();
        }
    }

    //f6快捷键事件
    class f6Event implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            button2Event();
        }
    }

    private void button1Event() {
        // 清空表格数据
        dtm.setRowCount(0);
        String tktNo = textField.getText();
        // 预览
        preview(j2, j3, dtm, tktNo, true);
        // 打印行程单按钮可用
        button2.setEnabled(true);
    }

    private void button2Event() {
        try {
            // 打印行程单按钮不可用
            button2.setEnabled(false);
            List<String> tktNoList = new ArrayList<>();
            // 选中的行
            int rowCont = table.getRowCount();
            for (int i = 0; i < rowCont; i++) {
                String value = (String) dtm.getValueAt(i, 0);
                if (StrUtil.isNotBlank(value)) {
                    tktNoList.add(value);
                }
            }

            // 打印数据获取
            Map<String, Object> map = getPrintData(tktNoList);
            if (map != null) {
                if (map.get("data") != null) {
                    List<Map<String, Object>> listMap = (List<Map<String, Object>>) map.get("data");
                    // 打印
                    print(listMap);
                }
            }
        } catch (Exception e1) {
            e1.printStackTrace();
            JOptionPane.showMessageDialog(null, e1.getMessage());
        } finally {
            // 打印行程单按钮可用
            button2.setEnabled(true);
        }
    }

    public static JFrame createJframe() {
        // 这是一个窗口类
        JFrame frame = new JFrame();
        // 设置窗口位置
        frame.setLocation(100, 100);
        // 设置窗口大小
        frame.setSize(1200, 600);

        try {
            String path = System.getProperty("user.dir");
            String icoPah = path.endsWith("mnjx_swapp") ? path + "/image/print_title.jpg" : path + "/mnjx_swapp/image/print_title.jpg";
            log.info("line 206 ico path:{}", icoPah);
            Image image = ImageIO.read(new File(icoPah));
            frame.setIconImage(image);
        } catch (Exception e1) {
            e1.printStackTrace();
        }

        // 设置窗口布局为BorderLayout格式
        frame.setLayout(null);
        // 设置这窗口可见
        frame.setVisible(true);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        // 不可放大
        frame.setResizable(false);
        frame.setTitle("eterm3插件-电子报销凭证+行程单+本票打印");
        return frame;
    }

    /**
     * @param tktNoList
     * @throws Exception <br>
     * @description：获取打印数据
     * <AUTHOR> <br>
     * @date 2023/07/12 <br>
     */
    private Map<String, Object> getPrintData(List<String> tktNoList) throws Exception {
        Map<String, Object> ymlMap = getYamlData();
        if (CollectionUtil.isNotEmpty(tktNoList)) {
            String ip = (String) ymlMap.get("serviceIp");
            Integer port = (Integer) ymlMap.get("servicePort");
            String listStr = StrUtil.strip(tktNoList.toString(), "[", "]");
            String url = StrUtil.format("http://{}:{}/mnjx/print/getPrintData?tktNoList={}", ip, port, listStr);
            String result = null;
            try {
                result = HttpUtil.get(url);
                if (StrUtil.isNotBlank(result)) {
                    log.info("获取<打印>数据是：{}", JSONUtil.toJsonStr(result));
                    Map<String, Object> map = JSONUtil.toBean(result, Map.class);
                    if ((int) map.get("code") != 200) {
                        throw new Exception((String) map.get("message"));
                    }
                    return map;
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception(e.getMessage());
            }
            log.info(result);
        } else {
            throw new Exception("请输入正确的打印凭证！");
        }
        return null;
    }

    /**
     * @param tktNoList
     * @return
     * @throws Exception <br>
     * @title：getPreviewData <br>
     * @description：获取数据 <br>
     * <AUTHOR> <br>
     * @date 2023/07/12 <br>
     */
    private Map<String, Object> getPreviewData(List<String> tktNoList) throws Exception {
        Map<String, Object> yamlMap = getYamlData();
        if (CollectionUtil.isNotEmpty(tktNoList)) {
            String ip = (String) yamlMap.get("serviceIp");
            Integer port = (Integer) yamlMap.get("servicePort");
            String listStr = StrUtil.strip(tktNoList.toString(), "[", "]");
            String url = StrUtil.format("http://{}:{}/mnjx/print/getPreviewData?tktNoList={}", ip, port, listStr);
            String result = null;
            try {
                result = HttpUtil.get(url);
                log.info("获取到的<预览>数据是：{}", JSONUtil.toJsonStr(result));
                if (StrUtil.isNotBlank(result)) {
                    Map<String, Object> map = JSONUtil.toBean(result, Map.class);
                    if ((int) map.get("code") != 200) {
                        throw new Exception((String) map.get("message"));
                    }
                    return map;
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception(e.getMessage());
            }
        } else {
            throw new Exception("请输入正确的打印凭证！");
        }
        return null;
    }

    private List<String> getTktNoByPnr(String crsPnr) throws Exception {
        Map<String, Object> yamlMap = getYamlData();
        String ip = (String) yamlMap.get("serviceIp");
        Integer port = (Integer) yamlMap.get("servicePort");
        String url = StrUtil.format("http://{}:{}/mnjx/print/getTktNoListByPnr?crsPnr={}", ip, port, crsPnr);
        String result = null;
        try {
            result = HttpUtil.get(url);
            log.info("获取到的<预览>数据是：{}", JSONUtil.toJsonStr(result));
            if (StrUtil.isNotBlank(result)) {
                Map<String, Object> map = JSONUtil.toBean(result, Map.class);
                if ((int) map.get("code") != 200) {
                    throw new Exception((String) map.get("message"));
                }
                return (List<String>) map.get("data");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
        return null;
    }

    /**
     * @param list
     * @throws Exception <br>
     * @title：print <br>
     * @description：打印 <br>
     * <AUTHOR> <br>
     * @date 2023/07/26 <br>
     */
    private void print(List<Map<String, Object>> list) throws Exception {
        Map<String, Object> ymlMap = getYamlData();
        if (CollectionUtil.isNotEmpty(list)) {
            String printIp = (String) ymlMap.get("printIp");
            Integer printPort = (Integer) ymlMap.get("printPort");
            Map<String, Object> map = new HashMap<>(1024);
            map.put("data", list);
            String url = StrUtil.format("http://{}:{}/printer/itinerary/printEnpson", printIp, printPort);
            JSON json = JSONUtil.parse(map);
            log.info("<打印>URL：{}", url);
            try {
                String result = HttpRequest.post(url).timeout(20000).body(json.toString()).execute().body();
                log.info("<打印>结果：{}", result);
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception("打印机连接超时，请重试！");
            }
        } else {
            throw new Exception("请输入正确的打印凭证！");
        }
    }

    private void preview(JPanel j2, JPanel j3, DefaultTableModel dtm, String tktNo, boolean flag) {
        // 判断是否输入了
        if (StrUtil.isBlank(tktNo)) {
            Object[] options = {"OK "};
            JOptionPane.showOptionDialog(null, "您还没有输入票号", "提示", JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE, null, options, options[0]);
            return;
        }
        try {
            String path = System.getProperty("user.dir");
            String newPath = path.endsWith("mnjx_swapp") ? path + "/image/print_new.jpg" : path + "/mnjx_swapp/image/print_new.jpg";
            log.info("line 336 new path:{}", newPath);
            Component[] components = j2.getComponents();
            if (components != null && components.length > 0) {
                for (Component component : components) {
                    j2.remove(component);
                }
                File file = new File(newPath);
                if (file.exists()) {
                    file.delete();
                }
            }
            j2.updateUI();

            tktNo = tktNo.trim();
            // 检查，解析票号
            List<String> tktNoList = checkInputTktNo(tktNo);
            // 获取数据
            Map<String, Object> map = getPreviewData(tktNoList);

            List<Map<String, Object>> listMap = (List<Map<String, Object>>) map.get("data");
            if (CollectionUtil.isNotEmpty(listMap)) {
                List<String> queryTicketNoList = listMap.stream()
                        .map(m -> m.get("ticketNo").toString())
                        .sorted()
                        .collect(Collectors.toList());
                if (tktNoList.get(0).matches("[A-Z0-9]{6}")) {
                    List<String> inputTktNoList = this.getTktNoByPnr(tktNoList.get(0));
                    if (!CollUtil.isEqualList(inputTktNoList, queryTicketNoList)) {
                        int result = JOptionPane.showConfirmDialog(null, "您查询的行程中，存在部分旅客不可打印行程单，是否继续？", "确认", JOptionPane.OK_CANCEL_OPTION, JOptionPane.INFORMATION_MESSAGE);
                        if (result != JOptionPane.OK_OPTION) {
                            return;
                        }
                    }
                } else {
                    if (!CollUtil.isEqualList(tktNoList, queryTicketNoList)) {
                        int result = JOptionPane.showConfirmDialog(null, "您查询的行程中，存在部分旅客不可打印行程单，是否继续？", "确认", JOptionPane.OK_CANCEL_OPTION, JOptionPane.INFORMATION_MESSAGE);
                        if (result != JOptionPane.OK_OPTION) {
                            return;
                        }
                    }
                }
                Map<String, Object> map1 = listMap.stream()
                        .filter(l -> ObjectUtil.isNotEmpty(l.get("tnId")))
                        .collect(Collectors.toList())
                        .get(0);
                if (tktNoList.size() == 1) {
                    if (ReUtil.isMatch(REG_2, tktNoList.get(0))) {
                        map1 = listMap.stream()
                                .filter(l -> ObjectUtil.isNotEmpty(l.get("pnrICs")) && l.get("pnrICs").toString().equals(tktNoList.get(0)))
                                .collect(Collectors.toList())
                                .get(0);
                    } else {
                        map1 = listMap.stream()
                                .filter(l -> l.get("ticketNo").toString().equals(tktNoList.get(0)))
                                .collect(Collectors.toList())
                                .get(0);
                    }
                }
                // 创建图片
                new PrintImage().create(map1);
                JLabel label = new JLabel();
                label.setBounds(10, 15, 865, 500);
                label.setIcon(new ImageIcon(ImageIO.read(new File(newPath))));
                j2.add(label);
                j2.updateUI();
                // 票号列表
                if (flag) {
                    for (int i = 0; i < listMap.size(); i++) {
                        String[] rowData = {(String) listMap.get(i).get("ticketNo")};
                        dtm.insertRow(i, rowData);
                    }
                    j3.setVisible(true);
                }
            } else {
                JOptionPane.showMessageDialog(null, "当前票号无法打印，请检查后继续！");
            }
        } catch (Exception e1) {
            log.error(e1.getMessage());
            e1.printStackTrace();
            JOptionPane.showMessageDialog(null, e1.getMessage());
        }
    }

    /**
     * @return <br>
     * @title：getYamlData <br>
     * @description 获取yaml数据<br>
     * <AUTHOR> <br>
     * @date 2023/07/26 <br>
     */
    private Map<String, Object> getYamlData() {
        Yaml yaml = new Yaml();
        InputStream is = PrintFrame.class.getClassLoader().getResourceAsStream("application.yml");
        return yaml.load(is);
    }

    /**
     * @param tktNo
     * @return
     * @throws Exception <br>
     * @title：checkTktNo <br>
     * @description：检查解析输入票号 <br>
     * <AUTHOR> <br>
     * @date 2023/07/12 <br>
     */
    private List<String> checkInputTktNo(String tktNo) throws Exception {
        List<String> tktNoList = new ArrayList<>();
        String g1 = null;
        String g2 = null;
        String g4 = null;
        String g0 = null;
        if (ReUtil.isMatch(REG_1, tktNo)) {
            Pattern p = Pattern.compile(REG_1);
            Matcher m = p.matcher(tktNo);
            while (m.find()) {
                g1 = m.group(1);
                g2 = m.group(2);
                g4 = m.group(4);
            }
        } else if (ReUtil.isMatch(REG_2, tktNo)) {
            Pattern p = Pattern.compile(REG_2);
            Matcher m = p.matcher(tktNo);
            while (m.find()) {
                g0 = m.group(0);
            }
            if (StrUtil.isNotBlank(g0)) {
                tktNoList.add(g0);
                return tktNoList;
            }
        } else {
            throw new Exception("请输入正确的票号/PNR！");
        }

        if (StrUtil.isBlank(g4) && StrUtil.isNotBlank(g1) && StrUtil.isNotBlank(g2)) {
            tktNoList.add(g1 + g2);
            return tktNoList;
        } else if (StrUtil.isNotBlank(g4)) {
            String startTmp = g1 + g2;
            String endTmp = startTmp.substring(0, startTmp.length() - 2) + g4;
            int firstTicketNoLastTwoNumber = Integer.parseInt(g2.substring(g2.length() - 2));
            if (firstTicketNoLastTwoNumber > Integer.parseInt(g4)) {
                endTmp = StrUtil.format("{}{}", endTmp.substring(0, 10), Integer.parseInt(endTmp.substring(endTmp.length() - 3)) + 100);
            }
            long start = Long.parseLong(startTmp);
            long end = Long.parseLong(endTmp);
            while (start <= end) {
                // 13位票号前面补充0，中间转换过可能导致开头的0丢失
                tktNoList.add(StrUtil.fill(String.valueOf(start), '0', 13, true));
                start++;
            }
            return tktNoList;
        } else {
            throw new Exception("请输入正确的票号/PNR！");
        }
    }

}
