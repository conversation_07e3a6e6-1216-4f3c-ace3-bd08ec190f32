import{L as j,M as F,q as $,v as G,x as c,B as u,D as f,A as i,F as A,ak as M,Q as d,J as y,P as n,C as q,_ as J,K,bS as Q,bT as X,r as p,aw as D,a9 as E,o as k,b5 as b,ae as I,a5 as R,G as h,z as g,ai as T,aj as B,H as P,c9 as U,am as Y,an as Z,y as L,w as V,a4 as S}from"./index-a2fbd71b.js";import{A as O,f as N,d as x}from"./config-6a4e2d9f.js";/* empty css              */import{V as H}from"./index.min-09ff5f99.js";import{_ as z}from"./_plugin-vue_export-helper-c27b6911.js";import{E as ee}from"./index-d88b3135.js";import{N as te}from"./NoticeModal-80d36050.js";import"./refs-b36b7130.js";import"./isUndefined-aa0326a0.js";import"./NoticeList.vue_vue_type_script_setup_true_lang-30d32bad.js";import"./index-350461d4.js";import"./index-ff93035e.js";import"./castArray-2ae6cabb.js";import"./index-e5c2c3ff.js";import"./index-a42e5d8f.js";import"./throttle-d22b7fb0.js";import"./index-ac58700d.js";const se=j({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:F([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),oe=$({name:"ElCard"}),ae=$({...oe,props:se,setup(e){const s=G("card");return(t,m)=>(c(),u("div",{class:f([i(s).b(),i(s).is(`${t.shadow}-shadow`)])},[t.$slots.header||t.header?(c(),u("div",{key:0,class:f(i(s).e("header"))},[A(t.$slots,"header",{},()=>[M(d(t.header),1)])],2)):y("v-if",!0),n("div",{class:f([i(s).e("body"),t.bodyClass]),style:q(t.bodyStyle)},[A(t.$slots,"default")],6),t.$slots.footer||t.footer?(c(),u("div",{key:1,class:f(i(s).e("footer"))},[A(t.$slots,"footer",{},()=>[M(d(t.footer),1)])],2)):y("v-if",!0)],2))}});var ne=J(ae,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/card/src/card.vue"]]);const le=K(ne),W=()=>Q.post(`${X}/message/listNotReadMessagesInCondition`),re=()=>{const e=p([]),s=p(!0),t=p([]),m=D(),_=E(),l=async()=>{const a=await W();e.value=a.data;const v=e.value.length<30?e.value.length:30;setTimeout(()=>{var w;for(let C=0;C<v;C+=1)H.preview(document.getElementsByClassName("vditor")[C],b.decode((w=e.value[C])==null?void 0:w.content),{mode:"light",anchor:0,theme:{current:"light",path:`${N}/dist/css/content-theme`},cdn:N})},500)},o=a=>{m.push({name:"NotificationManage",params:{keyWord:a.title,startDate:a.createDate,endDate:a.createDate,msgStatus:"0"}})},r=()=>{e.value=[]};return k(async()=>{(await _.getters.roleResource).includes(O)&&l()}),{dialogTableVisible:s,unReadList:e,getUnReadList:l,readColection:t,routerToNotice:o,closeAnnounce:r}},ie=re,ce=e=>(Y("data-v-0c474faf"),e=e(),Z(),e),de={key:0,class:"announcement"},ue={class:"header"},me={class:"list"},pe=["onClick"],_e={class:"title"},he={class:"time"},ve=ce(()=>n("div",{class:"content"},[n("div",{class:"vditor"})],-1)),fe=$({__name:"Announcement",setup(e){const{unReadList:s,routerToNotice:t}=ie();return(m,_)=>{const l=I("permission");return i(s).length?R((c(),u("div",de,[h(i(le),{class:"box-card"},{header:g(()=>[n("div",ue,[n("div",null,d(m.$t("app.anouncement.title")),1)])]),default:g(()=>[n("div",me,[(c(!0),u(T,null,B(i(s).slice(0,30),o=>(c(),u("div",{key:o.id,class:"item",onClick:r=>i(t)(o)},[n("div",_e,d(o.title),1),n("div",he,[h(i(P),null,{default:g(()=>[h(i(U))]),_:1}),n("div",null,d(m.$t("app.anouncement.time"))+" "+d(o.createDate),1)]),ve],8,pe))),128))])]),_:1})])),[[l,"home-announcement-unreadMessages"]]):y("",!0)}}});const ge=z(fe,[["__scopeId","data-v-0c474faf"]]),ye={class:"customWidth"},$e={class:"first-title"},we={class:"list"},Ce=["onClick"],Ne={class:"header"},Ae=n("div",{class:"border"},null,-1),Se={class:"title"},ke={class:"time"},Me=n("div",{class:"content"},[n("div",{class:"modalVditor"})],-1),Ve=n("p",{class:"line"},null,-1),De=$({__name:"Modal",setup(e){const s=p([]),t=p(!0),m=D(),_=l=>{m.push({name:"NotificationManage",params:{keyWord:l.title,startDate:l.createDate,endDate:l.createDate,msgStatus:"0"}})};return k(async()=>{const l=await W();s.value=l.data;const o=s.value.length<10?s.value.length:10;setTimeout(()=>{if(document.getElementsByClassName("modalVditor").length)for(let r=0;r<o;r+=1)H.preview(document.getElementsByClassName("modalVditor")[r],b.decode(s.value[r].content),{mode:"light",anchor:0,theme:{current:"light",path:`${N}/dist/css/content-theme`},cdn:N})},500)}),(l,o)=>{const r=I("permission");return R((c(),u("div",null,[s.value.length?(c(),L(i(ee),{key:0,modelValue:t.value,"onUpdate:modelValue":o[0]||(o[0]=a=>t.value=a),"close-on-click-modal":!1,class:"custom"},{default:g(()=>[n("div",ye,[n("p",$e,d(l.$t("app.anouncement.title")),1),n("div",we,[(c(!0),u(T,null,B(s.value.slice(0,10),a=>(c(),u("div",{key:a.id,class:"text item",onClick:v=>_(a)},[n("div",Ne,[Ae,n("div",Se,d(a.title),1)]),n("div",ke,[h(i(P),null,{default:g(()=>[h(i(U))]),_:1}),n("div",null,d(l.$t("app.anouncement.time"))+" "+d(a.createDate),1)]),Me,Ve],8,Ce))),128))])])]),_:1},8,["modelValue"])):y("",!0)])),[[r,"home-announcement-unreadMessages"]])}}});const Ee=$({name:"Main",components:{Modal:De,Announcement:ge,NoticeModal:te},setup(){const e=p(!1),s=p(!1),t=E(),m=V(()=>{var o;return((o=t.state.user)==null?void 0:o.crsSystem)??!1}),_=V(()=>{var o;return((o=t.state.user)==null?void 0:o.defaultOfficeInternational)??!1}),l=p(!1);return k(async()=>{if(window.firstInApp)e.value=!1,l.value=!1;else{window.firstInApp=!0;const o=await t.getters.roleResource;s.value=o.includes(O);let r;const a=await t.getters.routes;a&&a.length>0?r=a==null?void 0:a.map(w=>w.url):r=await t.getters.roleMenu,r.includes(x)&&(l.value=!0),e.value=!0}}),{showAnnounce:e,hasPermission:s,showAgentNotice:l,isCrs:m,isIntlCrs:_}}});const be={class:"footer"},Ie={key:0,class:"text-gray-4 text-xs font-normal ading-tight"},Re={key:1,class:"text-gray-4 text-xs font-normal ading-tight"};function Te(e,s,t,m,_,l){const o=S("Announcement"),r=S("Modal"),a=S("NoticeModal");return c(),u("div",{class:f([e.isCrs?"main-new":"main",e.isIntlCrs?"main-new-intl":""])},[h(o),e.showAnnounce&&e.hasPermission?(c(),L(r,{key:0})):y("",!0),h(a,{modelValue:e.showAgentNotice,"onUpdate:modelValue":s[0]||(s[0]=v=>e.showAgentNotice=v)},null,8,["modelValue"]),n("footer",be,[e.isIntlCrs?(c(),u("div",Ie,d(e.$t("app.login.companyNameIntl")),1)):(c(),u("div",Re,d(e.$t("app.login.companyName")),1))])],2)}const Ze=z(Ee,[["render",Te],["__scopeId","data-v-104823ef"]]);export{Ze as default};
