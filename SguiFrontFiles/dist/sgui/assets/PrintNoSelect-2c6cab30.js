import{fe as Q,r as y,ab as R,a9 as $,aC as z,w as D,ac as A,aY as F,hk as G,aS as N,q as j,x as k,y as x,z as E,B,ai as q,aj as L,A as c,J as U,ak as H,Q as J,b3 as Y,D as K,H as W}from"./index-a2fbd71b.js";import{G as X}from"./ticketOperationApi-4e3e854b.js";import{E as Z,a as ee}from"./index-68054860.js";import{_ as te}from"./_plugin-vue_export-helper-c27b6911.js";const ae=Q("printNo",()=>{const n=y(new Map);return{printNosByOffice:n,setPrintNosByOffice:(r,o)=>{n.value.set(r,o)},deletePrintNosExceptOffice:r=>{const o=[];for(const[l]of n.value)l!==r&&o.push(l);for(const l of o)n.value.delete(l)}}}),ne=(n,g)=>{const{t:i}=R(),r=ae(),o=$(),{printNosByOffice:l}=z(r),u=y(n.modelValue),p=y([]),t=y([]),S=y([]),m=y(n.ticketType??""),P=y(!1),O=D(()=>{var e;return((e=o.state.user)==null?void 0:e.crsSystem)??!1}),C=D(()=>`printNo-select ${n.selectClass??""}`),h=(e,a)=>{var s,v,_;const d=((s=o.state.user)==null?void 0:s[e])??"";return d||(((_=(((v=o.state.user)==null?void 0:v[a])??"").split(";"))==null?void 0:_[0])??"")},V=e=>{p.value=(l.value.get(e)??[]).map(a=>({value:a.devno,label:a.devno,type:`${a.devno} ${i("app.agentTicketQuery.ticketMachine.type_"+a.type)}`})),t.value=N(p.value),S.value=N(t.value),I(n.ticketType??""),n.needDistinguish&&(n.isInter?t.value=t.value.filter(a=>!a.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(a=>a.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))))},b=async()=>{var a;if(!O.value)return;const e=h("defaultOffice","office");if(!l.value.get(e)||((a=l.value.get(e))==null?void 0:a.length)===0)try{P.value=!0;const d=F("10270401"),s=(await X({office:e},d,!0)).data.value;await r.setPrintNosByOffice((s==null?void 0:s.office.office)??e,(s==null?void 0:s.ticketMachines)??[])}finally{P.value=!1}await r.deletePrintNosExceptOffice(e),await V(e)},T=()=>{n.isInter?t.value=t.value.filter(e=>!e.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(e=>e.type.includes(i("app.agentTicketQuery.ticketMachine.domestic")))},f=e=>{var a,d,s;if(e){let v=[];G.test(e.trim())?v=(p.value??[]).filter(_=>_.value===e):v=(p.value??[]).filter(_=>_.type.includes(e)),t.value=N(v),u.value=((a=t.value)==null?void 0:a.length)>0?(s=(d=t.value)==null?void 0:d[0])==null?void 0:s.value:e.toUpperCase()}else t.value=N(p.value);n.needDistinguish&&T(),t.value=(t.value??[]).filter(v=>v.type.includes(m.value))},I=e=>{e&&(u.value=n.modelValue,m.value=N(e),e==="ARL"&&(m.value=i("app.pnrManagement.paymentMethod.currentTicketReal")),n.needDistinguish&&(t.value=N(p.value),T()),t.value=(t.value??[]).filter(a=>a.type.includes(m.value)))},M=()=>{var a;const e=t.value.find(d=>d.value===u.value);return e?(a=e.type)!=null&&a.includes(i("app.agentTicketQuery.ticketMachine.domestic"))?"D":"I":""},w=()=>{g("update:modelValue",u.value),g("deliverPrintType",M())};return A(()=>n.modelValue,()=>{u.value=n.modelValue}),{printNo:u,printNos:t,loading:P,selectClass:C,filterPrintNo:f,setPrintNo:w,filterPrintToTicketType:I,init:b}},le=ne,se={key:0},ie={key:1,class:"inline-block w-[12px]"},oe=j({__name:"PrintNoSelect",props:{modelValue:{},selectClass:{},isInter:{type:Boolean},needDistinguish:{type:Boolean},ticketType:{}},emits:["update:modelValue","deliverPrintType"],setup(n,{expose:g,emit:i}){const r=n,o=i,{printNo:l,printNos:u,loading:p,selectClass:t,filterPrintNo:S,setPrintNo:m,filterPrintToTicketType:P,init:O}=le(r,o);return g({filterPrintToTicketType:P}),(C,h)=>{const V=W,b=Z,T=ee;return k(),x(T,{modelValue:c(l),"onUpdate:modelValue":h[0]||(h[0]=f=>Y(l)?l.value=f:null),class:K(c(t)),"popper-class":"printNo-select-option",placeholder:" ",loading:c(p),filterable:"",clearable:"","filter-method":c(S),onFocus:c(O),onBlur:c(m)},{default:E(()=>[(k(!0),B(q,null,L(c(u),f=>(k(),x(b,{key:f.value,label:f.label,value:f.value},{default:E(()=>[c(u).some(I=>I.value===c(l))?(k(),B("span",se,[f.value===c(l)?(k(),x(V,{key:0,size:12,class:"iconfont icon-right-line"})):(k(),B("span",ie))])):U("",!0),H(" "+J(f.type),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","class","loading","filter-method","onFocus","onBlur"])}}});const de=te(oe,[["__scopeId","data-v-db081c8d"]]);export{de as P};
