import{a as u}from"./FileSaver.min-fcefb866.js";import{E as f}from"./exceljs.min-bcd6e76f.js";import{dV as p,e_ as i,aS as x}from"./index-a2fbd71b.js";function E(e,n){return p(n,function(a){return e[a]})}function b(e){return e==null?[]:E(e,i(e))}function m(e,n,a){const c=x(e);c.forEach(o=>{Object.keys(n).forEach(t=>{Object.prototype.hasOwnProperty.call(n,t)&&(o[n[t]]=o[t]),delete o[t]})});const s=new f.Workbook,r=s.addWorksheet(a);r.addRow(b(n)),c.forEach(o=>{r.addRow(Object.values(o))});const l={size:12};r==null||r.eachRow({includeEmpty:!0},function(o){o.eachCell({includeEmpty:!0},function(t){t.style={font:l}})}),s.xlsx.writeBuffer().then(o=>{const t=new Blob([o],{type:"application/octet-stream"});u.saveAs(t,`${a}.xlsx`)})}export{m as e};
