import{L as h,M as o,e5 as u,q as m,O as g,w as c,v as _,e7 as p,dF as $,x as N,y as x,z as C,F as j,D as v,A as f,C as O,b9 as E,_ as w,K as k}from"./index-a2fbd71b.js";import{r as K}from"./index-4cb0667b.js";const S=h({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:o([Number,Object]),default:()=>u({})},sm:{type:o([Number,Object]),default:()=>u({})},md:{type:o([Number,Object]),default:()=>u({})},lg:{type:o([Number,Object]),default:()=>u({})},xl:{type:o([Number,Object]),default:()=>u({})}}),B=m({name:"ElCol"}),D=m({...B,props:S,setup(d){const t=d,{gutter:n}=g(K,{gutter:c(()=>0)}),a=_("col"),b=c(()=>{const e={};return n.value&&(e.paddingLeft=e.paddingRight=`${n.value/2}px`),e}),i=c(()=>{const e=[];return["span","offset","pull","push"].forEach(s=>{const l=t[s];p(l)&&(s==="span"?e.push(a.b(`${t[s]}`)):l>0&&e.push(a.b(`${s}-${t[s]}`)))}),["xs","sm","md","lg","xl"].forEach(s=>{p(t[s])?e.push(a.b(`${s}-${t[s]}`)):$(t[s])&&Object.entries(t[s]).forEach(([l,r])=>{e.push(l!=="span"?a.b(`${s}-${l}-${r}`):a.b(`${s}-${r}`))})}),n.value&&e.push(a.is("guttered")),[a.b(),e]});return(e,y)=>(N(),x(E(e.tag),{class:v(f(i)),style:O(f(b))},{default:C(()=>[j(e.$slots,"default")]),_:3},8,["class","style"]))}});var F=w(D,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/col/src/col.vue"]]);const A=k(F);export{A as E};
