import{E as c}from"./index-fe3402a7.js";import{q as _,r as n,o as f,dN as u,x as a,B as l,G as m,z as r,ak as v,Q as h,P as y,F as i}from"./index-a2fbd71b.js";import{_ as k}from"./_plugin-vue_export-helper-c27b6911.js";const W={key:0},T=_({__name:"Tip",props:{showVal:{}},setup(V){const t=n(),o=n(!1),s=()=>{const e=t.value.offsetWidth;t.value.firstElementChild.offsetWidth>e?o.value=!0:o.value=!1};return f(()=>{s()}),u(()=>{s()}),(e,d)=>{const p=c;return a(),l("div",{onMouseenter:s},[o.value?(a(),l("div",W,[m(p,{placement:"top"},{content:r(()=>[v(h(e.showVal),1)]),default:r(()=>[y("div",{ref_key:"slotRef",ref:t,class:"ellipsis"},[i(e.$slots,"default",{},void 0,!0)],512)]),_:3})])):(a(),l("div",{key:1,ref_key:"slotRef",ref:t},[i(e.$slots,"default",{},void 0,!0)],512))],32)}}});const E=k(T,[["__scopeId","data-v-9b451be1"]]);export{E as m};
