import{hj as Ue,fL as ze,t as Re,ab as De,a9 as He,w as me,r as j,b0 as Le,o as Ae,ac as Ge,eN as ie,q as ue,x,y as O,z as v,P as e,Q as r,A as s,B as R,G as h,ai as Y,aj as J,D as T,aU as qe,J as W,ak as z,ag as Qe,H as Ye,ah as je,al as he,am as xe,an as ye,b5 as Ve,C as Je,s as de,aF as Xe,aY as Te,bI as Ne,aX as Ke,a5 as $e,b3 as Ce,aZ as We}from"./index-a2fbd71b.js";import{P as Ze}from"./PrintNoSelect-2c6cab30.js";import{p as Fe,f as et,h as tt}from"./refundUtil-46d54931.js";import{D as be,h as st}from"./html2canvas.esm-cf66fc0f.js";import{k as Ie,D as at,P as nt,b as ne,c as it,d as ot,t as rt,f as Se,g as Ee,h as lt,i as dt,j as ut,S as ct}from"./regular-crs-531dcd3f.js";import{c as Oe}from"./_createMathOperation-9624aa42.js";import{t as pt,E as ft,a as mt}from"./index-68054860.js";import{s as ce}from"./subtract-21d41b77.js";import{a as te}from"./add-2f19224f.js";import{E as gt,a as ht}from"./index-96be5bee.js";import{E as xt}from"./index-fe3402a7.js";import{E as Me,a as yt}from"./index-e149ca58.js";import{E as vt}from"./index-61b49c38.js";import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{E as ke}from"./index-d88b3135.js";import{t as bt}from"./throttle-d22b7fb0.js";import{E as _t}from"./index-e246ba26.js";import{E as kt,F as wt}from"./ticketOperationApi-4e3e854b.js";var Rt=Ue.isFinite,Tt=Math.min;function Nt(n){var d=Math[n];return function(y,t){if(y=ze(y),t=t==null?0:Tt(pt(t),292),t&&Rt(y)){var b=(Re(y)+"e").split("e"),a=d(b[0]+"e"+(+b[1]+t));return b=(Re(a)+"e").split("e"),+(b[0]+"e"+(+b[1]-t))}return d(y)}}var $t=Oe(function(n,d){return n/d},1);const pe=$t;var Ct=Nt("floor");const _e=Ct;var It=Oe(function(n,d){return n*d},1);const fe=It,St=n=>{const{t:d}=De(),y=He(),t=me(()=>{var f;return(f=y.state.user)==null?void 0:f.entityType}),b=j(),a=Le({...n.data}),k=j([]),p=j([]),I=me(()=>!["CDS","GPCDS"].includes(a.ticketManagementOrganizationCode??""));!n.disabled&&Ie.test(a.name)&&(a.name="");const o=(f,i,c)=>{a.payType==="TC"&&(i.length===0?c(d("app.agentTicketRefund.creditCardNotEmpty")):!n.isDragonBoatOffice&&!lt.test(i)?c(d("app.agentTicketRefund.creditCardInput")):n.isDragonBoatOffice&&!dt.test(i)&&c(d("app.agentTicketRefund.dragonBoatOfficeInput"))),c()},H=(f,i,c)=>{var g;(i??"")!==""?((g=b.value)==null||g.validateField("remarkCode"),(a.remarkCode??"")==="IC"?ut.test(`${a.remarkCode}${i}`)?c():c(d("app.agentTicketRefund.remarkIC")):(a.remarkCode??"")!==""&&!Ee.test(`${a.remarkCode}${i}`)?c(d("app.agentTicketRefund.remarkHint")):c()):a.remarkCode?c(d("app.agentTicketRefund.remarkHint")):c()},C=(f,i,c)=>{(a.remark??"")!==""&&!Se.test(i??"")?c(d("app.agentTicketRefund.formatError")):c()},V=(f,i,c)=>{var l;const g=f.field.split(".")[1];a.taxs[Number(g)].taxAmount!==""&&i===""?c(d("app.agentTicketRefund.taxes")):(a.taxs[Number(g)].taxAmount===""&&i===""&&((l=b.value)==null||l.clearValidate(`taxs.${g}.taxAmount`)),c())},F=(f,i,c)=>{var l;const g=f.field.split(".")[1];a.taxs[Number(g)].taxCode!==""&&i===""?c(d("app.agentTicketRefund.taxAmount")):(a.taxs[Number(g)].taxCode===""&&i===""&&((l=b.value)==null||l.clearValidate(`taxs.${g}.taxCode`)),c())},$={ticketManagementOrganizationCode:[{required:!0,message:d("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],prntNo:[{required:!0,message:d("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:at,trigger:"blur",message:d("app.ticketStatus.deviceError")}],currency:[{required:!0,message:d("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:d("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:nt,message:d("app.agentTicketRefund.paymentInput"),trigger:"change"}],price:[{pattern:ne,message:d("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxValue:[{pattern:it,message:d("app.agentTicketRefund.correctPrice"),trigger:"change"},{validator:F,trigger:"change"}],taxName:[{pattern:ot,message:d("app.agentTicketRefund.taxes"),trigger:"change"},{validator:V,trigger:"change"}],rate:[{pattern:ne,message:d("app.agentTicketRefund.correctRate"),trigger:"change"}],commision:[{pattern:ne,message:d("app.agentTicketRefund.correctPrice"),trigger:"change"}],psgName:[{pattern:rt,message:d("app.agentTicketRefund.psgNameError"),trigger:"change"}],creditCard:[{validator:o,required:!0,trigger:"change"}],remarkCode:[{pattern:Se,message:d("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:C,trigger:["change","blur"]}],remark:[{validator:H,trigger:["change","blur"]}],remarkInfo:[{pattern:Ee,message:d("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],totalAmount:[{pattern:ne,message:d("app.agentTicketRefund.correctPrice"),trigger:"change"}],netRefund:[{required:!0,message:d("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:ne,message:d("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}]},S=()=>{const{totalAmount:f,totalTaxs:i,otherDeduction:c,commision:g,commisionRate:l}=a;if(!G())if(l){const _=pe(fe(Number(f),Number(l)),100).toString(),w=_e(Number(_),2).toString();a.commision=w;const E=`${ce(te(Number(f),Number(i)),te(Number(c),Number(w)))}`;a.netRefund=Number(E).toFixed(2)}else{const _=`${ce(te(Number(f),Number(i)),te(Number(c),Number(g)))}`;a.netRefund=Number(_).toFixed(2)}},P=()=>{a.commisionRate||(a.commision="")},M=()=>{let f=new be(0);a.taxs.forEach((i,c)=>{var g;(g=b.value)==null||g.validateField(`taxs.${c}.taxAmount`).then(l=>{l&&i.taxAmount?(a.taxs[c].taxAmount=ie(a.currency)?i.taxAmount:Number(i.taxAmount).toFixed(2),f=f.add(new be(i.taxAmount))):l&&!i.taxAmount&&(f=f.add(new be(0))),a.totalTaxs=ie(a.currency)?f.toString():Number(f).toFixed(2),ie(a.currency)?u(""):S()})})},B=async()=>{const f=[];a.taxs.forEach((i,c)=>{var g,l;f.push((g=b.value)==null?void 0:g.validateField(`taxs.${c}.taxCode`)),f.push((l=b.value)==null?void 0:l.validateField(`taxs.${c}.taxAmount`))}),await Promise.all(f),a.taxs.forEach((i,c)=>{a.taxs[c].taxAmount&&(a.taxs[c].taxAmount=ie(a.currency)?a.taxs[c].taxAmount??0:Number(a.taxs[c].taxAmount??0).toFixed(2))}),M()},U=f=>f&&!ne.test(f.toString()),G=()=>{const{totalAmount:f,otherDeductionRate:i,otherDeduction:c,commision:g,commisionRate:l}=a;return U(f??"")||U(i??"")||U(c??"")||U(g??"")||U(l??"")},u=f=>{if(f==="otherDeductionRate"&&a.otherDeductionRate){const w=pe(fe(Number(a.totalAmount),Number(a.otherDeductionRate)),100).toString();a.otherDeduction=_e(Number(w),2).toString()}const{totalAmount:i,totalTaxs:c,otherDeduction:g,commision:l,commisionRate:_}=a;if(!G())if(_){const w=pe(fe(Number(i),Number(_)),100).toString(),E=_e(Number(w),2).toString();a.commision=E.endsWith(".00")?E.slice(0,-3):E;const K=`${ce(te(Number(i),Number(c)),te(Number(g),Number(E)))}`;a.netRefund=Number(K).toFixed(2),a.netRefund.endsWith(".00")&&(a.netRefund=a.netRefund.slice(0,-3))}else a.netRefund=`${ce(te(Number(i),Number(c)),te(Number(g),Number(l)))}`},m=f=>{if(!G()){if(ie(a.currency)){u(f);return}f==="otherDeductionRate"&&(a.otherDeductionRate??"")!==""&&(a.otherDeductionRate=Number(a.otherDeductionRate).toFixed(2),a.otherDeduction=pe(fe(Number(a.totalAmount),Number(a.otherDeductionRate)),100).toString()),S(),a.totalAmount!==""&&(a.totalAmount=Number(a.totalAmount).toFixed(2)),a.commision!==""&&(a.commision=Number(a.commision).toFixed(2)),a.commisionRate!==""&&(a.commisionRate=Number(a.commisionRate).toFixed(2)),a.otherDeduction!==""&&(a.otherDeduction=Number(a.otherDeduction).toFixed(2))}},Z=f=>{f.target.value!==""&&!Fe.some(i=>i.label===f.target.value)&&(a.payType=f.target.value)},D=f=>{var i;return(((i=a.checkedSeg)==null?void 0:i.filter(c=>c.etSegIndex===f.etSegIndex))??[]).length>0},L=async()=>{b.value&&(await b.value.resetFields(),a.etTagNew=n.data.etTagNew,a.checkedSeg=n.data.checkedSeg,a.couponNos=n.couponNoHistory?JSON.parse(JSON.stringify(n.couponNoHistory)):[],a.taxs=JSON.parse(JSON.stringify(n.taxsHistory)),k.value=[],M(),m(""))},N=async()=>{if(!b.value)return!1;try{return await b.value.validate()}catch{return!1}},ee=()=>{a.taxs.length!==27&&(a.taxs=a.taxs.length>=25&&a.taxs.length<27?a.taxs.concat(new Array(27-a.taxs.length).fill({taxCode:"",taxAmount:""})).map(f=>({...f})):a.taxs.concat(new Array(5).fill({taxCode:"",taxAmount:""})).map(f=>({...f})))},A=()=>a,q=()=>(k.value=new Array(4).fill(""),(a.couponNos??[]).some(f=>f)?(a.couponNos??[]).forEach((f,i)=>{f&&!ct.test(f)&&(k.value[i]=d("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):k.value[0]=d("app.pnrManagement.validate.required"),k.value.every(f=>!f)),ae=()=>{I.value||(a.prntNo="")},re=f=>{var c,g;return(p.value??[]).some(l=>f===l.value)&&f?f:((g=(c=p.value)==null?void 0:c[0])==null?void 0:g.value)??""},Q={BSP:{label:d("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:d("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:d("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:d("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:d("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:d("app.agentTicketQuery.OWNTicket"),value:"ARL"}},le=()=>{var f,i,c,g,l,_,w,E,K,se;((f=t.value)!=null&&f.includes("$$$")||(i=t.value)!=null&&i.includes("BSP"))&&(p.value.push(Q.BSP),p.value.push(Q.GPBSP)),!((c=t.value)!=null&&c.includes("BSP"))&&((g=t.value)!=null&&g.includes("GP"))&&p.value.push(Q.GPBSP),((l=t.value)!=null&&l.includes("$$$")||(_=t.value)!=null&&_.includes("BOP"))&&p.value.push(Q.BOPBSP),((w=t.value)!=null&&w.includes("$$$")||(E=t.value)!=null&&E.includes("CDS"))&&(p.value.push(Q.CDS),p.value.push(Q.GPCDS)),((K=t.value)!=null&&K.includes("$$$")||(se=t.value)!=null&&se.includes("本票"))&&p.value.push(Q.ARL),a.ticketManagementOrganizationCode=re(a.ticketManagementOrganizationCode??"")};return Ae(async()=>{le()}),Ge(()=>n.disabled,()=>{!n.disabled&&Ie.test(a.name)&&(a.name="")}),{segmentErrorMessage:k,formData:a,formRef:b,rules:$,ticketOrganizationList:p,ticketOrganizationListEnum:Q,checkTax:B,countAmount:m,bindPaymentValue:Z,isCheckSeg:D,resetForm:L,validate:N,getFormDate:A,addTax:ee,commisionRateChange:P,validSegment:q,isShowPrintNo:I,changeTicketManagementOrganizationCode:ae}},Et=St,oe=n=>(xe("data-v-8c536061"),n=n(),ye(),n),Dt={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},At={class:"self-stretch justify-start items-start gap-5 inline-flex"},jt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Vt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ft={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ot={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Mt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Pt={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Bt={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ut={key:0,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},zt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ht={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Lt={key:1,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Gt={class:"carType-option-panel"},qt={key:2,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Qt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Yt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Jt={key:3,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Xt={class:"self-stretch justify-start items-center gap-5 inline-flex"},Kt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Wt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Zt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},es={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ts={class:"w-[84px] text-gray-3 text-xs shrink-0"},ss={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},as={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ns={class:"w-[84px] text-gray-3 text-xs shrink-0"},is={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},os={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},rs={class:"w-[84px] text-gray-3 text-xs shrink-0"},ls={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ds={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},us={class:"self-stretch justify-start items-start gap-5 inline-flex"},cs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ps={class:"w-[84px] text-gray-3 text-xs shrink-0"},fs={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ms={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},gs={class:"w-[84px] text-gray-3 text-xs shrink-0"},hs={class:"text-gray-2 text-xs font-bold whitespace-nowrap w-[150px]"},xs=oe(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),ys={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},vs={class:"w-[84px] text-gray-3 text-xs shrink-0"},bs={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},_s={class:"self-stretch justify-start items-start gap-5 inline-flex"},ks={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ws={key:0,class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},Rs={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},Ts={key:1,class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},Ns={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},$s={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Cs={class:"justify-start items-center gap-4 flex"},Is={class:"text-gray-2 text-xs leading-tight"},Ss={key:1,class:"flex-col"},Es={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},Ds={class:"justify-start items-center gap-4 flex h-[20px]"},As={class:"text-gray-2 text-xs leading-tight"},js={class:"self-stretch justify-start items-start gap-5 inline-flex"},Vs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Fs={key:0,class:"not-required-tip"},Os={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ms={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ps=oe(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},[e("em")],-1)),Bs={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Us={class:"self-stretch justify-start items-start gap-5 inline-flex"},zs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Hs={class:"w-full mb-[10px]"},Ls={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},Gs={class:"ml-[20px]"},qs={class:"text-gray-2 font-[700]"},Qs={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},Ys={class:"w-[20px] text-gray-3 text-xs shrink-0"},Js={class:"w-[40px] mr-[6px] shrink-0"},Xs={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},Ks={class:"self-stretch justify-start items-start gap-5 inline-flex"},Ws={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Zs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},ea=oe(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),ta={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},sa=oe(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px] min-w-[11px]"},"%",-1)),aa={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},na={class:"self-stretch justify-start items-center gap-5 inline-flex"},ia={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},oa={class:"w-[84px] text-gray-3 text-xs shrink-0"},ra={class:"justify-start items-center flex text-gray-2 text-xs relative"},la=oe(()=>e("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1)),da={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},ua={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:basis-1/4"},ca={key:1},pa=oe(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:hidden"},[e("em")],-1)),fa={class:"ml-[260px] text-yellow-1"},ma=ue({__name:"RefundForm",props:{data:{},refundTickets:{},disabled:{type:Boolean,default:!1},isSupplementRefund:{type:Boolean,default:!1},taxsHistory:{},couponNoHistory:{}},setup(n,{expose:d}){const y=n,{formData:t,formRef:b,rules:a,ticketOrganizationList:k,ticketOrganizationListEnum:p,checkTax:I,countAmount:o,bindPaymentValue:H,isCheckSeg:C,resetForm:V,validate:F,getFormDate:$,addTax:S,commisionRateChange:P,segmentErrorMessage:M,validSegment:B,isShowPrintNo:U,changeTicketManagementOrganizationCode:G}=Et(y);return d({resetForm:V,validate:F,getFormDate:$,validSegment:B}),(u,m)=>{const Z=Ye,D=ft,L=mt,N=gt,ee=xt,A=je,q=Me,ae=yt,re=vt,Q=he,le=ht;return x(),O(le,{ref_key:"formRef",ref:b,model:s(t),class:"refund-form","label-position":"left","require-asterisk-position":"right"},{default:v(()=>{var f;return[e("div",Dt,[e("div",At,[e("div",jt,[e("div",Vt,r(u.$t("app.agentTicketRefund.refundTicketNumber")),1),e("div",Ft,r(s(t).refundNo),1)]),e("div",Ot,[e("div",Mt,r(u.$t("app.agentTicketRefund.rtType")),1),u.isSupplementRefund?(x(),R("div",Pt,r(s(t).refundType),1)):(x(),R("div",Bt,r(s(t).international??"-"),1))]),u.isSupplementRefund?(x(),R("div",Lt,[h(N,{prop:"ticketManagementOrganizationCode",rules:s(a).ticketManagementOrganizationCode,label:u.$t("app.agentTicketQuery.ticketOrganization")},{default:v(()=>[h(L,{modelValue:s(t).ticketManagementOrganizationCode,"onUpdate:modelValue":m[0]||(m[0]=i=>s(t).ticketManagementOrganizationCode=i),class:"ticket-management-organization",disabled:s(t).ticketManagementOrganizationCode==="",placeholder:s(t).ticketManagementOrganizationCode===""?u.$t("app.agentTicketQuery.noData"):"",onChange:s(G)},{default:v(()=>[(x(!0),R(Y,null,J(s(k),i=>(x(),O(D,{key:i.value,label:i.label,value:i.value},{default:v(()=>[e("div",Gt,[e("div",{class:T(s(t).ticketManagementOrganizationCode===i.value?"show-select":"hidden-select")},[h(Z,null,{default:v(()=>[h(s(qe))]),_:1})],2),e("span",null,r(i.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["rules","label"])])):(x(),R("div",Ut,[e("div",zt,r(u.$t("app.agentTicketQuery.ticketOrganization")),1),e("div",Ht,r(((f=s(p)[s(t).ticketManagementOrganizationCode??""])==null?void 0:f.label)||"-"),1)])),u.isSupplementRefund?(x(),R("div",Jt,[s(U)?(x(),O(N,{key:0,prop:"prntNo",rules:s(a).prntNo,label:u.$t("app.ticketStatus.deviceNum")},{default:v(()=>[h(Ze,{modelValue:s(t).prntNo,"onUpdate:modelValue":[m[1]||(m[1]=i=>s(t).prntNo=i),m[2]||(m[2]=i=>s(b).validateField("prntNo"))],"select-class":""},null,8,["modelValue"])]),_:1},8,["rules","label"])):W("",!0)])):(x(),R("div",qt,[e("div",Qt,r(u.$t("app.agentTicketRefund.prntNo")),1),e("div",Yt,r(["CDS","GPCDS"].includes(s(t).ticketManagementOrganizationCode??"")?"-":s(t).prntNo),1)]))]),e("div",Xt,[e("div",Kt,[e("div",Wt,r(u.$t("app.agentTicketRefund.refundAgent")),1),e("div",Zt,r(s(t).agent),1)]),e("div",es,[e("div",ts,r(u.$t("app.agentTicketRefund.refundIataNo")),1),e("div",ss,r(s(t).iata),1)]),e("div",as,[e("div",ns,r(u.$t("app.agentTicketRefund.refundOffice")),1),e("div",is,r(s(t).office),1)]),e("div",os,[e("div",rs,r(u.$t("app.agentTicketRefund.refundDate")),1),e("div",ls,r(s(t).refundDate),1)])])]),e("div",ds,[e("div",us,[e("div",cs,[e("div",ps,r(u.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),e("div",fs,r(s(t).airline),1)]),e("div",ms,[e("div",gs,r(u.$t("app.agentTicketRefund.refundTicketNo")),1),h(ee,{placement:"top",trigger:"hover",content:s(t).ticketNoView},{default:v(()=>[e("div",hs,r(s(t).ticketNoView),1)]),_:1},8,["content"])]),xs,e("div",ys,[e("div",vs,r(u.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),e("div",bs,r(s(t).conjunction),1)])]),e("div",_s,[e("div",ks,[h(N,{label:u.$t("app.agentTicketRefund.passName"),prop:"name",rules:s(a).psgName},{default:v(()=>[h(A,{modelValue:s(t).name,"onUpdate:modelValue":m[3]||(m[3]=i=>s(t).name=i),disabled:u.disabled,clearable:"",onInput:m[4]||(m[4]=i=>s(t).name=s(t).name.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),u.isSupplementRefund?(x(),R("div",Ts,[e("div",Ns,r(u.$t("app.agentTicketRefund.refundSeg")),1),e("div",$s,[s(t).conjunction===1?(x(!0),R(Y,{key:0},J(u.refundTickets,(i,c)=>(x(),O(N,{key:c,prop:`segment[${c}]`},{default:v(()=>[h(ae,{modelValue:s(t).checkedSeg,"onUpdate:modelValue":m[5]||(m[5]=g=>s(t).checkedSeg=g),disabled:u.disabled},{default:v(()=>[e("div",Cs,[(x(!0),R(Y,null,J(i,(g,l)=>(x(),R("div",{key:l+"segmet",class:"justify-start items-center gap-[2px] flex"},[h(q,{disabled:!g.select,label:g},{default:v(()=>[e("div",Is,r(g.deptCity)+"-"+r(g.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:T([!g.select||!s(C)(g)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},r(u.$t(`app.queryRefunds.number_${l+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"]))),128)):(x(),R("div",Ss,[(x(!0),R(Y,null,J(u.refundTickets,(i,c)=>(x(),R("div",{key:c,class:T({"mb-[10px]":c<u.refundTickets.length-1})},[e("div",Es,r(u.$t("app.agentTicketRefund.couponNo",{a:s(et)(Number(i[0].conjunctionIndex)-1)})),1),h(N,{prop:`segment[${c}]`},{default:v(()=>[h(ae,{modelValue:s(t).checkedSeg,"onUpdate:modelValue":m[6]||(m[6]=g=>s(t).checkedSeg=g),disabled:u.disabled},{default:v(()=>[e("div",Ds,[(x(!0),R(Y,null,J(i,(g,l)=>(x(),R("div",{key:l+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[h(q,{disabled:!g.select,label:g},{default:v(()=>[e("div",As,r(g.deptCity)+"-"+r(g.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:T([!g.select||!s(C)(g)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},r(u.$t(`app.queryRefunds.number_${l+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"])],2))),128))]))])])):(x(),R("div",ws,[e("div",Rs,r(u.$t("app.agentTicketRefund.refundSeg")),1),(x(!0),R(Y,null,J(s(t).couponNos,(i,c)=>(x(),O(N,{key:"couponNo"+c,prop:"couponNos."+c,error:s(M)[c],class:"mr-[10px] coupon-no"},{default:v(()=>[h(A,{modelValue:s(t).couponNos[c],"onUpdate:modelValue":g=>s(t).couponNos[c]=g,modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",onBlur:s(B)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"])]),_:2},1032,["prop","error"]))),128))]))]),e("div",js,[e("div",Vs,[h(N,{label:u.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:s(a).totalAmount,class:T({"not-required-container":!s(t).totalAmount})},{default:v(()=>[h(A,{modelValue:s(t).totalAmount,"onUpdate:modelValue":m[7]||(m[7]=i=>s(t).totalAmount=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",onBlur:m[8]||(m[8]=i=>s(o)(""))},null,8,["modelValue","disabled"]),s(t).totalAmount?W("",!0):(x(),R("div",Fs,r(u.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),e("div",Os,[h(N,{label:u.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:s(a).payType},{default:v(()=>[u.disabled?(x(),R("div",{key:1,class:T([{"bg-brand-7":!u.disabled},"bg-gray-7 w-full h-[32px] px-[12px] py-4[px] flex items-center text-xs font-bold cursor-not-allowed text-[--bkc-el-text-color-placeholder] border-solid border-[1px] border-[--bkc-el-disabled-border-color] rounded-[4px]"])},[z(r(s(tt)(s(t).payType))+" ",1),h(Z,{class:"refundForm-icon brand text-gray-7"},{default:v(()=>[h(s(Qe))]),_:1})],2)):(x(),O(L,{key:0,modelValue:s(t).payType,"onUpdate:modelValue":m[9]||(m[9]=i=>s(t).payType=i),modelModifiers:{trim:!0},disabled:u.disabled,class:"pay",filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:u.$t("app.agentTicketRefund.paymentSel"),clearable:"",onBlur:s(H)},{default:v(()=>[(x(!0),R(Y,null,J(s(Fe),(i,c)=>(x(),O(D,{key:c,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onBlur"]))]),_:1},8,["label","rules"])]),e("div",Ms,[h(N,{label:u.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:s(a).currency},{default:v(()=>[h(A,{modelValue:s(t).currency,"onUpdate:modelValue":m[10]||(m[10]=i=>s(t).currency=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",onInput:m[11]||(m[11]=i=>s(t).currency=s(t).currency.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),Ps])]),e("div",Bs,[e("div",Us,[e("div",zs,[h(N,{label:u.$t("app.agentTicketRefund.etTag")},{default:v(()=>[h(re,{modelValue:s(t).etTagNew,"onUpdate:modelValue":m[12]||(m[12]=i=>s(t).etTagNew=i),disabled:u.disabled||u.isSupplementRefund,"inline-prompt":"","active-text":"Y","inactive-text":"N"},null,8,["modelValue","disabled"])]),_:1},8,["label"])])]),e("div",Hs,[e("div",Ls,[e("div",null,[e("span",null,r(u.$t("app.agentTicketRefund.refundTax")),1),e("span",Gs,r(u.$t("app.fare.singleFare.totalTax")),1),e("span",qs," "+r(s(t).currency)+" "+r(s(t).totalTaxs),1)]),u.disabled?W("",!0):(x(),O(Q,{key:0,link:"",type:"primary",size:"small",disabled:s(t).taxs.length===27,onClick:s(S)},{default:v(()=>[z(r(u.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"]))]),e("div",Qs,[(x(!0),R(Y,null,J(s(t).taxs,(i,c)=>(x(),R("div",{key:c+new Date,class:"grow shrink-0 basis-0 h-[32px] justify-start items-center flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[e("div",Ys,r(c+1),1),e("div",Js,[h(N,{prop:"taxs."+c+".taxCode",rules:s(a).taxName},{default:v(()=>[h(A,{modelValue:i.taxCode,"onUpdate:modelValue":g=>i.taxCode=g,modelModifiers:{trim:!0},disabled:u.disabled,onInput:g=>i.taxCode=i.taxCode.toUpperCase(),onBlur:s(I)},null,8,["modelValue","onUpdate:modelValue","disabled","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),h(N,{prop:"taxs."+c+".taxAmount",rules:s(a).taxValue},{default:v(()=>[h(A,{modelValue:i.taxAmount,"onUpdate:modelValue":g=>i.taxAmount=g,modelModifiers:{trim:!0},disabled:u.disabled,onBlur:s(I)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),e("div",Xs,[e("div",Ks,[e("div",Ws,[h(N,{label:u.$t("app.agentTicketRefund.commision"),prop:"commision",rules:s(a).price},{default:v(()=>[h(A,{modelValue:s(t).commision,"onUpdate:modelValue":m[13]||(m[13]=i=>s(t).commision=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",placeholder:"0.00",onBlur:m[14]||(m[14]=i=>s(o)(""))},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),e("div",Zs,[h(N,{label:u.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:s(a).rate},{default:v(()=>[h(A,{modelValue:s(t).commisionRate,"onUpdate:modelValue":m[15]||(m[15]=i=>s(t).commisionRate=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",placeholder:"0.00",onBlur:m[16]||(m[16]=i=>s(o)("")),onInput:s(P)},null,8,["modelValue","disabled","onInput"]),ea]),_:1},8,["label","rules"])]),e("div",ta,[h(N,{label:u.disabled?u.$t("app.agentTicketRefund.otherDeductionRate"):u.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:s(a).rate},{default:v(()=>[h(A,{modelValue:s(t).otherDeductionRate,"onUpdate:modelValue":m[17]||(m[17]=i=>s(t).otherDeductionRate=i),modelModifiers:{trim:!0},class:"min-width-42",disabled:u.disabled,clearable:"",placeholder:"1-100",onBlur:m[18]||(m[18]=i=>s(o)("otherDeductionRate"))},null,8,["modelValue","disabled"]),sa]),_:1},8,["label","rules"])]),e("div",aa,[h(N,{label:u.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:s(a).price},{default:v(()=>[h(A,{modelValue:s(t).otherDeduction,"onUpdate:modelValue":m[19]||(m[19]=i=>s(t).otherDeduction=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",placeholder:"0.00",onBlur:m[20]||(m[20]=i=>s(o)(""))},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])])]),e("div",na,[e("div",ia,[e("div",oa,r(u.$t("app.agentTicketRefund.remark")),1),e("div",ra,[h(N,{prop:"remarkInfo",rules:s(a).remarkInfo},{default:v(()=>[h(A,{modelValue:s(t).remarkInfo,"onUpdate:modelValue":m[21]||(m[21]=i=>s(t).remarkInfo=i),disabled:u.disabled,clearable:"",placeholder:u.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:m[22]||(m[22]=i=>{var c;return s(t).remarkInfo=(c=s(t).remarkInfo)==null?void 0:c.toUpperCase()})},null,8,["modelValue","disabled","placeholder"])]),_:1},8,["rules"]),h(ee,{placemant:"top",content:u.$t("app.agentTicketRefund.remarkTips")},{default:v(()=>[la]),_:1},8,["content"])])]),e("div",da,[h(N,{label:u.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund",rules:s(a).netRefund},{default:v(()=>[h(A,{modelValue:s(t).netRefund,"onUpdate:modelValue":m[23]||(m[23]=i=>s(t).netRefund=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:""},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),e("div",ua,[s(t).payType==="TC"?(x(),O(N,{key:0,label:u.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:s(a).creditCard},{default:v(()=>[h(A,{modelValue:s(t).creditCard,"onUpdate:modelValue":m[24]||(m[24]=i=>s(t).creditCard=i),modelModifiers:{trim:!0},disabled:u.disabled,clearable:"",onInput:m[25]||(m[25]=i=>s(t).creditCard=s(t).creditCard.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])):(x(),R("em",ca))]),pa]),e("div",fa,r(u.$t("app.agentTicketRefund.netRefundTip")),1)])]}),_:1},8,["model"])}}});const ga=ve(ma,[["__scopeId","data-v-8c536061"]]),ha=n=>(xe("data-v-1c46f5f6"),n=n(),ye(),n),xa={class:"w-[460px] h-12 justify-start items-center gap-4 inline-flex mb-[10px]"},ya=ha(()=>e("em",{class:"iconfont icon-info-circle-line text-brand-2"},null,-1)),va={class:"h-[50px] grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal whitespace-normal"},ba={class:"w-[206px] h-8 justify-end items-center gap-2.5 inline-flex"},_a=ue({__name:"DeleteConfirm",props:{airlineCode:{}},emits:["update:modelValue","confirm"],setup(n,{emit:d}){const y=d,t=()=>{y("update:modelValue",!1)},a=j(n.airlineCode!=="784");return(k,p)=>{const I=Me,o=he,H=ke;return x(),O(H,{class:"rval-dialog",width:"500px","close-on-click-modal":!1,"show-close":!1,onClose:p[2]||(p[2]=C=>t())},{footer:v(()=>[e("span",ba,[k.airlineCode!=="784"?(x(),O(I,{key:0,modelValue:a.value,"onUpdate:modelValue":p[0]||(p[0]=C=>a.value=C),label:k.$t("app.refundForm.restore"),size:"large"},null,8,["modelValue","label"])):W("",!0),h(o,{type:"primary",onClick:p[1]||(p[1]=C=>k.$emit("confirm",a.value))},{default:v(()=>[z(r(k.$t("app.refundForm.confirmBtn")),1)]),_:1}),h(o,{plain:"",onClick:t},{default:v(()=>[z(r(k.$t("app.refundForm.cancelBtn")),1)]),_:1})])]),default:v(()=>[e("div",xa,[ya,e("div",va,r(k.$t("app.refundForm.wainMsg")),1)])]),_:1})}}});const ka=ve(_a,[["__scopeId","data-v-1c46f5f6"]]),X=n=>(xe("data-v-afa211f4"),n=n(),ye(),n),wa=X(()=>e("div",{class:"w-[1012px] h-6 justify-center items-center gap-2.5 inline-flex relative mb-[10px]"},[e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"}),e("div",{class:"text-gray-2 text-base font-bold leading-normal"},"AIRLINE / BSP AUTO REFUND FORM"),e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"})],-1)),Ra={class:"w-[1012px] h-[158px] flex-col justify-start items-start gap-2.5 inline-flex"},Ta={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Na={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},$a={class:"text-gray-2 font-bold leading-tight"},Ca={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ia={class:"text-gray-2 font-bold leading-tight"},Sa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ea={class:"text-gray-2 font-bold leading-tight"},Da={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Aa={class:"text-gray-2 font-bold leading-tight"},ja={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Va={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Fa={class:"text-gray-2 font-bold leading-tight"},Oa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ma={class:"text-gray-2 font-bold leading-tight"},Pa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ba=X(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"CURRENCY CODE",-1)),Ua=[Ba],za={class:"text-gray-2 font-bold leading-tight"},Ha={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},La=X(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"FORM PAYMENT",-1)),Ga=[La],qa={class:"text-gray-2 font-bold leading-tight"},Qa={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},Ya={class:"text-gray-2 font-bold leading-tight"},Ja={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Xa={class:"grow shrink basis-0 h-5 justify-start items-center gap-2.5 flex"},Ka={class:"text-gray-2 font-bold leading-tight"},Wa={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Za={class:"text-gray-2 font-bold leading-tight"},en={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},tn={class:"text-gray-2 font-bold leading-tight"},sn={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},an={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},nn={class:"text-gray-2 font-bold leading-tight"},on={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},rn={class:"text-gray-2 font-bold leading-tight"},ln={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},dn={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},un={class:"text-gray-2 font-bold leading-tight"},cn={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},pn={class:"text-gray-2 font-bold leading-tight"},fn={class:"w-[1012px]"},mn={class:"w-[1012px] h-[44px] flex-col justify-start items-start gap-2.5 inline-flex"},gn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},hn=X(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"GROSS REFUND",-1)),xn=[hn],yn={class:"text-gray-2 font-bold leading-tight"},vn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},bn={class:"text-gray-2 font-bold leading-tight"},_n={class:"w-[1012px]"},kn={class:"w-[1012px] h-[100px] flex-col justify-start items-start gap-2.5 inline-flex"},wn={class:"self-stretch h-[18px] justify-start items-center gap-5 inline-flex"},Rn={class:"h-5 justify-start items-center gap-2.5 flex"},Tn=X(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),Nn={class:"text-gray-2 font-bold leading-tight"},$n=X(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"=",-1)),Cn={class:"text-gray-2 font-bold leading-tight"},In=X(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"%",-1)),Sn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},En=X(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),Dn={class:"text-gray-2 font-bold leading-tight"},An={class:"self-stretch grow shrink basis-0 justify-start items-center gap-2.5 inline-flex"},jn={class:"text-gray-2 font-bold leading-tight"},Vn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},Fn=X(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"= ",-1)),On={class:"text-gray-2 font-bold leading-tight"},Mn={class:"text-gray-2 font-bold leading-tight"},Pn={key:0},Bn=X(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),Un={key:0,class:"text-gray-2 font-bold inline-block whitespace-nowrap text-base"},zn=X(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),Hn=ue({__name:"PrintRefundForm",props:{refundInfo:{},originalTickets:{},isElectronic:{type:Boolean},windowHeight:{}},setup(n,{expose:d}){const y=j(),t=j(!1),b=j(!1),a=j("AIRLINE / BSP AUTO REFUND FORM COMPLETED"),k=bt(p=>{p==="enter"?b.value=!0:b.value=!1},200);return d({captureRef:y,originInfo:t}),(p,I)=>{var C,V,F;const o=_t,H=je;return x(),R("div",{ref_key:"captureRef",ref:y,class:T([p.isElectronic?"overflow-y-scroll w-full p-[14px]":"w-full p-[14px]"]),style:Je({height:p.windowHeight+"px"})},[wa,e("div",Ra,[e("div",Ta,[e("div",Na,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND NUMBER",2),e("span",$a,r(((C=p.refundInfo.refundNo)==null?void 0:C.length)===13?p.refundInfo.refundNo.substring(4):p.refundInfo.refundNo),1)]),e("div",Ca,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND TYPE",2),e("span",Ia,r(p.refundInfo.tktType==="D"?"DOMESTIC":"INTERNATIONAL"),1)]),e("div",Sa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CITY / OFFICE",2),e("div",Ea,r(p.refundInfo.office),1)]),e("div",Da,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AGENT",2),e("span",Aa,r(p.refundInfo.agent),1)])]),e("div",ja,[e("div",Va,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"IATA NUMBER",2),e("span",Fa,r(p.refundInfo.iata),1)]),e("div",Oa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"DATE / TIME",2),e("span",Ma,r(p.refundInfo.refundDate),1)]),e("div",Pa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Ua,2),e("span",za,r(p.refundInfo.refundFormCurrency),1)]),e("div",Ha,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Ga,2),e("span",qa,r(p.refundInfo.payType),1)])]),e("div",Qa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"PASSENGER NAM",2),e("span",Ya,r(p.refundInfo.name),1)]),e("div",Ja,[e("div",Xa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AIRLINE CODE",2),e("span",Ka,r(p.refundInfo.airline),1)]),e("div",Wa,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TICKET NO.",2),e("span",Za,r(p.refundInfo.ticketNoView),1)]),e("div",en,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CONJUNCTION",2),e("span",tn,r(p.refundInfo.conjunction),1)])]),e("div",sn,[e("div",an,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND COUPON-",2),e("div",nn,[(x(!0),R(Y,null,J(p.refundInfo.couponNos,($,S)=>(x(),R("span",{key:S},r(S+1)+":"+r($)+"  ",1))),128))])]),e("div",on,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ET(Y/N)",2),e("div",rn,r(p.refundInfo.etTagNew?"Y":"N"),1)])]),e("div",ln,[e("div",dn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REMARK",2),e("span",un,r(p.refundInfo.remarkInfo??""?`${(V=p.refundInfo.remarkInfo)==null?void 0:V.substring(0,2)}-${(F=p.refundInfo.remarkInfo)==null?void 0:F.substring(2)}`:""),1)]),e("div",cn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CREDIT CARD",2),e("span",pn,r(p.refundInfo.creditCard??""),1)])])]),e("div",fn,[h(o,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",mn,[e("div",gn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},xn,2),e("span",yn,r(Number(p.refundInfo.totalAmount).toFixed(2)),1)]),e("div",vn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ADD TAX(ES)",2),e("span",bn,"+ "+r(p.refundInfo.totalTaxs),1)])]),e("div",_n,[h(o,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",kn,[e("div",wn,[e("div",Rn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"COMMISSION",2),Tn,e("span",Nn,r(p.refundInfo.commision),1),$n,e("span",Cn,r(p.refundInfo.commisionRate),1),In])]),e("div",Sn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"OTHER DEDUCTION",2),En,e("span",Dn,r(p.refundInfo.otherDeduction),1)]),e("div",An,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"NET REFUND",2),e("span",jn,"= "+r(p.refundInfo.netRefund)+" ("+r(p.refundInfo.payType)+" "+r(p.refundInfo.refundFormCurrency)+")",1)]),e("div",Vn,[e("div",{class:T([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TAX",2),Fn,e("span",On,r(p.refundInfo.refundFormCurrency),1),e("div",Mn,[(x(!0),R(Y,null,J((p.refundInfo.taxs??[]).filter($=>$.taxAmount||$.taxCode),($,S)=>(x(),R("span",{key:S},[z(r(Number($.taxAmount).toFixed(2))+r($.taxCode)+" ",1),S!==(p.refundInfo.taxs??[]).filter(P=>P.taxAmount||P.taxCode).length-1?(x(),R("span",Pn,"+")):W("",!0)]))),128))])])]),e("div",{class:"w-[1012px] h-[auto] items-center gap-2.5 relative mt-[10px] mb-[20px] inline-flex",onMouseenter:I[2]||(I[2]=$=>s(k)("enter")),onMouseleave:I[3]||(I[3]=$=>s(k)("leave"))},[Bn,b.value?(x(),O(H,{key:1,modelValue:a.value,"onUpdate:modelValue":I[0]||(I[0]=$=>a.value=$),class:"end-tilte",maxlength:"60","show-word-limit":"",onInput:I[1]||(I[1]=$=>a.value=a.value.toUpperCase())},null,8,["modelValue"])):(x(),R("div",Un,r(a.value),1)),zn],32),p.originalTickets.length>0?(x(!0),R(Y,{key:0},J(p.originalTickets,($,S)=>(x(),R("div",{key:S,class:T([t.value?"text-[18px]":"","w-[1012px] p-[10px] mb-[14px] bg-gray-7 text-gray-1 text-[14px] last:mb-[0px]"])},[e("pre",null,r(s(Ve).decode($)),1)],2))),128)):W("",!0)],6)}}});const Pe=ve(Hn,[["__scopeId","data-v-afa211f4"]]),ge=function(n,d){if(!(this instanceof ge))return new ge(n,d);this.options=this.extend({noPrint:"",style:"",paging:!1},d),typeof n=="string"?this.dom=document.querySelector(n):(this.isDOM(n),this.dom=this.isDOM(n)?n:n.$el),this.init()};ge.prototype={init:function(){var n=this.getStyle()+this.getHtml();this.writeIframe(n)},extend:function(n,d){for(var y in d)n[y]=d[y];return n},getStyle:function(){for(var n="",d=document.querySelectorAll("style,link"),y=0;y<d.length;y++)n+=d[y].outerHTML;return n+="<style>"+(this.options.noPrint?this.options.noPrint:".no-print")+"{display:none;}</style>",this.options.paging&&(n+="<style>html,body{padding: 20px},div{height: auto!important;-webkit-print-color-adjust: exact}</style>"),n},getHtml:function(){for(var n=document.querySelectorAll("input"),d=document.querySelectorAll("textarea"),y=document.querySelectorAll("select"),t=0;t<n.length;t++)n[t].type=="checkbox"||n[t].type=="radio"?n[t].checked==!0?n[t].setAttribute("checked","checked"):n[t].removeAttribute("checked"):(n[t].type=="text",n[t].setAttribute("value",n[t].value));for(var b=0;b<d.length;b++)d[b].type=="textarea"&&(d[b].innerHTML=d[b].value);for(var a=0;a<y.length;a++)if(y[a].type=="select-one"){var k=y[a].children;for(var p in k)k[p].tagName=="OPTION"}return this.options.paging?this.dom.outerHTML:this.wrapperRefDom(this.dom).outerHTML},wrapperRefDom:function(n){let d=null,y=n;if(!this.isInBody(y))return y;for(;y;){if(d){let t=y.cloneNode(!1);t.appendChild(d),d=t}else d=y.cloneNode(!0);y=y.parentElement}return d},isInBody:function(n){return n===document.body?!1:document.body.contains(n)},writeIframe:function(n){n=n+"<style>.print{transform: scale(2) !important;}@page {margin-top: 1mm;margin-bottom: 1mm;}</style>"+this.options.style;var d,y,t=document.createElement("iframe"),b=document.body.appendChild(t);t.id="myIframe",t.setAttribute("style","position:absolute;width:0;height:0;top:-10px;left:-10px;"),d=b.contentWindow||b.contentDocument,y=b.contentDocument||b.contentWindow.document,y.open(),y.write(n),y.close();var a=this;t.onload=function(){a.toPrint(d),setTimeout(function(){document.body.removeChild(t)},100)}},toPrint:function(n){try{setTimeout(function(){n.focus();try{n.document.execCommand("print",!1,null)||n.print()}catch{n.print()}n.close()},10)}catch(d){console.log("err",d)}},isDOM:typeof HTMLElement=="object"?function(n){return n instanceof HTMLElement}:function(n){return n&&typeof n=="object"&&n.nodeType===1&&typeof n.nodeName=="string"}};const Ln={class:"electronic-print-refund-form"},Gn={class:"flex justify-center mt-5"},qn=ue({__name:"ElectronicPrintRefundForm",props:{modelValue:{type:Boolean},refundInfo:{},originalTickets:{},isElectronic:{type:Boolean}},emits:["update:modelValue"],setup(n,{emit:d}){const y=n,t=d,b=de(y.modelValue),a=j(),k=j(!1),p=()=>{t("update:modelValue",!1)},I=async()=>{k.value=!0,a.value.originInfo=!0,await Xe(),await ge(a.value,{paging:!0,style:`<style>
          .print-refund-form-panel {
            width: 1050px
          }
          * {
            font-size: 15px;
          }
        </style>`}),a.value.originInfo=!1,k.value=!1},o=me(()=>window.innerHeight-120),H=async()=>{var P,M,B,U;if(!((P=a.value)!=null&&P.captureRef))return;const C=((M=a.value)==null?void 0:M.captureRef.scrollHeight)+20,V=(B=a.value)==null?void 0:B.captureRef.scrollWidth;a.value.captureRef.style.height=C+"px";const F=await st((U=a.value)==null?void 0:U.captureRef,{width:V,height:C,scale:1});a.value.captureRef.style.height=o.value+"px";const $=F.toDataURL("image/png"),S=document.createElement("a");S.href=$,S.download=`${y.refundInfo.ticketNo}.png`,S.click()};return(C,V)=>{const F=he,$=ke;return x(),R("div",Ln,[h($,{modelValue:b.value,"onUpdate:modelValue":V[0]||(V[0]=S=>b.value=S),width:"1046","custom-class":"print-refund-form","show-close":!1,"close-on-click-modal":!1,onClose:p},{default:v(()=>[h(Pe,{ref_key:"printRef",ref:a,class:T(["print-refund-form-panel",k.value?"":"text-xs"]),"refund-info":C.refundInfo,"original-tickets":C.originalTickets,"is-electronic":C.isElectronic,"window-height":o.value},null,8,["class","refund-info","original-tickets","is-electronic","window-height"]),e("div",Gn,[h(F,{class:"print-btn",type:"primary",onClick:I},{default:v(()=>[z(r(C.$t("app.refundForm.print")),1)]),_:1}),h(F,{class:"print-btn text-color",onClick:H},{default:v(()=>[z(r(C.$t("app.refundForm.download")),1)]),_:1},8,["onClick"]),h(F,{class:"print-btn text-color",onClick:p},{default:v(()=>[z(r(C.$t("app.refundForm.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["modelValue"])])}}});const Qn=(n,d)=>{const{t:y}=De(),t=de(!0),b=de(""),a=j(),k=j(),p=de(!1),I=me(()=>window.innerHeight-120),o=j({}),H=j({}),C=j({}),V=j([]),F=j(!1),$=de(!1),S=j([]),P=j([]),M=(l,_)=>ie(l)?_.toString().endsWith(".00")?_.toString().slice(0,-3):_:Number(_).toFixed(2),B=(l,_)=>{let w=[];return w=l.map(E=>({taxCode:E.taxCode,taxAmount:M(_,E.taxAmount)})),w.length<10?w.concat(new Array(10-w.length).fill({taxCode:"",taxAmount:""})).map(E=>({...E})):w},U=()=>{for(const l in o.value.segmentInfos){const _=[...o.value.segmentInfos[l]];V.value.push(_)}},G=()=>{const l=[];return o.value.segmentInfos&&Object.values(o.value.segmentInfos).forEach(_=>{(_??[]).forEach(w=>{w.ticketStatus==="REFUNDED"&&l.push(w)})}),l},u=(l,_,w)=>_?w!=="ARL"||_.startsWith(l)?_:`${l}-${_}`:"-",m=()=>{if(!o.value.ticketNoEnd&&o.value.ticketNoView){const l=o.value.ticketNoView.length,_=o.value.ticketNoView.substring(l-2);return`${o.value.ticketNo}-${_}`}return o.value.ticketNo===o.value.ticketNoEnd?o.value.ticketNo:`${o.value.ticketNo}-${o.value.ticketNoEnd}`},Z=()=>{var l,_,w;U(),S.value=B(o.value.taxInfos,o.value.currency),P.value=o.value.couponNo?JSON.parse(JSON.stringify(o.value.couponNo)):[],C.value={refundNo:n.isSupplementRefund?"-":u(o.value.airlineCode,o.value.cmdNo,o.value.ticketManagementOrganizationCode??""),volunteer:o.value.cmdOption,createUser:o.value.operator,prntNo:o.value.deviceNum,refundType:n.isSupplementRefund?y("app.refundForm.manualRefundType"):"-",refundDate:o.value.refundDate??"",international:o.value.international==="I"?"INTERNATIONAL":"DOMESTIC",agent:o.value.agent,iata:o.value.iataNo,office:o.value.office,tktType:o.value.ticketType==="D"?"D":"I",ticketManagementOrganizationCode:o.value.ticketManagementOrganizationCode,ticketNo:m(),ticketNoView:o.value.ticketNoView,conjunction:o.value.conjunction,name:o.value.passengerName,psgType:o.value.passengerType,totalAmount:M(o.value.currency,o.value.grossRefund),payType:o.value.payMethod,currency:o.value.currency,etTagNew:o.value.refund==="Y",taxs:B(o.value.taxInfos,o.value.currency),totalTaxs:M(o.value.currency,o.value.totalTaxs),commision:M(o.value.currency,o.value.commission),commisionRate:Number(o.value.commissionRate)>0?M(o.value.currency,o.value.commissionRate):"",rate:o.value.commissionRate?"1":"0",otherDeductionRate:"",otherDeduction:M(o.value.currency,o.value.deduction),remark:o.value.remark?o.value.remark.substring(2):"",netRefund:M(o.value.currency,o.value.netRefund),creditCard:o.value.creditCard,airline:o.value.airlineCode,crsPnrNo:"",pnr:"",isCoupon:"",receiptPrinted:"",check:"",remarkCode:o.value.remark?o.value.remark.substring(0,2):"",remarkInfo:o.value.remark,checkedSeg:G(),couponNos:o.value.couponNo??[],printRefundFormTicketNo:`${(l=o.value.ticketNo)==null?void 0:l.substring(3)}-${(_=o.value.ticketNoSecond)==null?void 0:_.substring(3)}`,refundFormCurrency:((w=o.value)==null?void 0:w.refundFormCurrency)??""}},D=()=>{d("update:modelValue",!1)},L=()=>{t.value=!1,k.value.resetForm()},N=async()=>{$.value=!0},ee=()=>{F.value=!0},A=async l=>{var w,E,K;F.value=!1;const _={refundNo:(w=k.value)==null?void 0:w.getFormDate().refundNo,ticketNo:n.refundOperationCondition.ticketNo,ticketType:n.printerType?n.printerType:n.refundOperationCondition.ticketType,ticketManagementOrganizationCode:(E=k.value)==null?void 0:E.getFormDate().ticketManagementOrganizationCode,printerNo:(K=k.value)==null?void 0:K.getFormDate().prntNo,resetTicketStatus:l};try{const se=Te("091T0105");p.value=!0,await kt(_,se),p.value=!1,await Ne(y("app.refundForm.successMsg")),(n==null?void 0:n.isSalesDaily)??!1?d("reSalesDaily"):await d("reQueryTicket"),await D()}finally{p.value=!1}},q=()=>{t.value=!1},ae=()=>{t.value=!0},re=()=>{k.value.resetForm()},Q=l=>({refund:l.etTagNew?"Y":"N",currency:l.currency,payMethod:l.payType,remark:l.remarkInfo??"",creditCard:l.creditCard?l.creditCard:"",couponNos:l.couponNos,name:Ve.encode((l.name??"").trim())}),le=l=>{const _=[];return l.forEach(w=>{if(w.taxAmount&&w.taxCode){const E={taxCode:w.taxCode,taxAmount:Number(w.taxAmount)};_.push(E)}}),_},f=l=>({commission:l.commision&&Number(l.commision)>0?l.commision.toString():"0",commissionRate:l.commisionRate.toString()??"",grossRefund:l.totalAmount.toString(),deduction:l.otherDeduction.toString(),netRefund:l.netRefund.toString(),taxInfos:le(l.taxs)}),i=l=>({refundNo:l.refundNo,ticketNo:l.ticketNo.includes("-")?l.ticketNo.split("-")[0]:l.ticketNo,ticketType:n.printerType?n.printerType:l.tktType,printerNo:l.prntNo,ticketManagementOrganizationCode:l.ticketManagementOrganizationCode??"",refundFormPassengerItem:Q(l),refundFormPriceItem:f(l)}),c=async l=>{p.value=!0;const _=i(l);let w;try{const E=Te("091T0106");w=(await wt(_,E)).data.value,(w==null?void 0:w.code)==="200"&&(await Ne(y("app.refundForm.editSuccess")),d("update:modelValue",!1))}finally{p.value=!1}},g=async()=>{var K,se,we;const l=(K=k.value)==null?void 0:K.getFormDate();if(!l.couponNos.some(Be=>Be!=="0000")){Ke({type:"warning",message:y("app.agentTicketRefund.selSeg")});return}const w=await((se=k.value)==null?void 0:se.validate()),E=(we=k.value)==null?void 0:we.validSegment();w&&E&&c(l)};return Ae(()=>{t.value=n.isSupplementRefund,b.value=t.value?y("app.refundForm.supplementaryRefundInfo"):y("app.refundForm.refundFormInfo"),o.value=n.refundTicketData,n.refundTicketData&&Z()}),{taxsHistory:S,refundTicketData:o,renderData:H,showEdit:t,title:b,fullscreenLoading:p,showDeleteConfirm:F,closeDialog:D,deleteRefund:ee,editRefund:ae,print:N,showRefundInfo:q,refundSegmentInfo:V,formInfo:C,formRef:k,printRef:a,initFormData:re,handleCommit:g,cancelEdit:L,onDeleteConfirm:A,showPrintRefundForm:$,windowHeight:I,couponNoHistory:P}},Yn=Qn,Jn=n=>(xe("data-v-ef7d2d0d"),n=n(),ye(),n),Xn=Jn(()=>e("i",{class:"iconfont icon-close"},null,-1)),Kn=[Xn],Wn={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},Zn={key:0},ei={key:0,class:"mt-[10px] py-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},ti={key:1},si={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},ai=ue({__name:"TicketRefundForm",props:{isSupplementRefund:{type:Boolean},printerNo:{},printerType:{},refundOperationCondition:{},refundTicketData:{},isSalesDaily:{type:Boolean},disableOperateButton:{type:Boolean}},emits:["update:modelValue","reQueryTicket","reSalesDaily"],setup(n,{emit:d}){const y=n,t=d,{showEdit:b,title:a,fullscreenLoading:k,closeDialog:p,deleteRefund:I,editRefund:o,print:H,refundSegmentInfo:C,formInfo:V,formRef:F,printRef:$,initFormData:S,handleCommit:P,cancelEdit:M,showDeleteConfirm:B,onDeleteConfirm:U,showPrintRefundForm:G,windowHeight:u,taxsHistory:m,couponNoHistory:Z}=Yn(y,t);return(D,L)=>{const N=he,ee=ke,A=We;return x(),O(ee,{title:s(a),width:"1040px",class:"refund-form-dialog","show-close":!1,"close-on-click-modal":!1,onClose:s(p)},{default:v(()=>[e("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:L[0]||(L[0]=(...q)=>s(p)&&s(p)(...q))},Kn),e("div",null,[e("div",Wn,r(D.$t("app.agentTicketRefund.refundInformationForm")),1),h(ga,{ref_key:"formRef",ref:F,data:s(V),"coupon-no-history":s(Z),"taxs-history":s(m),"refund-tickets":s(C),disabled:!s(b)},null,8,["data","coupon-no-history","taxs-history","refund-tickets","disabled"])]),D.disableOperateButton?W("",!0):(x(),R("div",Zn,[s(b)?(x(),R("div",ti,[e("div",si,[$e((x(),O(N,{type:"primary",onClick:s(P)},{default:v(()=>[z(r(D.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[A,s(k),void 0,{fullscreen:!0,lock:!0}]]),h(N,{onClick:s(S)},{default:v(()=>[z(r(D.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),h(N,{onClick:s(M)},{default:v(()=>[z(r(D.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])])):(x(),R("div",ei,[D.isSalesDaily?W("",!0):(x(),O(N,{key:0,type:"primary",onClick:s(o)},{default:v(()=>[z(r(D.$t("app.refundForm.edit")),1)]),_:1},8,["onClick"])),$e((x(),O(N,{onClick:s(I)},{default:v(()=>[z(r(D.$t("app.refundForm.delete")),1)]),_:1},8,["onClick"])),[[A,s(k),void 0,{fullscreen:!0,lock:!0}]]),D.isSalesDaily?W("",!0):(x(),O(N,{key:1,onClick:s(H)},{default:v(()=>[z(r(D.$t("app.refundForm.print")),1)]),_:1},8,["onClick"]))]))])),h(ka,{modelValue:s(B),"onUpdate:modelValue":L[1]||(L[1]=q=>Ce(B)?B.value=q:null),"airline-code":D.refundTicketData.airlineCode,onConfirm:s(U)},null,8,["modelValue","airline-code","onConfirm"]),h(Pe,{ref_key:"printRef",ref:$,class:"print-area hidden","refund-info":s(V),"original-tickets":D.refundTicketData.originalTickets??[],"is-electronic":!1,"window-height":s(u)},null,8,["refund-info","original-tickets","window-height"]),s(G)?(x(),O(qn,{key:1,modelValue:s(G),"onUpdate:modelValue":L[2]||(L[2]=q=>Ce(G)?G.value=q:null),"refund-info":s(V),"is-electronic":!0,"original-tickets":D.refundTicketData.originalTickets??[]},null,8,["modelValue","refund-info","original-tickets"])):W("",!0)]),_:1},8,["title","onClose"])}}});const ki=ve(ai,[["__scopeId","data-v-ef7d2d0d"]]);export{ge as P,ga as R,ki as T,pe as d,_e as f,fe as m};
