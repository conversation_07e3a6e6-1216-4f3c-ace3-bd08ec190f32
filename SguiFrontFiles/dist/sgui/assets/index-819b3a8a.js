import{dL as r}from"./index-a2fbd71b.js";const u=100,v=600,m={beforeMount(l,s){const e=s.value,{interval:d=u,delay:i=v}=r(e)?{}:e;let t,n;const o=()=>r(e)?e():e.handler(),a=()=>{n&&(clearTimeout(n),n=void 0),t&&(clearInterval(t),t=void 0)};l.addEventListener("mousedown",c=>{c.button===0&&(a(),o(),document.addEventListener("mouseup",()=>a(),{once:!0}),n=setTimeout(()=>{t=setInterval(()=>{o()},d)},i))})}};export{m as v};
