echo "删除mnjx_sgui_front镜像"
docker rmi -f $(docker images "*/*/mnjx_sgui_front" -aq)
echo "使用本地环境打包镜像"
# 使用系统时间作为版本号
currentDateTime=$(date "+%Y%m%d%H%M%S")
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_sgui_front:latest .
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_sgui_front:"${currentDateTime}" .
echo "登录harbor服务器"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "推送镜像到远程的harbor"
docker push harbor.kaiya.com:30443/sts/mnjx_sgui_front:latest
docker push harbor.kaiya.com:30443/sts/mnjx_sgui_front:"${currentDateTime}"
