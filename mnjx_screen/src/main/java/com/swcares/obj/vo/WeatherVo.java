package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> by yaodan
 * 2022-12-12 10:42:43
 */
@Api(tags = "天气返回对象")
@Data
public class WeatherVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "天气中文名")
    private String weatherCname;
    @ApiModelProperty(value = "天气英文名")
    private String weatherEname;
    @ApiModelProperty(value = "温度")
    private String temperature;
    @ApiModelProperty(value = "PM值")
    private String pmValue;
    @ApiModelProperty(value = "风力")
    private String windPower;
    @ApiModelProperty(value = "风向中文名")
    private String windDirectionCname;
    @ApiModelProperty(value = "风向英文名")
    private String windDirectionEname;
}
