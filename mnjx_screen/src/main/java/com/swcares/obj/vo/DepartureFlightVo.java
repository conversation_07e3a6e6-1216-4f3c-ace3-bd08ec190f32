package com.swcares.obj.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022/12/14
 */
@Data
public class DepartureFlightVo {

    @ApiModelProperty(value = "计划航班Id")
    @JsonIgnore
    private String planFlightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @JsonIgnore
    private String flightDate;

    @ApiModelProperty(value = "航空公司")
    private String airlineCode;

    @ApiModelProperty(value = "航班状态")
    @JsonIgnore
    private String flightStatus;

    @ApiModelProperty(value = "登机口")
    private String gate;

    @ApiModelProperty(value = "是否是尾航节：1是  0否")
    @JsonIgnore
    private String isLastSection;

    @ApiModelProperty(value = "出港航站")
    @JsonIgnore
    private String org;

    @ApiModelProperty(value = "出港航站")
    @JsonIgnore
    private String arrAptId;

    @ApiModelProperty(value = "预计离港时间（计划）")
    private String estimateOff;

    @ApiModelProperty(value = "预计离港时间跨天（计划）")
    @JsonIgnore
    private String estimateOffChange;

    @ApiModelProperty(value = "实际离港时间（预计）")
    private String actualOff;

    @ApiModelProperty(value = "实际登机时间")
    @JsonIgnore
    private String actualBoarding;

    @ApiModelProperty(value = "城市中文名")
    private List<String> cityCnames;

    @ApiModelProperty(value = "城市英文名")
    private List<String> cityEnames;

    @ApiModelProperty(value = "状态中文")
    private String flightStatusCname;

    @ApiModelProperty(value = "状态英文")
    private String flightStatusEname;
    @ApiModelProperty(value = "共享航班")
    private List<String> shareFlight;
}
