package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.obj.vo.GateScreenVo;
import com.swcares.service.IGateScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Api(tags = "登机屏显管理接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping(value = "/flightScreen")
@RestController
public class GateScreenController {
    @Resource
    private IGateScreenService iGateScreenService;

    @ApiOperation("根据登机口获取航班列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "gate",
                    value = "登机口",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    @GetMapping("/retrieveFlightByGate/{gate}")
    public List<GateScreenVo> retrieveFlightByGate(@PathVariable String gate) throws UnifiedResultException {
        return iGateScreenService.retrieveFlightByGate(gate);
    }
}
