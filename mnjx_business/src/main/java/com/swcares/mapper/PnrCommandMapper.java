package com.swcares.mapper;

import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPlanSection;
import com.swcares.entity.MnjxPlaneModel;
import com.swcares.obj.dto.HbnbNoDto;
import com.swcares.obj.dto.OpenCabinFlightDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
public interface PnrCommandMapper {

    /**
     * @param flightNo
     * @param flightDate
     * @return
     */
    List<MnjxOpenCabin> retrieveOpenCabinList(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    /**
     * @param flightNo
     * @param flightDate
     * @return
     */
    List<MnjxPlanSection> retrievePlanSection(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);


    List<MnjxOpenCabin> retrieveOpenCabin(@Param("flightNo") String fltNo, @Param("flightDate") String flightDate,
                                          @Param("cabinClass") String cabinClass, @Param("sellCabin") String sellCabin);

    MnjxPlaneModel retrievePlaneModel(@Param("flightNo") String fltNo, @Param("flightDate") String flightDate);

    List<HbnbNoDto> retrieveHbnb(@Param("queryParams") List<String> queryParams);

    List<OpenCabinFlightDto> retrieveOpenCabinListByFlightNoDateList(@Param("queryParams") List<String> queryParams);
}
