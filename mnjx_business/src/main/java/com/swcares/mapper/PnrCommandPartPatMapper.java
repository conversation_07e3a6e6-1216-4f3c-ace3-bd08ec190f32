package com.swcares.mapper;

import com.swcares.entity.MnjxCnd;
import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPlaneModel;
import com.swcares.entity.MnjxTcardSection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
public interface PnrCommandPartPatMapper {

    /**
     * retrieveOpenCabin
     *
     * @param fltNo      fltNo
     * @param flightDate flightDate
     * @param cabinClass cabinClass
     * @param sellCabin  sellCabin
     * @param date       date
     * @param org        org
     * @param dst        dst
     * @return retrieveOpenCabin
     */
    List<MnjxOpenCabin> retrieveOpenCabin(@Param("flightNo") String fltNo, @Param("flightDate") String flightDate, @Param("cabinClass") String cabinClass,
                                          @Param("sellCabin") String sellCabin, @Param("date") String date, @Param("org") String org, @Param("dst") String dst);


    /**
     * retrievePlaneModel
     *
     * @param fltNo      fltNo
     * @param flightDate flightDate
     * @return retrievePlaneModel
     */
    MnjxPlaneModel retrievePlaneModel(@Param("flightNo") String fltNo, @Param("flightDate") String flightDate);

    /**
     * 取cnd
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return 取cnd
     */
    List<MnjxCnd> retrieveCnd(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    /**
     * retrieveTcardSection
     *
     * @param flightNo   flightNo
     * @param orgDstList orgDstList
     * @return retrieveTcardSection
     */
    List<MnjxTcardSection> retrieveTcardSection(@Param("flightNo") String flightNo, @Param("orgDstList") List<String> orgDstList);
}
