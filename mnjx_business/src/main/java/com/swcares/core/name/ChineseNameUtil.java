package com.swcares.core.name;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.core.util.Constant;
import com.swcares.core.util.IdcardUtils;
import com.swcares.core.util.StrUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 获取中文名
 *
 * <AUTHOR>
 */
@Slf4j
public class ChineseNameUtil {
    private final static String[] SURNAMES = {"赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋",
            "沈", "韩", "杨", "朱", "秦", "尤", "许", "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜",
            "戚", "谢", "邹", "喻", "柏", "水", "窦", "章", "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦", "昌",
            "马", "苗", "凤", "花", "方", "俞", "任", "袁", "柳", "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺",
            "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬", "安", "常", "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍",
            "余", "元", "卜", "顾", "孟", "平", "黄", "和", "穆", "萧", "尹", "姚", "邵", "湛", "汪", "祁", "毛", "禹", "狄",
            "米", "贝", "明", "臧", "计", "伏", "成", "戴", "谈", "宋", "茅", "庞", "熊", "纪", "舒", "屈", "项", "祝", "董",
            "梁", "杜", "阮", "蓝", "闵", "席", "季", "禽", "禚", "蔡",
            "欧阳", "太史", "上官", "端木", "司马", "东方", "独孤", "南宫", "万俟", "闻人", "夏侯", "诸葛", "尉迟", "公羊", "赫连",
            "澹台", "皇甫", "宗政", "濮阳", "公冶", "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于",
            "司空", "闾丘", "子车", "亓官", "司寇", "巫马", "公西", "颛孙", "壤驷", "公良", "漆雕", "乐正", "宰父", "谷梁", "拓跋",
            "夹谷", "轩辕", "令狐", "段干", "百里", "呼延", "东郭", "南门", "羊舌", "微生", "公户", "公玉", "公仪", "梁丘", "公仲",
            "公上", "公门", "公山", "公坚", "左丘", "公伯", "西门", "公祖", "第五", "公乘", "贯丘", "公皙", "南荣", "东里", "东宫",
            "仲长", "子书", "子桑", "即墨", "达奚", "褚师", "吴铭"};

    /**
     * 获取指定数量的中文姓名对象
     *
     * @param number 指定数量
     * @return 中文姓名对象集合
     */
    public static List<ChineseNameDto> getChineseNames(int number) {
        // 生成一个集合
        List<ChineseNameDto> chineseNameDtos = new ArrayList<>(number);
        // 往这个集合添加姓名对象
        Stream.iterate(1, i -> i++).limit(number).forEach(i -> chineseNameDtos.add(ChineseNameUtil.getChineseName()));
        // 排查姓名对象是空的
        return chineseNameDtos.stream().filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    /**
     * 随机获取一个中文名对象
     *
     * @return 中文名
     */
    public static ChineseNameDto getChineseName() {
        // 获得姓名对象
        ChineseNameDto chineseNameDto = ChineseNameDto.builder().build();
        // 获得姓
        String surname = getRandomSurname();
        // 姓
        chineseNameDto.setSurname(surname);
        // 姓的拼音
        chineseNameDto.setSurnamePinyin(PinyinUtil.getPinyin(surname));
        // 证件类型(编码)
        chineseNameDto.setCertificateCode(CertificateType.ID_CARD.getCode());
        // 证件类型(中文)
        chineseNameDto.setCertificateType(CertificateType.ID_CARD.getName());
        // 证件号码
        chineseNameDto.setCertificateNo(IdcardUtils.randomCertificateNo18());
        // 确定性别
        int sexCode = IdcardUtils.getGenderByIdCard(chineseNameDto.getCertificateNo());
        // 当为1时，是男性。当为0时是为女性
        chineseNameDto.setSexCode(sexCode);
        String subname;
        switch (sexCode) {
            // 女性
            case 0:
                chineseNameDto.setSex("女");
                subname = getRandomSubname(BigDecimal.ZERO.intValue());
                break;
            // 男性
            case 1:
                chineseNameDto.setSex("男");
                subname = getRandomSubname(BigDecimal.ONE.intValue());
                break;
            default:
                chineseNameDto.setFullName("未知");
                subname = getRandomSubname(BigDecimal.TEN.intValue());
        }
        chineseNameDto.setSexPinyin(PinyinUtil.getPinyin(chineseNameDto.getSex()));
        // 名字
        chineseNameDto.setSubname(subname);
        // 名字拼音
        chineseNameDto.setSubnamePinyin(PinyinUtil.getPinyin(subname));
        // 全名
        String fullName = StrUtil.format("{}{}", chineseNameDto.getSurname(), chineseNameDto.getSubname());
        chineseNameDto.setFullName(fullName);
        // 全名拼音
        chineseNameDto.setFullNamePinyin(PinyinUtil.getPinyin(fullName));
        // 省份
        String province = IdcardUtils.getProvinceByIdCard(chineseNameDto.getCertificateNo());
        chineseNameDto.setProvince(province);
        // 生日
        String birth = IdcardUtils.getBirth(chineseNameDto.getCertificateNo());
        chineseNameDto.setBirth(birth);
        // 年龄
        int age = IdcardUtils.getAgeByIdCard(chineseNameDto.getCertificateNo());
        chineseNameDto.setAge(age);

        return chineseNameDto;
    }

    private static String getRandomSubname(int sexCode) {
        String girlSubnames = "秀娟英华慧巧美娜静淑惠珠翠雅芝玉萍红娥玲芬芳燕彩春菊兰凤洁梅琳素云莲真环雪荣爱妹霞香月莺媛艳瑞凡佳嘉琼勤珍贞莉桂娣叶璧璐娅琦晶妍茜秋珊莎锦黛青倩婷姣婉娴瑾颖露瑶怡婵雁蓓纨仪荷丹蓉眉君琴蕊薇菁梦岚苑婕馨瑗琰韵融园艺咏卿聪澜纯毓悦昭冰爽琬茗羽希宁欣飘育滢馥筠柔竹霭凝晓欢霄枫芸菲寒伊亚宜可姬舒影荔枝思丽";
        String boySubnames = "伟刚勇毅俊峰强军平保东文辉力明永健世广志义兴良海山仁波宁贵福生龙元全国胜学祥才发武新利清飞彬富顺信子杰涛昌成康星光天达安岩中茂进林有坚和彪博诚先敬震振壮会思群豪心邦承乐绍功松善厚庆磊民友裕河哲江超浩亮政谦亨奇固之轮翰朗伯宏言若鸣朋斌梁栋维启克伦翔旭鹏泽晨辰士以建家致树炎德行时泰盛雄琛钧冠策腾楠榕风航弘";
        String subName;
        switch (sexCode) {
            // 女性
            case 0:
                subName = getSubnameBySexCode(girlSubnames);
                break;
            // 男性
            case 1:
                subName = getSubnameBySexCode(boySubnames);
                break;
            // 不知道的情况下
            default:
                subName = "无名";
        }
        return subName;
    }

    private static String getSubnameBySexCode(String girlSubnames) {
        String subName;
        // 获取备选名的长度
        int subnameLength = StrUtil.length(girlSubnames);
        // 在备选名长度中随机索引
        int subnameIndex = RandomUtil.randomInt(subnameLength);
        // 当所有到最后一个位置，那就取最后一个名字
        if (subnameIndex == subnameLength - 1) {
            subName = girlSubnames.substring(subnameIndex);
        } else {
            // 如果不是最后一个，那就确定取个字的名字还是3个字的名字
            int name1Or2 = RandomUtil.randomInt(2);
            if (name1Or2 > 0) {
                // 取2个字的名
                subName = girlSubnames.substring(subnameIndex, subnameIndex + 2);
            } else {
                // 取1个字的名
                subName = girlSubnames.substring(subnameIndex, subnameIndex + 1);
            }
        }
        return subName;
    }

    /**
     * 获得一个姓
     *
     * @return 姓
     */
    private static String getRandomSurname() {
        // 获得一个随机的姓氏
        return SURNAMES[RandomUtil.randomInt(SURNAMES.length)];
    }

    /**
     * 获取姓，如果没找到就默认取第一个字为姓，还可以通过手工添加来实现找到 <br/>
     * eg: 姚文兵 - > 姚<br/>
     * 东方文兵-> 东方
     *
     * @param fullName 全中文名
     * @return 姓
     */
    public static String getSurname(String fullName) {
        String finalFullName = fullName;
        String surname = Arrays.stream(SURNAMES).filter(sn -> sn.equalsIgnoreCase(finalFullName)).findFirst().orElse(StrUtil.EMPTY);
        if (StrUtils.isNotBlank(surname)) {
            return surname;
        } else {
            int tail = fullName.length() - Constant.ONE;
            if (tail > BigDecimal.ZERO.intValue()) {
                fullName = fullName.substring(BigDecimal.ZERO.intValue(), tail);
                return getSurname(fullName);
            } else {
                return fullName;
            }
        }
    }

    /**
     * 返回中文的名
     * eg: 姚文兵 - > 文兵<br/>
     * 东方子沁-> 子沁
     *
     * @param fullName 全名
     * @return 返回中文的名
     */
    public static String getSubname(String fullName) {
        String surname = getSurname(fullName);
        int surnameIndex = fullName.indexOf(surname);
        return StrUtils.subSuf(fullName, surnameIndex + surname.length());
    }
}
