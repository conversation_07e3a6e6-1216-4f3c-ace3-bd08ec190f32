package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxFlight;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24 11:04
 */
public interface IEffectFlightService {

    void createEffect(MnjxFlight mnjxFlight) throws UnifiedResultException;

    void doDeleteExistPlanFlight(List<String> seatsIds, List<String> openCabinsIds, List<String> planSectionsIds, List<String> planFlightsIds);

    List<String> getExistPassengerDateList(String flightNo);
}
