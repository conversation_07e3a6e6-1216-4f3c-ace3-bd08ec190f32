package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.mapper.MnjxPnrSegMapper;
import com.swcares.obj.dto.*;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@CacheConfig(cacheNames = "PnrOperationServiceImpl")
@Slf4j
@Service
public class PnrOperationServiceImpl implements IPnrOperationService {

    private static final Pattern FMT_RESULT = Pattern.compile(".*\\r?\\n?");

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxSpInfoService iMnjxSpInfoService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private MnjxPnrSegMapper mnjxPnrSegMapper;

    @Override
    public MnjxPnr perfectMdpMnjxPnrAndPnrCtDtos(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 补充MemoryDataPnr中的MnjxPnr对象
        this.perfectMdpMnjxPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        // 新建PNR时会自动新建的一个PNR的CT项
        this.perfectRandomMdpPnrCtDtos(memoryDataPnr, mnjxOffice);
        // 更新MemoryDataPnr中的MnjxPnr对象中的索引值
        memoryDataPnr.getMnjxPnr().setMaxIndex(memoryDataPnr.getMnjxPnr().getMaxIndex() + 1);
        // 设置是否为新的PNR对象的标识
        memoryDataPnr.setNewPnr(true);
        // 设置当前PNR是否是有控制
        memoryDataPnr.setBeControlled(true);
        return memoryDataPnr.getMnjxPnr();
    }

    private void perfectRandomMdpPnrCtDtos(MemoryDataPnr mdp, MnjxOffice mo) {
        MnjxPnrCt mnjxPnrCt = new MnjxPnrCt();
        // 设置当前联系组关联MnjxPnr对象
        mnjxPnrCt.setPnrId(mdp.getMnjxPnr().getPnrId());
        // 设置当前联系组的在PNR中的索引值
        mnjxPnrCt.setPnrIndex(mdp.getMnjxPnr().getMaxIndex() + 1);
        // 设置当前联系方式的城市码
        String cityCode = mo.getOfficeNo().substring(0, 3);
        mnjxPnrCt.setCityCode(cityCode);
        // 拼接一个联系组
        String ctInputValue;
        if (Constant.STR_ZERO.equals(mo.getOfficeType())) {
            MnjxAgent agent = iMnjxAgentService.getById(mo.getOrgId());
            String agentCname = agent.getAgentContactCname();
            if (agentCname.matches(Constant.CHINESE_REG)) {
                agentCname = PinyinUtils.getPinYin(agentCname, false);
            }
            ctInputValue = StrUtil.format("{}/T-{} {}", cityCode, agent.getAgentContactPhone(), agentCname.toUpperCase());
        } else {
            ctInputValue = StrUtil.format("{}/T-13980234715 LIU DAI", cityCode);
        }
        mnjxPnrCt.setInputValue(ctInputValue);
        String ctText = ctInputValue.substring(6);
        mnjxPnrCt.setCtText(ctText);
        PnrCtDto pnrCtDto = new PnrCtDto(mnjxPnrCt);
        // 当前MnjxPnrCt的索引设置到Dto中
        pnrCtDto.setPnrIndex(mnjxPnrCt.getPnrIndex());
        // 添加到大对象中
        mdp.getPnrCtDtos().add(pnrCtDto);
    }

    private void perfectMdpMnjxPnr(MemoryDataPnr mdp, MnjxOffice mnjxOffice, MnjxSi mnjxSi) {
        MnjxPnr mnjxPnr = mdp.getMnjxPnr();
        // ID 必须在创建的时候生成，否则PNR没有用于判断是否已创建的条件
        mnjxPnr.setPnrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxPnr.setCreateSiId(mnjxSi.getSiId());
        mnjxPnr.setCreateTime(new Date());
        // 责任组
        mnjxPnr.setCreateOfficeNo(mnjxOffice.getOfficeNo());
        // 创建时没有封口，状态为OP
        mnjxPnr.setPnrStatus(Constant.PNR_OP);
        // 序号从0开始
        mnjxPnr.setMaxIndex(0);
    }

    @Override
    public String recall(MemoryDataPnr memoryDataPnr) {
        memoryDataPnr.setEtdz(false);
        // 获取PNR各项map
        Map<Integer, Object> map = this.setPnrRecallValue(memoryDataPnr);
        // map的键排序（PNR中各项的index）
        List<Integer> collect = map.keySet().stream().sorted().collect(Collectors.toList());
        Optional<Integer> max = collect.stream().max(Integer::compare);
        StringBuilder sb = new StringBuilder();
        boolean hasGn = map.containsKey(0);
        // RT:N方式提取PNR，具体判断在做RT再处理，目前赋值false
        boolean rtN = StrUtil.isNotEmpty(memoryDataPnr.getRtType()) && memoryDataPnr.getRtType().contains("N");
        // 如果有姓名组，则进入其他组的渲染时需要进行一次换行
        boolean haveNmToWrap = false;
        // 按index顺序开始渲染回显
        for (Integer i : collect) {
            Object o = map.get(i);
            if (o instanceof MnjxPnrNm) {
                haveNmToWrap = true;
                // 如果团队存在，没有使用RT:N提取时，隐藏姓名组
                if (hasGn && !rtN) {
                    continue;
                }
                MnjxPnrNm mnjxPnrNm = (MnjxPnrNm) o;
                String value = this.setInputValue(mnjxPnrNm.getPnrIndex(), mnjxPnrNm.getInputValue());
                sb.append(value);
                sb.append(" ");
            } else {
                if (haveNmToWrap && i != 0 && (!hasGn || rtN)) {
                    sb.append("\r\n");
                    haveNmToWrap = false;
                }
                String value = StrUtil.toString(o);
                sb.append(value);
                if (i < max.get()) {
                    sb.append("\r\n");
                }
            }
        }
        // 非RT提取时调用这个接口表示PNR有修改
        if (!memoryDataPnr.isByRt()) {
            memoryDataPnr.setPnrChanged(true);
        } else {
            memoryDataPnr.setByRt(false);
        }
        return sb.toString();
    }


    /**
     * Title: setPnr
     * Description: 设置PNR各项到map中，键为PNR各项的index，值为PNR各项自身对象
     *
     * @param memoryDataPnr memoryDataPnr
     * @return 设置PNR各项到map中，键为PNR各项的index，值为PNR各项自身对象
     * <AUTHOR>
     * @date 2022/5/25 16:19
     */
    private Map<Integer, Object> setPnrRecallValue(MemoryDataPnr memoryDataPnr) {
        // PNR各项列表，用于XE和RR等选取序号提取项进行操作
        memoryDataPnr.getAbstractPnrDtos().clear();

        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        Map<Integer, Object> map = new HashMap<>(mnjxPnr.getMaxIndex());

        // NM中的其他附属项
        List<PnrXnDto> pnrXnDtos = new ArrayList<>();
        List<PnrNmEiDto> pnrNmEiDtos = new ArrayList<>();
        List<PnrOiDto> pnrOiDtos = new ArrayList<>();
        List<PnrNmFcDto> pnrNmFcDtos = new ArrayList<>();
        List<PnrNmFnDto> pnrNmFnDtos = new ArrayList<>();
        List<PnrNmFpDto> pnrNmFpDtos = new ArrayList<>();
        List<PnrNmOsiDto> pnrNmOsiDtos = new ArrayList<>();
        List<PnrNmCtDto> pnrNmCtDtos = new ArrayList<>();
        List<PnrNmRmkDto> pnrNmRmkDtos = new ArrayList<>();
        List<PnrSsrDto> pnrSsrDtos = new ArrayList<>();

        // 调用回显的时候就开始处理PNR封口编号：新创建的PNR封口编号为001，已封口过的PNR编号+1
        String atNo = "001";
        if (StrUtil.isEmpty(memoryDataPnr.getThisAtNo())) {
            if (!memoryDataPnr.isNewPnr()) {
                // 查询所有的封口数据
                List<MnjxPnrAt> mnjxPnrAts = iMnjxPnrAtService.lambdaQuery().eq(MnjxPnrAt::getPnrId, mnjxPnr.getPnrId()).list();
                // 找到最大的封口次数
                OptionalInt maxAtNo = mnjxPnrAts.stream().mapToInt(mnjxPnrAt -> Integer.parseInt(mnjxPnrAt.getAtNo())).max();
                if (maxAtNo.isPresent()) {
                    atNo = StrUtil.fill(String.valueOf(maxAtNo.getAsInt() + 1), Constant.C_ZERO, Integer.parseInt(Constant.STR_THREE), true);
                }
            }
            memoryDataPnr.setThisAtNo(atNo);
        }

        int max = 1;
        int atPnrIndex = 1;
        String pnrId = mnjxPnr.getPnrId();
        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        List<String> existPnrItem = memoryDataPnr.getExistHistoryPnrItem();
        //========================开始处理回显顺序排序========================//
        // 团队组
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        for (PnrGnDto pnrGnDto : pnrGnDtos) {
            MnjxPnrGn mnjxPnrGn = pnrGnDto.getMnjxPnrGn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, 0, Constant.PNR_GN);
            mnjxPnrRecord.setInputValue(mnjxPnrGn.getInputValue());
            String item = StrUtil.format("GN:{}", mnjxPnrGn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrGnDto)) {
                memoryDataPnr.getAbstractPnrDtos().add(pnrGnDto);
                pnrGnDto.setPnrIndex(0);
                mnjxPnrGn.setPnrIndex(0);
                String inputValue = this.setInputValue(0, mnjxPnrGn.getInputValue());
                map.put(mnjxPnrGn.getPnrIndex(), inputValue);
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, 0, isX, false);
        }

        // 姓名组
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM);
            mnjxPnrRecord.setInputValue(mnjxPnrNm.getInputValue());
            String item = StrUtil.format("NM:{}", mnjxPnrNm.getInputValue());
            if (Constant.TO_UPDATE.equals(pnrNmDto.getUpdateMark())) {
                item = StrUtil.format("NNM:{}", mnjxPnrNm.getInputValue());
            }
            boolean isX = false;
            boolean isNmChange = false;

            if (!this.beXe(pnrNmDto) && !Constant.NM_CHANGE_TYPE.equals(mnjxPnrNm.getChangeType())) {
                pnrNmDto.setPnrIndex(max);
                mnjxPnrNm.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmDto);
                // NM项回显要显示在一起，需要单独处理
                map.put(mnjxPnrNm.getPnrIndex(), mnjxPnrNm);
                // 添加附属项
                CollUtil.addAll(pnrXnDtos, pnrNmDto.getPnrXnDtos());
                CollUtil.addAll(pnrNmFcDtos, pnrNmDto.getPnrNmFcDtos());
                CollUtil.addAll(pnrNmFnDtos, pnrNmDto.getPnrNmFnDtos());
                CollUtil.addAll(pnrNmFpDtos, pnrNmDto.getPnrNmFpDtos());
                CollUtil.addAll(pnrNmEiDtos, pnrNmDto.getPnrNmEiDtos());
                CollUtil.addAll(pnrOiDtos, pnrNmDto.getPnrOiDtos());
                CollUtil.addAll(pnrNmRmkDtos, pnrNmDto.getPnrNmRmkDtos());
                CollUtil.addAll(pnrSsrDtos, pnrNmDto.getPnrSsrDtos());
                CollUtil.addAll(pnrNmOsiDtos, pnrNmDto.getPnrNmOsiDtos());
                CollUtil.addAll(pnrNmCtDtos, pnrNmDto.getPnrNmCtDtos());
            }
            // 如果NM被XE了，则该NM下所有的都被连带XE掉
            else if (this.beXe(pnrNmDto)) {
                isX = true;
                // 添加附属项
                CollUtil.addAll(pnrXnDtos, pnrNmDto.getPnrXnDtos());
                CollUtil.addAll(pnrNmFcDtos, pnrNmDto.getPnrNmFcDtos());
                CollUtil.addAll(pnrNmFnDtos, pnrNmDto.getPnrNmFnDtos());
                CollUtil.addAll(pnrNmFpDtos, pnrNmDto.getPnrNmFpDtos());
                CollUtil.addAll(pnrNmEiDtos, pnrNmDto.getPnrNmEiDtos());
                CollUtil.addAll(pnrOiDtos, pnrNmDto.getPnrOiDtos());
                CollUtil.addAll(pnrNmRmkDtos, pnrNmDto.getPnrNmRmkDtos());
                CollUtil.addAll(pnrSsrDtos, pnrNmDto.getPnrSsrDtos());
                CollUtil.addAll(pnrNmOsiDtos, pnrNmDto.getPnrNmOsiDtos());
                CollUtil.addAll(pnrNmCtDtos, pnrNmDto.getPnrNmCtDtos());
            }
            // 被修改的不添加附属项
            else if (Constant.NM_CHANGE_TYPE.equals(mnjxPnrNm.getChangeType())) {
                isNmChange = true;
            }
            max++;

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, isNmChange);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // 航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecordSeg(pnrId, atNo, atPnrIndex, Constant.PNR_SEG, mnjxPnrSeg);
            mnjxPnrRecord.setInputValue(mnjxPnrSeg.getInputValue());
            String item = StrUtil.format("SEG:{}", mnjxPnrSeg.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrSegDto)) {
                pnrSegDto.setPnrIndex(max);
                mnjxPnrSeg.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrSegDto);
                map.put(mnjxPnrSeg.getPnrIndex(), this.setInputValue(mnjxPnrSeg.getPnrIndex(), mnjxPnrSeg.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR CT联系组
        List<PnrCtDto> pnrCtDtos = memoryDataPnr.getPnrCtDtos();
        for (PnrCtDto pnrCtDto : pnrCtDtos) {
            MnjxPnrCt mnjxPnrCt = pnrCtDto.getMnjxPnrCt();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_CT);
            mnjxPnrRecord.setInputValue(mnjxPnrCt.getInputValue());
            String item = StrUtil.format("CT:{}", mnjxPnrCt.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrCtDto)) {
                pnrCtDto.setPnrIndex(max);
                mnjxPnrCt.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrCtDto);
                map.put(mnjxPnrCt.getPnrIndex(), this.setInputValue(mnjxPnrCt.getPnrIndex(), mnjxPnrCt.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM CT联系组
        for (PnrNmCtDto pnrNmCtDto : pnrNmCtDtos) {
            MnjxNmCt mnjxNmCt = pnrNmCtDto.getMnjxNmCt();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_CT);
            mnjxPnrRecord.setInputValue(mnjxNmCt.getInputValue());
            String item = StrUtil.format("NMCT:{}", mnjxNmCt.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmCtDto)) {
                pnrNmCtDto.setPnrIndex(max);
                mnjxNmCt.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmCtDto);
                map.put(mnjxNmCt.getPnrIndex(), this.setInputValue(mnjxNmCt.getPnrIndex(), mnjxNmCt.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                if (!isX) {
                    atPnrIndex++;
                }
            }
        }

        // TK组
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        for (PnrTkDto pnrTkDto : pnrTkDtos) {
            MnjxPnrTk mnjxPnrTk = pnrTkDto.getMnjxPnrTk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_TK);
            mnjxPnrRecord.setInputValue(mnjxPnrTk.getInputValue());
            String item = StrUtil.format("TK:{}", mnjxPnrTk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrTkDto)) {
                pnrTkDto.setPnrIndex(max);
                mnjxPnrTk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrTkDto);
                map.put(mnjxPnrTk.getPnrIndex(), this.setInputValue(pnrTkDto.getPnrIndex(), mnjxPnrTk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR FC
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
        for (PnrFcDto pnrFcDto : pnrFcDtos) {
            MnjxPnrFc mnjxPnrFc = pnrFcDto.getMnjxPnrFc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FC);
            mnjxPnrRecord.setInputValue(mnjxPnrFc.getInputValue());
            String item = StrUtil.format("FC:{}", mnjxPnrFc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFcDto)) {
                pnrFcDto.setPnrIndex(max);
                mnjxPnrFc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFcDto);
                map.put(mnjxPnrFc.getPnrIndex(), this.setInputValue(mnjxPnrFc.getPnrIndex(), mnjxPnrFc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM FC
        for (PnrNmFcDto pnrNmFcDto : pnrNmFcDtos) {
            MnjxNmFc mnjxNmFc = pnrNmFcDto.getMnjxNmFc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FC);
            mnjxPnrRecord.setInputValue(mnjxNmFc.getInputValue());
            String item = StrUtil.format("NMFC:{}", mnjxNmFc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFcDto)) {
                pnrNmFcDto.setPnrIndex(max);
                mnjxNmFc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFcDto);
                map.put(mnjxNmFc.getPnrIndex(), this.setInputValue(pnrNmFcDto.getPnrIndex(), mnjxNmFc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM SSR组
        for (PnrSsrDto pnrSsrDto : pnrSsrDtos) {
            MnjxNmSsr mnjxNmSsr = pnrSsrDto.getMnjxNmSsr();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_SSR);
            mnjxPnrRecord.setInputValue(mnjxNmSsr.getInputValue());
            String item = StrUtil.format("SSR:{}", mnjxNmSsr.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrSsrDto)) {
                pnrSsrDto.setPnrIndex(max);
                mnjxNmSsr.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrSsrDto);
                map.put(mnjxNmSsr.getPnrIndex(), this.setInputValue(mnjxNmSsr.getPnrIndex(), mnjxNmSsr.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR OSI
        List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos();
        for (PnrOsiDto pnrOsiDto : pnrOsiDtos) {
            MnjxPnrOsi mnjxPnrOsi = pnrOsiDto.getMnjxPnrOsi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_OSI);
            mnjxPnrRecord.setInputValue(mnjxPnrOsi.getInputValue());
            String item = StrUtil.format("OSI:{}", mnjxPnrOsi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrOsiDto)) {
                pnrOsiDto.setPnrIndex(max);
                mnjxPnrOsi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrOsiDto);
                map.put(mnjxPnrOsi.getPnrIndex(), this.setInputValue(mnjxPnrOsi.getPnrIndex(), mnjxPnrOsi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM OSI
        for (PnrNmOsiDto pnrNmOsiDto : pnrNmOsiDtos) {
            MnjxNmOsi mnjxNmOsi = pnrNmOsiDto.getMnjxNmOsi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_OSI);
            mnjxPnrRecord.setInputValue(mnjxNmOsi.getInputValue());
            String item = StrUtil.format("NMOSI:{}", mnjxNmOsi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmOsiDto)) {
                pnrNmOsiDto.setPnrIndex(max);
                mnjxNmOsi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmOsiDto);
                map.put(mnjxNmOsi.getPnrIndex(), this.setInputValue(mnjxNmOsi.getPnrIndex(), mnjxNmOsi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR RMK
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        for (PnrRmkDto pnrRmkDto : pnrRmkDtos) {
            MnjxPnrRmk mnjxPnrRmk = pnrRmkDto.getMnjxPnrRmk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_RMK);
            mnjxPnrRecord.setInputValue(mnjxPnrRmk.getInputValue());
            String item = StrUtil.format("RMK:{}", mnjxPnrRmk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrRmkDto)) {
                pnrRmkDto.setPnrIndex(max);
                mnjxPnrRmk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrRmkDto);
                map.put(mnjxPnrRmk.getPnrIndex(), this.setInputValue(mnjxPnrRmk.getPnrIndex(), mnjxPnrRmk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM RMK
        for (PnrNmRmkDto pnrNmRmkDto : pnrNmRmkDtos) {
            MnjxNmRmk mnjxNmRmk = pnrNmRmkDto.getMnjxNmRmk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_RMK);
            mnjxPnrRecord.setInputValue(mnjxNmRmk.getInputValue());
            String item = StrUtil.format("NMRMK:{}", mnjxNmRmk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmRmkDto)) {
                pnrNmRmkDto.setPnrIndex(max);
                mnjxNmRmk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmRmkDto);
                map.put(mnjxNmRmk.getPnrIndex(), this.setInputValue(mnjxNmRmk.getPnrIndex(), mnjxNmRmk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR TC
        List<PnrTcDto> pnrTcDtos = memoryDataPnr.getPnrTcDtos();
        for (PnrTcDto pnrTcDto : pnrTcDtos) {
            MnjxPnrTc mnjxPnrTc = pnrTcDto.getMnjxPnrTc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_TC);
            mnjxPnrRecord.setInputValue(mnjxPnrTc.getInputValue());
            String item = StrUtil.format("TC:{}", mnjxPnrTc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrTcDto)) {
                pnrTcDto.setPnrIndex(max);
                mnjxPnrTc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrTcDto);
                map.put(mnjxPnrTc.getPnrIndex(), this.setInputValue(pnrTcDto.getPnrIndex(), mnjxPnrTc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR FN
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
        for (PnrFnDto pnrFnDto : pnrFnDtos) {
            MnjxPnrFn mnjxPnrFn = pnrFnDto.getMnjxPnrFn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FN);
            mnjxPnrRecord.setInputValue(mnjxPnrFn.getInputValue());
            String item = StrUtil.format("FN:{}", mnjxPnrFn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFnDto)) {
                pnrFnDto.setPnrIndex(max);
                mnjxPnrFn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFnDto);
                map.put(mnjxPnrFn.getPnrIndex(), this.setInputValue(mnjxPnrFn.getPnrIndex(), mnjxPnrFn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM FN
        for (PnrNmFnDto pnrNmFnDto : pnrNmFnDtos) {
            MnjxNmFn mnjxNmFn = pnrNmFnDto.getMnjxNmFn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FN);
            mnjxPnrRecord.setInputValue(mnjxNmFn.getInputValue());
            String item = StrUtil.format("NMFN:{}", mnjxNmFn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFnDto)) {
                pnrNmFnDto.setPnrIndex(max);
                mnjxNmFn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFnDto);
                map.put(mnjxNmFn.getPnrIndex(), this.setInputValue(pnrNmFnDto.getPnrIndex(), mnjxNmFn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR EI
        List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos();
        for (PnrEiDto pnrEiDto : pnrEiDtos) {
            MnjxPnrEi mnjxPnrEi = pnrEiDto.getMnjxPnrEi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_EI);
            mnjxPnrRecord.setInputValue(mnjxPnrEi.getInputValue());
            String item = StrUtil.format("EI:{}", mnjxPnrEi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrEiDto)) {
                pnrEiDto.setPnrIndex(max);
                mnjxPnrEi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrEiDto);
                map.put(mnjxPnrEi.getPnrIndex(), this.setInputValue(mnjxPnrEi.getPnrIndex(), mnjxPnrEi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }


        // NM EI
        for (PnrNmEiDto pnrNmEiDto : pnrNmEiDtos) {
            MnjxNmEi mnjxNmEi = pnrNmEiDto.getMnjxNmEi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_EI);
            mnjxPnrRecord.setInputValue(mnjxNmEi.getInputValue());
            String item = StrUtil.format("NMEI:{}", mnjxNmEi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmEiDto)) {
                pnrNmEiDto.setPnrIndex(max);
                mnjxNmEi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmEiDto);
                map.put(mnjxNmEi.getPnrIndex(), this.setInputValue(mnjxNmEi.getPnrIndex(), mnjxNmEi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM OI
        for (PnrOiDto pnrOiDto : pnrOiDtos) {
            MnjxNmOi mnjxNmOi = pnrOiDto.getMnjxNmOi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_OI);
            mnjxPnrRecord.setInputValue(mnjxNmOi.getOiInfo());
            String item = StrUtil.format("OI:{}", mnjxNmOi.getOiInfo());
            boolean isX = false;

            if (!this.beXe(pnrOiDto)) {
                pnrOiDto.setPnrIndex(max);
                mnjxNmOi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrOiDto);
                map.put(mnjxNmOi.getPnrIndex(), this.setInputValue(mnjxNmOi.getPnrIndex(), mnjxNmOi.getOiInfo()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR TN
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        for (PnrNmTnDto pnrNmTnDto : pnrNmTnDtos) {
            MnjxPnrNmTn mnjxPnrNmTn = pnrNmTnDto.getMnjxPnrNmTn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecordTn(pnrId, atNo, atPnrIndex, Constant.PNR_TN, mnjxPnrNmTn);
            mnjxPnrRecord.setInputValue(mnjxPnrNmTn.getInputValue());
            String item = StrUtil.format("TN:{}", mnjxPnrNmTn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmTnDto)) {
                pnrNmTnDto.setPnrIndex(max);
                mnjxPnrNmTn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmTnDto);
                map.put(mnjxPnrNmTn.getPnrIndex(), this.setInputValue(mnjxPnrNmTn.getPnrIndex(), mnjxPnrNmTn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM XN
        for (PnrXnDto pnrXnDto : pnrXnDtos) {
            MnjxNmXn mnjxNmXn = pnrXnDto.getMnjxNmXn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_XN);
            mnjxPnrRecord.setInputValue(mnjxNmXn.getInputValue());
            String item = StrUtil.format("XN:{}", mnjxNmXn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrXnDto)) {
                pnrXnDto.setPnrIndex(max);
                mnjxNmXn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrXnDto);
                map.put(mnjxNmXn.getPnrIndex(), this.setInputValue(mnjxNmXn.getPnrIndex(), mnjxNmXn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // PNR FP
        List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos();
        for (PnrFpDto pnrFpDto : pnrFpDtos) {
            MnjxPnrFp mnjxPnrFp = pnrFpDto.getMnjxPnrFp();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FP);
            mnjxPnrRecord.setInputValue(mnjxPnrFp.getInputValue());
            String item = StrUtil.format("FP:{}", mnjxPnrFp.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFpDto)) {
                pnrFpDto.setPnrIndex(max);
                mnjxPnrFp.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFpDto);
                map.put(mnjxPnrFp.getPnrIndex(), this.setInputValue(mnjxPnrFp.getPnrIndex(), mnjxPnrFp.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        // NM FP
        for (PnrNmFpDto pnrNmFpDto : pnrNmFpDtos) {
            MnjxNmFp mnjxNmFp = pnrNmFpDto.getMnjxNmFp();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FP);
            mnjxPnrRecord.setInputValue(mnjxNmFp.getInputValue());
            String item = StrUtil.format("NMFP:{}", mnjxNmFp.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFpDto)) {
                pnrNmFpDto.setPnrIndex(max);
                mnjxNmFp.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFpDto);
                map.put(mnjxNmFp.getPnrIndex(), this.setInputValue(pnrNmFpDto.getPnrIndex(), mnjxNmFp.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            if (!isX) {
                atPnrIndex++;
            }
        }

        mnjxPnrRecords.sort(Comparator.comparing(MnjxPnrRecord::getPnrIndex));
        mnjxPnr.setMaxIndex(max - 1);
        mnjxPnr.setCreateOfficeNoEid(mnjxPnr.getMaxIndex() + 1);
        map.put(mnjxPnr.getCreateOfficeNoEid(), this.setInputValue(mnjxPnr.getCreateOfficeNoEid(), mnjxPnr.getCreateOfficeNo()));
        return map;
    }

    /**
     * Title: addOrChangeRecordList
     * Description: 添加历史记录，如果已存在则更新序号
     *
     * @param existPnrItem   existPnrItem
     * @param item           item
     * @param mnjxPnrRecords mnjxPnrRecords
     * @param mnjxPnrRecord  mnjxPnrRecord
     * @param atPnrIndex     atPnrIndex
     * <AUTHOR>
     * @date 2022/6/24 14:55
     */
    private void addOrChangeRecordList(List<String> existPnrItem, String item, List<MnjxPnrRecord> mnjxPnrRecords, MnjxPnrRecord mnjxPnrRecord, int atPnrIndex, boolean isX, boolean isNmChange) {
        // 没有添加该项得历史时直接添加
        if (!existPnrItem.contains(item)) {
            Iterator<String> iterator = existPnrItem.iterator();
            // 如果有GN，因为GN的座位数可能变化了，字符串不能对应上，重新设置GN
            if (item.startsWith("GN:")) {
                boolean gnChange = false;
                String oldGnItem = "";
                while (iterator.hasNext()) {
                    String nextItem = iterator.next();
                    if (nextItem.startsWith("GN:") && !item.equals(nextItem)) {
                        gnChange = true;
                        oldGnItem = nextItem;
                        iterator.remove();
                        break;
                    }
                }
                if (gnChange) {
                    String privateOldGnItem = oldGnItem.split(":")[1];
                    MnjxPnrRecord record = mnjxPnrRecords.stream().filter(m -> privateOldGnItem.equals(m.getInputValue())).collect(Collectors.toList()).get(0);
                    record.setInputValue(item.split(":")[1]);
                    existPnrItem.add(item);
                } else {
                    mnjxPnrRecords.add(mnjxPnrRecord);
                    existPnrItem.add(item);
                }
            }
            // 如果有SEG，因为SEG的座位数可能变化了，或者SEG的行动代码变化了，字符串不能对应上，重新设置SEG
            else if (item.startsWith("SEG:")) {
                boolean segChange = false;
                String oldSegItem = "";
                while (iterator.hasNext()) {
                    String nextItem = iterator.next();
                    if (nextItem.startsWith("SEG:") && !item.equals(nextItem) && item.substring(0, 28).equals(nextItem.substring(0, 28))) {
                        segChange = true;
                        oldSegItem = nextItem;
                        iterator.remove();
                        break;
                    }
                }
                if (segChange) {
                    String privateOldSegItem = oldSegItem.split(":")[1];
                    List<MnjxPnrRecord> filterList = mnjxPnrRecords.stream()
                            .filter(m -> privateOldSegItem.equals(m.getInputValue()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(filterList)) {
                        MnjxPnrRecord record = filterList.get(0);
                        record.setInputValue(item.split(":")[1]);
                        existPnrItem.add(item);
                        if (isX) {
                            record.setChangeMark(Constant.DELETE_TYPE);
                        }
                    }
                } else {
                    mnjxPnrRecords.add(mnjxPnrRecord);
                    existPnrItem.add(item);
                }
            } else {
                mnjxPnrRecords.add(mnjxPnrRecord);
                existPnrItem.add(item);
            }
        }
        // 已存在该项历史，更新其序号，根据是否被XE或修改姓名更新标记
        else {
            List<MnjxPnrRecord> collect = mnjxPnrRecords.stream().filter(r -> mnjxPnrRecord.getInputValue().equals(r.getInputValue())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                MnjxPnrRecord record = collect.get(0);
                if ((item.startsWith("NM") || item.startsWith("NNM")) && collect.size() > 1) {
                    collect = collect.stream().filter(c -> atPnrIndex == c.getPnrIndex()).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        record = collect.get(0);
                    }
                }
                record.setPnrIndex(atPnrIndex);
                if (isX) {
                    record.setChangeMark(Constant.DELETE_TYPE);
                } else if (isNmChange) {
                    record.setChangeMark(Constant.NM_CHANGE_TYPE);
                } else {
                    record.setChangeMark(null);
                }
            }
        }
    }

    /**
     * Title: constructRecord
     * Description: 构建历史记录对象
     *
     * @param pnrId      pnrId
     * @param atNo       atNo
     * @param atPnrIndex atPnrIndex
     * @param pnrType    pnrType
     * @return 构建历史记录对象
     * <AUTHOR>
     * @date 2022/6/24 14:55
     */
    private MnjxPnrRecord constructRecord(String pnrId, String atNo, int atPnrIndex, String pnrType) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(pnrType);
        return mnjxPnrRecord;
    }

    /**
     * 构建历史记录对象
     *
     * @param pnrId       pnrId
     * @param atNo        atNo
     * @param atPnrIndex  atPnrIndex
     * @param pnrType     pnrType
     * @param mnjxPnrNmTn mnjxPnrNmTn
     * @return
     */
    private MnjxPnrRecord constructRecordTn(String pnrId, String atNo, int atPnrIndex, String pnrType, MnjxPnrNmTn mnjxPnrNmTn) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(pnrType);
        mnjxPnrRecord.setIssuedTime(mnjxPnrNmTn.getIssuedTime());
        if (StrUtil.isNotEmpty(mnjxPnrNmTn.getPnrNmId())) {
            mnjxPnrRecord.setPnrNmId(mnjxPnrNmTn.getPnrNmId());
        } else {
            mnjxPnrRecord.setPnrNmId(mnjxPnrNmTn.getNmXnId());
        }
        mnjxPnrRecord.setPrinterId(mnjxPnrNmTn.getPrinterId());
        return mnjxPnrRecord;
    }

    /**
     * 构建历史记录对象航段
     *
     * @param pnrId
     * @param atNo
     * @param atPnrIndex
     * @param pnrType
     * @param mnjxPnrSeg
     * @return
     */
    private MnjxPnrRecord constructRecordSeg(String pnrId, String atNo, int atPnrIndex, String pnrType, MnjxPnrSeg mnjxPnrSeg) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(pnrType);
        mnjxPnrRecord.setPnrSegId(mnjxPnrSeg.getPnrSegId());
        return mnjxPnrRecord;
    }

    /**
     * Title: setInputValue
     * Description: 组装PNR序号和对应项的值
     *
     * @param pnrIndex   pnrIndex
     * @param inputValue inputValue
     * @return 组装PNR序号和对应项的值
     * <AUTHOR>
     * @date 2022/5/25 16:53
     */
    private String setInputValue(Integer pnrIndex, String inputValue) {
        return StrUtil.format("{}.{}", pnrIndex, inputValue);
    }

    /**
     * Title: beXe
     * Description: PNR项是否被XE
     *
     * @param item item
     * @return PNR项是否被XE
     * <AUTHOR>
     * @date 2022/5/26 15:16
     */
    private boolean beXe(AbstractPnrDto item) {
        return item.isXe();
    }


    @Override
    public MnjxPnr createNewPnr(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        return this.perfectMdpMnjxPnrAndPnrCtDtos(memoryDataPnr, mnjxOffice, mnjxSi);
    }


    @Cacheable(cacheNames = "mnjxAirports")
    @Override
    public List<MnjxAirport> getMnjxAirports() {
        return iMnjxAirportService.lambdaQuery().list();
    }


    @Cacheable(cacheNames = "mnjxCities")
    @Override
    public List<MnjxCity> getMnjxCities() {
        return iMnjxCityService.lambdaQuery().list();
    }


    @Cacheable(cacheNames = "mnjxStandardPats")
    @Override
    public List<MnjxStandardPat> getMnjxStandardPats() {
        return iMnjxStandardPatService.lambdaQuery().list();
    }

    @Override
    public String recallByRtC(MemoryDataPnr memoryDataPnr, Integer pageType) {
        StringBuffer resultStr = new StringBuffer();
        this.setPnrRecallValue(memoryDataPnr);
        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        List<MnjxPnrAt> mnjxPnrAts = memoryDataPnr.getMnjxPnrAts();
        String pnrCode = memoryDataPnr.getMnjxPnr().getPnrCrs();
        StringBuilder nmStr = new StringBuilder();
        StringBuffer gnStr = new StringBuffer();
        List<MnjxPnrRecord> deleteList = new ArrayList<>();
        MnjxPnr dbPnr = iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrId, memoryDataPnr.getMnjxPnr().getPnrId()).one();
        Boolean delPnr = false;
        Boolean delName = false;
        if (ObjectUtil.isNotNull(dbPnr) && "DEL".equals(dbPnr.getPnrStatus())) {
            delPnr = true;
            delName = true;
        }
        if (CollUtil.isNotEmpty(mnjxPnrRecords)) {
            for (MnjxPnrRecord mnjxPnrRecord : mnjxPnrRecords) {
                if (StrUtil.isNotBlank(mnjxPnrRecord.getChangeAtNo()) && !Constant.S.equals(mnjxPnrRecord.getChangeMark())) {
                    deleteList.add(mnjxPnrRecord);
                    continue;
                }
                switch (mnjxPnrRecord.getPnrType()) {
                    case Constant.PNR_NM:
                        if ("S".equals(mnjxPnrRecord.getChangeMark())) {
                            break;
                        }
                        nmStr.append("    ").append(StrUtil.isNotBlank(mnjxPnrRecord.getChangeMark()) ? mnjxPnrRecord.getChangeMark() : " ").append(delName ? "X" : "").append(mnjxPnrRecord.getPnrIndex()).append(".").append(mnjxPnrRecord.getInputValue()).append("(").append(mnjxPnrRecord.getAtNo()).append(")");
                        if (delName) {
                            delName = false;
                        }
                        break;
                    case Constant.PNR_SEG:
                        String inputValue = mnjxPnrRecord.getInputValue();
                        String replaceStr = inputValue.replaceFirst("DK\\d", "HK1");
                        resultStr.append(mnjxPnrRecord.getAtNo()).append(" ").append(StrUtil.isNotBlank(mnjxPnrRecord.getChangeMark()) ? mnjxPnrRecord.getChangeMark() : " ").append(delPnr ? "X" : "").append(mnjxPnrRecord.getPnrIndex()).append(".").append(replaceStr);
                        resultStr.append("\r\n");
                        resultStr.append("     " + "NN(").append(mnjxPnrRecord.getAtNo()).append(")  ").append("DK(").append(mnjxPnrRecord.getAtNo()).append(")  ").append("HK(").append(mnjxPnrRecord.getAtNo()).append(")");
                        resultStr.append("\r\n");
                        break;
                    case Constant.PNR_GN:
                        gnStr.append(mnjxPnrRecord.getAtNo()).append(" ").append(StrUtil.isNotBlank(mnjxPnrRecord.getChangeMark()) ? mnjxPnrRecord.getChangeMark() : " ").append(delPnr ? "X" : "").append(mnjxPnrRecord.getPnrIndex()).append(".").append(mnjxPnrRecord.getInputValue());
                        gnStr.append("\r\n");
                        break;
                    default:
                        resultStr.append(mnjxPnrRecord.getAtNo()).append(" ").append(StrUtil.isNotBlank(mnjxPnrRecord.getChangeMark()) ? mnjxPnrRecord.getChangeMark() : " ").append(delPnr ? "X" : "").append(mnjxPnrRecord.getPnrIndex()).append(".").append(mnjxPnrRecord.getInputValue());
                        resultStr.append("\r\n");
                        break;
                }
            }
        }
        nmStr.append(" ").append(StrUtil.isNotBlank(pnrCode) ? pnrCode : "");
        nmStr.append("\r\n");
        resultStr.insert(0, nmStr);
        if (StrUtil.isNotBlank(gnStr)) {
            resultStr.insert(0, gnStr);
        }
        Map<String, MnjxSi> siMap = iMnjxSiService.lambdaQuery().list().stream().collect(Collectors.toMap(MnjxSi::getSiId, it -> it));
        Map<String, MnjxOffice> officeMap = iMnjxOfficeService.lambdaQuery().list().stream().collect(Collectors.toMap(MnjxOffice::getOfficeId, it -> it));
        if (CollUtil.isNotEmpty(mnjxPnrAts)) {
            MnjxPnrAt maxAt = mnjxPnrAts.get(mnjxPnrAts.size() - 1);
            if (StrUtil.isBlank(maxAt.getAtType()) || "I".equalsIgnoreCase(maxAt.getAtType()) || "IK".equalsIgnoreCase(maxAt.getAtType())) {
                MnjxSi mnjxSi = siMap.get(maxAt.getAtSiId());
                MnjxOffice mnjxOffice = officeMap.get(mnjxSi.getOfficeId());
                DateTime dateTime = DateUtil.offset(maxAt.getAtDateTime(), DateField.HOUR_OF_DAY, -8);
                String ymdDate = DateUtils.ymd2Com(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
                resultStr.insert(0, maxAt.getAtNo() + "    " + mnjxOffice.getOfficeNo() + " " + mnjxSi.getSiNo() + " " + DateUtil.format(dateTime, "HHmm") + " " + ymdDate.substring(0, 5) + "\r\n");
            } else {
                DateTime dateTime = DateUtil.offset(maxAt.getAtDateTime(), DateField.HOUR_OF_DAY, -8);
                String ymdDate = DateUtils.ymd2Com(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
                int parseAtNo = Integer.parseInt(maxAt.getAtNo());
                resultStr.insert(0, maxAt.getAtNo() + "    HDQCA" + " " + DateUtil.format(dateTime, "HHmm") + " " + ymdDate.substring(0, 5) + " RLC" + (parseAtNo - 1) + "\r\n");
            }
        }
        if (delPnr) {
            resultStr.insert(0, "   *THIS PNR WAS ENTIRELY CANCELLED*\r\n");
        }
        if (pageType != null) {
            List<String> normalLines = ReUtils.findAll(FMT_RESULT, resultStr, 0);
            normalLines = CollUtil.removeEmpty(normalLines);
            if (CollUtil.isNotEmpty(normalLines)) {
                int remainder = normalLines.size() % pageType;
                if (remainder != 0) {
                    for (int i = 0; i <= (pageType - remainder); i++) {
                        resultStr.append("\r\n");
                    }
                }
            }
        }
        Map<String, List<MnjxPnrRecord>> groupMap = new HashMap<>();
        if (CollUtil.isNotEmpty(deleteList)) {
            groupMap = deleteList.stream().collect(Collectors.groupingBy(MnjxPnrRecord::getAtNo, Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(mnjxPnrAts)) {
            List<MnjxSpInfo> spInfoList = iMnjxSpInfoService.lambdaQuery()
                    .eq(MnjxSpInfo::getPnrId, memoryDataPnr.getMnjxPnr().getPnrId())
                    .list();
            for (int i = 0; i < mnjxPnrAts.size(); i++) {
                MnjxPnrAt mnjxPnrAt = mnjxPnrAts.get(i);
                if (StrUtil.isBlank(mnjxPnrAt.getAtType()) || "I".equalsIgnoreCase(mnjxPnrAt.getAtType()) || "IK".equalsIgnoreCase(mnjxPnrAt.getAtType())) {
                    MnjxSi mnjxSi = siMap.get(mnjxPnrAt.getAtSiId());
                    MnjxOffice mnjxOffice = officeMap.get(mnjxSi.getOfficeId());
                    DateTime dateTime = DateUtil.offset(mnjxPnrAt.getAtDateTime(), DateField.HOUR_OF_DAY, -8);
                    String ymdDate = DateUtils.ymd2Com(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
                    resultStr.append(mnjxPnrAt.getAtNo()).append("     ").append(mnjxOffice.getOfficeNo()).append(" ").append(mnjxSi.getSiNo()).append(" ").append(DateUtil.format(dateTime, "HHmm")).append(" ").append(ymdDate, 0, 5);
                    resultStr.append("\r\n");
                } else {
                    DateTime dateTime = DateUtil.offset(mnjxPnrAt.getAtDateTime(), DateField.HOUR_OF_DAY, -8);
                    String ymdDate = DateUtils.ymd2Com(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
                    int parseAtNo = Integer.parseInt(mnjxPnrAt.getAtNo());
                    resultStr.append(mnjxPnrAt.getAtNo()).append("     HDQCA").append(" ").append(DateUtil.format(dateTime, "HHmm")).append(" ").append(ymdDate, 0, 5).append(" RLC").append(parseAtNo - 1);
                    resultStr.append("\r\n");
                }
                // 查询是否进行过SP
                List<MnjxSpInfo> thisAtSpInfoList = spInfoList.stream()
                        .filter(s -> s.getAtNo().equals(mnjxPnrAt.getAtNo()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(thisAtSpInfoList)) {
                    List<MnjxSpInfo> spFromList = thisAtSpInfoList.stream()
                            .filter(s -> StrUtil.isNotEmpty(s.getSpFromCrsPnr()))
                            .collect(Collectors.toList());
                    List<MnjxSpInfo> spToList = thisAtSpInfoList.stream()
                            .filter(s -> StrUtil.isNotEmpty(s.getSpToCrsPnr()))
                            .collect(Collectors.toList());
                    // 这次封口记录是从其他PNR分离而来的，只会存在一条记录
                    if (CollUtil.isNotEmpty(spFromList)) {
                        MnjxSpInfo spInfo = spFromList.get(0);
                        String spHistory = StrUtil.format("{}/{} RMK SP {}", spInfo.getAtNo(), spInfo.getAtNo(), spInfo.getSpFromCrsPnr());
                        resultStr.append(spHistory);
                        resultStr.append("\r\n");
                    }
                    // 作为原PNR分离出去
                    else {
                        MnjxSpInfo spInfo = spToList.get(0);
                        String spAtNo = spInfo.getAtNo();
                        List<MnjxPnrRecord> sNmRecordList = memoryDataPnr.getMnjxPnrRecords().stream()
                                .filter(p -> Constant.NM.equals(p.getPnrType()) && Constant.S.equals(p.getChangeMark()) && p.getChangeAtNo().equals(spAtNo))
                                .collect(Collectors.toList());
                        // 处理分离出去的姓名
                        // 如果是英文姓名且姓氏一样，需要处理成 2ZHANG/YI/ER的格式
                        List<String> nmList = sNmRecordList.stream()
                                .map(MnjxPnrRecord::getInputValue)
                                .sorted()
                                .collect(Collectors.toList());
                        List<String> englishNameList = nmList.stream()
                                .filter(n -> ReUtils.isMatch("[A-Z]+/[A-Z]+", n))
                                .collect(Collectors.toList());
                        Map<String, List<String>> map = englishNameList.stream()
                                .collect(Collectors.groupingBy(n -> n.split("/")[0]));
                        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                            StringBuilder sb = new StringBuilder();
                            sb.append(StrUtil.format("{}/{} S ", sNmRecordList.get(0).getAtNo(), sNmRecordList.get(0).getChangeAtNo()));
                            List<String> value = entry.getValue();
                            if (value.size() == 1) {
                                sb.append(StrUtil.format("1{}", value.get(0)));
                            } else {
                                sb.append(value.size());
                                sb.append(entry.getKey());
                                value.forEach(v -> sb.append("/").append(v.split("/")[1]));
                            }
                            resultStr.append(sb);
                            resultStr.append("\r\n");
                        }
                        List<String> chineseNameList = nmList.stream()
                                .filter(n -> !ReUtils.isMatch("[A-Z]+/[A-Z]+", n))
                                .collect(Collectors.toList());
                        for (String chineseName : chineseNameList) {
                            String sNameHistory = StrUtil.format("{}/{} S 1{}", sNmRecordList.get(0).getAtNo(), sNmRecordList.get(0).getChangeAtNo(), chineseName);
                            resultStr.append(sNameHistory);
                            resultStr.append("\r\n");
                        }
                        String spHistory = StrUtil.format("{}/{} RMK SP {} {}", sNmRecordList.get(0).getAtNo(), sNmRecordList.get(0).getChangeAtNo(), sNmRecordList.size(), spInfo.getSpToCrsPnr());
                        resultStr.append(spHistory);
                        resultStr.append("\r\n");
                    }
                }
                List<MnjxPnrRecord> deleteRecord = groupMap.get(mnjxPnrAt.getAtNo());
                if (CollUtil.isNotEmpty(deleteRecord)) {
                    for (MnjxPnrRecord deletePo : deleteRecord) {
                        resultStr.append(deletePo.getAtNo()).append("/").append(deletePo.getChangeAtNo()).append(" ").append(deletePo.getInputValue());
                        resultStr.append("\r\n");
                    }
                }
            }
        }
        // 非RT提取时调用这个接口表示PNR有修改
        if (!memoryDataPnr.isByRt()) {
            memoryDataPnr.setPnrChanged(true);
        } else {
            memoryDataPnr.setByRt(false);
        }
        return resultStr.toString();
    }

    @Override
    public String getGroupName(MemoryDataPnr memoryDataPnr) {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<MnjxPnrSeg> mnjxPnrSegs = this.retrieveMnjxSegByFltNoAndDate(pnrSegDtos);
        //这个航班上所有的订座记录pnrId（如果是多航段就是所有航班上的pnrId）
        List<String> pnrIdList = mnjxPnrSegs.stream().map(MnjxPnrSeg::getPnrId).collect(Collectors.toList());
        int psgNum = pnrNmDtos.size();
        List<MnjxPnr> pnrList = iMnjxPnrService.listByIds(pnrIdList);
        List<String> pnrDefaultGnNameList = pnrList.stream().map(MnjxPnr::getDefaultGroupName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());
        //去掉数字，用于后面的判断
        pnrDefaultGnNameList = pnrDefaultGnNameList.stream()
                .map(gnName -> ReUtils.replaceAll(gnName, "(?<=\\w+)\\d+", StrUtil.EMPTY))
                .sorted(Comparator.comparing(String::hashCode))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrDefaultGnNameList)) {
            return "A" + psgNum;
        }

        String maxGnName = pnrDefaultGnNameList.get(pnrDefaultGnNameList.size() - 1);
        int newGroupNameLength = maxGnName.length();
        String[] split = maxGnName.split("");
        int maxGnHashCode = 0;
        for (String s : split) {
            maxGnHashCode += s.hashCode();
        }
        // 当字母是Z ZZ ZZZ...的时候，下一个需要加一位，从AAA...开始
        if (maxGnHashCode == maxGnName.length() * 90) {
            newGroupNameLength += 1;
        }
        StringBuilder sb = new StringBuilder();
        StringBuilder tailSb = new StringBuilder();
        // 当新团队名判断是1位字母的情况
        if (newGroupNameLength == 1) {
            for (char letter : Constant.LETTERS) {
                if (StrUtil.toString(letter).hashCode() > maxGnHashCode) {
                    return StrUtil.format("{}{}", letter, psgNum);
                }
            }
        } else {
            // 团名长度相同，从最后一位开始判断字母顺序（AA->AB，AZ->BA，AAA->AAB，AAZ->ABA，AZZ->BAA）
            if (newGroupNameLength == maxGnName.length()) {
                int tailCount = 0;
                f1:
                for (int i = split.length - 1; i >= 0; i--) {
                    // Z需要替换成A，下一个字母再走else计算
                    if (Constant.Z.equals(split[i])) {
                        tailSb.append(Constant.A);
                        tailCount++;
                    } else {
                        for (char letter : Constant.LETTERS) {
                            if (StrUtil.toString(letter).hashCode() > split[i].hashCode()) {
                                // 这里尾部的添加顺序实际是反着的，下面会反向添加回正确的字符串
                                tailSb.append(letter);
                                tailCount++;
                                break f1;
                            }
                        }
                    }
                }
                // 没有修改的部分先添加进去
                sb.append(maxGnName, 0, maxGnName.length() - tailCount);
                String[] tailSplit = tailSb.toString().split("");
                // 修改的部分添加需要从后往前添加，才是正确的顺序
                for (int i = tailSplit.length - 1; i >= 0; i--) {
                    sb.append(tailSplit[i]);
                }
            }
            // 新团队名长度加1，Z->AA，ZZ->AAA，ZZZ->AAAA
            else {
                for (int i = 0; i < newGroupNameLength; i++) {
                    sb.append("A");
                }
            }
        }
        return StrUtil.format("{}{}", sb.toString().trim(), psgNum);
    }

    /**
     * 根据 航班号+日期组合字段查询mnjxSeg表数据
     * 考虑到多航段的情况 查询所有航班
     */
    @Override
    public List<MnjxPnrSeg> retrieveMnjxSegByFltNoAndDate(List<PnrSegDto> pnrSegDtos) {
        List<String> list = new ArrayList<>();
        // 按航班日期分组
        Map<String, List<PnrSegDto>> listMap = pnrSegDtos.stream()
                .filter(p -> !p.isXe() && !Constant.SA.equals(p.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.groupingBy(s -> s.getMnjxPnrSeg().getFlightDate()));
        for (Map.Entry<String, List<PnrSegDto>> entry : listMap.entrySet()) {
            List<PnrSegDto> value = entry.getValue();
            // 获取航段组现有的航班号
            List<String> flightNos = value.stream()
                    .map(p -> p.getMnjxPnrSeg().getFlightNo())
                    .collect(Collectors.toList());
            // 查询航班数据
            List<MnjxFlight> flights = iMnjxFlightService.lambdaQuery()
                    .in(MnjxFlight::getFlightNo, flightNos)
                    .list();
            // 筛选有承运航班字段值的航班
            List<MnjxFlight> carrierFlights = flights.stream()
                    .filter(f -> StrUtil.isNotEmpty(f.getCarrierFlight()))
                    .collect(Collectors.toList());
            if (CollUtils.isNotEmpty(carrierFlights)) {
                // 获取承运航班号
                List<String> carrierFlightNos = carrierFlights.stream()
                        .map(MnjxFlight::getCarrierFlight)
                        .collect(Collectors.toList());
                // 通过承运航班号查出所有的共享航班
                List<MnjxFlight> shareFlights = iMnjxFlightService.lambdaQuery()
                        .in(MnjxFlight::getCarrierFlight, carrierFlightNos)
                        .eq(MnjxFlight::getShareState, Constant.STR_ONE)
                        .list();
                // 加入共享航班号
                flightNos.addAll(shareFlights.stream().map(MnjxFlight::getFlightNo).collect(Collectors.toList()));
                flightNos.addAll(carrierFlightNos);
                flightNos = CollUtils.distinct(flightNos);
            }
            // 筛选没有共享航班的航班，即承运航班本身，或无共享的航班
            List<MnjxFlight> noShareFlights = flights.stream()
                    .filter(f -> StrUtil.isEmpty(f.getCarrierFlight()))
                    .collect(Collectors.toList());
            if (CollUtils.isNotEmpty(noShareFlights)) {
                // 获取承运航班号
                List<String> noShareFlightNos = noShareFlights.stream()
                        .map(MnjxFlight::getFlightNo)
                        .collect(Collectors.toList());
                // 通过承运航班号查出所有的共享航班
                List<MnjxFlight> shareFlights = iMnjxFlightService.lambdaQuery()
                        .in(MnjxFlight::getCarrierFlight, noShareFlightNos)
                        .eq(MnjxFlight::getShareState, Constant.STR_ONE)
                        .list();
                // 加入共享航班号
                if (CollUtils.isNotEmpty(shareFlights)) {
                    flightNos.addAll(shareFlights.stream().map(MnjxFlight::getFlightNo).collect(Collectors.toList()));
                    flightNos = CollUtils.distinct(flightNos);
                }
            }
            for (String flightNo : flightNos) {
                list.add(StrUtil.format("{}{}", flightNo, entry.getKey()));
            }
        }
        QueryWrapper<MnjxPnrSeg> wrapper = new QueryWrapper<>();
        wrapper.in("concat(flight_no, flight_date)", list);
        return mnjxPnrSegMapper.selectList(wrapper);
    }
}
