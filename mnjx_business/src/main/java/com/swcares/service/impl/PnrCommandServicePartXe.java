package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.*;
import com.swcares.mapper.PnrCommandMapper;
import com.swcares.obj.dto.*;
import com.swcares.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PnrCommandServicePartXe {
    final PnrCommandMapper pnrCommandMapper;
    final IMnjxPnrService iMnjxPnrService;
    final IMnjxPnrAtService iMnjxPnrAtService;
    final IMnjxPnrRecordService iMnjxPnrRecordService;
    final IMnjxOpenCabinService iMnjxOpenCabinService;
    final IMnjxPnrSegService iMnjxPnrSegService;
    final IMnjxAirlineService iMnjxAirlineService;
    final IMnjxAgentAirlineService iMnjxAgentAirlineService;
    final IMnjxPrinterService iMnjxPrinterService;
    final IMnjxPsgCkiService iMnjxPsgCkiService;
    final IMnjxPsgSeatService iMnjxPsgSeatService;
    final IMnjxPnrNmTicketService iMnjxPnrNmTicketService;
    final IMnjxPnrNmService iMnjxPnrNmService;
    final IMnjxNmXnService iMnjxNmXnService;
    final IMnjxPnrNmTnService iMnjxPnrNmTnService;
    final IMnjxOfficeService iMnjxOfficeService;
    final IMnjxSiService iMnjxSiService;
    final IMnjxAirportService iMnjxAirportService;
    final IMnjxNmCtService iMnjxNmCtService;
    final IMnjxPnrCtService iMnjxPnrCtService;
    final IMnjxPnrFpService iMnjxPnrFpService;
    final IMnjxNmRmkService iMnjxNmRmkService;
    final IMnjxPnrTcService iMnjxPnrTcService;
    final IMnjxPnrEiService iMnjxPnrEiService;
    final IMnjxNmFcService iMnjxNmFcService;
    final IMnjxNmFpService iMnjxNmFpService;
    final IMnjxNmOiService iMnjxNmOiService;
    final IMnjxPnrRmkService iMnjxPnrRmkService;
    final IMnjxNmFnService iMnjxNmFnService;
    final IMnjxPnrOsiService iMnjxPnrOsiService;
    final IMnjxPnrNmUmService iMnjxPnrNmUmService;
    final IMnjxNmOsiService iMnjxNmOsiService;
    final IMnjxNmEiService iMnjxNmEiService;
    final IMnjxPnrFnService iMnjxPnrFnService;
    final IMnjxNmSsrService iMnjxNmSsrService;
    final IMnjxPnrTkService iMnjxPnrTkService;
    final IMnjxPnrGnService iMnjxPnrGnService;
    final IMnjxPnrFcService iMnjxPnrFcService;

    public String recall(MemoryDataPnr memoryDataPnr) {
        memoryDataPnr.setEtdz(false);
        // 获取PNR各项map
        Map<Integer, Object> map = this.setPnrRecallValue(memoryDataPnr);
        // map的键排序（PNR中各项的index）
        List<Integer> collect = map.keySet().stream().sorted().collect(Collectors.toList());
        Optional<Integer> max = collect.stream().max(Integer::compare);
        StringBuilder sb = new StringBuilder();
        boolean hasGn = map.containsKey(0);
        // RT:N方式提取PNR，具体判断在做RT再处理，目前赋值false
        boolean rtN = StrUtil.isNotEmpty(memoryDataPnr.getRtType()) && memoryDataPnr.getRtType().contains("N");
        // 如果有姓名组，则进入其他组的渲染时需要进行一次换行
        boolean haveNmToWrap = false;
        // 按index顺序开始渲染回显
        for (Integer i : collect) {
            Object o = map.get(i);
            if (o instanceof MnjxPnrNm) {
                haveNmToWrap = true;
                // 如果团队存在，没有使用RT:N提取时，隐藏姓名组
                if (hasGn && !rtN) {
                    continue;
                }
                MnjxPnrNm mnjxPnrNm = (MnjxPnrNm) o;
                String value = this.setInputValue(mnjxPnrNm.getPnrIndex(), mnjxPnrNm.getInputValue());
                sb.append(value);
                sb.append(" ");
            } else {
                if (haveNmToWrap && i != 0 && (!hasGn || rtN)) {
                    sb.append("\r\n");
                    haveNmToWrap = false;
                }
                String value = StrUtil.toString(o);
                sb.append(value);
                if (i < max.get()) {
                    sb.append("\r\n");
                }
            }
        }
        // 非RT提取时调用这个接口表示PNR有修改
        if (!memoryDataPnr.isByRt()) {
            memoryDataPnr.setPnrChanged(true);
        } else {
            memoryDataPnr.setByRt(false);
        }
        return sb.toString();
    }

    public void xe(MemoryDataPnr memoryDataPnr, XeDto xeDto) throws UnifiedResultException {
        // 获取已有NM列表，排序
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        pnrNmDtos = pnrNmDtos.stream()
                .filter(p -> !p.isXe())
                .sorted(Comparator.comparing(PnrNmDto::getPnrIndex))
                .collect(Collectors.toList());
        int lastNmDtoIndex = 0;
        if (CollUtil.isNotEmpty(pnrNmDtos)) {
            PnrNmDto lastNmDto = pnrNmDtos.get(pnrNmDtos.size() - 1);
            // 获取最后一个NM的序号
            lastNmDtoIndex = lastNmDto.getPnrIndex();
        }

        List<AbstractPnrDto> abstractPnrDtos = memoryDataPnr.getAbstractPnrDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        int xeOtherCount = 0;
        // 正常项的XE（如果有）
        List<Integer> xePnrIndexList = xeDto.getXeIndexs();
        boolean isGn = CollUtil.isNotEmpty(memoryDataPnr.getPnrGnDtos());
        for (AbstractPnrDto abstractPnrDto : abstractPnrDtos) {
            for (int i : xePnrIndexList) {
                if (i == abstractPnrDto.getPnrIndex()) {
                    // 如果XE的是姓名组，则需要处理关联该姓名组的其他项一起XE
                    if (abstractPnrDto instanceof PnrNmDto) {
                        // 如果当前操作的是SP的PNR，不允许XE姓名组
                        this.validateSp(memoryDataPnr);
                        abstractPnrDto.setXe(true);
                        PnrNmDto toXeNmDto = (PnrNmDto) abstractPnrDto;
                        this.handleXeNm(toXeNmDto, memoryDataPnr, pnrNmDtos);
                    }
                    // 不允许XE团队组
                    else if (abstractPnrDto instanceof PnrGnDto) {
                        throw new UnifiedResultException(Constant.ELE_NBR);
                    }
                    // 非团队时不允许XE军警残项RMK
                    else if (!isGn && abstractPnrDto instanceof PnrRmkDto && Constant.RMK_TYPE_GMJC.equals(((PnrRmkDto) abstractPnrDto).getMnjxPnrRmk().getRmkName())) {
                        throw new UnifiedResultException("NOT ALLOWED TO XE RMK GMJC");
                    }
                    else if (!isGn && abstractPnrDto instanceof PnrNmRmkDto && Constant.RMK_TYPE_GMJC.equals(((PnrNmRmkDto) abstractPnrDto).getMnjxNmRmk().getRmkName())) {
                        throw new UnifiedResultException("NOT ALLOWED TO XE RMK GMJC");
                    }
                    // 其他组
                    else {
                        abstractPnrDto.setXe(true);
                        // 有TN（表示已经出票了，自动将FC移入历史记录时不会连带将FN也移入历史记录）
                        boolean haveTn = CollUtil.isNotEmpty(memoryDataPnr.getPnrNmTnDtos());
                        // 如果XE的是SSR CHLD，需要将旅客类型更新成成人
                        if (abstractPnrDto instanceof PnrSsrDto) {
                            PnrSsrDto pnrSsrDto = (PnrSsrDto) abstractPnrDto;
                            String ssrType = pnrSsrDto.getMnjxNmSsr().getSsrType();
                            if (Constant.SSR_TYPE_CHLD.equals(ssrType)) {
                                List<AbstractPnrDto> abstractPnrDtoIn = memoryDataPnr.getAbstractPnrDtos();
                                for (AbstractPnrDto p : abstractPnrDtoIn) {
                                    //判断是否有TKNE项
                                    if (p instanceof PnrSsrDto) {
                                        PnrSsrDto pnrSsrDto1In = (PnrSsrDto) p;
                                        String ssrTypeIn = pnrSsrDto1In.getMnjxNmSsr().getSsrType();
                                        if (Constant.TKNE.equals(ssrTypeIn)) {
                                            throw new UnifiedResultException(Constant.PNR_DEL_SSR_TKNE_FIRST);
                                        }
                                    }
                                }
                                // 如果已经有了FC FN，不允许删除
                                List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
                                boolean havePnrFn = CollUtil.isNotEmpty(pnrFnDtos) && pnrFnDtos.stream().anyMatch(p -> !p.isXe());
                                List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
                                boolean havePnrFc = CollUtil.isNotEmpty(pnrFcDtos) && pnrFcDtos.stream().anyMatch(p -> !p.isXe());
                                String nmId = pnrSsrDto.getMnjxNmSsr().getPnrNmId();
                                PnrNmDto pnrNmDto = pnrNmDtos.stream().filter(n -> nmId.equals(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList()).get(0);
                                List<PnrNmFnDto> pnrNmFnDtos = pnrNmDto.getPnrNmFnDtos();
                                boolean havePnrNmFn = CollUtil.isNotEmpty(pnrNmFnDtos) && pnrNmFnDtos.stream().anyMatch(p -> !p.isXe());
                                List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos();
                                boolean havePnrNmFc = CollUtil.isNotEmpty(pnrNmFcDtos) && pnrNmFcDtos.stream().anyMatch(p -> !p.isXe());
                                if (havePnrFc || havePnrFn || havePnrNmFc || havePnrNmFn) {
                                    throw new UnifiedResultException(Constant.PLS_DEL_FN_FC);
                                }
                                pnrNmDto.getMnjxPnrNm().setPsgType(Constant.ADULT_TYPE);
                                // 未封口前直接删除
                                pnrSsrDto.setXe(ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr()) || ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr().getPnrCrs()));
                                pnrSsrDto.getMnjxNmSsr().setActionCode(Constant.ACTION_CODE_XX);
                                StringBuilder builder = new StringBuilder(pnrSsrDto.getMnjxNmSsr().getInputValue());
                                builder.replace(Constant.TWELVE, Constant.FOURTEEN, Constant.ACTION_CODE_XX);
                                pnrSsrDto.getMnjxNmSsr().setInputValue(builder.toString());
                                pnrSsrDto.getMnjxNmSsr().setSsrInfo(builder.toString());
                            } else if (StrUtil.equalsAny(ssrType, Constant.SSR_TYPE_FOID, Constant.SSR_TYPE_FQTV)) {
                                // 未封口前直接删除
                                pnrSsrDto.setXe(ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr()) || ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr().getPnrCrs()));
                                pnrSsrDto.getMnjxNmSsr().setActionCode(Constant.ACTION_CODE_XX);
                                StringBuilder builder = new StringBuilder(pnrSsrDto.getMnjxNmSsr().getInputValue());
                                builder.replace(Constant.TWELVE, Constant.FOURTEEN, Constant.ACTION_CODE_XX);
                                pnrSsrDto.getMnjxNmSsr().setInputValue(builder.toString());
                                pnrSsrDto.getMnjxNmSsr().setSsrInfo(builder.toString());
                                // 删除FQTV还需要从PnrNmDto里面的常客列表中移除当前删除的常客
                                if (Constant.SSR_TYPE_FQTV.equals(ssrType)) {
                                    memoryDataPnr.getPnrNmDtos().stream()
                                            .filter(p -> !p.isXe())
                                            .forEach(p ->
                                                    p.getMnjxFrequenterList().removeIf(f ->
                                                            pnrSsrDto.getMnjxNmSsr().getInputValue().contains(StrUtil.format("{}{}", f.getAirlineCode(), f.getFrequenterCard()))
                                                    )
                                            );
                                }
                            }
                        }
                        // 如果自动运价，XE了FN 或 FC，另外一个也自动删除
                        else if (abstractPnrDto instanceof PnrFnDto && (!haveTn || !ObjectUtils.isEmpty(xeDto.getXe()))) {
                            PnrFnDto fnDto = (PnrFnDto) abstractPnrDto;
                            if (fnDto.getMnjxPnrFn().getInputValue().startsWith("FN/A")) {
                                List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
                                pnrFcDtos.stream().filter(p -> p.getMnjxPnrFc().getInputValue().startsWith("FC/A")).forEach(p -> p.setXe(true));
                                xeOtherCount++;
                                //同时删除  RMK CMS项
                                List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
                                for (PnrRmkDto p : pnrRmkDtos) {
                                    if (p.getMnjxPnrRmk().getInputValue().startsWith("RMK CMS")) {
                                        p.setXe(true);
                                        xeOtherCount++;
                                    }
                                }
                            }
                        } else if (abstractPnrDto instanceof PnrFcDto && (!haveTn || !ObjectUtils.isEmpty(xeDto.getXe()))) {
                            PnrFcDto fcDto = (PnrFcDto) abstractPnrDto;
                            if (fcDto.getMnjxPnrFc().getInputValue().startsWith("FC/A")) {
                                List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
                                pnrFnDtos.stream().filter(p -> p.getMnjxPnrFn().getInputValue().startsWith("FN/A")).forEach(p -> p.setXe(true));
                                xeOtherCount++;
                                //同时删除  RMK CMS项
                                List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
                                for (PnrRmkDto p : pnrRmkDtos) {
                                    if (p.getMnjxPnrRmk().getInputValue().startsWith("RMK CMS")) {
                                        p.setXe(true);
                                        xeOtherCount++;
                                    }
                                }
                            }
                        } else if (abstractPnrDto instanceof PnrNmFnDto && (!haveTn || !ObjectUtils.isEmpty(xeDto.getXe()))) {
                            PnrNmFnDto fnDto = (PnrNmFnDto) abstractPnrDto;
                            MnjxNmFn item = fnDto.getMnjxNmFn();
                            String pnrNmId = item.getPnrNmId();
                            if (item.getInputValue().startsWith("FN/A")) {
                                PnrNmDto pnrNmDto = memoryDataPnr.getPnrNmDtos().stream().filter(n -> pnrNmId.equals(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList()).get(0);
                                List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos();
                                pnrNmFcDtos.stream().filter(p -> p.getMnjxNmFc().getInputValue().startsWith("FC/A")).forEach(z -> z.setXe(true));
                                xeOtherCount++;

                                //同时删除pnrNmRmk  RMK CMS项
                                List<PnrNmRmkDto> pnrNmRmkDtos = pnrNmDto.getPnrNmRmkDtos();
                                pnrNmRmkDtos.stream().filter(p -> p.getMnjxNmRmk().getInputValue().startsWith("RMK CMS")).forEach(z -> z.setXe(true));
                                xeOtherCount++;
                            }
                        } else if (abstractPnrDto instanceof PnrNmFcDto && (!haveTn || !ObjectUtils.isEmpty(xeDto.getXe()))) {
                            PnrNmFcDto fcDto = (PnrNmFcDto) abstractPnrDto;
                            MnjxNmFc item = fcDto.getMnjxNmFc();
                            String pnrNmId = item.getPnrNmId();
                            if (item.getInputValue().startsWith("FC/A")) {
                                PnrNmDto pnrNmDto = memoryDataPnr.getPnrNmDtos().stream().filter(n -> pnrNmId.equals(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList()).get(0);
                                List<PnrNmFnDto> pnrNmFnDtos = pnrNmDto.getPnrNmFnDtos();
                                pnrNmFnDtos.stream().filter(p -> p.getMnjxNmFn().getInputValue().startsWith("FN/A")).forEach(z -> z.setXe(true));
                                xeOtherCount++;

                                //同时删除pnrNmRmk  RMK CMS项
                                List<PnrNmRmkDto> pnrNmRmkDtos = pnrNmDto.getPnrNmRmkDtos();
                                pnrNmRmkDtos.stream().filter(p -> p.getMnjxNmRmk().getInputValue().startsWith("RMK CMS")).forEach(z -> z.setXe(true));
                                xeOtherCount++;
                            }
                        }
                        // 删除XN同时删除OSI YY INF
                        else if (abstractPnrDto instanceof PnrXnDto) {
                            PnrXnDto pnrXnDto = (PnrXnDto) abstractPnrDto;
                            String pnrNmId = pnrXnDto.getMnjxNmXn().getPnrNmId();
                            PnrNmDto pnrNmDto = memoryDataPnr.getPnrNmDtos().stream().filter(n -> pnrNmId.equals(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList()).get(0);
                            List<PnrNmOsiDto> pnrNmOsiDtos = pnrNmDto.getPnrNmOsiDtos();
                            List<PnrNmOsiDto> collect = pnrNmOsiDtos.stream().filter(p -> p.getMnjxNmOsi().getInputValue().startsWith("OSI YY 1INF")).collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(collect)) {
                                collect.get(0).setXe(true);
                                xeOtherCount++;
                            }
                        }
                        //航段信息组
                        else if (abstractPnrDto instanceof PnrSegDto) {
                            PnrSegDto pnrSegDto = (PnrSegDto) abstractPnrDto;
                            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                                // 如果已值机或登机，不允许XE航段组
                                List<MnjxPsgCki> ckiList = iMnjxPsgCkiService.lambdaQuery()
                                        .in(MnjxPsgCki::getPnrNmId, pnrNmDtos.stream().filter(n -> !n.isXe()).map(n -> n.getMnjxPnrNm().getPnrNmId()).collect(Collectors.toList()))
                                        .eq(MnjxPsgCki::getPnrSegNo, StrUtil.toString(pnrSegDto.getMnjxPnrSeg().getPnrSegNo()))
                                        .in(MnjxPsgCki::getCkiStatus, Constant.ACC, Constant.CKI_STATUS_GT)
                                        .list();
                                if (CollUtil.isNotEmpty(ckiList)) {
                                    throw new UnifiedResultException("旅客已值机，不允许删除航段组");
                                }
                            }
                            for (PnrNmDto p : pnrNmDtos) {
                                //同步删除FN/FC
                                List<PnrNmFnDto> pnrNmFnDtos = p.getPnrNmFnDtos().stream().filter(z -> (z.getMnjxNmFn().getInputValue().startsWith("FN/A") || z.getMnjxNmFn().getInputValue().startsWith("FN/I"))).collect(Collectors.toList());
                                if (!ObjectUtils.isEmpty(pnrNmFnDtos)) {
                                    for (PnrNmFnDto q : pnrNmFnDtos) {
                                        q.setXe(true);
                                        xeOtherCount++;
                                    }
                                }
                                List<PnrNmFcDto> pnrNmFcDtos = p.getPnrNmFcDtos().stream().filter(z -> z.getMnjxNmFc().getInputValue().startsWith("FC/A")).collect(Collectors.toList());
                                if (!ObjectUtils.isEmpty(pnrNmFcDtos)) {
                                    for (PnrNmFcDto q : pnrNmFcDtos) {
                                        q.setXe(true);
                                        xeOtherCount++;
                                    }
                                }
                            }
                            //同步删除FN/FC
                            List<PnrFnDto> pnrFnDtoList = memoryDataPnr.getPnrFnDtos();
//                            List<PnrFnDto> pnrFnDtoList = pnrFnDtos.stream().filter(p -> p.getMnjxPnrFn().getInputValue().startsWith("FN/A")).collect(Collectors.toList());
                            if (!ObjectUtils.isEmpty(pnrFnDtoList)) {
                                for (PnrFnDto p : pnrFnDtoList) {
                                    p.setXe(true);
                                    xeOtherCount++;
                                }
                            }
                            List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
                            List<PnrFcDto> pnrFcDtoList = pnrFcDtos.stream().filter(p -> p.getMnjxPnrFc().getInputValue().startsWith("FC/A")).collect(Collectors.toList());
                            if (!ObjectUtils.isEmpty(pnrFcDtoList)) {
                                for (PnrFcDto p : pnrFcDtoList) {
                                    p.setXe(true);
                                    xeOtherCount++;
                                }
                            }
                            //同时删除  RMK CMS项
                            for (PnrNmDto p : pnrNmDtos) {
                                if (!ObjectUtils.isEmpty(p) && !ObjectUtils.isEmpty(p.getPnrNmRmkDtos())) {
                                    for (PnrNmRmkDto pnrNmRmkDto : p.getPnrNmRmkDtos()) {
                                        if (!ObjectUtils.isEmpty(pnrNmRmkDto) && !ObjectUtils.isEmpty(pnrNmRmkDto.getMnjxNmRmk()) && !ObjectUtils.isEmpty(pnrNmRmkDto.getMnjxNmRmk().getInputValue()) && pnrNmRmkDto.getMnjxNmRmk().getInputValue().startsWith("RMK CMS")) {
                                            pnrNmRmkDto.setXe(true);
                                            xeOtherCount++;
                                        }
                                    }
                                }
                            }
                            //同时删除  RMK CMS项
                            List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
                            for (PnrRmkDto p : pnrRmkDtos) {
                                if (p.getMnjxPnrRmk().getInputValue().startsWith("RMK CMS")) {
                                    p.setXe(true);
                                    xeOtherCount++;
                                }
                            }
                        }
                        //RMK CMS
                        else if (abstractPnrDto instanceof PnrRmkDto) {
                            PnrRmkDto pnrRmkDto = (PnrRmkDto) abstractPnrDto;
                            if (pnrRmkDto.getMnjxPnrRmk().getInputValue().startsWith("RMK CMS")) {
                                //同步删除FN/FC
                                List<PnrNmDto> pnrNmDtoss = memoryDataPnr.getPnrNmDtos();
                                for (PnrNmDto p : pnrNmDtoss) {
                                    p.getPnrNmFnDtos().stream().filter(z -> z.getMnjxNmFn().getInputValue().startsWith("FN/A")).forEach(q -> q.setXe(true));
                                    xeOtherCount++;
                                    p.getPnrNmFcDtos().stream().filter(z -> z.getMnjxNmFc().getInputValue().startsWith("FC/A")).forEach(q -> q.setXe(true));
                                    xeOtherCount++;
                                }
                            }
                        }
                        //PnrNmRmk RMK CMS
                        else if (abstractPnrDto instanceof PnrNmRmkDto) {
                            PnrNmRmkDto pnrNmRmkDtoIn = (PnrNmRmkDto) abstractPnrDto;
                            PnrNmDto pnrNmDto = memoryDataPnr.getPnrNmDtos().stream().filter(n -> pnrNmRmkDtoIn.getMnjxNmRmk().getPnrNmId().equals(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList()).get(0);
                            pnrNmDto.getPnrNmFnDtos().stream().filter(z -> z.getMnjxNmFn().getInputValue().startsWith("FN/A")).forEach(q -> q.setXe(true));
                            xeOtherCount++;
                            pnrNmDto.getPnrNmFcDtos().stream().filter(z -> z.getMnjxNmFc().getInputValue().startsWith("FC/A")).forEach(q -> q.setXe(true));
                            xeOtherCount++;
                        }
                        xeOtherCount++;
                    }
                    break;
                }
            }
        }
        // XE:G/xxx 处理，只减少GN项的未输入姓名座位数
        if (xeDto.isXeGn()) {
            // 如果有姓名的新增或修改且未封口，不允许XE:G
            if (memoryDataPnr.isNmInsertOrUpdate()) {
                throw new UnifiedResultException(Constant.NAME_CHANGE);
            }
            this.handleXeGn(xeDto, memoryDataPnr);
        }
        int indexCount = lastNmDtoIndex;
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        boolean hasGn = CollUtil.isNotEmpty(pnrGnDtos) && pnrGnDtos.stream().anyMatch(g -> !g.isXe());
        if (hasGn && CollUtil.isNotEmpty(pnrNmDtos)) {
            // 团队项存在时abstractPnr列表获取其他项需要下标+1
            lastNmDtoIndex++;
        }
        // XE完重新排序，NM列表项的序号不变（原1234，XE:P2之后为134），其他项需要重新按顺序开始排，
        // 从最后一个NM的序号为起点+1。封口时才会变更NM的序号，并且重新排序所有的序号
        // 内存中对应的pnr项的实体pnrIndex在recall中再进行修改
        for (int i = lastNmDtoIndex; i < abstractPnrDtos.size(); i++) {
            // 团队项序号是0
            if (hasGn && abstractPnrDtos.get(i) instanceof PnrGnDto) {
                abstractPnrDtos.get(i).setPnrIndex(indexCount);
            }
            // 其他项的处理
            else if (!abstractPnrDtos.get(i).isXe()) {
                indexCount++;
                abstractPnrDtos.get(i).setPnrIndex(indexCount);
            }
        }
        memoryDataPnr.getMnjxPnr().setMaxIndex(memoryDataPnr.getMnjxPnr().getMaxIndex() - xeOtherCount);
    }

    /**
     * Title: validateSp
     * Description: 当前控制的是SP的PNR，不允许XE姓名组<br>
     *
     * @param controlledPnr
     * @return void
     * <AUTHOR>
     * @date 2023/11/7 15:49
     */
    private void validateSp(MemoryDataPnr controlledPnr) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataPnr tmpMemoryDataPnr = memoryData.getTmpMemoryDataPnr();
        MnjxPnr tmpPnr = tmpMemoryDataPnr.getMnjxPnr();
        if (controlledPnr.equals(tmpMemoryDataPnr) && StrUtil.isEmpty(tmpPnr.getPnrCrs())) {
            throw new UnifiedResultException(Constant.NEED_EOT);
        }
    }

    /**
     * Title: handleXeNm
     * Description: 处理XE姓名组
     *
     * @param toXeNmDto     toXeNmDto
     * @param memoryDataPnr memoryDataPnr
     * @param pnrNmDtos     pnrNmDtos
     * <AUTHOR>
     * @date 2022/6/7 14:04
     */
    private void handleXeNm(PnrNmDto toXeNmDto, MemoryDataPnr memoryDataPnr, List<PnrNmDto> pnrNmDtos) {
        toXeNmDto.getMnjxFrequenterList().clear();
        MnjxPnrNm toXeNm = toXeNmDto.getMnjxPnrNm();
        String toXeNmId = toXeNm.getPnrNmId();
        int xeCount = 0;
        int xeNm = 0;
        for (PnrNmDto existNmDto : pnrNmDtos) {
            MnjxPnrNm existNm = existNmDto.getMnjxPnrNm();
            if (existNm.getPnrNmId().equals(toXeNmId) && StrUtil.isEmpty(existNm.getChangeType())) {
                existNm.setChangeType(Constant.DELETE_TYPE);
                if (CollUtil.isNotEmpty(existNmDto.getPnrXnDtos())) {
                    long count = existNmDto.getPnrXnDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrXnDtos().forEach(x -> x.setXe(true));
                    //同时处理婴儿TN
                    List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
                    for (PnrNmTnDto p : pnrNmTnDtos) {
                        //婴儿项该项为空
                        if (!ObjectUtils.isEmpty(p.getMnjxPnrNmTn().getNmXnId())) {
                            for (PnrXnDto pnrXnDto : existNmDto.getPnrXnDtos()) {
                                if (p.getMnjxPnrNmTn().getNmXnId().equals(pnrXnDto.getMnjxNmXn().getNmXnId())) {
                                    p.setXe(true);
                                    xeCount++;
                                }
                            }
                        }
                    }
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmEiDtos())) {
                    long count = existNmDto.getPnrNmEiDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmEiDtos().forEach(x -> x.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrOiDtos())) {
                    long count = existNmDto.getPnrOiDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrOiDtos().forEach(x -> x.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmCtDtos())) {
                    long count = existNmDto.getPnrNmCtDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmCtDtos().forEach(c -> c.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrSsrDtos())) {
                    long count = existNmDto.getPnrSsrDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrSsrDtos().forEach(pnrSsrDto -> {
                        //为封口前直接删除
                        pnrSsrDto.setXe(ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr()) || ObjectUtils.isEmpty(memoryDataPnr.getMnjxPnr().getPnrCrs()));
                        pnrSsrDto.getMnjxNmSsr().setActionCode(Constant.ACTION_CODE_XX);
                        StringBuilder builder = new StringBuilder(pnrSsrDto.getMnjxNmSsr().getInputValue());
                        builder.replace(Constant.TWELVE, Constant.FOURTEEN, Constant.ACTION_CODE_XX);
                        pnrSsrDto.getMnjxNmSsr().setInputValue(builder.toString());
                        pnrSsrDto.getMnjxNmSsr().setSsrInfo(builder.toString());
                    });
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmOsiDtos())) {
                    long count = existNmDto.getPnrNmOsiDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmOsiDtos().forEach(o -> o.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmRmkDtos())) {
                    long count = existNmDto.getPnrNmRmkDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmRmkDtos().forEach(o -> o.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmFcDtos())) {
                    long count = existNmDto.getPnrNmFcDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmFcDtos().forEach(x -> x.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmFnDtos())) {
                    long count = existNmDto.getPnrNmFnDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmFnDtos().forEach(x -> x.setXe(true));
                }
                if (CollUtil.isNotEmpty(existNmDto.getPnrNmFpDtos())) {
                    long count = existNmDto.getPnrNmFpDtos().stream().filter(x -> !x.isXe()).count();
                    xeCount += count;
                    existNmDto.getPnrNmFpDtos().forEach(x -> x.setXe(true));
                }
                xeCount++;
                xeNm++;
            }
        }
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        for (PnrNmTnDto p : pnrNmTnDtos) {
            //婴儿项该项为空
            if (!ObjectUtils.isEmpty(p.getMnjxPnrNmTn().getPnrNmId())) {
                if (p.getMnjxPnrNmTn().getPnrNmId().equals(toXeNmDto.getMnjxPnrNm().getPnrNmId())) {
                    p.setXe(true);
                    xeCount++;
                }
            }
        }
        // 如果航段组存在，需要同时减少航段组中订的座位数
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            //为什么后面 有数字！！
            String actionCode = mnjxPnrSeg.getActionCode().length() == Constant.THREE ? mnjxPnrSeg.getActionCode().substring(0, 2) : mnjxPnrSeg.getActionCode();
            int seatNumber = mnjxPnrSeg.getSeatNumber();
            int newSeatNumber = seatNumber - xeNm;
            pnrSegDto.setChange(true);
            mnjxPnrSeg.setSeatNumber(newSeatNumber);
            mnjxPnrSeg.setInputValue(mnjxPnrSeg.getInputValue().replace(actionCode + seatNumber, actionCode + newSeatNumber));
        }
        // 如果团队存在，需要同时减少团队人数
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        if (CollUtil.isNotEmpty(pnrGnDtos) && pnrGnDtos.stream().anyMatch(g -> !g.isXe())) {
            PnrGnDto pnrGnDto = pnrGnDtos.stream().filter(g -> !g.isXe()).collect(Collectors.toList()).get(0);
            pnrGnDto.setChange(true);
            pnrGnDto.setXeGnNumber(true);
            MnjxPnrGn mnjxPnrGn = pnrGnDto.getMnjxPnrGn();
            mnjxPnrGn.setGroupNumber(mnjxPnrGn.getGroupNumber() - xeNm);
            mnjxPnrGn.setNameNumber(mnjxPnrGn.getNameNumber() - xeNm);
            mnjxPnrGn.setInputValue(StrUtil.format("{}{} NM{}", mnjxPnrGn.getGroupNumber(), mnjxPnrGn.getGroupName(), mnjxPnrGn.getNameNumber()));
        }
        memoryDataPnr.getMnjxPnr().setMaxIndex(memoryDataPnr.getMnjxPnr().getMaxIndex() - (xeCount - xeNm));
    }

    /**
     * Title: handleXeGn
     * Description: 处理XE:G
     *
     * @param xeDto         xeDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/8 14:48
     */
    private void handleXeGn(XeDto xeDto, MemoryDataPnr memoryDataPnr) {
        int xeGnNumber = xeDto.getXeGnNumber();
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        PnrGnDto pnrGnDto = pnrGnDtos.stream().filter(g -> !g.isXe()).collect(Collectors.toList()).get(0);
        MnjxPnrGn mnjxPnrGn = pnrGnDto.getMnjxPnrGn();
        pnrGnDto.setChange(true);
        pnrGnDto.setXeGnNumber(true);
        mnjxPnrGn.setGroupNumber(mnjxPnrGn.getGroupNumber() - xeGnNumber);
        mnjxPnrGn.setInputValue(StrUtil.format("{}{} NM{}", mnjxPnrGn.getGroupNumber(), mnjxPnrGn.getGroupName(), mnjxPnrGn.getNameNumber()));
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        // 航段组的座位数也要减少
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            pnrSegDtos.forEach(p -> {
                MnjxPnrSeg mnjxPnrSeg = p.getMnjxPnrSeg();
                String actionCode = mnjxPnrSeg.getActionCode();
                int seatNumber = mnjxPnrSeg.getSeatNumber();
                int newSeatNumber = seatNumber - xeGnNumber;
                p.setChange(true);
                mnjxPnrSeg.setSeatNumber(newSeatNumber);
                mnjxPnrSeg.setInputValue(mnjxPnrSeg.getInputValue().replace(actionCode + seatNumber, actionCode + newSeatNumber));
            });
        }
    }


    /**
     * Title: setPnr
     * Description: 设置PNR各项到map中，键为PNR各项的index，值为PNR各项自身对象
     *
     * @param memoryDataPnr memoryDataPnr
     * @return 设置PNR各项到map中，键为PNR各项的index，值为PNR各项自身对象
     * <AUTHOR>
     * @date 2022/5/25 16:19
     */
    private Map<Integer, Object> setPnrRecallValue(MemoryDataPnr memoryDataPnr) {
        // PNR各项列表，用于XE和RR等选取序号提取项进行操作
        memoryDataPnr.getAbstractPnrDtos().clear();

        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        Map<Integer, Object> map = new HashMap<>(mnjxPnr.getMaxIndex());

        // NM中的其他附属项
        List<PnrXnDto> pnrXnDtos = new ArrayList<>();
        List<PnrNmEiDto> pnrNmEiDtos = new ArrayList<>();
        List<PnrOiDto> pnrOiDtos = new ArrayList<>();
        List<PnrNmFcDto> pnrNmFcDtos = new ArrayList<>();
        List<PnrNmFnDto> pnrNmFnDtos = new ArrayList<>();
        List<PnrNmFpDto> pnrNmFpDtos = new ArrayList<>();
        List<PnrNmOsiDto> pnrNmOsiDtos = new ArrayList<>();
        List<PnrNmCtDto> pnrNmCtDtos = new ArrayList<>();
        List<PnrNmRmkDto> pnrNmRmkDtos = new ArrayList<>();
        List<PnrSsrDto> pnrSsrDtos = new ArrayList<>();

        // 调用回显的时候就开始处理PNR封口编号：新创建的PNR封口编号为001，已封口过的PNR编号+1
        String atNo = "001";
        if (StrUtil.isEmpty(memoryDataPnr.getThisAtNo())) {
            if (!memoryDataPnr.isNewPnr()) {
                // 查询所有的封口数据
                List<MnjxPnrAt> mnjxPnrAts = iMnjxPnrAtService.lambdaQuery().eq(MnjxPnrAt::getPnrId, mnjxPnr.getPnrId()).list();
                // 找到最大的封口次数
                OptionalInt maxAtNo = mnjxPnrAts.stream().mapToInt(mnjxPnrAt -> Integer.parseInt(mnjxPnrAt.getAtNo())).max();
                if (maxAtNo.isPresent()) {
                    atNo = StrUtil.fill(String.valueOf(maxAtNo.getAsInt() + 1), Constant.C_ZERO, Integer.parseInt(Constant.STR_THREE), true);
                }
            }
            memoryDataPnr.setThisAtNo(atNo);
        }

        int max = 1;
        int atPnrIndex = 1;
        String pnrId = mnjxPnr.getPnrId();
        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        List<String> existPnrItem = memoryDataPnr.getExistHistoryPnrItem();
        //========================开始处理回显顺序排序========================//
        // 团队组
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        for (PnrGnDto pnrGnDto : pnrGnDtos) {
            MnjxPnrGn mnjxPnrGn = pnrGnDto.getMnjxPnrGn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, 0, Constant.PNR_GN);
            mnjxPnrRecord.setInputValue(mnjxPnrGn.getInputValue());
            String item = StrUtil.format("GN:{}", mnjxPnrGn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrGnDto)) {
                memoryDataPnr.getAbstractPnrDtos().add(pnrGnDto);
                pnrGnDto.setPnrIndex(0);
                mnjxPnrGn.setPnrIndex(0);
                String inputValue = this.setInputValue(0, mnjxPnrGn.getInputValue());
                map.put(mnjxPnrGn.getPnrIndex(), inputValue);
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, 0, isX, false);
        }

        // 姓名组
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM);
            mnjxPnrRecord.setInputValue(mnjxPnrNm.getInputValue());
            String item = StrUtil.format("NM:{}", mnjxPnrNm.getInputValue());
            if (Constant.TO_UPDATE.equals(pnrNmDto.getUpdateMark())) {
                item = StrUtil.format("NNM:{}", mnjxPnrNm.getInputValue());
            }
            boolean isX = false;
            boolean isNmChange = false;

            if (!this.beXe(pnrNmDto) && !Constant.NM_CHANGE_TYPE.equals(mnjxPnrNm.getChangeType())) {
                pnrNmDto.setPnrIndex(max);
                mnjxPnrNm.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmDto);
                // NM项回显要显示在一起，需要单独处理
                map.put(mnjxPnrNm.getPnrIndex(), mnjxPnrNm);
                // 添加附属项
                CollUtil.addAll(pnrXnDtos, pnrNmDto.getPnrXnDtos());
                CollUtil.addAll(pnrNmFcDtos, pnrNmDto.getPnrNmFcDtos());
                CollUtil.addAll(pnrNmFnDtos, pnrNmDto.getPnrNmFnDtos());
                CollUtil.addAll(pnrNmFpDtos, pnrNmDto.getPnrNmFpDtos());
                CollUtil.addAll(pnrNmEiDtos, pnrNmDto.getPnrNmEiDtos());
                CollUtil.addAll(pnrOiDtos, pnrNmDto.getPnrOiDtos());
                CollUtil.addAll(pnrNmRmkDtos, pnrNmDto.getPnrNmRmkDtos());
                CollUtil.addAll(pnrSsrDtos, pnrNmDto.getPnrSsrDtos());
                CollUtil.addAll(pnrNmOsiDtos, pnrNmDto.getPnrNmOsiDtos());
                CollUtil.addAll(pnrNmCtDtos, pnrNmDto.getPnrNmCtDtos());
            }
            // 如果NM被XE了，则该NM下所有的都被连带XE掉
            else if (this.beXe(pnrNmDto)) {
                isX = true;
                // 添加附属项
                CollUtil.addAll(pnrXnDtos, pnrNmDto.getPnrXnDtos());
                CollUtil.addAll(pnrNmFcDtos, pnrNmDto.getPnrNmFcDtos());
                CollUtil.addAll(pnrNmFnDtos, pnrNmDto.getPnrNmFnDtos());
                CollUtil.addAll(pnrNmFpDtos, pnrNmDto.getPnrNmFpDtos());
                CollUtil.addAll(pnrNmEiDtos, pnrNmDto.getPnrNmEiDtos());
                CollUtil.addAll(pnrOiDtos, pnrNmDto.getPnrOiDtos());
                CollUtil.addAll(pnrNmRmkDtos, pnrNmDto.getPnrNmRmkDtos());
                CollUtil.addAll(pnrSsrDtos, pnrNmDto.getPnrSsrDtos());
                CollUtil.addAll(pnrNmOsiDtos, pnrNmDto.getPnrNmOsiDtos());
                CollUtil.addAll(pnrNmCtDtos, pnrNmDto.getPnrNmCtDtos());
            }
            // 被修改的不添加附属项
            else if (Constant.NM_CHANGE_TYPE.equals(mnjxPnrNm.getChangeType())) {
                isNmChange = true;
            }
            max++;

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, isNmChange);
            atPnrIndex++;
        }

        // 航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecordSeg(pnrId, atNo, atPnrIndex, mnjxPnrSeg);
            mnjxPnrRecord.setInputValue(mnjxPnrSeg.getInputValue());
            String item = StrUtil.format("SEG:{}", mnjxPnrSeg.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrSegDto)) {
                pnrSegDto.setPnrIndex(max);
                mnjxPnrSeg.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrSegDto);
                map.put(mnjxPnrSeg.getPnrIndex(), this.setInputValue(mnjxPnrSeg.getPnrIndex(), mnjxPnrSeg.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR CT联系组
        List<PnrCtDto> pnrCtDtos = memoryDataPnr.getPnrCtDtos();
        for (PnrCtDto pnrCtDto : pnrCtDtos) {
            MnjxPnrCt mnjxPnrCt = pnrCtDto.getMnjxPnrCt();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_CT);
            mnjxPnrRecord.setInputValue(mnjxPnrCt.getInputValue());
            String item = StrUtil.format("CT:{}", mnjxPnrCt.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrCtDto)) {
                pnrCtDto.setPnrIndex(max);
                mnjxPnrCt.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrCtDto);
                map.put(mnjxPnrCt.getPnrIndex(), this.setInputValue(mnjxPnrCt.getPnrIndex(), mnjxPnrCt.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM CT联系组
        for (PnrNmCtDto pnrNmCtDto : pnrNmCtDtos) {
            MnjxNmCt mnjxNmCt = pnrNmCtDto.getMnjxNmCt();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_CT);
            mnjxPnrRecord.setInputValue(mnjxNmCt.getInputValue());
            String item = StrUtil.format("NMCT:{}", mnjxNmCt.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmCtDto)) {
                pnrNmCtDto.setPnrIndex(max);
                mnjxNmCt.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmCtDto);
                map.put(mnjxNmCt.getPnrIndex(), this.setInputValue(mnjxNmCt.getPnrIndex(), mnjxNmCt.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // TK组
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        for (PnrTkDto pnrTkDto : pnrTkDtos) {
            MnjxPnrTk mnjxPnrTk = pnrTkDto.getMnjxPnrTk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_TK);
            mnjxPnrRecord.setInputValue(mnjxPnrTk.getInputValue());
            String item = StrUtil.format("TK:{}", mnjxPnrTk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrTkDto)) {
                pnrTkDto.setPnrIndex(max);
                mnjxPnrTk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrTkDto);
                map.put(mnjxPnrTk.getPnrIndex(), this.setInputValue(pnrTkDto.getPnrIndex(), mnjxPnrTk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR FC
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
        for (PnrFcDto pnrFcDto : pnrFcDtos) {
            MnjxPnrFc mnjxPnrFc = pnrFcDto.getMnjxPnrFc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FC);
            mnjxPnrRecord.setInputValue(mnjxPnrFc.getInputValue());
            String item = StrUtil.format("FC:{}", mnjxPnrFc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFcDto)) {
                pnrFcDto.setPnrIndex(max);
                mnjxPnrFc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFcDto);
                map.put(mnjxPnrFc.getPnrIndex(), this.setInputValue(mnjxPnrFc.getPnrIndex(), mnjxPnrFc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM FC
        for (PnrNmFcDto pnrNmFcDto : pnrNmFcDtos) {
            MnjxNmFc mnjxNmFc = pnrNmFcDto.getMnjxNmFc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FC);
            mnjxPnrRecord.setInputValue(mnjxNmFc.getInputValue());
            String item = StrUtil.format("NMFC:{}", mnjxNmFc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFcDto)) {
                pnrNmFcDto.setPnrIndex(max);
                mnjxNmFc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFcDto);
                map.put(mnjxNmFc.getPnrIndex(), this.setInputValue(pnrNmFcDto.getPnrIndex(), mnjxNmFc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM SSR组
        for (PnrSsrDto pnrSsrDto : pnrSsrDtos) {
            MnjxNmSsr mnjxNmSsr = pnrSsrDto.getMnjxNmSsr();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_SSR);
            mnjxPnrRecord.setInputValue(mnjxNmSsr.getInputValue());
            String item = StrUtil.format("SSR:{}", mnjxNmSsr.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrSsrDto)) {
                pnrSsrDto.setPnrIndex(max);
                mnjxNmSsr.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrSsrDto);
                map.put(mnjxNmSsr.getPnrIndex(), this.setInputValue(mnjxNmSsr.getPnrIndex(), mnjxNmSsr.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR OSI
        List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos();
        for (PnrOsiDto pnrOsiDto : pnrOsiDtos) {
            MnjxPnrOsi mnjxPnrOsi = pnrOsiDto.getMnjxPnrOsi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_OSI);
            mnjxPnrRecord.setInputValue(mnjxPnrOsi.getInputValue());
            String item = StrUtil.format("OSI:{}", mnjxPnrOsi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrOsiDto)) {
                pnrOsiDto.setPnrIndex(max);
                mnjxPnrOsi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrOsiDto);
                map.put(mnjxPnrOsi.getPnrIndex(), this.setInputValue(mnjxPnrOsi.getPnrIndex(), mnjxPnrOsi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM OSI
        for (PnrNmOsiDto pnrNmOsiDto : pnrNmOsiDtos) {
            MnjxNmOsi mnjxNmOsi = pnrNmOsiDto.getMnjxNmOsi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_OSI);
            mnjxPnrRecord.setInputValue(mnjxNmOsi.getInputValue());
            String item = StrUtil.format("NMOSI:{}", mnjxNmOsi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmOsiDto)) {
                pnrNmOsiDto.setPnrIndex(max);
                mnjxNmOsi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmOsiDto);
                map.put(mnjxNmOsi.getPnrIndex(), this.setInputValue(mnjxNmOsi.getPnrIndex(), mnjxNmOsi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR RMK
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        for (PnrRmkDto pnrRmkDto : pnrRmkDtos) {
            MnjxPnrRmk mnjxPnrRmk = pnrRmkDto.getMnjxPnrRmk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_RMK);
            mnjxPnrRecord.setInputValue(mnjxPnrRmk.getInputValue());
            String item = StrUtil.format("RMK:{}", mnjxPnrRmk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrRmkDto)) {
                pnrRmkDto.setPnrIndex(max);
                mnjxPnrRmk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrRmkDto);
                map.put(mnjxPnrRmk.getPnrIndex(), this.setInputValue(mnjxPnrRmk.getPnrIndex(), mnjxPnrRmk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM RMK
        for (PnrNmRmkDto pnrNmRmkDto : pnrNmRmkDtos) {
            MnjxNmRmk mnjxNmRmk = pnrNmRmkDto.getMnjxNmRmk();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_RMK);
            mnjxPnrRecord.setInputValue(mnjxNmRmk.getInputValue());
            String item = StrUtil.format("NMRMK:{}", mnjxNmRmk.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmRmkDto)) {
                pnrNmRmkDto.setPnrIndex(max);
                mnjxNmRmk.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmRmkDto);
                map.put(mnjxNmRmk.getPnrIndex(), this.setInputValue(mnjxNmRmk.getPnrIndex(), mnjxNmRmk.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR TC
        List<PnrTcDto> pnrTcDtos = memoryDataPnr.getPnrTcDtos();
        for (PnrTcDto pnrTcDto : pnrTcDtos) {
            MnjxPnrTc mnjxPnrTc = pnrTcDto.getMnjxPnrTc();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_TC);
            mnjxPnrRecord.setInputValue(mnjxPnrTc.getInputValue());
            String item = StrUtil.format("TC:{}", mnjxPnrTc.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrTcDto)) {
                pnrTcDto.setPnrIndex(max);
                mnjxPnrTc.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrTcDto);
                map.put(mnjxPnrTc.getPnrIndex(), this.setInputValue(pnrTcDto.getPnrIndex(), mnjxPnrTc.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR FN
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
        for (PnrFnDto pnrFnDto : pnrFnDtos) {
            MnjxPnrFn mnjxPnrFn = pnrFnDto.getMnjxPnrFn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FN);
            mnjxPnrRecord.setInputValue(mnjxPnrFn.getInputValue());
            String item = StrUtil.format("FN:{}", mnjxPnrFn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFnDto)) {
                pnrFnDto.setPnrIndex(max);
                mnjxPnrFn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFnDto);
                map.put(mnjxPnrFn.getPnrIndex(), this.setInputValue(mnjxPnrFn.getPnrIndex(), mnjxPnrFn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM FN
        for (PnrNmFnDto pnrNmFnDto : pnrNmFnDtos) {
            MnjxNmFn mnjxNmFn = pnrNmFnDto.getMnjxNmFn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FN);
            mnjxPnrRecord.setInputValue(mnjxNmFn.getInputValue());
            String item = StrUtil.format("NMFN:{}", mnjxNmFn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFnDto)) {
                pnrNmFnDto.setPnrIndex(max);
                mnjxNmFn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFnDto);
                map.put(mnjxNmFn.getPnrIndex(), this.setInputValue(pnrNmFnDto.getPnrIndex(), mnjxNmFn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR EI
        List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos();
        for (PnrEiDto pnrEiDto : pnrEiDtos) {
            MnjxPnrEi mnjxPnrEi = pnrEiDto.getMnjxPnrEi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_EI);
            mnjxPnrRecord.setInputValue(mnjxPnrEi.getInputValue());
            String item = StrUtil.format("EI:{}", mnjxPnrEi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrEiDto)) {
                pnrEiDto.setPnrIndex(max);
                mnjxPnrEi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrEiDto);
                map.put(mnjxPnrEi.getPnrIndex(), this.setInputValue(mnjxPnrEi.getPnrIndex(), mnjxPnrEi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }


        // NM EI
        for (PnrNmEiDto pnrNmEiDto : pnrNmEiDtos) {
            MnjxNmEi mnjxNmEi = pnrNmEiDto.getMnjxNmEi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_EI);
            mnjxPnrRecord.setInputValue(mnjxNmEi.getInputValue());
            String item = StrUtil.format("NMEI:{}", mnjxNmEi.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmEiDto)) {
                pnrNmEiDto.setPnrIndex(max);
                mnjxNmEi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmEiDto);
                map.put(mnjxNmEi.getPnrIndex(), this.setInputValue(mnjxNmEi.getPnrIndex(), mnjxNmEi.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM OI
        for (PnrOiDto pnrOiDto : pnrOiDtos) {
            MnjxNmOi mnjxNmOi = pnrOiDto.getMnjxNmOi();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_OI);
            mnjxPnrRecord.setInputValue(mnjxNmOi.getOiInfo());
            String item = StrUtil.format("OI:{}", mnjxNmOi.getOiInfo());
            boolean isX = false;

            if (!this.beXe(pnrOiDto)) {
                pnrOiDto.setPnrIndex(max);
                mnjxNmOi.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrOiDto);
                map.put(mnjxNmOi.getPnrIndex(), this.setInputValue(mnjxNmOi.getPnrIndex(), mnjxNmOi.getOiInfo()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR TN
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        for (PnrNmTnDto pnrNmTnDto : pnrNmTnDtos) {
            MnjxPnrNmTn mnjxPnrNmTn = pnrNmTnDto.getMnjxPnrNmTn();

            // 记录旅客id修改后需通过该Id查询电子客票信息
            MnjxPnrRecord mnjxPnrRecord = this.constructRecordTn(pnrId, atNo, atPnrIndex, mnjxPnrNmTn);
            mnjxPnrRecord.setInputValue(mnjxPnrNmTn.getInputValue());
            String item = StrUtil.format("TN:{}", mnjxPnrNmTn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmTnDto)) {
                pnrNmTnDto.setPnrIndex(max);
                mnjxPnrNmTn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmTnDto);
                map.put(mnjxPnrNmTn.getPnrIndex(), this.setInputValue(mnjxPnrNmTn.getPnrIndex(), mnjxPnrNmTn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM XN
        for (PnrXnDto pnrXnDto : pnrXnDtos) {
            MnjxNmXn mnjxNmXn = pnrXnDto.getMnjxNmXn();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_XN);
            mnjxPnrRecord.setInputValue(mnjxNmXn.getInputValue());
            String item = StrUtil.format("XN:{}", mnjxNmXn.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrXnDto)) {
                pnrXnDto.setPnrIndex(max);
                mnjxNmXn.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrXnDto);
                map.put(mnjxNmXn.getPnrIndex(), this.setInputValue(mnjxNmXn.getPnrIndex(), mnjxNmXn.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // PNR FP
        List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos();
        for (PnrFpDto pnrFpDto : pnrFpDtos) {
            MnjxPnrFp mnjxPnrFp = pnrFpDto.getMnjxPnrFp();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_FP);
            mnjxPnrRecord.setInputValue(mnjxPnrFp.getInputValue());
            String item = StrUtil.format("FP:{}", mnjxPnrFp.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrFpDto)) {
                pnrFpDto.setPnrIndex(max);
                mnjxPnrFp.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrFpDto);
                map.put(mnjxPnrFp.getPnrIndex(), this.setInputValue(mnjxPnrFp.getPnrIndex(), mnjxPnrFp.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        // NM FP
        for (PnrNmFpDto pnrNmFpDto : pnrNmFpDtos) {
            MnjxNmFp mnjxNmFp = pnrNmFpDto.getMnjxNmFp();

            MnjxPnrRecord mnjxPnrRecord = this.constructRecord(pnrId, atNo, atPnrIndex, Constant.PNR_NM_FP);
            mnjxPnrRecord.setInputValue(mnjxNmFp.getInputValue());
            String item = StrUtil.format("NMFP:{}", mnjxNmFp.getInputValue());
            boolean isX = false;

            if (!this.beXe(pnrNmFpDto)) {
                pnrNmFpDto.setPnrIndex(max);
                mnjxNmFp.setPnrIndex(max);
                memoryDataPnr.getAbstractPnrDtos().add(pnrNmFpDto);
                map.put(mnjxNmFp.getPnrIndex(), this.setInputValue(pnrNmFpDto.getPnrIndex(), mnjxNmFp.getInputValue()));
                max++;
            } else {
                isX = true;
            }

            this.addOrChangeRecordList(existPnrItem, item, mnjxPnrRecords, mnjxPnrRecord, atPnrIndex, isX, false);
            atPnrIndex++;
        }

        mnjxPnrRecords.sort(Comparator.comparing(MnjxPnrRecord::getPnrIndex));
        mnjxPnr.setMaxIndex(max - 1);
        mnjxPnr.setCreateOfficeNoEid(mnjxPnr.getMaxIndex() + 1);
        map.put(mnjxPnr.getCreateOfficeNoEid(), this.setInputValue(mnjxPnr.getCreateOfficeNoEid(), mnjxPnr.getCreateOfficeNo()));
        return map;
    }

    /**
     * Title: addOrChangeRecordList
     * Description: 添加历史记录，如果已存在则更新序号
     *
     * @param existPnrItem   existPnrItem
     * @param item           item
     * @param mnjxPnrRecords mnjxPnrRecords
     * @param mnjxPnrRecord  mnjxPnrRecord
     * @param atPnrIndex     atPnrIndex
     * <AUTHOR>
     * @date 2022/6/24 14:55
     */
    private void addOrChangeRecordList(List<String> existPnrItem, String item, List<MnjxPnrRecord> mnjxPnrRecords, MnjxPnrRecord mnjxPnrRecord, int atPnrIndex, boolean isX, boolean isNmChange) {
        // 没有添加该项得历史时直接添加
        if (!existPnrItem.contains(item)) {
            Iterator<String> iterator = existPnrItem.iterator();
            // 如果有GN，因为GN的座位数可能变化了，字符串不能对应上，重新设置GN
            if (item.startsWith("GN:")) {
                boolean gnChange = false;
                String oldGnItem = "";
                while (iterator.hasNext()) {
                    String nextItem = iterator.next();
                    if (nextItem.startsWith("GN:") && !item.equals(nextItem)) {
                        gnChange = true;
                        oldGnItem = nextItem;
                        iterator.remove();
                        break;
                    }
                }
                if (gnChange) {
                    String finalOldGnItem = oldGnItem.split(":")[1];
                    MnjxPnrRecord record = mnjxPnrRecords.stream().filter(m -> finalOldGnItem.equals(m.getInputValue())).collect(Collectors.toList()).get(0);
                    record.setInputValue(item.split(":")[1]);
                    existPnrItem.add(item);
                } else {
                    mnjxPnrRecords.add(mnjxPnrRecord);
                    existPnrItem.add(item);
                }
            }
            // 如果有SEG，因为SEG的座位数可能变化了，或者SEG的行动代码变化了，字符串不能对应上，重新设置SEG
            else if (item.startsWith("SEG:")) {
                boolean segChange = false;
                String oldSegItem = "";
                while (iterator.hasNext()) {
                    String nextItem = iterator.next();
                    if (nextItem.startsWith("SEG:") && !item.equals(nextItem) && item.substring(0, 28).equals(nextItem.substring(0, 28))) {
                        segChange = true;
                        oldSegItem = nextItem;
                        iterator.remove();
                        break;
                    }
                }
                if (segChange) {
                    String finalOldSegItem = oldSegItem.split(":")[1];
                    MnjxPnrRecord record = mnjxPnrRecords.stream().filter(m -> finalOldSegItem.equals(m.getInputValue())).collect(Collectors.toList()).get(0);
                    record.setInputValue(item.split(":")[1]);
                    existPnrItem.add(item);
                    if (isX) {
                        record.setChangeMark(Constant.DELETE_TYPE);
                    }
                } else {
                    mnjxPnrRecords.add(mnjxPnrRecord);
                    existPnrItem.add(item);
                }
            } else {
                mnjxPnrRecords.add(mnjxPnrRecord);
                existPnrItem.add(item);
            }
        }
        // 已存在该项历史，更新其序号，根据是否被XE或修改姓名更新标记
        else {
            List<MnjxPnrRecord> collect = mnjxPnrRecords.stream().filter(r -> mnjxPnrRecord.getInputValue().equals(r.getInputValue())).collect(Collectors.toList());
            MnjxPnrRecord record = collect.get(0);
            boolean cmdIsStart = item.startsWith(Constant.CMD_NM) || item.startsWith(Constant.CMD_NNM);
            if (cmdIsStart && collect.size() > 1) {
                record = collect.stream().filter(c -> atPnrIndex == c.getPnrIndex()).collect(Collectors.toList()).get(0);
            }
            record.setPnrIndex(atPnrIndex);
            if (isX) {
                record.setChangeMark(Constant.DELETE_TYPE);
            } else if (isNmChange) {
                record.setChangeMark(Constant.NM_CHANGE_TYPE);
            } else {
                record.setChangeMark(null);
            }
        }
    }

    /**
     * Title: constructRecord
     * Description: 构建历史记录对象
     *
     * @param pnrId      pnrId
     * @param atNo       atNo
     * @param atPnrIndex atPnrIndex
     * @param pnrType    pnrType
     * @return 构建历史记录对象
     * <AUTHOR>
     * @date 2022/6/24 14:55
     */
    private MnjxPnrRecord constructRecord(String pnrId, String atNo, int atPnrIndex, String pnrType) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(pnrType);
        return mnjxPnrRecord;
    }

    /**
     * 构建历史记录对象
     *
     * @param pnrId       pnrId
     * @param atNo        atNo
     * @param atPnrIndex  atPnrIndex
     * @param mnjxPnrNmTn mnjxPnrNmTn
     * @return 构建历史记录对象
     */
    private MnjxPnrRecord constructRecordTn(String pnrId, String atNo, int atPnrIndex, MnjxPnrNmTn mnjxPnrNmTn) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(Constant.PNR_TN);
        if (StrUtil.isNotEmpty(mnjxPnrNmTn.getPnrNmId())) {
            mnjxPnrRecord.setPnrNmId(mnjxPnrNmTn.getPnrNmId());
        } else {
            mnjxPnrRecord.setPnrNmId(mnjxPnrNmTn.getNmXnId());
        }
        return mnjxPnrRecord;
    }

    /**
     * 构建历史记录对象 航段
     *
     * @param pnrId      pnrId
     * @param atNo       atNo
     * @param atPnrIndex atPnrIndex
     * @param mnjxPnrSeg mnjxPnrSeg
     * @return 构建历史记录对象 航段
     */
    private MnjxPnrRecord constructRecordSeg(String pnrId, String atNo, int atPnrIndex, MnjxPnrSeg mnjxPnrSeg) {
        MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
        mnjxPnrRecord.setPnrId(pnrId);
        mnjxPnrRecord.setAtNo(atNo);
        mnjxPnrRecord.setPnrIndex(atPnrIndex);
        mnjxPnrRecord.setPnrType(Constant.PNR_SEG);
        mnjxPnrRecord.setPnrSegId(mnjxPnrSeg.getPnrSegId());
        return mnjxPnrRecord;
    }

    /**
     * Title: setInputValue
     * Description: 组装PNR序号和对应项的值
     *
     * @param pnrIndex   pnrIndex
     * @param inputValue inputValue
     * @return 组装PNR序号和对应项的值
     * <AUTHOR>
     * @date 2022/5/25 16:53
     */
    private String setInputValue(Integer pnrIndex, String inputValue) {
        return StrUtil.format("{}.{}", pnrIndex, inputValue);
    }

    /**
     * Title: beXe
     * Description: PNR项是否被XE
     *
     * @param item item
     * @return PNR项是否被XE
     * <AUTHOR>
     * @date 2022/5/26 15:16
     */
    private boolean beXe(AbstractPnrDto item) {
        return item.isXe();
    }
}
