package com.swcares.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxNmOsi;
import com.swcares.entity.MnjxNmXn;
import com.swcares.entity.MnjxPnrNm;
import com.swcares.obj.dto.PnrNmDto;
import com.swcares.obj.dto.PnrNmOsiDto;
import com.swcares.obj.dto.PnrXnDto;
import com.swcares.obj.dto.XnDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PnrCommandServicePartXn {
    public void xn(MemoryDataPnr memoryDataPnr, XnDto xnDto) {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        int psgIndex = xnDto.getPsgIndex();
        PnrNmDto pnrNmDto = pnrNmDtos.get(psgIndex - 1);
        MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();

        // 输入XN项会自动添加一项OSI ：OSI YY 1INF ZHANG/YI INF/P1
        MnjxNmOsi mnjxNmOsi = new MnjxNmOsi();
        mnjxNmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxNmOsi.setPnrNmId(mnjxPnrNm.getPnrNmId());
        String xnName = xnDto.getXnName();
        mnjxNmOsi.setPnrOsiInfo(StrUtil.format("OSI YY 1INF {} INF/P{}", xnName, psgIndex));
        mnjxNmOsi.setInputValue(mnjxNmOsi.getPnrOsiInfo());
        PnrNmOsiDto pnrNmOsiDto = new PnrNmOsiDto();
        pnrNmOsiDto.setMnjxNmOsi(mnjxNmOsi);
        List<PnrNmOsiDto> pnrNmOsiDtos = pnrNmDto.getPnrNmOsiDtos();
        pnrNmOsiDtos.add(pnrNmOsiDto);

        // 构建XN
        MnjxNmXn mnjxNmXn = new MnjxNmXn();
        mnjxNmXn.setNmXnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxNmXn.setPnrNmId(mnjxPnrNm.getPnrNmId());
        String inputValue = StrUtil.format("XN/IN/{} INF({})/P{}", xnName, xnDto.getXnBirthMonthYear(), xnDto.getPsgIndex());
        mnjxNmXn.setInputValue(inputValue);
        mnjxNmXn.setXnCname(xnName);
        String tmpBirth = StrUtil.format("01{}", xnDto.getXnBirthMonthYear());
        tmpBirth = DateUtils.com2ymd(tmpBirth);
        tmpBirth = tmpBirth.substring(0, tmpBirth.length() - 3);
        mnjxNmXn.setXnBirthday(tmpBirth);
        PnrXnDto pnrXnDto = new PnrXnDto();
        pnrXnDto.setMnjxNmXn(mnjxNmXn);
        pnrNmDto.getPnrXnDtos().add(pnrXnDto);
    }
}
