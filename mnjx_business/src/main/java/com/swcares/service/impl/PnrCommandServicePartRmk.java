/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CollUtils;
import com.swcares.core.util.Constant;
import com.swcares.core.util.IdUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.PnrNmDto;
import com.swcares.obj.dto.PnrNmRmkDto;
import com.swcares.obj.dto.PnrRmkDto;
import com.swcares.obj.dto.RmkDto;
import com.swcares.service.IPnrOperationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class PnrCommandServicePartRmk {

    @Resource
    private IPnrOperationService iPnrOperationService;

    public void rmk(MemoryDataPnr memoryDataPnr, RmkDto rmkDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 判断缓存中有无PNR，如果没有则生成一个基本PNR
        MnjxPnr mnjxPnr;
        if (ObjectUtil.isNotEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            mnjxPnr = memoryDataPnr.getMnjxPnr();
            //RMK GMJC/P1、RMK GMJC   ---非团队旅客时，多人的情况下，添加军警残标识时必须指定旅客序号，否则报错 PSGR ID。团队旅客时，可以添加军警残标识时，可以不用带旅客序号
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            if (CollUtils.isNotEmpty(pnrNmDtos) && StrUtils.isEmpty(rmkDto.getPsgIndex())) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
        } else {
            // 新建PNR
            mnjxPnr = iPnrOperationService.createNewPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        }

        // 格式：RMK:xxxxxxxxxx/P1 的情况
        if (StrUtils.isNotEmpty(rmkDto.getPsgIndex())) {
            // 获取到该旅客
            PnrNmDto pnrNmDto = this.getPnrNmDtoByPsgId(memoryDataPnr.getPnrNmDtos(), rmkDto.getPsgIndex());
            // 构建nmRmk项
            String pnrNmId = pnrNmDto.getMnjxPnrNm().getPnrNmId();
            MnjxNmRmk mnjxNmRmk = new MnjxNmRmk();
            mnjxNmRmk.setNmRmkId(IdUtils.getId());
            mnjxNmRmk.setPnrNmId(pnrNmId);
            mnjxNmRmk.setRmkName(rmkDto.getRmkType().getDescribe());
            String rmkInfo = StrUtils.format("{}/P{}", rmkDto.getRmkText(), rmkDto.getPsgIndex());
            mnjxNmRmk.setRmkInfo(rmkInfo);
            mnjxNmRmk.setInputValue(StrUtils.addPrefixByCond(true, rmkInfo, "RMK "));
            pnrNmDto.getPnrNmRmkDtos().add(new PnrNmRmkDto(mnjxNmRmk));
        } else {
            // 构建pnrRmk项
            MnjxPnrRmk mnjxPnrRmk = new MnjxPnrRmk();
            mnjxPnrRmk.setPnrRmkId(IdUtils.getId());
            mnjxPnrRmk.setPnrId(mnjxPnr.getPnrId());
            mnjxPnrRmk.setRmkName(rmkDto.getRmkType().getDescribe());
            String rmkInfo = StrUtils.format("{}", rmkDto.getRmkText());
            mnjxPnrRmk.setRmkInfo(rmkInfo);
            mnjxPnrRmk.setInputValue(StrUtils.addPrefixByCond(true, rmkInfo, "RMK "));
            memoryDataPnr.getPnrRmkDtos().add(new PnrRmkDto(mnjxPnrRmk));
        }
    }

    /**
     * 通过旅客序号获得旅客数据
     *
     * @param pnrNmDtos PNR的大对象中的旅客列表
     * @param psgId     旅客序号
     * @return 旅客数据
     */
    private PnrNmDto getPnrNmDtoByPsgId(List<PnrNmDto> pnrNmDtos, String psgId) throws UnifiedResultException {
        Optional<PnrNmDto> pnrNmDtoOptional = pnrNmDtos.stream()
                .filter(k -> k.getMnjxPnrNm().getPsgIndex().equals(Integer.valueOf(psgId)) && !Constant.BE_UPDATED.equals(k.getUpdateMark()) && !k.isXe())
                .findFirst();
        if (!pnrNmDtoOptional.isPresent()) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        return pnrNmDtoOptional.get();
    }
}
