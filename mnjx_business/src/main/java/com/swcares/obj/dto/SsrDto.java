package com.swcares.obj.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Data
public class SsrDto {
    /**
     * 特殊服务组 类型
     */
    private String ssrType;

    /**
     * 航空公司代码
     */
    private String airlineCode;

    /**
     * 行动代码，一般都是NN（FOID是HK）
     */
    private String actionCode;

    private String cityPair;

    private String flightNo;

    private String flightDate;

    private String sellCabin;

    private String infName;

    /**
     * 婴儿INFT或者儿童CHLD的生日
     */
    private String birthDay;

    private String psgIndex;

    /**
     * 航段组序号，只有指令输入 /Sn 这种才会存在
     */
    private String segIndex;

    private String certificateType;

    private String foidNumber;

    /**
     * SPML、其他特服 的自由文本
     */
    private String freeText;

    /**
     * 常客卡号
     */
    private String frequentCard;

    /**
     * 常客卡等级
     */
    private String frequentCardLevel;

    /**
     * 指令是否未输入旅客序号
     */
    private boolean noPsgIndexInput;

    /**
     * 指令是否输入了完整的航班号
     */
    private boolean inputAllFlightNo;
}
