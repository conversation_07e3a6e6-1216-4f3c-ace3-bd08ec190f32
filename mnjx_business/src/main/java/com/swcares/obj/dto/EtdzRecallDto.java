package com.swcares.obj.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022/7/6-10:48
 * 存储ETDZ出票异常时需要还原的数据项
 */
@Data
public class EtdzRecallDto implements Serializable {
    private BigInteger lastTicket;
    private String printerId;
    private String tkTid;
    private String defaultGroupName;
    private final List<String> tnIds = new ArrayList<>();
    private final List<String> tkIds = new ArrayList<>();
    private final List<String> fcIds = new ArrayList<>();
    private final List<String> eiIds = new ArrayList<>();
    private final List<String> rmkIds = new ArrayList<>();
    private final List<String> psgCkiIds = new ArrayList<>();
    private final List<String> nmTicketIds = new ArrayList<>();
    private final List<String> psgSeatIds = new ArrayList<>();
    private final List<String> ssrTkneIds = new ArrayList<>();
    private final List<String> ticketOptRecordIds = new ArrayList<>();
}
