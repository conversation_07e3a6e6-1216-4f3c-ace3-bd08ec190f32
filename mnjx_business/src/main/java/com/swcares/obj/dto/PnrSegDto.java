package com.swcares.obj.dto;

import com.swcares.entity.MnjxPnrSeg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/5/26
 */
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class PnrSegDto extends AbstractPnrDto {
    /**
     * PNR的航段对象
     */
    private MnjxPnrSeg mnjxPnrSeg;
    /**
     * 是否改期的标志
     */
    private boolean isChange;

    /**
     * 承运航班
     */
    private String carrierFlight;

    public PnrSegDto(MnjxPnrSeg mnjxPnrSeg) {
        this.mnjxPnrSeg = mnjxPnrSeg;
    }
}
