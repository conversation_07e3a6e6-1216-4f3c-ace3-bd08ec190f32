package com.swcares.obj.type;

import com.swcares.core.util.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumCertificateType {
    /**
     * 身份证
     */
    ID_CARD("身份证", Constant.CERTIFICATE_TYPE_NI),
    /**
     * 护照
     */
    PASSPORT("护照", Constant.CERTIFICATE_TYPE_PP),
    /**
     * 军官证
     */
    OFFICER("军官证", Constant.CERTIFICATE_TYPE_ID);

    @Getter
    private final String describe;
    @Getter
    private final String code;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param describe 中文描述
     * @return 具体的枚举类型
     */
    public static EnumCertificateType ofDescribe(String describe) {
        return Arrays.stream(EnumCertificateType.values())
                .filter(enumCertificateType -> enumCertificateType.getDescribe().equalsIgnoreCase(describe))
                .findAny()
                .orElse(ID_CARD);
    }

    /**
     * 通过编码获取具体的枚举类型
     *
     * @param code 编码
     * @return 具体的枚举类型
     */
    public static EnumCertificateType ofCode(String code) {
        return Arrays.stream(EnumCertificateType.values())
                .filter(enumCertificateType -> enumCertificateType.getCode().equalsIgnoreCase(code))
                .findAny()
                .orElse(ID_CARD);
    }
}
