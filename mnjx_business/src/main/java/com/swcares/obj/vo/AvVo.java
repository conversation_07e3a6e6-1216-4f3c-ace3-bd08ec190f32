package com.swcares.obj.vo;

import com.swcares.entity.MnjxOpenCabin;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
@Data
public class AvVo implements Serializable {

    /**
     * 航班计划ID
     */
    private String planFlightId;

    /**
     * 航节计划ID
     */
    private String planSectionId;

    /**
     * Tcard ID
     */
    private String tcardId;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 出发机场三字码
     */
    private String org;

    /**
     * 出发机场ID
     */
    private String depAptId;

    /**
     * 到达机场三字码
     */
    private String dst;

    /**
     * 到达机场ID
     */
    private String arrAptId;

    /**
     * 计划起飞时间
     */
    private String estimateOff;

    /**
     * 计划起飞时间跨天
     */
    private String estimateOffChange;

    /**
     * 计划到达时间
     */
    private String estimateArr;

    /**
     * 计划到达时间跨天
     */
    private String estimateArrChange;

    /**
     * 飞机机型
     */
    private String planeModelType;

    /**
     * 经停点数量
     */
    private int stopPoint = 0;

    /**
     * 允许ASR
     */
    private String allowAsr;

    /**
     * 餐食代码
     */
    private String mealCode;

    /**
     * 星期
     */
    private String week;

    /**
     * 飞行时间
     */
    private String fly;

    /**
     * 经停时间
     */
    private String ground;

    /**
     * 航班排期
     */
    private String cycle;

    /**
     * 销售舱位及其剩余座位数
     */
    private List<String> sellCabinAndSeat;

    /**
     * 舱等和开舱关系 key:舱等  value:对应的开舱数据
     */
    private Map<String, List<MnjxOpenCabin>> cabinClassSellCabin;

    /**
     * 承运航班号，当前航班是共享航班时才存在
     */
    private String carrierFlight;
}
