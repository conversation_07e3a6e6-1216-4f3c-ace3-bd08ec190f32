#=============================端口===========================#
server:
  port: 8350
  tomcat:
    max-connections: 200
    max-threads: 300
    uri-encoding: utf-8
    max-swallow-size: 5MB
  servlet:
    context-path:
#======================spring配置===============================#
spring:
  # 项目名称
  application:
    name: sgui-bkc
  # 个人理解为禁用了开发属性(默认为true，生产我们设置为false)
  devtools:
    add-properties: false
  jackson:
    default-property-inclusion: always
#======================配置允许跨域源=============================#
sgui:
  allow:
    origin: ${SGUI_ALLOW_ORIGIN}

# 国密加密配置
crypto:
  sm2:
    # 对应前端公钥的私钥
    private-key: 609DEB0A80859686453BEE85C6CBDEC33598B75377EE45E480B1A4AF4D02779C
    # 前端使用的公钥 (更新到JS文件中)
    public-key: 04CE524881F3EA91B340646199A426D5B70F254606923B7E96E7CBE4CC24514CCA54406ECCE471E73C150C6C9C2741C857DABA453DDC9AFEF7C1AD28530562A779