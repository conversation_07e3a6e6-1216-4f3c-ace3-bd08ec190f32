<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.EtV2Mapper">

    <select id="retrieveTcardIdsByCityPair" resultType="java.lang.String">
        SELECT
            c.tcard_id
        FROM (
            SELECT
                a.tcard_id
            FROM
                mnjx_tcard_section a
            WHERE
                a.airport_id IN
                <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
                AND a.tcard_id IN
                (SELECT b.tcard_id FROM mnjx_tcard_section b WHERE b.airport_id IN
                <foreach collection="dstIdList" item="dstId" open="(" separator="," close=")">
                    #{dstId}
                </foreach>
                )
        )c
    </select>

    <select id="retrieveReachableAirports" resultType="java.lang.String">
        SELECT DISTINCT
            ps.arr_apt_id
        FROM
            mnjx_plan_section ps
        JOIN
            mnjx_plan_flight pf ON ps.plan_flight_id = pf.plan_flight_id
        WHERE
            ps.dep_apt_id IN
            <foreach collection="departureAirportIdList" item="airportId" open="(" separator="," close=")">
                #{airportId}
            </foreach>
            AND pf.flight_date = #{flightDate}
    </select>

    <select id="retrieveAirportsReachingDestination" resultType="java.lang.String">
        SELECT DISTINCT
            ps.dep_apt_id
        FROM
            mnjx_plan_section ps
        JOIN
            mnjx_plan_flight pf ON ps.plan_flight_id = pf.plan_flight_id
        WHERE
            ps.arr_apt_id IN
            <foreach collection="arriveAirportIdList" item="airportId" open="(" separator="," close=")">
                #{airportId}
            </foreach>
            AND pf.flight_date = #{flightDate}
    </select>
</mapper>