package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 初始加载退票信息请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 12:00
 */
@Data
@ApiModel(value = "初始加载退票信息请求DTO")
public class FindRefundTicketDto {

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "二次验证因素")
    private SecondFactor secondFactor;

    @ApiModelProperty(value = "退票查询标识，默认true")
    private Boolean refundQuery;

    /**
     * 二次验证因素
     */
    @Data
    @ApiModel(value = "二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证因素代码：NI-证件号，NM-旅客姓名，CN-PNR")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }
}
