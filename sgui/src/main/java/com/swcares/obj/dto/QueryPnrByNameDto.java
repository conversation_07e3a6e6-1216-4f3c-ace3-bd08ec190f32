package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通过姓名查询PNR请求DTO
 *
 * <AUTHOR>
 * @date 2025/07/03 14:30
 */
@Data
@ApiModel(value = "通过姓名查询PNR请求DTO")
public class QueryPnrByNameDto {

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "航班号")
    private String flight;

    @ApiModelProperty(value = "起飞日期，格式：yyyy-MM-dd")
    private String date;
}
