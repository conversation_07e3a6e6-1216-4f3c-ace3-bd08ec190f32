package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/11 02:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryAirportDecodeDto", description = "机场查询加密传输对象")
public class QueryAirportDecodeDto implements Serializable {

    @ApiModelProperty(value = "加密数据")
    private String req;
}
