package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 修改退票单请求DTO
 *
 * <AUTHOR>
 * @date 2025/06/17 15:30
 */
@Data
@ApiModel(value = "修改退票单请求DTO")
public class ModifyRefundFormDto {

    @ApiModelProperty(value = "退票单号")
    private String refundNo;

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "票类型")
    private String ticketType;

    @ApiModelProperty(value = "打印机号")
    private String printerNo;

    @ApiModelProperty(value = "客票管理机构代码")
    private String ticketManagementOrganizationCode;

    @ApiModelProperty(value = "退票单旅客项")
    private RefundFormPassengerItem refundFormPassengerItem;

    @ApiModelProperty(value = "退票单价格项")
    private RefundFormPriceItem refundFormPriceItem;

    @Data
    @ApiModel(value = "退票单旅客项")
    public static class RefundFormPassengerItem {
        @ApiModelProperty(value = "退票标识")
        private String refund;

        @ApiModelProperty(value = "货币")
        private String currency;

        @ApiModelProperty(value = "支付方式")
        private String payMethod;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "信用卡")
        private String creditCard;

        @ApiModelProperty(value = "联票号")
        private List<String> couponNos;

        @ApiModelProperty(value = "姓名")
        private String name;
    }

    @Data
    @ApiModel(value = "退票单价格项")
    public static class RefundFormPriceItem {
        @ApiModelProperty(value = "代理费")
        private String commission;

        @ApiModelProperty(value = "代理费率")
        private String commissionRate;

        @ApiModelProperty(value = "总退款")
        private String grossRefund;

        @ApiModelProperty(value = "扣除费用")
        private String deduction;

        @ApiModelProperty(value = "净退款")
        private String netRefund;

        @ApiModelProperty(value = "税费信息")
        private List<TaxInfo> taxInfos;
    }

    @Data
    @ApiModel(value = "税费信息")
    public static class TaxInfo {
        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "税费金额")
        private BigDecimal taxAmount;
    }
}
