package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 自动退票请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 16:00
 */
@Data
@ApiModel(value = "自动退票请求DTO")
public class BatchAutoRefundDto {

    @ApiModelProperty(value = "票号列表")
    private List<TicketInfo> ticketList;

    @ApiModelProperty(value = "保存标识")
    private String save;

    /**
     * 票号信息
     */
    @Data
    @ApiModel(value = "票号信息")
    public static class TicketInfo {
        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "国内标识")
        private Boolean domestic;

        @ApiModelProperty(value = "打印号")
        private String printNo;
    }
}
