package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/11 05:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryTimeDiffDto", description = "时差查询传输对象")
public class QueryTimeDiffDto implements Serializable {

    @ApiModelProperty(value = "出发地")
    private String origin;

    @ApiModelProperty(value = "日期时间")
    private String dateTime;

    @ApiModelProperty(value = "目的地")
    private String destination;
}
