package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航司CTCT设置数据传输对象
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SwitchOfficeUpdateDto", description = "SwitchOfficeUpdate设置数据传输对象")
public class SwitchOfficeUpdateDto {

    @ApiModelProperty(value = "office")
    private String office;

    @ApiModelProperty(value = "roleName")
    private String roleName;

    @ApiModelProperty(value = "system")
    private String system;

    @ApiModelProperty(value = "userGroup")
    private String userGroup;

    @ApiModelProperty(value = "etermId")
    private String etermId;

    @ApiModelProperty(value = "etermPwd")
    private String etermPwd;

    @ApiModelProperty(value = "etermServerAddress")
    private String etermServerAddress;
}
