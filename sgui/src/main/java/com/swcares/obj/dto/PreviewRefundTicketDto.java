package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 退前预览请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/14 15:30
 */
@Data
@ApiModel(value = "PreviewRefundTicketDto", description = "退前预览请求DTO")
public class PreviewRefundTicketDto {

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "二次验证信息")
    private SecondFactor secondFactor;

    /**
     * 二次验证信息
     */
    @Data
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证值")
        private String secondFactorValue;
    }
}
