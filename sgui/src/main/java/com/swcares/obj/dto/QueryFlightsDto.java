package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 15:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryFlightsDto", description = "航班查询传输对象")
public class QueryFlightsDto {

    @ApiModelProperty(value = "出发城市")
    private String departureCity;

    @ApiModelProperty(value = "到达城市")
    private String arriveCity;

    @ApiModelProperty(value = "出发时间")
    private String departureTime;

    @ApiModelProperty(value = "出发日期")
    @NotNull(message = "出发日期不能为空")
    private String departureDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "仅直飞")
    private String onlyDirect;

    @ApiModelProperty(value = "航司")
    private String airCode;

    @ApiModelProperty(value = "seamlessOrDa")
    private String seamlessOrDa;

    @ApiModelProperty(value = "queryType")
    private String queryType;

    @ApiModelProperty(value = "pagingType")
    private String pagingType;

    @ApiModelProperty(value = "sessionId")
    private String sessionId;

    @ApiModelProperty(value = "航班类型")
    private String flightType;

    @ApiModelProperty(value = "中转城市")
    private String transitCity;

    @ApiModelProperty(value = "航班占位信息列表")
    private List<PreOccupySegmentInfo> preOccupySegmentInfoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "PreOccupySegmentInfo", description = "航班占位航段信息")
    public static class PreOccupySegmentInfo {
        @ApiModelProperty(value = "航空公司二字码")
        private String airline;

        @ApiModelProperty(value = "航班号")
        private Integer flightNumber;

        @ApiModelProperty(value = "航班后缀")
        private String flightSuffix;

        @ApiModelProperty(value = "舱位等级")
        private String cls;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发机场")
        private String origin;

        @ApiModelProperty(value = "到达机场")
        private String destination;

        @ApiModelProperty(value = "座位数")
        private String seats;

        @ApiModelProperty(value = "操作代码")
        private String action;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "是否联程")
        private Integer married;
    }
}
