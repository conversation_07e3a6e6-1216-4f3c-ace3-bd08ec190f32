package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 计算退票价格请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 15:00
 */
@Data
@ApiModel(value = "计算退票价格请求DTO")
public class BatchFindRefundFeeDto {

    @ApiModelProperty(value = "票号列表")
    private List<String> tktNos;

    @ApiModelProperty(value = "票证类型")
    private String tktType;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;
}
