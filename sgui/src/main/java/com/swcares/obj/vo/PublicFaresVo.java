package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/8 15:51
 */
@Data
@ApiModel(value = "QueryFlightsVo", description = "QueryFlightsVo")
public class PublicFaresVo implements Serializable {

    @ApiModelProperty("")
    private String carrier;

    @ApiModelProperty("")
    private String fbc;

    @ApiModelProperty("")
    private String farePriceOW;

    @ApiModelProperty("")
    private String farePriceRT;

    @ApiModelProperty("")
    private String rbdCode;

    @ApiModelProperty("")
    private String cabin;

    @ApiModelProperty("")
    private String travelTo;

    @ApiModelProperty("")
    private String travelFrom;

    @ApiModelProperty("")
    private String saleDate;

    @ApiModelProperty("")
    private String saleTime;

    @ApiModelProperty("")
    private String ruleID;

    @ApiModelProperty("")
    private String fareRule;

    @ApiModelProperty("")
    private String fareText;

    @ApiModelProperty("")
    private String distance;

}
