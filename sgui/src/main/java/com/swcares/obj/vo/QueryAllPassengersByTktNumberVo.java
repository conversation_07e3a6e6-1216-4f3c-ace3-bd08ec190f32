package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询PNR所有出票旅客信息响应VO
 *
 * <AUTHOR>
 * @date 2025/07/03 17:30
 */
@Data
@ApiModel(value = "查询PNR所有出票旅客信息响应VO")
public class QueryAllPassengersByTktNumberVo {

    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;

    @ApiModelProperty(value = "旅客姓名后缀")
    private String passengerNameSuffix;

    @ApiModelProperty(value = "旅客类型")
    private String passengerType;

    @ApiModelProperty(value = "PNR旅客类型")
    private String pnrPsgType;

    @ApiModelProperty(value = "特殊旅客类型")
    private String specialPassengerType;

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "ET类型")
    private String etType;

    @ApiModelProperty(value = "票务类型代码")
    private String ticketTypeCode;

    @ApiModelProperty(value = "是否政府采购")
    private Boolean governmentPurchase;

    @ApiModelProperty(value = "CRS PNR编号")
    private String crsPnrNo;

    @ApiModelProperty(value = "是否无效")
    private Boolean invalid;

    @ApiModelProperty(value = "是否已废票")
    private Boolean alreadyInvalid;

    @ApiModelProperty(value = "是否BOP支付")
    private Boolean paymentBOP;

    @ApiModelProperty(value = "票务管理机构代码")
    private String ticketManagementOrganizationCode;
}
