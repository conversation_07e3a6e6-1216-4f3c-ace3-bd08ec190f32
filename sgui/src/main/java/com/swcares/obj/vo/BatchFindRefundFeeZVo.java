package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;

/**
 * 计算批量退票费响应VO
 * 直接继承ArrayList，因为响应的data字段就是一个数组
 *
 * <AUTHOR>
 * @date 2025/06/26 15:30
 */
@ApiModel(value = "计算批量退票费响应VO")
public class BatchFindRefundFeeZVo extends ArrayList<BatchFindRefundFeeZVo.RefundFeeResult> {

    /**
     * 退票费计算结果
     */
    @Data
    @ApiModel(value = "退票费计算结果")
    public static class RefundFeeResult {
        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "是否失败")
        private Boolean failure;

        @ApiModelProperty(value = "退票费信息")
        private RefundFeeDTO refundFeeDTO;

        @ApiModelProperty(value = "错误代码")
        private String errorCode;

        @ApiModelProperty(value = "错误描述")
        private String description;

        @ApiModelProperty(value = "SAT事务号")
        private String satTransactionNo;

        @ApiModelProperty(value = "事务号")
        private String transactionNo;
    }

    /**
     * 退票费信息
     */
    @Data
    @ApiModel(value = "退票费信息")
    public static class RefundFeeDTO {
        @ApiModelProperty(value = "代理费")
        private String commissionAmount;

        @ApiModelProperty(value = "代理费率")
        private String commissionRate;

        @ApiModelProperty(value = "其他扣除")
        private String otherDeduction;

        @ApiModelProperty(value = "净退款")
        private String netRefund;

        @ApiModelProperty(value = "货币类型")
        private String currency;
    }
}
