package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除旅客响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15 10:30
 */
@Data
@ApiModel(value = "RemoveNameVo", description = "删除旅客响应VO")
public class RemoveNameVo {

    @ApiModelProperty(value = "操作结果")
    private String data;

    public RemoveNameVo() {
        this.data = "OK";
    }

    public RemoveNameVo(String data) {
        this.data = data;
    }
}
