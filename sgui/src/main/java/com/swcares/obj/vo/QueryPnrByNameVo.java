package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通过姓名查询PNR响应VO
 *
 * <AUTHOR>
 * @date 2025/07/03 14:30
 */
@Data
@ApiModel(value = "通过姓名查询PNR响应VO")
public class QueryPnrByNameVo {

    @ApiModelProperty(value = "查询记录列表")
    private List<PnrRecord> records;

    @ApiModelProperty(value = "PNR查询航班结果列表")
    private Object pnrQueryFlightResList;

    @ApiModelProperty(value = "PNR查询日期结果列表")
    private Object pnrQueryDateResList;

    /**
     * PNR记录
     */
    @Data
    @ApiModel(value = "PNR记录")
    public static class PnrRecord {
        @ApiModelProperty(value = "PNR编号")
        private String pnrNo;

        @ApiModelProperty(value = "PNR是否已取消")
        private Boolean pnrCanceled;

        @ApiModelProperty(value = "旅客姓名列表")
        private List<String> name;

        @ApiModelProperty(value = "航段信息")
        private String segment;

        @ApiModelProperty(value = "舱位")
        private String cabin;

        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "旅客数量")
        private Integer passengerCount;

        @ApiModelProperty(value = "航班号")
        private String flight;

        @ApiModelProperty(value = "起飞日期")
        private String departureDate;

        @ApiModelProperty(value = "OFFICE")
        private String office;

        @ApiModelProperty(value = "创建日期")
        private String createDate;
    }
}
