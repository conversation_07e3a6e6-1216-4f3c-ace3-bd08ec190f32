package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Data
@ApiModel(value = "AirCancelPorVo", description = "删除PNR中添加的航段响应参数")
public class AirCancelPorVo {
    
    @ApiModelProperty(value = "是否成功")
    private Boolean rs;
    
    @ApiModelProperty(value = "是否为空航段")
    private Boolean emptySegment;
}
