package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 生成PNR响应VO
 *
 * <AUTHOR>
 * @date 2025/5/19 14:00
 */
@Data
@ApiModel(value = "BookPnrVo", description = "生成PNR响应VO")
public class BookPnrVo {

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "")
    private String message;

    @ApiModelProperty(value = "")
    private String dishonestPassengers;

    @ApiModelProperty(value = "")
    private Boolean writePriceSuccess;

    @ApiModelProperty(value = "")
    private Boolean segmentMarkSuccess;

    @ApiModelProperty(value = "")
    private Boolean writeCcvSuccess;

    @ApiModelProperty(value = "")
    private Boolean dishonestCheck;
}
