package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 计算退票价格响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 15:00
 */
@Data
@ApiModel(value = "计算退票价格响应VO")
public class BatchFindRefundFeeVo {

    @ApiModelProperty(value = "退票费用查询结果列表")
    private List<QueryRefundFeeAggregateRespDTO> queryRefundFeeAggregateRespDTOList;

    @ApiModelProperty(value = "处理状态")
    private String status;

    @ApiModelProperty(value = "消息")
    private String msg;

    @ApiModelProperty(value = "成功处理的票号列表")
    private List<String> successTicketNos;

    @ApiModelProperty(value = "失败处理的票号列表")
    private List<String> failedTicketNos;

    /**
     * 退票费用查询结果
     */
    @Data
    @ApiModel(value = "退票费用查询结果")
    public static class QueryRefundFeeAggregateRespDTO {
        @ApiModelProperty(value = "联票标识")
        private String conjunction;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "信用卡")
        private String creditCard;

        @ApiModelProperty(value = "支付类型")
        private String payType;

        @ApiModelProperty(value = "货币类型")
        private String currency;

        @ApiModelProperty(value = "航段列表")
        private List<Object> segList;

        @ApiModelProperty(value = "金额信息")
        private Amount amount;

        @ApiModelProperty(value = "消息")
        private String msg;
    }

    /**
     * 金额信息
     */
    @Data
    @ApiModel(value = "金额信息")
    public static class Amount {
        @ApiModelProperty(value = "代理费")
        private String commision;

        @ApiModelProperty(value = "总金额")
        private String totalAmount;

        @ApiModelProperty(value = "代理费率")
        private String commisionRate;

        @ApiModelProperty(value = "净退款")
        private String netRefund;

        @ApiModelProperty(value = "其他扣除")
        private String otherDeduction;

        @ApiModelProperty(value = "税费列表")
        private List<Tax> taxs;

        @ApiModelProperty(value = "总税费")
        private String totalTaxs;

        @ApiModelProperty(value = "票号")
        private String ticketNo;
    }

    /**
     * 税费信息
     */
    @Data
    @ApiModel(value = "税费信息")
    public static class Tax {
        @ApiModelProperty(value = "税费名称")
        private String name;

        @ApiModelProperty(value = "税费金额")
        private String value;
    }
}
