package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 获取税项响应VO
 *
 * <AUTHOR>
 * @date 2025/06/16 15:30
 */
@Data
@ApiModel(value = "获取税项响应VO")
public class QueryRtktDetailVo {

    @ApiModelProperty(value = "佣金率")
    private String commissionRate;

    @ApiModelProperty(value = "佣金")
    private String commission;

    @ApiModelProperty(value = "总税费")
    private String totalTaxes;

    @ApiModelProperty(value = "票面金额")
    private String ticketAmount;

    @ApiModelProperty(value = "货币代码")
    private String currency;

    @ApiModelProperty(value = "税费明细")
    private List<RtKTTax> rtKTTaxes;

    /**
     * 税费明细
     */
    @Data
    @ApiModel(value = "税费明细")
    public static class RtKTTax {
        @ApiModelProperty(value = "税费类型")
        private String taxType;

        @ApiModelProperty(value = "税费金额")
        private String taxAmount;

        @ApiModelProperty(value = "货币代码")
        private String currency;
    }
}
