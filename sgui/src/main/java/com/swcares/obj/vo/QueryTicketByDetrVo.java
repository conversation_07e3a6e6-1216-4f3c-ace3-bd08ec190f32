package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通过DETR查询票面信息VO
 *
 * <AUTHOR>
 * @date 2025/5/31 10:00
 */
@Data
@ApiModel(value = "QueryTicketByDetrVo", description = "通过DETR查询票面信息VO")
public class QueryTicketByDetrVo {

    @ApiModelProperty(value = "凭证信息")
    private Credential credential;

    @ApiModelProperty(value = "票面信息文本")
    private String ticketFareInfoText;

    @ApiModelProperty(value = "开源文本（Base64编码）")
    private String openSourceText;

    @ApiModelProperty(value = "二次筛选条件")
    private SecondFactor secondFactor;

    @ApiModelProperty(value = "票历史文本")
    private String tktHistoryText;

    @Data
    @ApiModel(value = "SecondFactor", description = "二次筛选条件")
    public static class SecondFactor {

        @ApiModelProperty(value = "二次筛选类型")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次筛选值")
        private String secondFactorValue;
    }

    @Data
    @ApiModel(value = "Credential", description = "凭证信息")
    public static class Credential {

        @ApiModelProperty(value = "证件文本（Base64编码）")
        private String certificatesText;

        @ApiModelProperty(value = "证件列表")
        private List<Certificate> certificatesList;
    }

    @Data
    @ApiModel(value = "Certificate", description = "证件信息")
    public static class Certificate {

        @ApiModelProperty(value = "证件类型")
        private String certType;

        @ApiModelProperty(value = "证件号码")
        private String certNumber;

        @ApiModelProperty(value = "加密证件号码")
        private String encryptCertNumber;

        @ApiModelProperty(value = "出生日期")
        private String birthday;
    }
}
