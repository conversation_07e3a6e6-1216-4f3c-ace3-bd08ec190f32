package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Data
@ApiModel(value = "QueryDictEntryVo", description = "查询字典项列表响应参数")
public class QueryDictEntryVo {
    
    @ApiModelProperty(value = "页码")
    private Integer pageNumber;
    
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
    
    @ApiModelProperty(value = "总页数")
    private Integer totalPages;
    
    @ApiModelProperty(value = "总记录数")
    private Integer totalElements;
    
    @ApiModelProperty(value = "字典项列表")
    private List<DictEntry> content;
    
    @Data
    @ApiModel(value = "DictEntry", description = "字典项")
    public static class DictEntry {
        @ApiModelProperty(value = "ID")
        private Long id;
        
        @ApiModelProperty(value = "字典类型ID")
        private Long dictTypeId;
        
        @ApiModelProperty(value = "字典类型编码")
        private String dictTypeCode;
        
        @ApiModelProperty(value = "字典编码")
        private String dictCode;
        
        @ApiModelProperty(value = "字典名称")
        private String dictName;
        
        @ApiModelProperty(value = "状态")
        private Integer status;
        
        @ApiModelProperty(value = "序号")
        private Integer seq;
        
        @ApiModelProperty(value = "字典级别")
        private Integer dictLevel;
        
        @ApiModelProperty(value = "路径")
        private String path;
        
        @ApiModelProperty(value = "过滤条件1")
        private String filter1;
        
        @ApiModelProperty(value = "过滤条件2")
        private String filter2;
        
        @ApiModelProperty(value = "父ID")
        private Long parentId;
        
        @ApiModelProperty(value = "字段1")
        private String field1;
        
        @ApiModelProperty(value = "字段2")
        private String field2;
        
        @ApiModelProperty(value = "字段3")
        private String field3;
        
        @ApiModelProperty(value = "字段4")
        private String field4;
        
        @ApiModelProperty(value = "字典英文名称")
        private String dictEnName;
        
        @ApiModelProperty(value = "字段1英文名称")
        private String field1EnName;
        
        @ApiModelProperty(value = "字典类型名称")
        private String dictTypeName;
        
        @ApiModelProperty(value = "字典类型英文名称")
        private String dictTypeEnName;
    }
}
