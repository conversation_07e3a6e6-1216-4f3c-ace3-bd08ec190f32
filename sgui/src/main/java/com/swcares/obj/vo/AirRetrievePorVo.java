package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Data
@ApiModel(value = "AirRetrievePorVo", description = "航班POR检索响应对象")
public class AirRetrievePorVo {

    @ApiModelProperty(value = "POR ID")
    private String porID;

    @ApiModelProperty(value = "航班列表")
    private List<Flight> flights;

    @Data
    @ApiModel(value = "Flight", description = "航班信息")
    public static class Flight {
        
        @ApiModelProperty(value = "到达城市")
        private City arriveCity;
        
        @ApiModelProperty(value = "出发城市")
        private City departureCity;
        
        @ApiModelProperty(value = "日期")
        private Date date;
        
        @ApiModelProperty(value = "航段列表")
        private List<Segment> segments;
        
        @ApiModelProperty(value = "票证类型")
        private String tktType;
    }

    @Data
    @ApiModel(value = "City", description = "城市信息")
    public static class City {
        
        @ApiModelProperty(value = "城市代码")
        private String code;
        
        @ApiModelProperty(value = "城市名称")
        private String name;
    }

    @Data
    @ApiModel(value = "Date", description = "日期信息")
    public static class Date {
        
        @ApiModelProperty(value = "日期值")
        private String value;
    }

    @Data
    @ApiModel(value = "Segment", description = "航段信息")
    public static class Segment {
        
        @ApiModelProperty(value = "出发航站楼")
        private String departureTerminal;
        
        @ApiModelProperty(value = "到达机场中文名")
        private String arrivalAirportCN;
        
        @ApiModelProperty(value = "经停城市")
        private String stopCity;
        
        @ApiModelProperty(value = "出发机场中文名")
        private String departureAirportCN;
        
        @ApiModelProperty(value = "出发日期时间")
        private String departureDate;
        
        @ApiModelProperty(value = "到达机场代码")
        private String arrivalAirportCode;
        
        @ApiModelProperty(value = "到达航站楼")
        private String arrivalTerminal;
        
        @ApiModelProperty(value = "出发机场代码")
        private String departureAirportCode;
        
        @ApiModelProperty(value = "飞行时间")
        private String flightTime;
        
        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;
        
        @ApiModelProperty(value = "到达日期时间")
        private String arrivalDate;
        
        @ApiModelProperty(value = "出发时间")
        private String departureTime;
        
        @ApiModelProperty(value = "连接级别")
        private String connectLevel;
        
        @ApiModelProperty(value = "ASR")
        private String asr;
        
        @ApiModelProperty(value = "到达天数")
        private String arrDays;
        
        @ApiModelProperty(value = "飞行距离")
        private String flightDistance;
        
        @ApiModelProperty(value = "票号")
        private String tktNum;
        
        @ApiModelProperty(value = "行索引")
        private String lineIndex;
        
        @ApiModelProperty(value = "舱位列表")
        private List<Cabin> cabins;
        
        @ApiModelProperty(value = "航空公司信息")
        private Airlines airlines;
        
        @ApiModelProperty(value = "航段类型")
        private String segmentType;
        
        @ApiModelProperty(value = "关联航段数量")
        private Integer marriedSegmentNumber;
        
        @ApiModelProperty(value = "座位标签")
        private String seatTag;
    }

    @Data
    @ApiModel(value = "Cabin", description = "舱位信息")
    public static class Cabin {
        
        @ApiModelProperty(value = "状态")
        private String state;
        
        @ApiModelProperty(value = "是否开放")
        private Boolean on;
        
        @ApiModelProperty(value = "舱位名称")
        private String cabinName;
        
        @ApiModelProperty(value = "子舱位名称")
        private String subCabinName;
    }

    @Data
    @ApiModel(value = "Airlines", description = "航空公司信息")
    public static class Airlines {
        
        @ApiModelProperty(value = "航空公司代码")
        private String airCode;
        
        @ApiModelProperty(value = "航空公司中文名")
        private String airCN;
        
        @ApiModelProperty(value = "航空部门通用信息")
        private AviationDepartmentGeneral aviationDepartmentGeneral;
        
        @ApiModelProperty(value = "航班号")
        private String flightNo;
        
        @ApiModelProperty(value = "机型")
        private String planeType;
        
        @ApiModelProperty(value = "是否共享")
        private String isShared;
        
        @ApiModelProperty(value = "联盟")
        private String alliance;
    }

    @Data
    @ApiModel(value = "AviationDepartmentGeneral", description = "航空部门通用信息")
    public static class AviationDepartmentGeneral {
        
        @ApiModelProperty(value = "航空公司代码")
        private String airlineCode;
        
        @ApiModelProperty(value = "内容URL")
        private String contentUrl;
    }
}
