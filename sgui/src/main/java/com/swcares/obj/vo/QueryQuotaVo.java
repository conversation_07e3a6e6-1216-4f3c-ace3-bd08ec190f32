package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 航班特殊服务配额信息
 *
 * <AUTHOR>
 * @date 2025/5/15 11:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryQuotaVo", description = "航班特殊服务配额信息")
public class QueryQuotaVo implements Serializable {

    @ApiModelProperty(value = "特殊服务选项")
    private String option;

    @ApiModelProperty(value = "配额数量")
    private Integer quotaNumber;
}
