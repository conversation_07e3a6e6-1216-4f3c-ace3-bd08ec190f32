package com.swcares.core.constants;

/**
 * 缓存常量类
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
public class CacheConstants {
    
    /**
     * 用户偏好设置缓存前缀
     */
    public static final String USER_PREFERENCE_PREFIX = "USER_PREFERENCE:";
    
    /**
     * 用户偏好设置航司CTCT缓存前缀
     */
    public static final String USER_PREFERENCE_AIRLINE_CTCT_PREFIX = "USER_PREFERENCE_AIRLINE_CTCT:";
    public static final String USER_PREFERENCE_CHECK_TIP_PREFIX = "USER_PREFERENCE_CHECK_TIP:";
    public static final String USER_PREFERENCE_PASSENGER_INFO_PREFIX = "USER_PREFERENCE_PASSENGER_INFO:";
    public static final String USER_PREFERENCE_REMARK_PREFIX = "USER_PREFERENCE_REMARK:";

    /**
     * 用户偏好设置缓存过期时间（小时）
     */
    public static final int USER_PREFERENCE_EXPIRE_HOURS = 24;
    
    /**
     * 空值缓存过期时间（分钟）- 用于防止缓存穿透
     */
    public static final int EMPTY_VALUE_EXPIRE_MINUTES = 10;
    
    /**
     * 空值占位符
     */
    public static final String EMPTY_VALUE = "EMPTY";
}
