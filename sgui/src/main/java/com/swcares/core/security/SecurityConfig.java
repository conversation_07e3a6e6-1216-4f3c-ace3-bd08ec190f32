package com.swcares.core.security;

import com.swcares.core.security.bypass.SecurityAccessDeniedHandler;
import com.swcares.core.security.bypass.SecurityAuthenticationEntryPoint;
import com.swcares.core.security.custom.JwtAuthenticationFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

/**
 * 重写WebSecurityConfigurerAdapter
 * WebSecurity作为一个建造者就是用来创建核心过滤器FilterChainProxy实例的。
 * WebSecurity在初始化的时候会扫描WebSecurityConfigurerAdapter配置器适配器的子类（即生成HttpSecurity配置器）。
 *
 * <AUTHOR>
 */
@Slf4j
@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${sgui.allow.origin}")
    private String sguiAllowOrigin;

    @Resource
    private UserDetailsService userDetailsService;

    @Resource
    private Md5PasswordEncoder md5PasswordEncoder;

    @Resource
    private SecurityAuthenticationEntryPoint securityAuthenticationEntryPoint;

    @Resource
    private SecurityAccessDeniedHandler securityAccessDeniedHandler;

    @Resource
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 这个主要就是配置数据是从哪里来的，可能是内存，或者使用继承框架的某个接口
     *
     * @param auth 配置对象
     * @throws Exception 异常
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        // 设置UserDetailsService
        auth.userDetailsService(userDetailsService)
                // 设置加密方式
                .passwordEncoder(md5PasswordEncoder);
    }

    /**
     * 配置跨域
     *
     * @return
     */
    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // 允许跨域访问的主机
        log.info("=====获取到的sguiAllowOrigin====={}====", sguiAllowOrigin);
        configuration.setAllowedOrigins(Collections.singletonList(sguiAllowOrigin));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTION"));
        configuration.setAllowedHeaders(Collections.singletonList("*"));
        configuration.addExposedHeader("X-Authenticate");
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * 程序启动时就执行该方法
     * 设置http请求的一些规则：
     * 配置拦截的地方
     *
     * @param http http请求对象
     * @throws Exception 异常
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 关闭csrf（跨站请求）和frameOptions，如果不关闭会影响前端请求接口
        http.csrf().disable();
        http.headers()
                // 防止iframe 造成跨域
                .frameOptions()
                .disable()
                .and()
                .headers()
                .addHeaderWriter((httpServletRequest, httpServletResponse) -> httpServletResponse.setHeader("Access-Control-Allow-Origin", sguiAllowOrigin))
                .and()
                // 禁用缓存
                .headers()
                .cacheControl();
        // 开启跨域以便前端调用接口
        http.cors().configurationSource(corsConfigurationSource());
        // 这是配置的关键，决定哪些接口开启防护，哪些接口绕过防护
        http.authorizeRequests()
                // 注意这里，是允许前端跨域联调的一个必要配置
                .requestMatchers(CorsUtils::isPreFlightRequest).permitAll()
                // 指定某些接口不需要通过验证即可访问。像登陆、注册接口肯定是不需要认证的
                .antMatchers("/sgui-bkc/login", "/sgui-bkc/verifycode/**", "/sgui-bkc/auth/**", "/sgui-bkc/logout", "/sgui-bkc/v2/crs/system/query/domesticAgentsProhibitBrowsers").permitAll()
                // 方便后面写前后端分离的时候前端过来的第一次验证请求，这样做，会减少这种请求的时间和资源使用
                .antMatchers(HttpMethod.OPTIONS, "/**").anonymous()
                // 这里意思是其它所有接口需要认证才能访问
                .anyRequest().authenticated();
//                .anyRequest().permitAll();
        // 指定认证错误处理器
        http.exceptionHandling()
                // 用户未登录（匿名用户访问无权限的资源时的异常）
                .authenticationEntryPoint(securityAuthenticationEntryPoint)
                // 权限不足
                .accessDeniedHandler(securityAccessDeniedHandler);
        // 禁用session 本项目使用的是jwt
        http.sessionManagement()
                // 会话跟踪方式
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }

    /**
     * 用于访问一些静态的东西控制。其中ignoring()方法可以让访问跳过filter验证。
     *
     * @param web web
     */
    @Override
    public void configure(WebSecurity web) {
        web.ignoring()
                .antMatchers("/swagger-ui/**")
                .antMatchers("/swagger-resources/**")
                .antMatchers("/v3/**")
        ;
    }
}
