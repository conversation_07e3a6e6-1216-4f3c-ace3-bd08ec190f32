package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.vo.RuleVo;
import com.swcares.service.bkc.IComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:41
 */
@Slf4j
@Api(tags = "component接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/component")
public class ComponentController {

    @Resource
    private IComponentService iComponentService;

    @ApiOperation(value = "rules", notes = "rules")
    @GetMapping("/rules")
    public SguiResult rules() {
        List<RuleVo> ruleVoList = iComponentService.rules();
        return SguiResult.ok(null, ruleVoList);
    }
}
