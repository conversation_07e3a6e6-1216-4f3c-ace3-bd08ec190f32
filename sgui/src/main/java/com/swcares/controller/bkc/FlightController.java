package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.dto.AvDto;
import com.swcares.obj.vo.AvVo;
import com.swcares.service.bkc.IFlightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:12
 */
@Slf4j
@Api(tags = "航班接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-bkc/flight")
public class FlightController {

    @Resource
    private IFlightService iFlightService;

    @ApiOperation(value = "av查询", notes = "av查询航班的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "avDto", required = true, dataTypeClass = AvDto.class)
    })
    @PostMapping("/av")
    public UnifiedResult av(@RequestBody AvDto avDto) throws UnifiedResultException {
        List<AvVo> avVoList = iFlightService.av(avDto);
        return UnifiedResult.ok("查询av成功", avVoList);
    }
}
