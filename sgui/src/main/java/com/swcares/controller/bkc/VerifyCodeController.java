package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.service.bkc.IVerifyCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/4/16 11:09
 */
@Slf4j
@Api(tags = "验证码接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/verifycode")
public class VerifyCodeController {

    @Resource
    private IVerifyCodeService iVerifyCodeService;

    @ApiOperation(value = "验证码", notes = "获取验证码的接口")
    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response, @RequestParam double rdm) throws IOException {
        iVerifyCodeService.generateCaptcha(request, response, rdm);
    }
}
