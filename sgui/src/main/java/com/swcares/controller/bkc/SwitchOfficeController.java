package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.SwitchOfficeUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:14
 */
@Slf4j
@Api(tags = "SwitchOffice接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/switchOffice")
public class SwitchOfficeController {

    @ApiOperation(value = "searchTip", notes = "searchTip的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = SwitchOfficeUpdateDto.class)
    })
    @PostMapping("/update")
    public SguiResult update(@RequestBody SwitchOfficeUpdateDto dto) {
        return SguiResult.ok("切换成功", dto.getOffice());
    }
}
