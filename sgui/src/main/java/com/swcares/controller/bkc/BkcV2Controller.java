package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.bkc.IBkcV2Service;
import com.swcares.service.bkc.IUserPreferenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:33
 */
@Slf4j
@Api(tags = "V2接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-bkc/v2")
public class BkcV2Controller {

    @Resource
    private IBkcV2Service IBkcV2Service;

    @Resource
    private IUserPreferenceService iUserPreferenceService;

    @ApiOperation(value = "tokenNew", notes = "tokenNew的接口")
    @GetMapping("/crs/businessDictionary/userShuntSwitchIsOpen")
    public SguiResult userShuntSwitchIsOpen() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "checkVersion", notes = "checkVersion的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = CheckVersionDto.class)
    })
    @PostMapping("/ald/versionControlStrategy/checkVersion")
    public SguiResult checkVersion(@RequestBody CheckVersionDto dto) {
        CheckVersionVo vo = IBkcV2Service.checkVersion(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "frontLogMonitoring", notes = "frontLogMonitoring的接口")
    @GetMapping("/crs/system/query/frontLogMonitoring")
    public SguiResult frontLogMonitoring() {
        return SguiResult.ok(null, false);
    }

    @ApiOperation(value = "preferenceQuery", notes = "preferenceQuery的接口")
    @PostMapping("/preference/query")
    public SguiResult preferenceQuery() throws SguiResultException {
        UserPreferenceFullVo vo = iUserPreferenceService.getCurrentUserPreference();
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "preferenceUpdate", notes = "preferenceUpdate的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = UserPreferenceFullDto.class)
    })
    @PostMapping("/preference/update")
    public SguiResult preferenceUpdate(@RequestBody UserPreferenceFullDto dto) throws SguiResultException {
        iUserPreferenceService.updateCurrentUserPreference(dto);
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "domesticAgentsProhibitBrowsers", notes = "domesticAgentsProhibitBrowsers的接口")
    @GetMapping("/crs/system/query/domesticAgentsProhibitBrowsers")
    public SguiResult domesticAgentsProhibitBrowsers() throws SguiResultException {
        return SguiResult.ok(null, false);
    }

    @ApiOperation(value = "domesticairline", notes = "domesticairline")
    @GetMapping("/passenger/config/domesticairline")
    public SguiResult domesticairline() throws SguiResultException {
        List<String> resList = IBkcV2Service.domesticairline();
        return SguiResult.ok(null, resList);
    }

    @ApiOperation(value = "queryDftBalance", notes = "queryDftBalance（忽略）")
    @PostMapping("/crs/pnr/queryDftBalance")
    public SguiResult queryDftBalance() throws SguiResultException {
        return SguiResult.ok(null, null);
    }

    @ApiOperation(value = "queryPnrDetail", notes = "查询PNR详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPnrDetailDto.class)
    })
    @PostMapping("/query/pnr/detail")
    public SguiResult queryPnrDetail(@RequestBody QueryPnrDetailDto dto) throws SguiResultException {
        QueryPnrDetailVo vo = IBkcV2Service.queryPnrDetail(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryPnrHistory", notes = "查询PNR历史")
    @GetMapping("/crs/query/pnr/history")
    public SguiResult queryPnrHistory(@RequestParam String pnrNo) throws SguiResultException {
        PnrHistoryVo vo = IBkcV2Service.queryPnrHistory(pnrNo);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryPnrRtl", notes = "查询航司PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPnrRtlDto.class)
    })
    @PostMapping("/query/pnr/rtl")
    public SguiResult queryPnrRtl(@RequestBody QueryPnrRtlDto dto) throws SguiResultException {
        QueryPnrRtlVo vo = IBkcV2Service.queryPnrRtl(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "strongRefresh", notes = "强制刷新PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = StrongRefreshDto.class)
    })
    @PostMapping("/crs/pnr/strongRefresh")
    public SguiResult strongRefresh(@RequestBody StrongRefreshDto dto) throws SguiResultException {
        IBkcV2Service.strongRefresh(dto);
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "updatePnr", notes = "更新PNR（封口）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reqDto", required = true, dataTypeClass = UpdatePnrReqDto.class)
    })
    @PostMapping("/pnr/update")
    public SguiResult updatePnr(@RequestBody UpdatePnrReqDto reqDto) throws SguiResultException {
        UpdatePnrVo vo = IBkcV2Service.updatePnr(reqDto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "querySeatMap", notes = "查询预选座位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QuerySeatMapDto.class)
    })
    @PostMapping("/passenger/seat/querySeatMap")
    public SguiResult querySeatMap(@RequestBody QuerySeatMapDto dto) throws SguiResultException {
        Object result = IBkcV2Service.querySeatMap(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "issueTicket", notes = "出票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = IssueTicketDto.class)
    })
    @PostMapping("/crs/pnr/issue")
    public SguiResult issueTicket(@RequestBody IssueTicketDto dto) throws SguiResultException {
        IssueTicketVo vo = IBkcV2Service.issueTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "removeName", notes = "删除旅客")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = RemoveNameDto.class)
    })
    @PostMapping("/passenger/removname")
    public SguiResult removeName(@RequestBody RemoveNameDto dto) throws SguiResultException {
        IBkcV2Service.removeName(dto);
        return SguiResult.ok(null, "OK");
    }

    @ApiOperation(value = "splitPnrByPassenger", notes = "分离旅客")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = SplitPnrByPassengerDto.class)
    })
    @PostMapping("/passenger/splitPnrByPassenger")
    public SguiResult splitPnrByPassenger(@RequestBody SplitPnrByPassengerDto dto) throws SguiResultException {
        SplitPnrByPassengerVo vo = IBkcV2Service.splitPnrByPassenger(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "xePnr", notes = "取消PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = XePnrDto.class)
    })
    @PostMapping("/crs/pnr/xePnr")
    public SguiResult xePnr(@RequestBody XePnrDto dto) throws SguiResultException {
        String result = IBkcV2Service.xePnr(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "structuredPreview", notes = "预览票面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = StructuredPreviewDto.class)
    })
    @PostMapping("/crs/issue/structured/preview")
    public SguiResult structuredPreview(@RequestBody StructuredPreviewDto dto) throws SguiResultException {
        List<StructuredPreviewVo> result = IBkcV2Service.structuredPreview(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "queryPnrByName", notes = "通过姓名查询PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPnrByNameDto.class)
    })
    @PostMapping("/crs/pnr/query/name")
    public SguiResult queryPnrByName(@RequestBody QueryPnrByNameDto dto) throws SguiResultException {
        QueryPnrByNameVo vo = IBkcV2Service.queryPnrByName(dto);
        return SguiResult.ok(null, vo);
    }
}
