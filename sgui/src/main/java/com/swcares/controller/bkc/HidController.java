package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.core.unified.UnifiedResultException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:30
 */
@Slf4j
@Api(tags = "HID接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-bkc/hid")
public class HidController {

    @ApiOperation(value = "tokenNew", notes = "tokenNew的接口")
    @PostMapping("/token/new")
    public SguiResult tokenNew() throws UnifiedResultException {
        return SguiResult.ok(null, "HID-49041-33cc89c4-8900-466c-9903-93191545b2c1");
    }
}
