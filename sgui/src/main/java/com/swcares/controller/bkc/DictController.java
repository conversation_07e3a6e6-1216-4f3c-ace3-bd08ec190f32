package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.QueryDictEntryDto;
import com.swcares.obj.vo.QueryDictEntryVo;
import com.swcares.service.bkc.IDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Slf4j
@Api(tags = "字典接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/dict")
public class DictController {

    @Resource
    private IDictService iDictService;

    @ApiOperation(value = "queryDictEntryList", notes = "查询字典项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryDictEntryDto.class)
    })
    @PostMapping("/queryDictEntryList")
    public SguiResult queryDictEntryList(@RequestBody QueryDictEntryDto dto) {
        QueryDictEntryVo vo = iDictService.queryDictEntryList(dto);
        return SguiResult.ok(null, vo);
    }
}
