package com.swcares.service.et.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.entity.SguiData;
import com.swcares.obj.vo.HotCitiesVo;
import com.swcares.service.ISguiDataService;
import com.swcares.service.et.ICommonConfigurationService;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 13:59
 */
@Service
public class CommonConfigurationServiceServiceImpl implements ICommonConfigurationService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public List<HotCitiesVo> hotCities(HttpServletResponse response) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "hot_cities")
                .one();
        String jsonValue = data.getValue();
        ResponseCookie eff4Cookie = ResponseCookie.from("08496402a72148d015c9d57eebb03a88", "16fb827e9dde51d4ea846687aeae6ba2")
                // 启用HttpOnly
                .httpOnly(true)
                // 全局生效路径
                .path("/")
                .build();
        // 添加Cookie到响应头
        response.addHeader("Set-Cookie", eff4Cookie.toString());
        return JSONUtil.toList(JSONUtil.parseArray(jsonValue), HotCitiesVo.class);
    }
}
