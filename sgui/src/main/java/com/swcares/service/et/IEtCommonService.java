package com.swcares.service.et;

import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPlanFlight;
import com.swcares.entity.MnjxPlanSection;

import java.util.List;

/**
 * 航班查询公共服务接口
 * 只包含SK和AV共同使用的方法
 *
 * <AUTHOR>
 * @date 2025/5/8 13:51
 */
public interface IEtCommonService {

    /**
     * 获取飞行距离
     *
     * @param depCityId 出发城市ID
     * @param arrCityId 到达城市ID
     * @return 飞行距离，如果没有找到则返回null
     */
    Integer getFlightDistance(String depCityId, String arrCityId);

    /**
     * 获取开舱数据
     *
     * @param planSections  计划航节
     * @param orgs          出发机场
     * @param dsts          目的机场
     * @param openCabinList 所有开舱数据
     * @return 获取条件航段的所有开舱数据
     */
    List<MnjxOpenCabin> getOpenCabins(List<MnjxPlanSection> planSections, List<String> orgs, List<String> dsts, List<MnjxOpenCabin> openCabinList);

    List<MnjxPlanFlight> retrievePlanFlight(String date, List<String> tcardIds);

    List<MnjxPlanSection> retrievePlanSection(String time, List<String> planFlightIdList);
}