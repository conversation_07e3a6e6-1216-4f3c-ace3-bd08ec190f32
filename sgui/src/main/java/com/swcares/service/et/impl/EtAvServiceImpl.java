package com.swcares.service.et.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.FlightCacheConstants;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.*;
import com.swcares.mapper.EtV2Mapper;
import com.swcares.obj.dto.QueryFlightsDto;
import com.swcares.obj.dto.QueryMultiInfosDto;
import com.swcares.obj.vo.QueryFlightsVo;
import com.swcares.obj.vo.QueryMultiInfosVo;
import com.swcares.service.*;
import com.swcares.service.et.IEtAvService;
import com.swcares.service.et.IEtCommonService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AV航班查询服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/8 13:51
 */
@Service
public class EtAvServiceImpl implements IEtAvService {

    // 舱等排序
    private static final Map<String, Integer> CABIN_ORDER = new HashMap<>();

    static {
        CABIN_ORDER.put("J", 1); // 头等舱
        CABIN_ORDER.put("G", 2); // 公务舱
        CABIN_ORDER.put("Y", 3); // 经济舱
    }

    @Resource
    private EtV2Mapper etV2Mapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IEtCommonService iEtCommonService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Override
    public QueryMultiInfosVo queryMultiInfos(QueryMultiInfosDto dto) throws SguiResultException {
        // 参数校验
        if (dto.getAirlines() == null || dto.getAirlines().isEmpty()) {
            throw new SguiResultException("航空公司信息不能为空");
        }
        if (StrUtil.isEmpty(dto.getDepartureStart())) {
            throw new SguiResultException("出发开始日期不能为空");
        }
        if (StrUtil.isEmpty(dto.getDepartureEnd())) {
            throw new SguiResultException("出发结束日期不能为空");
        }

        // 验证日期格式
        try {
            Date startDate = DateUtil.parseDate(dto.getDepartureStart());
            Date endDate = DateUtil.parseDate(dto.getDepartureEnd());

            // 验证开始日期不能晚于结束日期
            if (startDate.after(endDate)) {
                throw new SguiResultException("出发开始日期不能晚于出发结束日期");
            }
        } catch (Exception e) {
            throw new SguiResultException("日期格式错误");
        }

        // 创建返回对象
        QueryMultiInfosVo result = new QueryMultiInfosVo();
        List<QueryMultiInfosVo.FlightInfo> flightInfoList = new ArrayList<>();
        result.setFlightInfoList(flightInfoList);
        result.setSessionId(null);

        // 计算日期范围内的所有日期
        List<String> dateRange = this.getDateRange(dto.getDepartureStart(), dto.getDepartureEnd());

        // 遍历航空公司信息列表
        for (QueryMultiInfosDto.AirlineInfo airlineInfo : dto.getAirlines()) {
            // 验证航空公司信息
            if (StrUtil.isEmpty(airlineInfo.getAirCode())) {
                throw new SguiResultException("航空公司代码不能为空");
            }
            if (StrUtil.isEmpty(airlineInfo.getFlightNo())) {
                throw new SguiResultException("航班号不能为空");
            }
            if (StrUtil.isEmpty(airlineInfo.getDepartureCity())) {
                throw new SguiResultException("出发城市不能为空");
            }
            if (StrUtil.isEmpty(airlineInfo.getArrivalCity())) {
                throw new SguiResultException("到达城市不能为空");
            }

            // 使用开始日期调用queryByFlightNo方法
            QueryFlightsDto flightsDto = new QueryFlightsDto();
            flightsDto.setFlightNo(airlineInfo.getFlightNo());
            flightsDto.setDepartureDate(dto.getDepartureStart());
            flightsDto.setDepartureCity(airlineInfo.getDepartureCity());
            flightsDto.setArriveCity(airlineInfo.getArrivalCity());

            // 调用queryByFlightNo方法获取航班信息
            QueryFlightsVo flightsVo = this.queryByFlightNo(flightsDto);

            // 如果没有找到航班信息，跳过当前循环
            if (flightsVo == null || CollUtil.isEmpty(flightsVo.getFlightInfoList())) {
                continue;
            }

            // 将QueryFlightsVo转换为QueryMultiInfosVo
            for (QueryFlightsVo.FlightInfo flightInfo : flightsVo.getFlightInfoList()) {
                // 创建新的FlightInfo对象
                QueryMultiInfosVo.FlightInfo newFlightInfo = new QueryMultiInfosVo.FlightInfo();
                newFlightInfo.setTransitCities(flightInfo.getTransitCities());
                newFlightInfo.setTotalTime(flightInfo.getTotalTime());
                newFlightInfo.setStopCity(flightInfo.getStopCity());
                newFlightInfo.setIndex(dto.getIndex());
                newFlightInfo.setPkId(dto.getIndex());
                newFlightInfo.setTktType(flightInfo.getTktType());

                // 创建新的Segment列表
                List<QueryMultiInfosVo.Segment> newSegments = new ArrayList<>();
                newFlightInfo.setSegments(newSegments);

                // 处理每个航段
                for (int i = 0; i < flightInfo.getSegments().size(); i++) {
                    QueryFlightsVo.Segment segment = flightInfo.getSegments().get(i);

                    // 创建新的Segment对象
                    QueryMultiInfosVo.Segment newSegment = new QueryMultiInfosVo.Segment();
                    newSegment.setArrDays(segment.getArrDays());
                    newSegment.setDepartureAirportCN(segment.getDepartureAirportCN());
                    newSegment.setStopCity(segment.getStopCity());
                    newSegment.setDepartureTerminal(segment.getDepartureTerminal());
                    newSegment.setArrivalAirportCN(segment.getArrivalAirportCN());
                    newSegment.setDepartureAirportCode(segment.getDepartureAirportCode());
                    newSegment.setArrivalAirportCode(segment.getArrivalAirportCode());
                    // LocalTime转String
                    newSegment.setDepartureTime(segment.getDepartureTime() != null ? segment.getDepartureTime().toString() : null);
                    newSegment.setArrivalTerminal(segment.getArrivalTerminal());
                    // LocalTime转String
                    newSegment.setArrivalTime(segment.getArrivalTime() != null ? segment.getArrivalTime().toString() : null);
                    // LocalDateTime转String
                    newSegment.setDepartureDate(segment.getDepartureDate() != null ? segment.getDepartureDate().toString() : null);
                    // LocalDateTime转String
                    newSegment.setArrivalDate(segment.getArrivalDate() != null ? segment.getArrivalDate().toString() : null);
                    newSegment.setDeptArrdays(segment.getDeptArrdays());
                    newSegment.setFlightTime(segment.getFlightTime());
                    // Integer转String
                    newSegment.setFlightDistance(segment.getFlightDistance() != null ? segment.getFlightDistance().toString() : null);
                    newSegment.setArrivalArrdays(segment.getArrivalArrdays());
                    newSegment.setAsr(segment.getAsr());
                    newSegment.setConnectLevel(segment.getConnectLevel());
                    newSegment.setTcFlight(segment.getTcFlight());
                    newSegment.setEtInd(segment.getEtInd());
                    newSegment.setCommonMeal(segment.getCommonMeal());
                    newSegment.setGroundTime(segment.getGroundTime());
                    newSegment.setFare(segment.getFare());
                    newSegment.setFareClass(segment.getFareClass());
                    newSegment.setDepartureTimezone(segment.getDepartureTimezone());
                    newSegment.setArrivalTimezone(segment.getArrivalTimezone());
                    newSegment.setAlliance(segment.getAlliance());

                    // 创建新的Airline对象
                    QueryMultiInfosVo.Airline newAirline = new QueryMultiInfosVo.Airline();
                    newAirline.setAirCN(segment.getAirlines().getAirCN());
                    newAirline.setAirCode(segment.getAirlines().getAirCode());
                    newAirline.setPlaneType(segment.getAirlines().getPlaneType());
                    newAirline.setIsShared(segment.getAirlines().getIsShared());
                    newAirline.setFlightNo(segment.getAirlines().getFlightNo());

                    // 处理airService
                    List<QueryMultiInfosVo.AirService> airServiceList = new ArrayList<>();
                    if (segment.getAirlines().getAirService() != null) {
                        for (QueryFlightsVo.AirService airService : segment.getAirlines().getAirService()) {
                            if (airService != null) {
                                QueryMultiInfosVo.AirService newAirService = new QueryMultiInfosVo.AirService();
                                newAirService.setCode(airService.getCode());
                                newAirService.setDescription(airService.getDescription());
                                airServiceList.add(newAirService);
                            }
                        }
                    }
                    newAirline.setAirService(airServiceList);

                    newAirline.setAlliance(segment.getAirlines().getAlliance());

                    // 创建新的AviationDepartmentGeneral对象
                    if (segment.getAirlines().getAviationDepartmentGeneral() != null) {
                        QueryMultiInfosVo.AviationDepartmentGeneral newAviationDepartmentGeneral = new QueryMultiInfosVo.AviationDepartmentGeneral();
                        newAviationDepartmentGeneral.setAirlineCode(segment.getAirlines().getAviationDepartmentGeneral().getAirlineCode());
                        newAviationDepartmentGeneral.setContentUrl(segment.getAirlines().getAviationDepartmentGeneral().getContentUrl());
                        newAirline.setAviationDepartmentGeneral(newAviationDepartmentGeneral);
                    }

                    newSegment.setAirlines(newAirline);

                    // 处理舱位信息
                    List<QueryMultiInfosVo.CabinInfo> newCabins = new ArrayList<>();
                    newSegment.setCabins(newCabins);

                    // 判断是否是经停航班的第二段
                    boolean isSecondSegment = (i > 0);

                    if (isSecondSegment) {
                        // 经停航班的第二段，只设置state和cabinName，不设置dateTime和cabinNos
                        for (QueryFlightsVo.Cabin cabin : segment.getCabins()) {
                            QueryMultiInfosVo.CabinInfo newCabinInfo = new QueryMultiInfosVo.CabinInfo();
                            newCabinInfo.setState(cabin.getState());
                            newCabinInfo.setCabinName(cabin.getCabinName());
                            newCabinInfo.setDateTime(null);
                            newCabinInfo.setCabinNos(null);
                            newCabins.add(newCabinInfo);
                        }
                    } else {
                        // 非经停航班或经停航班的第一段，需要为每个日期创建CabinInfo
                        for (String date : dateRange) {
                            QueryMultiInfosVo.CabinInfo newCabinInfo = new QueryMultiInfosVo.CabinInfo();
                            newCabinInfo.setDateTime(date);

                            // 创建舱位号列表
                            List<QueryMultiInfosVo.CabinNo> newCabinNos = new ArrayList<>();
                            newCabinInfo.setCabinNos(newCabinNos);

                            // 复制舱位信息
                            for (QueryFlightsVo.Cabin cabin : segment.getCabins()) {
                                QueryMultiInfosVo.CabinNo newCabinNo = new QueryMultiInfosVo.CabinNo();
                                newCabinNo.setState(cabin.getState());
                                newCabinNo.setCabinName(cabin.getCabinName());
                                newCabinNos.add(newCabinNo);
                            }

                            newCabins.add(newCabinInfo);
                        }
                    }

                    // 添加航段到航班信息
                    newSegments.add(newSegment);
                }

                // 添加航班信息到结果列表
                flightInfoList.add(newFlightInfo);
            }
        }

        return result;
    }

    /**
     * 获取日期范围内的所有日期
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    private List<String> getDateRange(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        // 计算日期范围内的所有日期
        long daysBetween = ChronoUnit.DAYS.between(start, end);

        for (int i = 0; i <= daysBetween; i++) {
            LocalDate date = start.plusDays(i);
            dateList.add(date.toString());
        }

        return dateList;
    }

    @Override
    public QueryFlightsVo queryFlights(QueryFlightsDto dto) throws SguiResultException {

        this.validateQueryFlightsDto(dto);
        if (ReUtil.isMatch("[A-Za-z]{3}\\s[A-Za-z]{3}\\s\\d{2}\\s\\d{2}:\\d{2}:\\d{2}\\s[A-Z]{3}\\s\\d{4}", dto.getDepartureDate())) {
            DateTime dateTime = DateUtil.parse(dto.getDepartureDate(),
                    "EEE MMM dd HH:mm:ss zzz yyyy",
                    Locale.ENGLISH);
            // 转换为 yyyy-MM-dd 格式
            String departureDate = DateUtil.format(dateTime, "yyyy-MM-dd");
            dto.setDepartureDate(departureDate);
        }
        // 如果查询的日期是当天，没有通过航班号查询，且没有输入出发时刻，需要设置一个默认当前时刻
        if (StrUtil.isEmpty(dto.getDepartureTime()) && DateUtil.today().equals(dto.getDepartureDate()) && StrUtil.isEmpty(dto.getFlightNo())) {
            dto.setDepartureTime(DateUtil.format(new Date(), "HHmm"));
        }

        // 通过航班号查询
        QueryFlightsVo result;
        if (StrUtil.isNotEmpty(dto.getFlightNo())) {
            result = this.queryByFlightNo(dto);
        }
        // 通过城市对查询
        else {
            // 获取出发和到达机场ID列表
            List<String> departureAirportIdList = this.getAirportIdList(dto.getDepartureCity());
            List<String> arriveAirportIdList = this.getAirportIdList(dto.getArriveCity());

            // 如果输入了中转点，查询中转航班
            if (StrUtil.isNotEmpty(dto.getTransitCity())) {
                // 勾选仅直飞不查询中转，直接返回空结果
                if (StrUtil.isNotEmpty(dto.getOnlyDirect())) {
                    return null;
                }
                result = this.queryTransitFlights(departureAirportIdList, arriveAirportIdList, dto);
            } else {
                QueryFlightsVo directFlights = this.queryDirectFlights(departureAirportIdList, arriveAirportIdList, dto);
                if (directFlights != null && CollUtil.isNotEmpty(directFlights.getFlightInfoList().stream().flatMap(v -> v.getSegments().stream()).collect(Collectors.toList()))) {
                    result = directFlights;
                } else {
                    // 没有查到直达航班，如果选择了仅直飞则直接返回空结果，否则查询中转航班
                    if (StrUtil.isNotEmpty(dto.getOnlyDirect())) {
                        return null;
                    }
                    result = this.queryTransitFlights(departureAirportIdList, arriveAirportIdList, dto);
                }
            }
        }

        // 处理preOccupySegmentInfoList参数，筛选匹配的航段
//        if (result != null && CollUtil.isNotEmpty(dto.getPreOccupySegmentInfoList())) {
//            result = this.filterByPreOccupySegmentInfo(result, dto.getPreOccupySegmentInfoList());
//        }

        return result;
    }

    private List<String> getAirportIdList(String airportCode) {
        // 先查城市，如果能查到就返回该城市下所有的机场id
        MnjxCity city = iMnjxCityService.lambdaQuery()
                .eq(MnjxCity::getCityCode, airportCode)
                .one();
        if (city != null) {
            return iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getCityId, city.getCityId())
                    .list()
                    .stream()
                    .map(MnjxAirport::getAirportId)
                    .collect(Collectors.toList());
        } else {
            return iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, airportCode)
                    .list()
                    .stream()
                    .map(MnjxAirport::getAirportId)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 根据占位航段信息筛选航班结果
     *
     * @param vo                       航班查询结果
     * @param preOccupySegmentInfoList 占位航段信息列表
     * @return 筛选后的航班查询结果
     */
    private QueryFlightsVo filterByPreOccupySegmentInfo(QueryFlightsVo vo, List<QueryFlightsDto.PreOccupySegmentInfo> preOccupySegmentInfoList) {
        if (vo == null || CollUtil.isEmpty(vo.getFlightInfoList()) || CollUtil.isEmpty(preOccupySegmentInfoList)) {
            return vo;
        }

        QueryFlightsVo result = new QueryFlightsVo();
        result.setSessionId(vo.getSessionId());
        List<QueryFlightsVo.FlightInfo> filteredFlightInfoList = new ArrayList<>();
        result.setFlightInfoList(filteredFlightInfoList);

        // 遍历原始航班信息列表
        for (QueryFlightsVo.FlightInfo flightInfo : vo.getFlightInfoList()) {
            if (CollUtil.isEmpty(flightInfo.getSegments())) {
                continue;
            }

            // 检查每个航段是否与占位信息匹配
            boolean allSegmentsMatch = true;
            for (QueryFlightsDto.PreOccupySegmentInfo preOccupyInfo : preOccupySegmentInfoList) {
                boolean segmentMatched = false;
                for (QueryFlightsVo.Segment segment : flightInfo.getSegments()) {
                    // 检查航空公司代码、航班号、出发地、目的地、出发日期是否匹配
                    if (segment.getAirlines() != null &&
                            preOccupyInfo.getAirline().equals(segment.getAirlines().getAirCode()) &&
                            preOccupyInfo.getFlightNumber().toString().equals(segment.getAirlines().getFlightNo()) &&
                            preOccupyInfo.getOrigin().equals(segment.getDepartureAirportCode()) &&
                            preOccupyInfo.getDestination().equals(segment.getArrivalAirportCode()) &&
                            preOccupyInfo.getDepartureDate().equals(segment.getDepartureDate().toLocalDate().toString())) {

                        // 检查出发时间和到达时间是否匹配
                        String segmentDepTime = segment.getDepartureTime().format(DateTimeFormatter.ofPattern("HHmm"));
                        String segmentArrTime = segment.getArrivalTime().format(DateTimeFormatter.ofPattern("HHmm"));

                        if (preOccupyInfo.getDepartureTime().equals(segmentDepTime) &&
                                preOccupyInfo.getArrivalTime().equals(segmentArrTime)) {

                            // 检查舱位是否匹配
                            boolean cabinMatched = false;
                            if (CollUtil.isNotEmpty(segment.getCabins())) {
                                for (QueryFlightsVo.Cabin cabin : segment.getCabins()) {
                                    if (preOccupyInfo.getCls().equals(cabin.getCabinName())) {
                                        cabinMatched = true;
                                        break;
                                    }
                                }
                            }

                            if (cabinMatched) {
                                segmentMatched = true;
                                break;
                            }
                        }
                    }
                }

                if (!segmentMatched) {
                    allSegmentsMatch = false;
                    break;
                }
            }

            // 如果所有占位航段都匹配，则添加到结果中
            if (allSegmentsMatch) {
                filteredFlightInfoList.add(flightInfo);
            }
        }

        return result;
    }

    /**
     * 验证航班查询参数
     *
     * @param dto 查询参数
     */
    private void validateQueryFlightsDto(QueryFlightsDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }
        if (StrUtil.isEmpty(dto.getDepartureDate())) {
            throw new IllegalArgumentException("出发日期不能为空");
        }
        if (StrUtil.isEmpty(dto.getFlightNo())) {
            if (StrUtil.isEmpty(dto.getDepartureCity())) {
                throw new IllegalArgumentException("出发城市不能为空");
            }
            if (StrUtil.isEmpty(dto.getArriveCity())) {
                throw new IllegalArgumentException("到达城市不能为空");
            }
        }
    }

    public QueryFlightsVo queryDirectFlights(List<String> departureAirportIdList, List<String> arriveAirportIdList, QueryFlightsDto dto) throws SguiResultException {
        // 查询mnjx_tcard_section表筛选符合条件的tcard id
        List<String> tcardIds = etV2Mapper.retrieveTcardIdsByCityPair(departureAirportIdList, arriveAirportIdList);
        if (CollUtil.isEmpty(tcardIds)) {
            return null;
        }

        QueryFlightsVo vo = new QueryFlightsVo();
        List<QueryFlightsVo.FlightInfo> flightInfoList = new ArrayList<>();
        vo.setFlightInfoList(flightInfoList);

        // 查询mnjx_tcard表
        List<MnjxTcard> tcardList = iMnjxTcardService.listByIds(tcardIds);

        // 查询航班计划
        List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(dto.getDepartureDate(), tcardIds);
        if (CollUtil.isEmpty(planFlightList)) {
            return null;
        }

        // 获取航班计划ID
        List<String> planFlightIdList = planFlightList.stream()
                .map(MnjxPlanFlight::getPlanFlightId)
                .collect(Collectors.toList());

        // 获取航节计划，并排序
        List<MnjxPlanSection> planSectionList = iEtCommonService.retrievePlanSection(dto.getDepartureTime(), planFlightIdList);
        if (CollUtil.isEmpty(planSectionList)) {
            return null;
        }

        List<String> planSectionIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());

        // 查询开舱数据
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .list();

        // 查询飞机和飞机模型
        List<String> planeIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlaneId)
                .collect(Collectors.toList());
        List<MnjxPlane> planes = iMnjxPlaneService.listByIds(planeIdList);
        List<String> planeModelIdList = planes.stream()
                .map(MnjxPlane::getPlaneModelId)
                .collect(Collectors.toList());
        List<MnjxPlaneModel> planeModels = iMnjxPlaneModelService.listByIds(planeModelIdList);

        // 获取需要用到的数据
        List<MnjxFlight> flights = iMnjxFlightService.list();
        List<MnjxAirport> airports = iMnjxAirportService.list();

        // 处理返回数据
        int index = 0;
        for (MnjxPlanFlight planFlight : planFlightList) {
            QueryFlightsVo.FlightInfo flightInfo = new QueryFlightsVo.FlightInfo();
            flightInfo.setIndex(index++);
            flightInfo.setPkId(planFlight.getPlanFlightId());

            // 获取航班信息
            String tcardId = planFlight.getTcardId();
            MnjxTcard tcard = tcardList.stream()
                    .filter(t -> t.getTcardId().equals(tcardId))
                    .findFirst()
                    .orElse(null);

            if (tcard == null) {
                continue;
            }

            String flightId = tcard.getFlightId();
            MnjxFlight flight = flights.stream()
                    .filter(f -> f.getFlightId().equals(flightId))
                    .findFirst()
                    .orElse(null);

            if (flight == null) {
                continue;
            }

            // 获取航节信息
            List<MnjxPlanSection> multiPlanSectionList = planSectionList.stream()
                    .filter(p -> p.getPlanFlightId().equals(planFlight.getPlanFlightId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(multiPlanSectionList)) {
                continue;
            }

            // 获取所有的开舱数据
            List<MnjxOpenCabin> mnjxOpenCabins = iEtCommonService.getOpenCabins(multiPlanSectionList, departureAirportIdList, arriveAirportIdList, openCabinList);
            // 如果开舱数据是空的，说明该航班的出发到达和查询的是相反的或不符合输入城市对的航班，跳过
            if (CollUtil.isEmpty(mnjxOpenCabins)) {
                continue;
            }

            // 处理航段信息
            List<QueryFlightsVo.Segment> segments = new ArrayList<>();
            flightInfo.setSegments(segments);

            // 设置航段信息
            int stopCount = this.setFlightSegments(multiPlanSectionList, flight, planFlight, airports, planes, planeModels, openCabinList, segments, departureAirportIdList, arriveAirportIdList, dto.getDepartureDate(), dto.getFlightType());

            if (CollUtil.isEmpty(segments)) {
                continue;
            }

            // 设置经停城市标识
            if (stopCount > 0 && !segments.isEmpty()) {
                flightInfo.setStopCity(segments.get(0).getStopCity());
            } else {
                flightInfo.setStopCity("");
            }

            // 如果有经停，计算并设置第一段segment的地面停留时间
            if (stopCount > 0 && segments.size() > 1) {
                this.calculateGroundTime(segments);
            }

            // 计算总飞行时间
            this.calculateTotalFlightTime(flightInfo);

            // 添加到结果列表
            flightInfoList.add(flightInfo);
        }

        // 按条件筛选和排序
        this.filterAndSortFlights(vo, dto);

        // 处理共享航班，确保共享航班排在其承运航班后面
        // 只有直飞查询才需要添加共享航班
        // flightType为O时不查询共享
        if (StrUtil.isEmpty(dto.getFlightType()) || !dto.getFlightType().contains("O")) {
            this.addSharedFlights(vo, flights);
        }

        return vo;
    }

    private QueryFlightsVo queryTransitFlights(List<String> departureAirportIdList, List<String> arriveAirportIdList, QueryFlightsDto dto) throws SguiResultException {
        QueryFlightsVo vo = new QueryFlightsVo();
        List<QueryFlightsVo.FlightInfo> flightInfoList = new ArrayList<>();
        vo.setFlightInfoList(flightInfoList);

        // 如果指定了中转城市，根据中转城市查询
        if (StrUtil.isNotEmpty(dto.getTransitCity())) {
            List<String> transitAirportIdList = this.getAirportIdList(dto.getTransitCity());

            // 先查询出发地到中转地的航班
            QueryFlightsVo firstLegVo = this.queryDirectFlights(departureAirportIdList, transitAirportIdList, dto);
            if (firstLegVo == null || CollUtil.isEmpty(firstLegVo.getFlightInfoList())) {
                return null;
            }

            // 再查询中转地到目的地的航班
            QueryFlightsVo secondLegVo = this.queryDirectFlights(transitAirportIdList, arriveAirportIdList, dto);
            if (secondLegVo == null || CollUtil.isEmpty(secondLegVo.getFlightInfoList())) {
                return null;
            }

            // 组合两段航班信息
            this.combineTransitFlights(vo, firstLegVo, secondLegVo, dto.getTransitCity());
        }
        // 如果没有指定中转城市，则查询可能的中转点
        else {
            // 获取可能的中转城市
            List<String> possibleTransitCities = this.findPossibleTransitCities(departureAirportIdList, arriveAirportIdList, dto);

            if (CollUtil.isNotEmpty(possibleTransitCities)) {
                for (String transitCity : possibleTransitCities) {
                    // 获取中转城市的机场
                    List<String> transitAirportIdList = this.getAirportIdList(transitCity);

                    if (CollUtil.isEmpty(transitAirportIdList)) {
                        continue;
                    }

                    // 查询出发地到中转地的航班
                    QueryFlightsVo firstLegVo = this.queryDirectFlights(departureAirportIdList, transitAirportIdList, dto);
                    if (firstLegVo == null || CollUtil.isEmpty(firstLegVo.getFlightInfoList())) {
                        continue;
                    }

                    // 查询中转地到目的地的航班
                    QueryFlightsVo secondLegVo = this.queryDirectFlights(transitAirportIdList, arriveAirportIdList, dto);
                    if (secondLegVo == null || CollUtil.isEmpty(secondLegVo.getFlightInfoList())) {
                        continue;
                    }

                    // 组合两段航班信息
                    this.combineTransitFlights(vo, firstLegVo, secondLegVo, transitCity);
                }
            }
        }

        // 按条件筛选和排序
        this.filterAndSortFlights(vo, dto);

        // 如果没有数据，返回null
        if (CollUtil.isEmpty(vo.getFlightInfoList())) {
            return null;
        }

        return vo;
    }

    /**
     * 查找可能的中转城市
     *
     * @param departureAirportIdList 出发机场ID列表
     * @param arriveAirportIdList    到达机场ID列表
     * @param dto                    查询参数
     * @return 可能的中转城市列表
     */
    private List<String> findPossibleTransitCities(List<String> departureAirportIdList, List<String> arriveAirportIdList, QueryFlightsDto dto) {
        // 查询出发地可达的城市
        List<String> departureReachableAirports = etV2Mapper.retrieveReachableAirports(departureAirportIdList, dto.getDepartureDate());

        // 查询可达到达地的城市
        List<String> arriveReachableAirports = etV2Mapper.retrieveAirportsReachingDestination(arriveAirportIdList, dto.getDepartureDate());

        // 取交集，找出可以作为中转点的机场
        List<String> transitAirports = departureReachableAirports.stream()
                .filter(arriveReachableAirports::contains)
                .collect(Collectors.toList());

        // 获取中转机场所在的城市
        List<String> transitCities = new ArrayList<>();
        if (CollUtil.isNotEmpty(transitAirports)) {
            List<MnjxAirport> airports = iMnjxAirportService.lambdaQuery()
                    .in(MnjxAirport::getAirportId, transitAirports)
                    .list();

            // 获取城市ID
            List<String> cityIds = airports.stream()
                    .map(MnjxAirport::getCityId)
                    .distinct()
                    .collect(Collectors.toList());

            // 获取城市信息
            List<MnjxCity> cities = iMnjxCityService.lambdaQuery()
                    .in(MnjxCity::getCityId, cityIds)
                    .list();

            // 获取城市代码
            transitCities = cities.stream()
                    .map(MnjxCity::getCityCode)
                    .collect(Collectors.toList());
        }

        return transitCities;
    }

    /**
     * 组合中转航班
     *
     * @param resultVo    结果对象
     * @param firstLegVo  第一段航班
     * @param secondLegVo 第二段航班
     * @param transitCity 中转城市
     */
    private void combineTransitFlights(QueryFlightsVo resultVo, QueryFlightsVo firstLegVo, QueryFlightsVo secondLegVo, String transitCity) {
        List<QueryFlightsVo.FlightInfo> resultList = resultVo.getFlightInfoList();
        List<QueryFlightsVo.FlightInfo> firstLegList = firstLegVo.getFlightInfoList();
        List<QueryFlightsVo.FlightInfo> secondLegList = secondLegVo.getFlightInfoList();

        int index = resultList.size();

        // 组合两段航班
        for (QueryFlightsVo.FlightInfo firstLeg : firstLegList) {
            // 过滤共享航班
            if (StrUtil.isNotEmpty(firstLeg.getSegments().get(0).getAirlines().getIsShared())) {
                continue;
            }
            for (QueryFlightsVo.FlightInfo secondLeg : secondLegList) {
                // 过滤共享航班
                if (StrUtil.isNotEmpty(secondLeg.getSegments().get(0).getAirlines().getIsShared())) {
                    continue;
                }
                // 检查中转时间是否合理（至少1小时，不超迄6小时）
                if (!this.isValidTransitTime(firstLeg, secondLeg)) {
                    continue;
                }

                // 创建新的中转航班信息
                QueryFlightsVo.FlightInfo transitFlight = new QueryFlightsVo.FlightInfo();
                transitFlight.setIndex(index++);
                transitFlight.setPkId(firstLeg.getPkId() + "-" + secondLeg.getPkId());

                // 设置中转城市
                List<String> transitCities = new ArrayList<>();
                transitCities.add(transitCity);
                transitFlight.setTransitCities(transitCities);

                // 合并航段
                List<QueryFlightsVo.Segment> segments = new ArrayList<>();
                segments.addAll(firstLeg.getSegments());
                segments.addAll(secondLeg.getSegments());
                transitFlight.setSegments(segments);

                // 计算地面停留时间
                if (!segments.isEmpty() && segments.size() > 1) {
                    // 在第一段航班的最后一个航段设置地面停留时间
                    int firstLegSegmentCount = firstLeg.getSegments().size();
                    if (firstLegSegmentCount > 0) {
                        QueryFlightsVo.Segment lastSegmentOfFirstLeg = segments.get(firstLegSegmentCount - 1);
                        QueryFlightsVo.Segment firstSegmentOfSecondLeg = segments.get(firstLegSegmentCount);

                        if (lastSegmentOfFirstLeg.getArrivalDate() != null && firstSegmentOfSecondLeg.getDepartureDate() != null) {
                            // 计算地面停留时间（分钟）
                            long minutes = ChronoUnit.MINUTES.between(lastSegmentOfFirstLeg.getArrivalDate(), firstSegmentOfSecondLeg.getDepartureDate());

                            // 格式化为小时:分钟
                            long hours = minutes / 60;
                            long remainingMinutes = minutes % 60;
                            lastSegmentOfFirstLeg.setGroundTime(String.format("%02d:%02d", hours, remainingMinutes));
                        }
                    }
                }

                // 计算总飞行时间
                this.calculateTotalFlightTime(transitFlight);

                // 添加到结果列表
                resultList.add(transitFlight);
            }
        }
    }

    /**
     * 通过航班号查询航班信息
     *
     * @param dto 查询参数
     * @return 航班查询结果
     * @throws SguiResultException 异常
     */
    private QueryFlightsVo queryByFlightNo(QueryFlightsDto dto) throws SguiResultException {
        // 根据航班号查询时，忽略城市对、起飞时间、航司、中转点、非共享等参数
        dto.setDepartureCity(null);
        dto.setArriveCity(null);
        dto.setDepartureTime(null);
        dto.setAirCode(null);
        dto.setTransitCity(null);
        if (StrUtil.isNotEmpty(dto.getFlightType()) && dto.getFlightType().contains("P")) {
            dto.setFlightType("P");
        } else {
            dto.setFlightType(null);
        }

        QueryFlightsVo vo = new QueryFlightsVo();
        List<QueryFlightsVo.FlightInfo> flightInfoList = new ArrayList<>();
        vo.setFlightInfoList(flightInfoList);

        // 从缓存中获取航班信息
        String flightKey = FlightCacheConstants.FLIGHT_PREFIX + dto.getFlightNo();
        String flightJson = stringRedisTemplate.opsForValue().get(flightKey);

        if (StrUtil.isBlank(flightJson) || FlightCacheConstants.EMPTY_VALUE.equals(flightJson)) {
            // 缓存中没有，从数据库查询
            MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, dto.getFlightNo())
                    .one();

            if (flight == null) {
                return null;
            }
            String flightId = flight.getFlightId();
            // 可能是共享航班
            if (StrUtil.isNotEmpty(flight.getCarrierFlight())) {
                MnjxFlight carrierFlight = iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getFlightNo, flight.getCarrierFlight())
                        .one();
                flightId = carrierFlight.getFlightId();
            }

            // 查询航节信息
            List<MnjxTcard> tcardList = iMnjxTcardService.lambdaQuery()
                    .eq(MnjxTcard::getFlightId, flightId)
                    .list();

            if (CollUtil.isEmpty(tcardList)) {
                return null;
            }

            // 获取航节ID列表
            List<String> tcardIds = tcardList.stream()
                    .map(MnjxTcard::getTcardId)
                    .collect(Collectors.toList());

            // 查询航班计划
            List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(dto.getDepartureDate(), tcardIds);
            if (CollUtil.isEmpty(planFlightList)) {
                throw new SguiResultException("AV结果为空，无可用航班");
            }

            // 处理航班信息
            return this.processFlightInfo(flight, tcardList, planFlightList, dto);
        } else {
            // 从缓存中获取航班信息
            MnjxFlight flight = JSONUtil.toBean(flightJson, MnjxFlight.class);
            String flightId = flight.getFlightId();
            // 可能是共享航班，共享航班的tcard没有在redis缓存中
            if (StrUtil.isNotEmpty(flight.getCarrierFlight())) {
                flightJson = stringRedisTemplate.opsForValue().get(FlightCacheConstants.FLIGHT_PREFIX + flight.getCarrierFlight());
                MnjxFlight carrierFlight = JSONUtil.toBean(flightJson, MnjxFlight.class);
                flightId = carrierFlight.getFlightId();
            }

            // 从缓存中获取航节信息
            String tcardKey = FlightCacheConstants.TCARD_PREFIX + dto.getFlightNo();
            String tcardJson = stringRedisTemplate.opsForValue().get(tcardKey);

            if (StrUtil.isBlank(tcardJson) || FlightCacheConstants.EMPTY_VALUE.equals(tcardJson)) {
                // 缓存中没有航节信息，从数据库查询
                List<MnjxTcard> tcardList = iMnjxTcardService.lambdaQuery()
                        .eq(MnjxTcard::getFlightId, flightId)
                        .list();

                if (CollUtil.isEmpty(tcardList)) {
                    return null;
                }

                // 获取航节ID列表
                List<String> tcardIds = tcardList.stream()
                        .map(MnjxTcard::getTcardId)
                        .collect(Collectors.toList());

                // 查询航班计划
                List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(dto.getDepartureDate(), tcardIds);
                if (CollUtil.isEmpty(planFlightList)) {
                    throw new SguiResultException("AV结果为空，无可用航班");
                }

                // 处理航班信息
                return this.processFlightInfo(flight, tcardList, planFlightList, dto);
            } else {
                // 从缓存中获取航节信息
                MnjxTcard tcard = JSONUtil.toBean(tcardJson, MnjxTcard.class);
                List<MnjxTcard> tcardList = Collections.singletonList(tcard);

                // 获取航节ID列表
                List<String> tcardIds = Collections.singletonList(tcard.getTcardId());

                // 查询航班计划
                List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(dto.getDepartureDate(), tcardIds);
                if (CollUtil.isEmpty(planFlightList)) {
                    throw new SguiResultException("AV结果为空，无可用航班");
                }

                // 处理航班信息
                return this.processFlightInfo(flight, tcardList, planFlightList, dto);
            }
        }
    }

    /**
     * 创建航段信息
     *
     * @param flight      航班信息
     * @param planSection 航节计划
     * @param depAirport  出发机场
     * @param arrAirport  到达机场
     * @param planFlight  航班计划
     * @param flightDate  航班日期
     * @return 航段信息
     */
    private QueryFlightsVo.Segment createSegment(MnjxFlight flight, MnjxPlanSection planSection, MnjxAirport depAirport,
                                                 MnjxAirport arrAirport, MnjxPlanFlight planFlight, String flightDate) {
        QueryFlightsVo.Segment segment = new QueryFlightsVo.Segment();

        // 设置出发信息
        segment.setDepartureAirportCode(depAirport.getAirportCode());
        segment.setDepartureAirportCN(depAirport.getAirportCname());
        segment.setDepartureTerminal("T2"); // 暂时统一使用T2

        // 设置出发时间
        String departureTime = planSection.getEstimateOff();
        segment.setDepartureTime(LocalTime.parse(departureTime.substring(0, 2) + ":" + departureTime.substring(2)));

        // 设置出发日期时间
        LocalDate date = LocalDate.parse(flightDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        segment.setDepartureDate(LocalDateTime.of(date, segment.getDepartureTime()));

        // 如果有到达机场信息，设置到达信息
        if (arrAirport != null) {
            this.updateArrivalInfo(segment, arrAirport, planSection);
        }

        // 设置航空公司信息
        QueryFlightsVo.Airlines airlines = new QueryFlightsVo.Airlines();
        segment.setAirlines(airlines);

        // 从航班号中提取航空公司代码和航班号
        String flightNo = flight.getFlightNo();
        airlines.setAirCode(flightNo.substring(0, 2));
        airlines.setFlightNo(flightNo.substring(2));

        // 从缓存中获取航空公司信息
        String airlineKey = FlightCacheConstants.AIRLINE_PREFIX + airlines.getAirCode();
        String airlineJson = stringRedisTemplate.opsForValue().get(airlineKey);
        if (StrUtil.isNotBlank(airlineJson) && !FlightCacheConstants.EMPTY_VALUE.equals(airlineJson)) {
            MnjxAirline airline = JSONUtil.toBean(airlineJson, MnjxAirline.class);
            airlines.setAirCN(airline.getAirlineFullName());
        }

        // 设置共享航班，如果是共享航班设置承运航班号
        airlines.setIsShared(Constant.STR_ONE.equals(flight.getShareState()) ? flight.getCarrierFlight() : "");

        // 设置电子票标识
        segment.setEtInd("E".equals(flight.getIsE()));

        // 设置ASR标识
        segment.setAsr(Constant.STR_ONE.equals(planFlight.getAllowAsr()) ? "^" : "");

        // 设置标准餐食
        segment.setCommonMeal(planSection.getMealCode());

        return segment;
    }

    /**
     * 更新到达信息
     *
     * @param segment     航段信息
     * @param arrAirport  到达机场
     * @param planSection 航节计划
     */
    private void updateArrivalInfo(QueryFlightsVo.Segment segment, MnjxAirport arrAirport, MnjxPlanSection planSection) {
        // 设置到达机场信息
        segment.setArrivalAirportCode(arrAirport.getAirportCode());
        segment.setArrivalAirportCN(arrAirport.getAirportCname());
        segment.setArrivalTerminal("T2"); // 暂时统一使用T2

        // 设置到达时间
        String arrivalTime = planSection.getEstimateArr();
        segment.setArrivalTime(LocalTime.parse(arrivalTime.substring(0, 2) + ":" + arrivalTime.substring(2)));

        // 设置是否跨天
        boolean isNextDay = StrUtil.isNotEmpty(planSection.getEstimateArrChange()) || Integer.parseInt(planSection.getEstimateArr()) < Integer.parseInt(planSection.getEstimateOff());
        segment.setArrDays(isNextDay ? "+1" : "");
        segment.setArrivalArrdays(isNextDay ? "+1" : "");

        // 设置到达日期时间
        LocalDateTime departureDateTime = segment.getDepartureDate();
        LocalDate arrivalDate = departureDateTime.toLocalDate();
        if (isNextDay) {
            arrivalDate = arrivalDate.plusDays(1);
        }
        segment.setArrivalDate(LocalDateTime.of(arrivalDate, segment.getArrivalTime()));

        // 计算飞行时间
        this.calculateFlightTime(segment);
    }

    /**
     * 计算飞行时间
     *
     * @param segment 航段信息
     */
    private void calculateFlightTime(QueryFlightsVo.Segment segment) {
        if (segment.getDepartureDate() == null || segment.getArrivalDate() == null) {
            return;
        }

        // 计算飞行时间（分钟）
        long minutes = ChronoUnit.MINUTES.between(segment.getDepartureDate(), segment.getArrivalDate());

        // 格式化为小时:分钟
        long hours = minutes / 60;
        long remainingMinutes = minutes % 60;
        segment.setFlightTime(String.format("%02d:%02d", hours, remainingMinutes));
    }

    /**
     * 设置飞机信息
     *
     * @param segment     航段信息
     * @param planes      飞机列表
     * @param planeModels 飞机模型列表
     * @param planSection 航节计划
     */
    private void setPlaneInfo(QueryFlightsVo.Segment segment, List<MnjxPlane> planes, List<MnjxPlaneModel> planeModels, MnjxPlanSection planSection) {
        MnjxPlane plane = planes.stream()
                .filter(p -> p.getPlaneId().equals(planSection.getPlaneId()))
                .findFirst()
                .orElse(null);

        if (plane != null) {
            MnjxPlaneModel planeModel = planeModels.stream()
                    .filter(p -> p.getPlaneModelId().equals(plane.getPlaneModelId()))
                    .findFirst()
                    .orElse(null);

            if (planeModel != null) {
                segment.getAirlines().setPlaneType(planeModel.getPlaneModelType());
            }
        }
    }

    /**
     * 设置舱位信息
     *
     * @param segment    航段信息
     * @param openCabins 开舱数据
     */
    private void setCabinInfo(QueryFlightsVo.Segment segment, List<MnjxOpenCabin> openCabins, String flightType) {
        if (CollUtil.isEmpty(openCabins)) {
            return;
        }

        // 如果勾选了最低公布运价，需要对segment设置fare和fareClass，对应最低的运价和销售舱位
        if (StrUtil.isNotEmpty(flightType) && flightType.contains("P") && CollUtil.isNotEmpty(openCabins)) {
            // 找到最低的公布运价和对应的销售舱位
            MnjxOpenCabin lowestPriceOpenCabin = openCabins.stream()
                    .filter(cabin -> "OPEN".equals(cabin.getOpenCabinStatus()) &&
                            cabin.getSeatAvailable() != null &&
                            cabin.getSeatAvailable() > 0)
                    .min(Comparator.comparing(MnjxOpenCabin::getSellCabinPrice))
                    .orElse(null);

            if (lowestPriceOpenCabin != null) {
                // 设置最低公布运价
                segment.setFare(String.valueOf(lowestPriceOpenCabin.getSellCabinPrice()));
                // 设置对应的销售舱位
                segment.setFareClass(lowestPriceOpenCabin.getSellCabin());
            }
        }

        List<QueryFlightsVo.Cabin> cabins = new ArrayList<>();
        segment.setCabins(cabins);

        // 按舱位分组，对于相同舱位，选择座位数较小的那一个
        Map<String, List<MnjxOpenCabin>> cabinMap = openCabins.stream()
                .collect(Collectors.groupingBy(MnjxOpenCabin::getSellCabin));

        List<MnjxOpenCabin> minSeatOpenCabinList = new ArrayList<>();

        for (Map.Entry<String, List<MnjxOpenCabin>> entry : cabinMap.entrySet()) {
            String cabinName = entry.getKey();
            List<MnjxOpenCabin> cabinList = entry.getValue();

            // 如果有多个相同舱位，选择座位数较小的那一个
            MnjxOpenCabin openCabin = cabinList.stream()
                    .min(Comparator.comparing(c -> c.getSeatAvailable() != null ? c.getSeatAvailable() : 0))
                    .orElse(cabinList.get(0));

            minSeatOpenCabinList.add(openCabin);
        }

        // 先按舱等排序，再按价格降序排序。在价格排序前判断是否有和舱等字符相同的销售舱位，例如J舱等有J销售舱位，G舱等有G销售舱位，Y舱等有Y销售舱位。如果有，该销售舱位排在当前舱等下的第一个，然后其他销售舱位再按价格降序排序
        // 例如：舱等J下面有销售舱位A B C D J P，舱等Y下面有销售舱位I U Y T E R。先按CABIN_ORDER对cabinClass排序，然后对销售舱位排序，排序时J销售舱位排第一个，剩下的A B C D P再按价格降序排序，之后的Y销售舱位排第一个，剩下的I U T E R再按价格降序排序
        minSeatOpenCabinList = minSeatOpenCabinList.stream()
                .sorted(Comparator
                        // 先按舱等排序
                        .comparing((MnjxOpenCabin o) -> CABIN_ORDER.getOrDefault(o.getCabinClass(), Integer.MAX_VALUE))
                        // 然后对于同一舱等下的销售舱位，如果销售舱位与舱等相同，则排在前面
                        .thenComparing((o1, o2) -> {
                            // 如果两个舱位的舱等不同，则保持原有排序
                            if (!o1.getCabinClass().equals(o2.getCabinClass())) {
                                return 0;
                            }
                            // 如果第一个舱位的销售舱位与舱等相同，则排在前面
                            if (o1.getSellCabin().equals(o1.getCabinClass())) {
                                return -1;
                            }
                            // 如果第二个舱位的销售舱位与舱等相同，则排在前面
                            if (o2.getSellCabin().equals(o2.getCabinClass())) {
                                return 1;
                            }
                            // 如果都不相同，则按价格降序排序
                            return o2.getSellCabinPrice().compareTo(o1.getSellCabinPrice());
                        })
                )
                .collect(Collectors.toList());

        for (MnjxOpenCabin openCabin : minSeatOpenCabinList) {
            QueryFlightsVo.Cabin cabin = new QueryFlightsVo.Cabin();
            cabin.setCabinName(openCabin.getSellCabin());

            // 设置舱位状态
            String status = openCabin.getOpenCabinStatus();
            if ("OPEN".equals(status)) {
                // OPEN状态的返回座位数，大于等于10的用A表示
                int seatAvailable = openCabin.getSeatAvailable() != null ? openCabin.getSeatAvailable() : 0;
                cabin.setState(seatAvailable >= 10 ? "A" : String.valueOf(seatAvailable));
            } else {
                cabin.setState(status);
            }

            cabins.add(cabin);
        }
    }

    /**
     * 计算地面停留时间
     *
     * @param segments 航段列表
     */
    private void calculateGroundTime(List<QueryFlightsVo.Segment> segments) {
        if (segments.size() < 2) {
            return;
        }

        // 获取第一段航段和第二段航段
        QueryFlightsVo.Segment firstSegment = segments.get(0);
        QueryFlightsVo.Segment secondSegment = segments.get(1);

        // 如果第一段航段的到达时间或第二段航段的出发时间为空，则无法计算
        if (firstSegment.getArrivalDate() == null || secondSegment.getDepartureDate() == null) {
            return;
        }

        // 计算地面停留时间（分钟）
        long minutes = ChronoUnit.MINUTES.between(firstSegment.getArrivalDate(), secondSegment.getDepartureDate());

        // 格式化为小时:分钟
        long hours = minutes / 60;
        long remainingMinutes = minutes % 60;
        firstSegment.setGroundTime(String.format("%02d:%02d", hours, remainingMinutes));
    }

    /**
     * 计算总飞行时间
     *
     * @param flightInfo 航班信息
     */
    private void calculateTotalFlightTime(QueryFlightsVo.FlightInfo flightInfo) {
        List<QueryFlightsVo.Segment> segments = flightInfo.getSegments();
        if (CollUtil.isEmpty(segments)) {
            return;
        }

        // 如果只有一个航段，直接使用航段的飞行时间
        if (segments.size() == 1) {
            QueryFlightsVo.Segment segment = segments.get(0);
            if (segment.getDepartureDate() != null && segment.getArrivalDate() != null) {
                long minutes = ChronoUnit.MINUTES.between(segment.getDepartureDate(), segment.getArrivalDate());
                flightInfo.setTotalTime(String.valueOf(minutes * 60 * 1000)); // 转换为毫秒
            }
            return;
        }

        // 如果有多个航段，计算从第一个航段的出发到最后一个航段的到达
        QueryFlightsVo.Segment firstSegment = segments.get(0);
        QueryFlightsVo.Segment lastSegment = segments.get(segments.size() - 1);

        if (firstSegment.getDepartureDate() != null && lastSegment.getArrivalDate() != null) {
            long minutes = ChronoUnit.MINUTES.between(firstSegment.getDepartureDate(), lastSegment.getArrivalDate());
            flightInfo.setTotalTime(String.valueOf(minutes * 60 * 1000)); // 转换为毫秒
        }
    }

    /**
     * 设置飞行距离
     *
     * @param segments 航段列表
     */
    private void setFlightDistanceForSegments(List<QueryFlightsVo.Segment> segments) {
        if (CollUtil.isEmpty(segments)) {
            return;
        }

        // 获取所有机场
        List<MnjxAirport> airports = iMnjxAirportService.list();

        for (QueryFlightsVo.Segment segment : segments) {
            // 获取出发机场和到达机场的城市ID
            String depAirportCode = segment.getDepartureAirportCode();
            String arrAirportCode = segment.getArrivalAirportCode();

            if (StrUtil.isEmpty(depAirportCode) || StrUtil.isEmpty(arrAirportCode)) {
                continue;
            }

            // 获取出发机场和到达机场所在的城市ID
            MnjxAirport depAirport = airports.stream()
                    .filter(airport -> depAirportCode.equals(airport.getAirportCode()))
                    .findFirst()
                    .orElse(null);

            MnjxAirport arrAirport = airports.stream()
                    .filter(airport -> arrAirportCode.equals(airport.getAirportCode()))
                    .findFirst()
                    .orElse(null);

            if (depAirport == null || arrAirport == null) {
                continue;
            }

            String depCityId = depAirport.getCityId();
            String arrCityId = arrAirport.getCityId();

            if (StrUtil.isEmpty(depCityId) || StrUtil.isEmpty(arrCityId)) {
                continue;
            }

            // 获取飞行距离
            Integer distance = iEtCommonService.getFlightDistance(depCityId, arrCityId);
            if (distance != null) {
                segment.setFlightDistance(distance);

                // 设置航空联盟，固定为SKYTEAM
                segment.setAlliance("SKYTEAM");
            }
        }
    }

    /**
     * 设置航班航段信息
     *
     * @param planSections           航节计划列表
     * @param flight                 航班信息
     * @param planFlight             航班计划
     * @param airports               机场列表
     * @param planes                 飞机列表
     * @param planeModels            飞机模型列表
     * @param openCabins             开舱数据
     * @param segments               航段列表
     * @param departureAirportIdList 出发机场ID列表
     * @param arriveAirportIdList    到达机场ID列表
     * @param departureDate          出发日期
     * @return 经停数量
     */
    private int setFlightSegments(List<MnjxPlanSection> planSections, MnjxFlight flight, MnjxPlanFlight planFlight,
                                  List<MnjxAirport> airports, List<MnjxPlane> planes, List<MnjxPlaneModel> planeModels,
                                  List<MnjxOpenCabin> openCabins, List<QueryFlightsVo.Segment> segments,
                                  List<String> departureAirportIdList, List<String> arriveAirportIdList, String departureDate, String flightType) {
        // 获取所有的开舱数据
        List<MnjxOpenCabin> allOpenCabins = iEtCommonService.getOpenCabins(planSections, departureAirportIdList, arriveAirportIdList, openCabins);
        // 如果开舱数据是空的，说明该航班的出发到达和查询的是相反的或不符合输入城市对的航班，跳过
        if (CollUtil.isEmpty(allOpenCabins)) {
            return 0;
        }

        // 如果出发机场和到达机场都为空，说明是通过航班号查询，直接处理所有航段
        if (departureAirportIdList == null || arriveAirportIdList == null) {
            for (MnjxPlanSection planSection : planSections) {
                MnjxAirport depAirport = airports.stream()
                        .filter(a -> a.getAirportId().equals(planSection.getDepAptId()))
                        .findFirst()
                        .orElse(null);

                MnjxAirport arrAirport = airports.stream()
                        .filter(a -> a.getAirportId().equals(planSection.getArrAptId()))
                        .findFirst()
                        .orElse(null);

                if (depAirport == null || arrAirport == null) {
                    continue;
                }

                // 创建航段信息
                QueryFlightsVo.Segment segment = this.createSegment(flight, planSection, depAirport, arrAirport, planFlight, departureDate);

                // 设置飞机信息
                this.setPlaneInfo(segment, planes, planeModels, planSection);

                // 设置舱位信息
                List<MnjxOpenCabin> segmentOpenCabins = openCabins.stream()
                        .filter(oc -> oc.getPlanSectionId().equals(planSection.getPlanSectionId()))
                        .collect(Collectors.toList());
                this.setCabinInfo(segment, segmentOpenCabins, flightType);

                // 通过航班号查询时，如果有经停，不需要设置经停城市标识
                segment.setStopCity("");

                segments.add(segment);
            }
            return planSections.size() - 1;
        }

        // 标识根据查询条件找到这个多航段航班有从指令输入开始的航站，开始记录
        boolean findFirstAirport = false;
        int stopCount = 0;

        if (planSections.size() == 1) {
            // 单航段
            MnjxPlanSection planSection = planSections.get(0);
            MnjxAirport depAirport = airports.stream()
                    .filter(a -> a.getAirportId().equals(planSection.getDepAptId()))
                    .findFirst()
                    .orElse(null);

            MnjxAirport arrAirport = airports.stream()
                    .filter(a -> a.getAirportId().equals(planSection.getArrAptId()))
                    .findFirst()
                    .orElse(null);

            if (depAirport == null || arrAirport == null) {
                return 0;
            }

            // 判断是否符合出发到达条件
            if (departureAirportIdList.contains(planSection.getDepAptId()) && arriveAirportIdList.contains(planSection.getArrAptId())) {
                QueryFlightsVo.Segment segment = this.createSegment(flight, planSection, depAirport, arrAirport, planFlight, departureDate);

                // 设置飞机信息
                this.setPlaneInfo(segment, planes, planeModels, planSection);

                // 设置舱位信息
                List<MnjxOpenCabin> segmentOpenCabins = openCabins.stream()
                        .filter(oc -> oc.getPlanSectionId().equals(planSection.getPlanSectionId()))
                        .collect(Collectors.toList());
                this.setCabinInfo(segment, segmentOpenCabins, flightType);

                segments.add(segment);
            }
        } else {
            // 多航段
            for (MnjxPlanSection planSection : planSections) {
                MnjxAirport depAirport = airports.stream()
                        .filter(a -> a.getAirportId().equals(planSection.getDepAptId()))
                        .findFirst()
                        .orElse(null);

                MnjxAirport arrAirport = airports.stream()
                        .filter(a -> a.getAirportId().equals(planSection.getArrAptId()))
                        .findFirst()
                        .orElse(null);

                if (depAirport == null || arrAirport == null) {
                    continue;
                }

                // 输入的航段是多航段中的某一个航段
                if (departureAirportIdList.contains(planSection.getDepAptId()) && arriveAirportIdList.contains(planSection.getArrAptId())) {
                    QueryFlightsVo.Segment segment = this.createSegment(flight, planSection, depAirport, arrAirport, planFlight, departureDate);

                    // 设置飞机信息
                    this.setPlaneInfo(segment, planes, planeModels, planSection);

                    // 设置舱位信息
                    List<MnjxOpenCabin> segmentOpenCabins = openCabins.stream()
                            .filter(oc -> oc.getPlanSectionId().equals(planSection.getPlanSectionId()))
                            .collect(Collectors.toList());
                    this.setCabinInfo(segment, segmentOpenCabins, flightType);

                    segments.add(segment);
                    // 单航段的情况下，经停数量为0
                    segment.setStopCity("");
                    return 0;
                }
                // 输入的航段是多航段中多个航段的组合
                else {
                    // 出发
                    if (departureAirportIdList.contains(planSection.getDepAptId())) {
                        findFirstAirport = true;
                        QueryFlightsVo.Segment segment = this.createSegment(flight, planSection, depAirport, null, planFlight, departureDate);

                        // 设置飞机信息
                        this.setPlaneInfo(segment, planes, planeModels, planSection);

                        // 设置舱位信息
                        List<MnjxOpenCabin> segmentOpenCabins = openCabins.stream()
                                .filter(oc -> oc.getPlanSectionId().equals(planSection.getPlanSectionId()))
                                .collect(Collectors.toList());
                        this.setCabinInfo(segment, segmentOpenCabins, flightType);

                        // 设置经停城市标识
                        segment.setStopCity("");
                        segments.add(segment);
                    }
                    // 到达
                    else if (arriveAirportIdList.contains(planSection.getArrAptId()) && findFirstAirport) {
                        stopCount++;
                        // 更新最后一个航段的到达信息
                        if (!segments.isEmpty()) {
                            QueryFlightsVo.Segment lastSegment = segments.get(segments.size() - 1);
                            this.updateArrivalInfo(lastSegment, arrAirport, planSection);

                            // 设置舱位信息
                            List<MnjxOpenCabin> segmentOpenCabins = openCabins.stream()
                                    .filter(oc -> oc.getPlanSectionId().equals(planSection.getPlanSectionId()))
                                    .collect(Collectors.toList());
                            this.setCabinInfo(lastSegment, segmentOpenCabins, flightType);
                        }
                    }
                    // 中间航段
                    else if (findFirstAirport) {
                        stopCount++;
                    }
                }
            }
        }

        // 设置经停城市标识
        if (stopCount > 0 && !segments.isEmpty()) {
            segments.get(0).setStopCity(String.valueOf(stopCount));
        }

        return stopCount;
    }

    /**
     * 按条件筛选和排序航班
     *
     * @param vo  结果对象
     * @param dto 查询参数
     */
    private void filterAndSortFlights(QueryFlightsVo vo, QueryFlightsDto dto) {
        if (vo == null) {
            return;
        }

        List<QueryFlightsVo.FlightInfo> flightInfoList = vo.getFlightInfoList();
        if (CollUtil.isEmpty(flightInfoList)) {
            return;
        }

        Stream<QueryFlightsVo.FlightInfo> stream = flightInfoList.stream();

        // 航班号筛选
        if (StrUtil.isNotEmpty(dto.getFlightNo())) {
            final String flightNoPattern = dto.getFlightNo();
            stream = stream.filter(f -> f.getSegments().stream()
                    .anyMatch(s -> (s.getAirlines().getAirCode() + s.getAirlines().getFlightNo()).contains(flightNoPattern)));
        }

        // 航空公司筛选
        if (StrUtil.isNotEmpty(dto.getAirCode())) {
            final String airCode = dto.getAirCode();
            stream = stream.filter(f -> f.getSegments().stream()
                    .anyMatch(s -> s.getAirlines().getAirCode().equals(airCode)));
        }

        // 出发时间筛选
        if (StrUtil.isNotEmpty(dto.getDepartureTime())) {
            final String departureTimeStr = dto.getDepartureTime();
            LocalTime departureTime = LocalTime.parse(departureTimeStr.substring(0, 2) + ":" + departureTimeStr.substring(2));
            stream = stream.filter(f -> {
                if (CollUtil.isEmpty(f.getSegments())) {
                    return false;
                }
                LocalTime firstSegmentDepartureTime = f.getSegments().get(0).getDepartureTime();
                return firstSegmentDepartureTime != null && !firstSegmentDepartureTime.isBefore(departureTime);
            });
        }

        // 仅直飞筛选
        if (StrUtil.isNotEmpty(dto.getOnlyDirect())) {
            stream = stream.filter(f -> (StrUtil.isEmpty(f.getStopCity()) || "0".equals(f.getStopCity())) && (f.getTransitCities() == null || f.getTransitCities().isEmpty()));
        }

        // 按出发时间排序
        List<QueryFlightsVo.FlightInfo> filteredList = stream
                .sorted((a, b) -> {
                    if (CollUtil.isEmpty(a.getSegments()) || CollUtil.isEmpty(b.getSegments())) {
                        return 0;
                    }
                    LocalTime timeA = a.getSegments().get(0).getDepartureTime();
                    LocalTime timeB = b.getSegments().get(0).getDepartureTime();
                    if (timeA == null || timeB == null) {
                        return 0;
                    }
                    return timeA.compareTo(timeB);
                })
                .collect(Collectors.toList());

        // 按行程时间排序
        if (StrUtil.isNotEmpty(dto.getFlightType()) && dto.getFlightType().contains("E")) {
            filteredList = filteredList.stream()
                    .sorted(Comparator.comparing(f -> Long.parseLong(f.getTotalTime())))
                    .collect(Collectors.toList());
        }

        vo.setFlightInfoList(filteredList);
    }

    /**
     * 添加共享航班到结果列表中
     * 参考SK查询的处理方式，确保共享航班排在其承运航班后面
     *
     * @param vo      结果对象
     * @param flights 航班列表
     */
    private void addSharedFlights(QueryFlightsVo vo, List<MnjxFlight> flights) {
        if (vo == null) {
            return;
        }

        List<QueryFlightsVo.FlightInfo> flightInfoList = vo.getFlightInfoList();
        if (CollUtil.isEmpty(flightInfoList)) {
            return;
        }

        List<QueryFlightsVo.FlightInfo> resultList = new ArrayList<>();

        // 排序完成后插入共享航班
        for (QueryFlightsVo.FlightInfo flightInfo : flightInfoList) {
            resultList.add(flightInfo);

            for (QueryFlightsVo.Segment segment : flightInfo.getSegments()) {
                String flightNo = segment.getAirlines().getAirCode() + segment.getAirlines().getFlightNo();
                // 如果存在共享航班
                if (flights.stream().anyMatch(f -> flightNo.equals(f.getCarrierFlight()) && Constant.STR_ONE.equals(f.getShareState()))) {
                    List<MnjxFlight> shareFlights = flights.stream()
                            .filter(f -> flightNo.equals(f.getCarrierFlight()) && Constant.STR_ONE.equals(f.getShareState()))
                            .collect(Collectors.toList());

                    for (MnjxFlight shareFlight : shareFlights) {
                        // 创建共享航班信息
                        QueryFlightsVo.FlightInfo sharedFlightInfo = new QueryFlightsVo.FlightInfo();
                        BeanUtil.copyProperties(flightInfo, sharedFlightInfo);

                        // 设置新的索引
                        sharedFlightInfo.setIndex(resultList.size());

                        // 复制航段信息
                        List<QueryFlightsVo.Segment> sharedSegments = new ArrayList<>();
                        for (QueryFlightsVo.Segment originalSegment : flightInfo.getSegments()) {
                            QueryFlightsVo.Segment sharedSegment = new QueryFlightsVo.Segment();
                            BeanUtil.copyProperties(originalSegment, sharedSegment);

                            // 复制航空公司信息
                            QueryFlightsVo.Airlines sharedAirlines = new QueryFlightsVo.Airlines();
                            BeanUtil.copyProperties(originalSegment.getAirlines(), sharedAirlines);

                            // 设置共享航班信息
                            sharedAirlines.setAirCode(shareFlight.getFlightNo().substring(0, 2));
                            sharedAirlines.setFlightNo(shareFlight.getFlightNo().substring(2));
                            sharedAirlines.setIsShared(flightNo);

                            sharedSegment.setAirlines(sharedAirlines);
                            sharedSegments.add(sharedSegment);
                        }

                        sharedFlightInfo.setSegments(sharedSegments);
                        resultList.add(sharedFlightInfo);
                    }
                }
            }
        }

        vo.setFlightInfoList(resultList);
    }

    /**
     * 检查中转时间是否合理
     *
     * @param firstLeg  第一段航班
     * @param secondLeg 第二段航班
     * @return 是否合理
     */
    private boolean isValidTransitTime(QueryFlightsVo.FlightInfo firstLeg, QueryFlightsVo.FlightInfo secondLeg) {
        if (CollUtil.isEmpty(firstLeg.getSegments()) || CollUtil.isEmpty(secondLeg.getSegments())) {
            return false;
        }

        // 获取第一段航班的到达时间
        QueryFlightsVo.Segment lastSegmentOfFirstLeg = firstLeg.getSegments().get(firstLeg.getSegments().size() - 1);
        LocalDateTime firstLegArrival = lastSegmentOfFirstLeg.getArrivalDate();

        // 获取第二段航班的出发时间
        QueryFlightsVo.Segment firstSegmentOfSecondLeg = secondLeg.getSegments().get(0);
        LocalDateTime secondLegDeparture = firstSegmentOfSecondLeg.getDepartureDate();

        if (firstLegArrival == null || secondLegDeparture == null) {
            return false;
        }

        // 计算中转时间（分钟）
        long transitMinutes = ChronoUnit.MINUTES.between(firstLegArrival, secondLegDeparture);

        // 中转时间至少1小时，不超迄6小时
        return transitMinutes >= 60 && transitMinutes <= 6 * 60;
    }

    /**
     * 处理航班信息
     *
     * @param flight         航班信息
     * @param tcardList      航节列表
     * @param planFlightList 航班计划列表
     * @param dto            查询参数
     * @return 航班查询结果
     */
    private QueryFlightsVo processFlightInfo(MnjxFlight flight, List<MnjxTcard> tcardList, List<MnjxPlanFlight> planFlightList, QueryFlightsDto dto) throws SguiResultException {
        QueryFlightsVo vo = new QueryFlightsVo();
        List<QueryFlightsVo.FlightInfo> flightInfoList = new ArrayList<>();
        vo.setFlightInfoList(flightInfoList);

        // 获取航班计划ID列表
        List<String> planFlightIdList = planFlightList.stream()
                .map(MnjxPlanFlight::getPlanFlightId)
                .collect(Collectors.toList());

        // 获取航节计划，并排序
        List<MnjxPlanSection> planSectionList = iEtCommonService.retrievePlanSection(dto.getDepartureTime(), planFlightIdList);
        if (CollUtil.isEmpty(planSectionList)) {
            throw new SguiResultException("AV结果为空，无可用航班");
        }

        List<String> planSectionIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());

        // 查询开舱数据
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .list();

        // 查询飞机和飞机模型
        List<String> planeIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlaneId)
                .collect(Collectors.toList());
        List<MnjxPlane> planes = iMnjxPlaneService.listByIds(planeIdList);
        List<String> planeModelIdList = planes.stream()
                .map(MnjxPlane::getPlaneModelId)
                .collect(Collectors.toList());
        List<MnjxPlaneModel> planeModels = iMnjxPlaneModelService.listByIds(planeModelIdList);

        // 获取需要用到的数据
        List<MnjxAirport> airports = iMnjxAirportService.list();

        // 处理返回数据
        int index = 0;
        for (MnjxPlanFlight planFlight : planFlightList) {
            QueryFlightsVo.FlightInfo flightInfo = new QueryFlightsVo.FlightInfo();
            flightInfo.setIndex(index++);
            flightInfo.setPkId(planFlight.getPlanFlightId());

            // 获取航班信息
            String tcardId = planFlight.getTcardId();
            MnjxTcard tcard = tcardList.stream()
                    .filter(t -> t.getTcardId().equals(tcardId))
                    .findFirst()
                    .orElse(null);

            if (tcard == null) {
                continue;
            }

            // 获取航节信息
            List<MnjxPlanSection> multiPlanSectionList = planSectionList.stream()
                    .filter(p -> p.getPlanFlightId().equals(planFlight.getPlanFlightId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(multiPlanSectionList)) {
                continue;
            }

            // 获取所有的开舱数据
            List<MnjxOpenCabin> mnjxOpenCabins = iEtCommonService.getOpenCabins(multiPlanSectionList, null, null, openCabinList);
            // 如果开舱数据是空的，跳过
            if (CollUtil.isEmpty(mnjxOpenCabins)) {
                continue;
            }

            // 处理航段信息
            List<QueryFlightsVo.Segment> segments = new ArrayList<>();
            flightInfo.setSegments(segments);

            // 设置航段信息
            int stopCount = this.setFlightSegments(multiPlanSectionList, flight, planFlight, airports, planes, planeModels, openCabinList, segments, null, null, dto.getDepartureDate(), dto.getFlightType());

            if (CollUtil.isEmpty(segments)) {
                continue;
            }

            // 通过航班号查询时，不需要设置经停城市标识
            flightInfo.setStopCity("");

            // 如果有经停，计算并设置第一段segment的地面停留时间
            if (stopCount > 0 && segments.size() > 1) {
                this.calculateGroundTime(segments);
            }

            // 设置飞行距离
            this.setFlightDistanceForSegments(segments);

            // 计算总飞行时间
            if (!segments.isEmpty()) {
                this.calculateTotalFlightTime(flightInfo);
            }

            // 添加到结果列表
            flightInfoList.add(flightInfo);
        }

        // 按条件筛选和排序
        this.filterAndSortFlights(vo, dto);

        return vo;
    }
}