package com.swcares.service.rpt.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.CrsDailySalesDto;
import com.swcares.obj.vo.CrsDailySalesVo;
import com.swcares.service.*;
import com.swcares.service.rpt.IRptV2Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表V2服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
@Service
public class RptV2ServiceImpl implements IRptV2Service {

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxRefundTicketService iMnjxRefundTicketService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Override
    public CrsDailySalesVo crsDailySales(CrsDailySalesDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getDate())) {
            throw new SguiResultException("查询日期不能为空");
        }
        if (StrUtil.isEmpty(dto.getDeviceNumber())) {
            throw new SguiResultException("打票机序号不能为空");
        }

        CrsDailySalesVo result = new CrsDailySalesVo();

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo == null) {
            throw new SguiResultException("无法获取当前用户信息");
        }

        // 根据打票机序号获取打票机信息
        MnjxOffice currentOffice = userInfo.getMnjxOffice();

        if (currentOffice == null) {
            throw new SguiResultException("无法获取当前办公室信息");
        }

        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, dto.getDeviceNumber())
                .eq(MnjxPrinter::getOfficeId, currentOffice.getOfficeId())
                .one();

        if (printer == null) {
            throw new SguiResultException("未找到指定的打票机");
        }

        // 设置基本信息
        result.setOffice(currentOffice.getOfficeNo());
        MnjxAgent mnjxAgent = iMnjxAgentService.getById(currentOffice.getOrgId());
        if (ObjectUtil.isNotEmpty(mnjxAgent)) {
            result.setIata(mnjxAgent.getAgentIata());
        } else {
            result.setIata("00000000");
        }

        // 根据value判断查询类型
        if ("2".equals(dto.getValue())) {
            // 异常票查询 - 目前返回空数据
            result.setItems(new ArrayList<>());
            result.setFilter(this.createEmptyFilter());
            result.setIssueErrorNumber(0);
            result.setRefundErrorNumber(0);
            result.setTotalErrorNumber(0);
        } else {
            // 正常票查询
            this.processNormalTickets(dto, printer, result);
        }

        return result;
    }

    /**
     * 处理正常票查询
     */
    private void processNormalTickets(CrsDailySalesDto dto, MnjxPrinter printer, CrsDailySalesVo result) {
        List<CrsDailySalesVo.TicketItem> items = new ArrayList<>();

        // 查询选择日期所有的出票记录
        List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .like(MnjxPnrNmTn::getIssuedTime, dto.getDate())
                .list();
        List<MnjxPnrNmTicket> issueTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .in(MnjxPnrNmTicket::getPnrNmTnId, nmTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()))
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();

        for (MnjxPnrNmTn nmTn : nmTnList) {
            List<MnjxPnrNmTicket> filterList = issueTicketList.stream()
                    .filter(t -> nmTn.getTnId().equals(t.getPnrNmTnId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(filterList)) {
                int coupon = 1;
                for (int i = 0; i < filterList.size(); i++) {
                    MnjxPnrNmTicket pnrNmTicket = filterList.get(i);
                    CrsDailySalesVo.TicketItem item = this.buildIssueTicketItem(pnrNmTicket.getTicketNo(), printer.getPrinterId(), dto);
                    if (item != null) {
                        if (filterList.size() > 1) {
                            item.setCouponNo(String.valueOf(coupon));
                            if (coupon > 1) {
                                item.setAmount("");
                                item.setTaxAmount("");
                                item.setAgencyFee("0.00");
                                item.setAgencyFeePercent("0.00");
                            }
                            coupon++;
                        }
                        items.add(item);
                    }
                    // todo 改签记录从这边筛选
                }
            }
        }

//        for (MnjxPnrNmTicket pnrNmTicket : issueTicketList) {
//            CrsDailySalesVo.TicketItem item = this.buildIssueTicketItem(pnrNmTicket.getTicketNo(), printer.getPrinterId(), dto);
//            if (item != null) {
//                items.add(item);
//            }
//        }

        // 查询选择日期的退票记录
        List<MnjxRefundTicket> refundTicketList = iMnjxRefundTicketService.lambdaQuery()
                .like(MnjxRefundTicket::getRefundDate, dto.getDate())
                .list();
        for (MnjxRefundTicket refundTicket : refundTicketList) {
            CrsDailySalesVo.TicketItem item = this.buildRefundTicketItem(refundTicket.getTicketNo(), printer.getPrinterId(), dto);
            if (item != null) {
                items.add(item);
            }
        }

        // 按PNR分组，ISSU票数大于1的要设置联票标识，并取消非首张票的价格信息
//        Map<String, List<CrsDailySalesVo.TicketItem>> map = items.stream().collect(Collectors.groupingBy(CrsDailySalesVo.TicketItem::getPnr));
//        for (Map.Entry<String, List<CrsDailySalesVo.TicketItem>> entry : map.entrySet()) {
//            List<CrsDailySalesVo.TicketItem> list = entry.getValue();
//            if (list.stream().anyMatch(l -> "ISSU".equals(l.getTicketStatus()))) {
//                if (list.size() > 1) {
//                    int coupon = 1;
//                    for (CrsDailySalesVo.TicketItem ticketItem : list) {
//                        if ("ISSU".equals(ticketItem.getTicketStatus())) {
//                            ticketItem.setCouponNo(String.valueOf(coupon));
//                            if (coupon > 1) {
//                                ticketItem.setAmount("");
//                                ticketItem.setTaxAmount("");
//                                ticketItem.setAgencyFee("0.00");
//                                ticketItem.setAgencyFeePercent("0.00");
//                            }
//                            coupon++;
//                        }
//                    }
//                }
//            }
//        }

        // 按退票单号分组，处理RFND的联票标识
        Map<String, List<CrsDailySalesVo.TicketItem>> map = items.stream()
                .filter(i -> StrUtil.isNotEmpty(i.getRefundNo()))
                .collect(Collectors.groupingBy(CrsDailySalesVo.TicketItem::getRefundNo));
        if (MapUtil.isNotEmpty(map)) {
            for (Map.Entry<String, List<CrsDailySalesVo.TicketItem>> entry : map.entrySet()) {
                List<CrsDailySalesVo.TicketItem> list = entry.getValue();
                if (list.size() > 1) {
                    int coupon = 1;
                    for (CrsDailySalesVo.TicketItem ticketItem : list) {
                        ticketItem.setCouponNo(String.valueOf(coupon));
                        if (coupon > 1) {
                            ticketItem.setAmount("");
                            ticketItem.setTaxAmount("");
                            ticketItem.setAgencyFee("0.00");
                            ticketItem.setAgencyFeePercent("0.00");
                        }
                        coupon++;
                    }
                }
            }
        }

        // 退票设置pnr为null
        items.stream()
                .filter(i -> "RFND".equals(i.getTicketStatus()))
                .forEach(i -> i.setPnr(null));

        // items排序：按票面状态排序、按联票标识排序、按票号排序
        List<String> ticketStatusOrder = CollUtil.newArrayList("RFND", "EXCH", "VOID", "ISSU");
        items = items.stream()
                .sorted(Comparator.comparing(CrsDailySalesVo.TicketItem::getCouponNo))
                .sorted(Comparator.comparing(CrsDailySalesVo.TicketItem::getTicket))
                .sorted((o1, o2) -> {
                    int o1Index = ticketStatusOrder.indexOf(o1.getTicketStatus());
                    int o2Index = ticketStatusOrder.indexOf(o2.getTicketStatus());
                    return Integer.compare(o2Index, o1Index);
                })
                .collect(Collectors.toList());

        result.setItems(items);
        result.setFilter(this.buildFilterInfo(items));
        result.setIssueErrorNumber(0);
        result.setRefundErrorNumber(0);
        result.setTotalErrorNumber(0);
    }

    /**
     * 构建出票票务明细项
     */
    private CrsDailySalesVo.TicketItem buildIssueTicketItemCommonPart(String ticketNo, String printerId, CrsDailySalesDto dto) {
        CrsDailySalesVo.TicketItem item = new CrsDailySalesVo.TicketItem();

        MnjxPnrNmTicket ticket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();
        MnjxPnrNmTn tn = iMnjxPnrNmTnService.getById(ticket.getPnrNmTnId());
        if (!printerId.equals(tn.getPrinterId())) {
            return null;
        }

        // 格式化票号
        if (StrUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
            String prefix = ticketNo.substring(0, 3);
            String suffix = ticketNo.substring(3);
            item.setTicket(prefix + "-" + suffix);
        } else {
            item.setTicket(ticketNo);
        }

        // 获取PNR信息
        String pnrNmId = tn.getPnrNmId();
        String pnrCrs = null;
        MnjxPnr pnr = null;
        if (StrUtil.isNotEmpty(pnrNmId)) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm != null) {
                pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
                if (pnr != null) {
                    pnrCrs = pnr.getPnrCrs();
                }
            }
        } else {
            MnjxNmXn nmXn = iMnjxNmXnService.getById(tn.getNmXnId());
            if (nmXn != null) {
                MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(nmXn.getPnrNmId());
                if (pnrNm != null) {
                    pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
                    if (pnr != null) {
                        pnrCrs = pnr.getPnrCrs();
                    }
                }
            }
        }
        item.setPnr(pnrCrs);

        // 设置基本信息
        // 支付方式系统只有现金，这里查询标识为CA
        item.setPayType("CA");
        MnjxSi issuedSi = iMnjxSiService.getById(tn.getIssuedSiId());
        item.setJobNo(issuedSi.getSiNo()); // 默认工作号
        item.setAirline(tn.getIssuedAirline());
        item.setPrntNo(dto.getDeviceNumber());

        // 格式化时间
        if (tn.getIssuedTime() != null) {
            item.setSalesTime(tn.getIssuedTime());
            item.setSalesDate(tn.getIssuedTime().substring(0, 10));
        }

        // 获取运价信息
        this.setPriceInfo(item, tn, pnr.getPnrId(), ticket);

        // 设置其他固定信息
        item.setTicketTypeCode("D"); // 国内客票
        item.setTicketType("BSP国内客票");
        item.setCurrencyType("CNY");
        item.setInvoluntaryIdentification("-");

        item.setCouponNo("0");
        item.setObTax(null);

        String desArr = this.buildDesArr(ticket);
        item.setDesArr(desArr);

        return item;
    }

    /**
     * 构建出票票务明细项
     */
    private CrsDailySalesVo.TicketItem buildIssueTicketItem(String ticketNo, String printerId, CrsDailySalesDto dto) {
        CrsDailySalesVo.TicketItem item = this.buildIssueTicketItemCommonPart(ticketNo, printerId, dto);
        if (item == null) {
            return null;
        }
        // 票面状态
        item.setTicketStatus("ISSU");
        return item;
    }

    /**
     * 构建退票票务明细项
     */
    private CrsDailySalesVo.TicketItem buildRefundTicketItem(String ticketNo, String printerId, CrsDailySalesDto dto) {
        CrsDailySalesVo.TicketItem item = this.buildIssueTicketItemCommonPart(ticketNo, printerId, dto);
        if (item == null) {
            return null;
        }
        item.setDesArr(null);
        MnjxRefundTicket refundTicket = iMnjxRefundTicketService.lambdaQuery()
                .eq(MnjxRefundTicket::getTicketNo, ticketNo)
                .one();
        if (ObjectUtil.isEmpty(refundTicket)) {
            return null;
        }
        // 退票单号
        item.setRefundNo(refundTicket.getRefundNo());
        // 退票手续费
        if (ObjectUtil.isNotEmpty(refundTicket.getComm())) {
            item.setServiceCharge(refundTicket.getComm().toString());
        }
        item.setTicketStatus("RFND");
        // 退票后的票面价变成了应退总额
        if (ObjectUtil.isNotEmpty(refundTicket.getNetRefund())) {
            item.setAmount(refundTicket.getNetRefund().toString());
        }

        return item;
    }

    /**
     * 构建目的地信息
     */
    private String buildDesArr(MnjxPnrNmTicket ticket) {
        List<String> pnrSegIdList = new ArrayList<>();
        if (StrUtil.isNotEmpty(ticket.getS1Id())) {
            pnrSegIdList.add(ticket.getS1Id());
        }
        if (StrUtil.isNotEmpty(ticket.getS2Id())) {
            pnrSegIdList.add(ticket.getS2Id());
        }
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .in(MnjxPnrSeg::getPnrSegId, pnrSegIdList)
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isNotEmpty(segments)) {
            String org = segments.get(0).getOrg();
            String dst = segments.get(segments.size() - 1).getDst();
            return org + dst;
        }
        return "";
    }

    /**
     * 设置价格信息
     */
    private void setPriceInfo(CrsDailySalesVo.TicketItem item, MnjxPnrNmTn tn, String pnrId, MnjxPnrNmTicket ticket) {
        boolean isBaby = StrUtil.isNotEmpty(tn.getNmXnId());
        // 默认值
        item.setAgencyFee("0.00");
        item.setAgencyFeePercent("0.00");
        item.setTaxAmount("0.00");
        item.setAmount("0.00");
        item.setOtherDeduction(null);
        item.setServiceCharge("");

        // 查询运价信息
        if (StrUtil.isNotEmpty(tn.getPnrNmId())) {
            List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, tn.getPnrNmId())
                    .list();

            if (CollUtil.isNotEmpty(nmFnList)) {
                MnjxNmFn nmFn = nmFnList.get(0);
                if (nmFn.getSPrice() != null) {
                    item.setAmount(nmFn.getSPrice().toString());
                }
                if (nmFn.getXPrice() != null) {
                    item.setTaxAmount(nmFn.getXPrice().toString());
                }
            } else {
                List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnrId)
                        .list();
                if (CollUtil.isNotEmpty(pnrFnList)) {
                    MnjxPnrFn pnrFn = pnrFnList.get(0);
                    if (pnrFn.getSPrice() != null) {
                        item.setAmount(pnrFn.getSPrice().toString());
                    }
                    if (pnrFn.getXPrice() != null) {
                        item.setTaxAmount(pnrFn.getXPrice().toString());
                    }
                }
            }
        } else if (StrUtil.isNotEmpty(tn.getNmXnId())) {
            List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, tn.getPnrNmId())
                    .eq(MnjxNmFn::getIsBaby, 1)
                    .list();

            if (CollUtil.isNotEmpty(nmFnList)) {
                MnjxNmFn nmFn = nmFnList.get(0);
                if (nmFn.getSPrice() != null) {
                    item.setAmount(nmFn.getSPrice().toString());
                }
                if (nmFn.getXPrice() != null) {
                    item.setTaxAmount(nmFn.getXPrice().toString());
                }
            } else {
                List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnrId)
                        .eq(MnjxPnrFn::getIsBaby, 1)
                        .list();
                if (CollUtil.isNotEmpty(pnrFnList)) {
                    MnjxPnrFn pnrFn = pnrFnList.get(0);
                    if (pnrFn.getSPrice() != null) {
                        item.setAmount(pnrFn.getSPrice().toString());
                    }
                    if (pnrFn.getXPrice() != null) {
                        item.setTaxAmount(pnrFn.getXPrice().toString());
                    }
                }
            }
        }

        int segCount = StrUtil.isAllNotEmpty(ticket.getS1Id(), ticket.getS2Id()) ? 2 : 1;
        item.setAgencyFee(NumberUtils.formatBigDecimalStr(StrUtil.toString(segCount * 5)));
    }

    /**
     * 构建过滤器信息
     */
    private CrsDailySalesVo.FilterInfo buildFilterInfo(List<CrsDailySalesVo.TicketItem> items) {
        CrsDailySalesVo.FilterInfo filter = new CrsDailySalesVo.FilterInfo();

        if (CollUtil.isEmpty(items)) {
            filter.setAirlines(new ArrayList<>());
            filter.setCurrencyTypes(new ArrayList<>());
            filter.setJobNos(new ArrayList<>());
            filter.setTicketKinds(new ArrayList<>());
            filter.setTicketTypes(new ArrayList<>());
            filter.setPayTypes(new ArrayList<>());
            filter.setPrntNos(new ArrayList<>());
            return filter;
        }

        // 从items中提取唯一值
        filter.setAirlines(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getAirline)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList())
        );
        filter.setCurrencyTypes(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getCurrencyType)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
        filter.setJobNos(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getJobNo)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
        filter.setTicketKinds(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getTicketType)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
        filter.setTicketTypes(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getTicketStatus)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
        filter.setPayTypes(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getPayType)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));
        filter.setPrntNos(
                items.stream()
                        .map(CrsDailySalesVo.TicketItem::getPrntNo)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()));

        return filter;
    }

    /**
     * 创建空的过滤器信息
     */
    private CrsDailySalesVo.FilterInfo createEmptyFilter() {
        CrsDailySalesVo.FilterInfo filter = new CrsDailySalesVo.FilterInfo();
        filter.setAirlines(new ArrayList<>());
        filter.setCurrencyTypes(new ArrayList<>());
        filter.setJobNos(new ArrayList<>());
        filter.setTicketKinds(new ArrayList<>());
        filter.setTicketTypes(new ArrayList<>());
        filter.setPayTypes(new ArrayList<>());
        filter.setPrntNos(new ArrayList<>());
        return filter;
    }
}
