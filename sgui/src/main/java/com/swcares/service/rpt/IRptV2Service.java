package com.swcares.service.rpt;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.CrsDailySalesDto;
import com.swcares.obj.vo.CrsDailySalesVo;

/**
 * 报表V2服务接口
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
public interface IRptV2Service {

    /**
     * 代理人销售日报
     *
     * @param dto 查询参数
     * @return 销售日报数据
     * @throws SguiResultException 异常
     */
    CrsDailySalesVo crsDailySales(CrsDailySalesDto dto) throws SguiResultException;
}
