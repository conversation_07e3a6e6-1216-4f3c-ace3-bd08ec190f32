package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.tc.IRefundTicketService;
import com.swcares.service.tc.ITcV2Service;
import com.swcares.service.tc.ITicketService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
@Service
public class TcV2ServiceImpl implements ITcV2Service {

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private ITicketService iTicketService;

    @Resource
    private IRefundTicketService iRefundTicketService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Override
    public ETReceiptDictEntryVo getETReceiptDictEntry() {
        return null;
    }

    @Override
    public QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = new QueryOfficeInformationVo();
        // 查询OFFICE
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, dto.getOffice())
                .one();
        // 创建返回office对象
        QueryOfficeInformationVo.OfficeInformationVo officeInformationVo = new QueryOfficeInformationVo.OfficeInformationVo();
        officeInformationVo.setOffice(office.getOfficeNo());
        officeInformationVo.setAllowTicket(true);
        officeInformationVo.setUseAms(false);
        // 只处理代理人类型的office
        if ("0".equals(office.getOfficeType())) {
            // 查询代理人信息
            MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
            officeInformationVo.setIata(agent.getAgentIata());
            // 查询代理航司信息
            List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                    .list();
            // 查询航司
            List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(agentAirlineList.stream()
                    .map(MnjxAgentAirline::getAirlineId)
                    .collect(Collectors.toList()));
            // 设置代理航司数据
            for (MnjxAgentAirline agentAirline : agentAirlineList) {
                QueryOfficeInformationVo.AirlineTicketVo airlineTicketVo = new QueryOfficeInformationVo.AirlineTicketVo();
                airlineTicketVo.setAirlineCode(airlineList.stream()
                        .filter(a -> agentAirline.getAirlineId().equals(a.getAirlineId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getAirlineCode());
                airlineTicketVo.setTicketPromise(true);
                vo.getAirlineTickets().add(airlineTicketVo);
            }

            // 设置代理人数据
            QueryOfficeInformationVo.AgentVo agentVo = new QueryOfficeInformationVo.AgentVo();
            agentVo.setAddress(agent.getAgentContactAddress());
            agentVo.setContact(agent.getAgentContactCname());
            agentVo.setFax("NULL");
            agentVo.setPhone(agent.getAgentContactPhone());
            vo.setAgent(agentVo);
        }

        // 查询打票机
        List<MnjxPrinter> printerList = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .isNotNull(MnjxPrinter::getTicketStart)
                .isNotNull(MnjxPrinter::getTicketEnd)
                .list();
        // 设置打票机数据
        for (MnjxPrinter printer : printerList) {
            QueryOfficeInformationVo.TicketMachineVo ticketMachineVo = new QueryOfficeInformationVo.TicketMachineVo();
            ticketMachineVo.setPid(printer.getPrinterPid());
            ticketMachineVo.setDevno(printer.getPrinterNo());
            ticketMachineVo.setCurrency("CNY");
            ticketMachineVo.setType("4");
            ticketMachineVo.setTkt("BSP");
            ticketMachineVo.setCtlPid(printer.getSiId());
            ticketMachineVo.setCtlAgnt("");
            String end = StrUtil.toString(printer.getTicketEnd());
            ticketMachineVo.setTnRange(StrUtil.format("{}-{}", printer.getTicketStart(), end.substring(end.length() - 5)));
            ticketMachineVo.setCurrentTicketNumber(StrUtil.toString(printer.getLastTicket()));
            ticketMachineVo.setTicketRemainder(null);
            vo.getTicketMachines().add(ticketMachineVo);
        }

        // 设置office数据
        vo.setOffice(officeInformationVo);
        return vo;
    }

    @Override
    public List<QueryTicketDetailVo> queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        return iTicketService.queryTicketDetail(dto);
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByDetr(dto);
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        return iTicketService.queryTicketByRtkt(ticketNo);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByPnr(dto);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException {
        return iTicketService.queryTicketByCert(dto);
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.previewRefundTicket(dto);
    }

    @Override
    public FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.findRefundTicket(dto);
    }

    @Override
    public QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException {
        return iRefundTicketService.queryPnrMessage(dto);
    }

    @Override
    public BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException {
        return iRefundTicketService.batchFindRefundFee(dto);
    }

    @Override
    public BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException {
        return iRefundTicketService.batchAutoRefund(dto);
    }

    @Override
    public DeletePnrAndDeleteInfantInfoVo deletePnrAndDeleteInfantInfo(DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException {
        return iRefundTicketService.deletePnrAndDeleteInfantInfo(dto);
    }

    @Override
    public String queryTicketManagementOrganization(String ticketNo) throws SguiResultException {
        return iRefundTicketService.queryTicketManagementOrganization(ticketNo);
    }

    @Override
    public QueryRefundFormVo queryRefundForm(QueryRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.queryRefundForm(dto);
    }

    @Override
    public String tssChangeTicketStatus(TssChangeTicketStatusDto dto) throws SguiResultException {
        // 1. 根据ticketNumber查询mnjx_pnr_nm_ticket表
        List<MnjxPnrNmTicket> ticketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, dto.getTicketNumber().replace("-", ""))
                .list();

        if (ticketList.isEmpty()) {
            throw new SguiResultException("票号不存在");
        }

        MnjxPnrNmTicket ticket = ticketList.get(0);

        // 2. 根据tssType设置ticketStatus1和ticketStatus2
        if ("Suspend".equals(dto.getTssType())) {
            // 3. 如果tssType是Suspend，验证ticketStatus1和ticketStatus2是否是OPEN FOR USE
            boolean hasValidStatus = false;

            // 检查status1
            if (StrUtil.isNotEmpty(ticket.getTicketStatus1())) {
                if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus1())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            // 检查status2
            if (StrUtil.isNotEmpty(ticket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus2())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            if (!hasValidStatus) {
                throw new SguiResultException("COUPON STATUS CODE INVALID");
            }

            // 5. Suspend设置ticketStatus1和ticketStatus2为SUSPENDED
            if (StrUtil.isNotEmpty(ticket.getTicketStatus1()) &&
                Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus1())) {
                ticket.setTicketStatus1(Constant.TICKET_STATUS_SUSPENDED);
            }
            if (StrUtil.isNotEmpty(ticket.getTicketStatus2()) &&
                Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus2())) {
                ticket.setTicketStatus2(Constant.TICKET_STATUS_SUSPENDED);
            }
            MnjxTicketOperateRecord ticketOperateRecord = new MnjxTicketOperateRecord();
            ticketOperateRecord.setTicketNo(ticket.getTicketNo());
            ticketOperateRecord.setTicketStatus1(ticket.getTicketStatus1());
            ticketOperateRecord.setTicketStatus2(ticket.getTicketStatus2());
            ticketOperateRecord.setOperateTime(DateUtils.now());
            ticketOperateRecord.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
            ticketOperateRecord.setSiNo(userInfo.getSiNo());
            iMnjxTicketOperateRecordService.save(ticketOperateRecord);
        } else if ("Resume".equals(dto.getTssType())) {
            // 4. 如果tssType是Resume，验证ticketStatus1和ticketStatus2是否是SUSPENDED
            boolean hasValidStatus = false;

            // 检查status1
            if (StrUtil.isNotEmpty(ticket.getTicketStatus1())) {
                if (Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus1())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            // 检查status2
            if (StrUtil.isNotEmpty(ticket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus2())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            if (!hasValidStatus) {
                throw new SguiResultException("COUPON STATUS CODE INVALID");
            }

            // 6. Resume设置ticketStatus1和ticketStatus2为OPEN FOR USE
            if (StrUtil.isNotEmpty(ticket.getTicketStatus1()) &&
                Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus1())) {
                ticket.setTicketStatus1(Constant.TICKET_STATUS_OPEN_FOR_USE);
            }
            if (StrUtil.isNotEmpty(ticket.getTicketStatus2()) &&
                Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus2())) {
                ticket.setTicketStatus2(Constant.TICKET_STATUS_OPEN_FOR_USE);
            }
            MnjxTicketOperateRecord ticketOperateRecord = new MnjxTicketOperateRecord();
            ticketOperateRecord.setTicketNo(ticket.getTicketNo());
            ticketOperateRecord.setTicketStatus1(ticket.getTicketStatus1());
            ticketOperateRecord.setTicketStatus2(ticket.getTicketStatus2());
            ticketOperateRecord.setOperateTime(DateUtils.now());
            ticketOperateRecord.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
            ticketOperateRecord.setSiNo(userInfo.getSiNo());
            iMnjxTicketOperateRecordService.save(ticketOperateRecord);
        } else {
            throw new SguiResultException("无效的操作类型");
        }

        // 7. 更新mnjx_pnr_nm_ticket表
        boolean updateResult = iMnjxPnrNmTicketService.updateById(ticket);
        if (!updateResult) {
            throw new SguiResultException("更新票务状态失败");
        }

        return "SUCCESS";
    }

    @Override
    public BatchManualRefundTicketVo batchManualRefundTicket(BatchManualRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.batchManualRefundTicket(dto);
    }

    @Override
    public QueryRtktDetailVo queryRtktDetail(QueryRtktDetailDto dto) throws SguiResultException {
        return iRefundTicketService.queryRtktDetail(dto);
    }

    @Override
    public String deleteRefundForm(DeleteRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.deleteRefundForm(dto);
    }

    @Override
    public ModifyRefundFormVo modifyRefundForm(ModifyRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.modifyRefundForm(dto);
    }

    @Override
    public ManualRefundTicketVo manualRefundTicket(BatchManualRefundTicketDto.RefundInfo dto) throws SguiResultException {
        return iRefundTicketService.manualRefundTicket(dto);
    }

    @Override
    public BatchFindRefundFeeZVo batchFindRefundFeeZ(BatchFindRefundFeeZDto dto) throws SguiResultException {
        return iRefundTicketService.batchFindRefundFeeZ(dto);
    }

    @Override
    public BatchRefundVo batchRefund(BatchRefundDto dto) throws SguiResultException {
        return iRefundTicketService.batchRefund(dto);
    }

    @Override
    public String etrfChangeTicketStatus(EtrfChangeTicketStatusDto dto) throws SguiResultException {
        return iRefundTicketService.etrfChangeTicketStatus(dto);
    }

    @Override
    public List<QueryTicketByInvalidVo> queryTicketByInvalid(QueryTicketByInvalidDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 去掉票号中的"-"
        String ticketNo = dto.getTicketNo().replace("-", "");

        // 1. 根据请求参数票号查询mnjx_pnr_nm_ticket表，获取票信息，获取tnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("未找到票面信息");
        }

        // 2. 查询mnjx_pnr_nm_tn表，根据tnId查询，获取pnrNmId或nmXnId
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("未找到票务信息");
        }

        String pnrNmId = pnrNmTn.getPnrNmId();
        String nmXnId = pnrNmTn.getNmXnId();
        boolean isInfant = false;

        // 如果是nmXnId，查询nmXn后获取pnrNmId
        MnjxNmXn nmXn = null;
        if (StrUtil.isNotEmpty(nmXnId)) {
            nmXn = iMnjxNmXnService.getById(nmXnId);
            if (nmXn != null) {
                pnrNmId = nmXn.getPnrNmId();
                isInfant = true;
            }
        }

        if (StrUtil.isEmpty(pnrNmId)) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 3. 查询mnjx_pnr_nm表，根据pnrNmId查询，获取pnrId
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 4. 查询mnjx_pnr表，根据pnrId查询，获取crsPnrNo
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 5. 构建返回数据
        return this.buildQueryTicketByInvalidResult(pnrNmTicket, pnrNm, nmXn, pnr, isInfant);
    }

    /**
     * 构建废票查询结果
     */
    private List<QueryTicketByInvalidVo> buildQueryTicketByInvalidResult(MnjxPnrNmTicket pnrNmTicket,
                                                                         MnjxPnrNm pnrNm, MnjxNmXn nmXn,
                                                                         MnjxPnr pnr, boolean isInfant) {
        QueryTicketByInvalidVo vo = new QueryTicketByInvalidVo();

        // 设置旅客姓名：根据该票对应的是婴儿还是非婴儿确定
        String passengerName;
        if (isInfant && nmXn != null) {
            passengerName = nmXn.getXnCname();
        } else {
            passengerName = pnrNm.getName();
        }
        vo.setPassengerName(passengerName);
        vo.setPassengerNameSuffix(passengerName);

        // 设置旅客类型
        if (isInfant) {
//            vo.setPassengerType("INF");
//            vo.setPnrPsgType("3"); // 婴儿
            vo.setSpecialPassengerType("INF");
        } else {
            // 根据pnrNm的psgType设置
            String psgType = pnrNm.getPsgType();
            if ("0".equals(psgType)) {
//                vo.setPassengerType("ADT");
                vo.setSpecialPassengerType("ADT");
            } else if ("1".equals(psgType)) {
//                vo.setPassengerType("CHD");
                vo.setSpecialPassengerType("CHD");
            } else {
//                vo.setPassengerType("ADT");
                vo.setSpecialPassengerType("ADT");
            }
//            vo.setPnrPsgType(psgType);
        }

        // 设置票号（格式化为带"-"的格式）
        String formattedTicketNo = this.formatTicketNo(pnrNmTicket.getTicketNo());
        vo.setTicketNo(formattedTicketNo);

        // 设置其他固定字段
        vo.setEtType("BSP");
        vo.setTicketTypeCode("D");
        vo.setGovernmentPurchase(false);
        vo.setCrsPnrNo(pnr.getPnrCrs());
        vo.setInvalid(true);
        vo.setAlreadyInvalid(false);
        vo.setPaymentBOP(false);
        vo.setTicketManagementOrganizationCode("BSP");

        return Collections.singletonList(vo);
    }

    /**
     * 格式化票号为带"-"的格式
     */
    private String formatTicketNo(String ticketNo) {
        if (StrUtil.isEmpty(ticketNo) || ticketNo.length() < 4) {
            return ticketNo;
        }
        // 格式：前3位-后面的位数
        return ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
    }

    @Override
    public List<QueryAllPassengersByTktNumberVo> queryAllPassengersByTktNumber(QueryAllPassengersByTktNumberDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 1. 根据请求参数pnrNo查询mnjx_pnr表，获取pnr信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 2. 根据pnrId查询mnjxPnrNm，获取旅客信息
        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("未找到旅客信息");
        }

        List<QueryAllPassengersByTktNumberVo> result = new ArrayList<>();

        // 3. 处理每个旅客的出票信息
        for (MnjxPnrNm passenger : passengers) {
            // 处理成人/儿童旅客的出票信息
            this.processPassengerTickets(passenger, null, pnr, result);

            // 4. 根据pnrNmId查询nmXn获取婴儿信息（如果存在）
            List<MnjxNmXn> infants = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .list();

            // 处理婴儿的出票信息
            for (MnjxNmXn infant : infants) {
                this.processPassengerTickets(passenger, infant, pnr, result);
            }
        }

        if (CollUtil.isEmpty(result)) {
            throw new SguiResultException("未找到出票信息");
        }

        return result;
    }

    /**
     * 处理旅客的出票信息
     */
    private void processPassengerTickets(MnjxPnrNm passenger, MnjxNmXn infant, MnjxPnr pnr,
                                       List<QueryAllPassengersByTktNumberVo> result) {
        // 5. 根据pnrNmId和nmXnId查询mnjx_pnr_nm_tn，获取tn中的票号信息
        List<MnjxPnrNmTn> tnList;
        if (infant != null) {
            // 婴儿票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, infant.getNmXnId())
                    .list();
        } else {
            // 成人/儿童票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, passenger.getPnrNmId())
                    .isNull(MnjxPnrNmTn::getNmXnId) // 确保不是婴儿票
                    .list();
        }

        // 如果查不到tn说明该旅客未出票，跳过
        if (CollUtil.isEmpty(tnList)) {
            return;
        }

        // 6. 处理每个tn的票号信息
        for (MnjxPnrNmTn tn : tnList) {
            QueryAllPassengersByTktNumberVo vo = this.buildPassengerTicketVo(passenger, infant, pnr, tn);
            result.add(vo);
        }
    }

    /**
     * 构建旅客票务信息VO
     */
    private QueryAllPassengersByTktNumberVo buildPassengerTicketVo(MnjxPnrNm passenger, MnjxNmXn infant,
                                                                  MnjxPnr pnr, MnjxPnrNmTn tn) {
        QueryAllPassengersByTktNumberVo vo = new QueryAllPassengersByTktNumberVo();

        // 设置旅客姓名和类型
        if (infant != null) {
            // 婴儿
            vo.setPassengerName(infant.getXnCname());
            vo.setPassengerNameSuffix(infant.getXnCname());
            vo.setPassengerType("3");
            vo.setPnrPsgType("INF");
            vo.setSpecialPassengerType("INF");
        } else {
            // 成人/儿童
            String psgType = passenger.getPsgType();
            vo.setPassengerName(passenger.getName());

            if ("0".equals(psgType)) {
                // 成人
                vo.setPassengerNameSuffix(passenger.getName());
                vo.setPassengerType("0");
                vo.setPnrPsgType("ADT");
                vo.setSpecialPassengerType("ADT");
            } else if ("1".equals(psgType)) {
                // 儿童
                vo.setPassengerNameSuffix(passenger.getName().contains(" CHD") ? passenger.getName() : passenger.getName() + " CHD");
                vo.setPassengerType("1");
                vo.setPnrPsgType("CHD");
                vo.setSpecialPassengerType("CHD");
            } else {
                // 默认成人
                vo.setPassengerNameSuffix(passenger.getName());
                vo.setPassengerType("0");
                vo.setPnrPsgType("ADT");
                vo.setSpecialPassengerType("ADT");
            }
        }

        vo.setTicketNo(tn.getInputValue().replace("TN/", "").replaceAll("/P\\d+", ""));

        // 设置其他固定字段
        vo.setEtType("BSP");
        vo.setTicketTypeCode("D");
        vo.setGovernmentPurchase(false);
        vo.setCrsPnrNo(pnr.getPnrCrs());
        vo.setInvalid(true);
        vo.setAlreadyInvalid(false);
        vo.setPaymentBOP(false);
        vo.setTicketManagementOrganizationCode("BSP");

        return vo;
    }
}
