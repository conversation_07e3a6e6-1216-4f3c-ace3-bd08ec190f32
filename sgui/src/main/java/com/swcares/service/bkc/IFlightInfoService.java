package com.swcares.service.bkc;

import com.swcares.obj.dto.FlightInfoDto;

import java.util.List;

/**
 * 航班信息服务接口
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
public interface IFlightInfoService {
    
    /**
     * 获取指定日期的所有航班信息
     *
     * @param date 日期，格式：yyyy-MM-dd
     * @return 航班信息列表
     */
    List<FlightInfoDto> getFlightInfoByDate(String date);
    
    /**
     * 获取指定航班ID的详细信息
     *
     * @param flightId 航班ID
     * @return 航班详细信息
     */
    FlightInfoDto getFlightInfoById(String flightId);
    
    /**
     * 根据航班号和日期获取航班信息
     *
     * @param flightNo 航班号
     * @param date 日期，格式：yyyy-MM-dd
     * @return 航班信息列表
     */
    List<FlightInfoDto> getFlightInfoByFlightNoAndDate(String flightNo, String date);
    
    /**
     * 刷新指定日期的航班信息缓存
     *
     * @param date 日期，格式：yyyy-MM-dd
     */
    void refreshFlightInfoByDate(String date);
    
    /**
     * 刷新指定航班ID的航班信息缓存
     *
     * @param flightId 航班ID
     */
    void refreshFlightInfoById(String flightId);
}
