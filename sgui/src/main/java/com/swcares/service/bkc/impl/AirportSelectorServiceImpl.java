package com.swcares.service.bkc.impl;

import com.swcares.entity.SguiData;
import com.swcares.service.bkc.IAirportSelectorService;
import com.swcares.service.ISguiDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/24 16:12
 */
@Service
public class AirportSelectorServiceImpl implements IAirportSelectorService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public String airports() {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "airports")
                .one();
        return data.getValue();
    }

    @Override
    public String airportsAll() {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "airports_all")
                .one();
        return data.getValue();
    }
}
