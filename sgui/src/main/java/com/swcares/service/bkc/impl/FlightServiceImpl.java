package com.swcares.service.bkc.impl;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxFlight;
import com.swcares.mapper.AvMapper;
import com.swcares.obj.dto.AvDto;
import com.swcares.obj.vo.AvVo;
import com.swcares.service.bkc.IFlightService;
import com.swcares.service.IMnjxFlightService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:15
 */
@Service
public class FlightServiceImpl implements IFlightService {

    @Resource
    private AvMapper avMapper;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Override
    public List<AvVo> av(AvDto avDto) throws UnifiedResultException {
        AvVo avVo = new AvVo();
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, avDto.getFlightNo())
                .one();
        avVo.setFlightNo(mnjxFlight.getFlightNo());
        List<AvVo> avVoList = avMapper.retrieveAv(avDto);
        return avVoList;
    }
}
