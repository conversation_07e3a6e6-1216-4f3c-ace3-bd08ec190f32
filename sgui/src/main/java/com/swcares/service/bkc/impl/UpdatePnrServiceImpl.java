package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.NumberUtils;
import com.swcares.core.util.PinyinUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.UpdatePnrDto;
import com.swcares.obj.vo.UpdatePnrVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IUpdatePnrService;
import com.swcares.service.et.IBookPnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 更新PNR服务实现
 *
 * <AUTHOR>
 * @date 2025/5/26 16:00
 */
@Slf4j
@Service
public class UpdatePnrServiceImpl implements IUpdatePnrService {

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IBookPnrService iBookPnrService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxNmTcService iMnjxNmTcService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdatePnrVo updatePnr(UpdatePnrDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        List<String> pnrNmIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        Integer kkCount = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                .eq(MnjxNmSsr::getSsrType, "INFT")
                .eq(MnjxNmSsr::getActionCode, "KK")
                .count();
        if (kkCount > 0 && !"KI".equals(dto.getEnvelopType())) {
            throw new SguiResultException("主机错误:CHECK BLINK CODE");
        }

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        List<MnjxPnrRecord> oldRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .list();

        // 初始化各项操作列表
        List<Object> addList = new ArrayList<>();
        List<Object> updateList = new ArrayList<>();
        List<Object> deleteList = new ArrayList<>();

        // 处理各项变更
        this.processPassengers(dto.getPassengers(), pnr, oldRecordList, addList, updateList, deleteList);
        this.processContacts(dto.getContacts(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
        this.processRemarks(dto.getRemarks(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
        this.processSpecialServices(dto.getSpecialServices(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
        this.processIssueLimit(dto.getIssueLimit(), pnr, pnrSegList, oldRecordList, addList, updateList, deleteList);
        this.processManualFare(dto.getManualFare(), dto.getPassengers().getSegments(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
        this.processSegments(dto.getSegments(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
        this.processAutoFares(dto, pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);

        // 处理婴儿SSR INFT修改
        this.processInfantSsrUpdate(pnr);

        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());
        // 筛选updateList中pnrRecord中changeMark不为空的数据，并设置changeAtNo为newAtNo
        updateList.stream()
                .filter(u -> u instanceof MnjxPnrRecord)
                .map(u -> (MnjxPnrRecord) u)
                .filter(r -> StrUtil.isNotEmpty(r.getChangeMark()))
                .forEach(r -> r.setChangeAtNo(newAtNo));

        // 批量执行数据库操作
        this.batchExecuteOperations(addList, updateList, deleteList);

        List<MnjxPnrRecord> recordList = new ArrayList<>();
        // 重新排序所有项的pnr_index
        this.reorderAllPnrIndexesAndUpdate(pnr, recordList);

        // 生成封口记录
        this.generateSealingRecord(pnr, recordList, dto.getEnvelopType(), newAtNo);

        // 构建返回结果
        UpdatePnrVo vo = new UpdatePnrVo();
        vo.setError(null);
        UpdatePnrVo.Dishonest dishonest = new UpdatePnrVo.Dishonest();
        dishonest.setDishonestCheck(false);
        dishonest.setDishonestPassengers("");
        vo.setDishonest(dishonest);

        return vo;
    }

    /**
     * 处理旅客信息变更
     */
    private void processPassengers(UpdatePnrDto.Passengers passengers, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (passengers == null) {
            return;
        }

        // 处理新增旅客
        if (CollUtil.isNotEmpty(passengers.getAddPassengers())) {
            this.processAddPassengers(passengers.getAddPassengers(), pnr, addList);
        }

        // 处理编辑旅客
        if (CollUtil.isNotEmpty(passengers.getEditPassengers())) {
            this.processEditPassengers(passengers.getEditPassengers(), pnr, oldRecordList, updateList, addList, deleteList);
        }
    }

    /**
     * 处理新增旅客
     */
    private void processAddPassengers(List<UpdatePnrDto.AddPassenger> addPassengers, MnjxPnr pnr, List<Object> addList) {
        // 获取当前最大旅客序号
        MnjxPnrNm maxPnrNm = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrNm::getPsgIndex)
                .last("LIMIT 1")
                .one();
        Integer maxPsgIndex = maxPnrNm != null ? maxPnrNm.getPsgIndex() : 0;

        int nextPsgIndex = maxPsgIndex == null ? 1 : maxPsgIndex + 1;

        for (UpdatePnrDto.AddPassenger passenger : addPassengers) {
            // 创建旅客信息
            MnjxPnrNm pnrNm = new MnjxPnrNm();
            pnrNm.setPnrNmId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrNm.setPnrId(pnr.getPnrId());
            pnrNm.setPnrIndex(0); // 后续重新排序
            pnrNm.setPsgIndex(nextPsgIndex++);
            pnrNm.setName(passenger.getFullName());

            // 设置旅客类型
            if ("CHD".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("1"); // 儿童
            } else if ("INF".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("4"); // 婴儿
            } else {
                pnrNm.setPsgType("0"); // 成人
            }

            // 构建input_value
            String inputValue = StrUtil.format("{}({})", passenger.getFullName(), passenger.getPassengerType());
            pnrNm.setInputValue(inputValue);

            addList.add(pnrNm);

            // 处理证件信息
            if (StrUtil.isNotEmpty(passenger.getCertificateType()) && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
                this.processPassengerDocument(passenger, pnrNm, addList);
            }
        }
    }

    /**
     * 处理旅客证件信息
     */
    private void processPassengerDocument(UpdatePnrDto.AddPassenger passenger, MnjxPnrNm pnrNm, List<Object> addList) {
        if ("NI".equals(passenger.getCertificateType())) {
            // 生成FOID SSR
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(pnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 后续重新排序
            nmSsr.setSsrType("FOID");
            nmSsr.setActionCode("HK");

            String ssrInfo = StrUtil.format("SSR FOID HK1 NI{}/P{}",
                    passenger.getCertificateNo(), pnrNm.getPsgIndex());
            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            addList.add(nmSsr);
        }
    }

    /**
     * 处理编辑旅客
     */
    private void processEditPassengers(List<UpdatePnrDto.EditPassenger> editPassengers, MnjxPnr pnr,
                                       List<MnjxPnrRecord> oldRecordList,
                                       List<Object> updateList, List<Object> addList, List<Object> deleteList) throws SguiResultException {
        for (UpdatePnrDto.EditPassenger passenger : editPassengers) {
            // 根据passengerId查找旅客
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passenger.getPassengerId())
                    .one();

            if (pnrNm != null) {
                UpdatePnrDto.OperatePassengerSegment operatePassengerSegment = passenger.getSegments().get(0);
                // 更新旅客姓名，只有未出票才能更新
                if (StrUtil.isNotEmpty(passenger.getFullName()) && ObjectUtil.isNotEmpty(passenger.getOptFlag()) && Boolean.TRUE.equals(passenger.getOptFlag().getUpdateFullName()) && oldRecordList.stream().noneMatch(r -> "TN".equals(r.getPnrType()))) {
//                    // 判断有没有出票记录
//                    boolean issued = oldRecordList.stream().anyMatch(r -> "TN".equals(r.getPnrType()));
//                    // 如果有
//                    if (issued) {
//                        // 判断该修改姓名的旅客是否出了票
//                        issued = oldRecordList.stream().anyMatch(r -> "TN".equals(r.getPnrType()) && !r.getInputValue().contains("/IN") && r.getInputValue().split("/P")[1].equals(pnrNm.getPsgIndex().toString()));
//                    }
//                    // 该旅客没有出票，允许修改姓名
//                    if (!issued) {
                    pnrNm.setName(passenger.getFullName());
                    pnrNm.setInputValue(passenger.getFullName());
                    String replaceName = ReUtil.replaceAll(passenger.getFullName(), "[^\\u4e00-\\u9fa5]+", "");
                    if (!passenger.getFullName().contains("/")) {
                        pnrNm.setIsCnin("CNIN");
                        pnrNm.setQueryName(PinyinUtils.getPinYin(passenger.getFullName(), false));
                    } else {
                        pnrNm.setIsCnin(null);
                        pnrNm.setQueryName(passenger.getFullName());
                    }
                    pnrNm.setInputValue(passenger.getFullName());
//                    }
                }
                UpdatePnrDto.Document document = passenger.getDocument();
                MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "CHLD")
                        .one();
                if (ObjectUtil.isNotEmpty(document) && ObjectUtil.isNotEmpty(passenger.getOptFlag()) && Boolean.TRUE.equals(passenger.getOptFlag().getUpdateDocument())) {
                    if (StrUtil.isEmpty(document.getIdCardNumber())) {
                        throw new SguiResultException("PLEASE INPUT VALID IDENTITY INFORMATION");
                    }
                    // 通过判断身份证确认年龄是否是2-12岁
                    boolean changeToChld = false;
                    if (StrUtil.isNotEmpty(document.getIdCardNumber()) && document.getIdCardNumber().length() == 18) {
                        int age = DateUtil.ageOfNow(DateUtils.ymd2Date(document.getBirthday()));
                        if (age >= 2 && age <= 12 && ObjectUtil.isEmpty(chldSsr)) {
                            changeToChld = true;
                        }
                    }
                    pnrNm.setSex(document.getGender());
                    List<MnjxNmSsr> documentSsrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .in(MnjxNmSsr::getSsrType, "DOCS", "FOID")
                            .list();
                    if (CollUtil.isNotEmpty(documentSsrList)) {
                        MnjxNmSsr documentSsr = documentSsrList.get(0);
                        documentSsr.setSsrType(document.getSsrType());
                        String inputValue;
                        if ("DOCS".equals(documentSsr.getSsrType())) {
                            // SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                            inputValue = StrUtil.format("SSR DOCS {} HK1 {}/{}/{}/{}/{}/{}/{}/{}/H/P{}",
                                    documentSsr.getAirlineCode(), document.getDocumentType(),
                                    document.getVisaIssueCountry(), document.getIdCardNumber(), document.getVisaIssueCountry(),
                                    DateUtils.ymd2Com(document.getBirthday()), document.getGender(), DateUtils.ymd2Com(document.getVisaExpiryDate()),
                                    document.getDocsName(), pnrNm.getPsgIndex());
                            documentSsr.setSsrType("DOCS");
                        } else {
                            inputValue = StrUtil.format("SSR FOID {} HK1 {}{}/P{}", documentSsr.getAirlineCode(), document.getDocumentType(), document.getIdCardNumber(), pnrNm.getPsgIndex());
                            documentSsr.setSsrType("FOID");
                        }
                        documentSsr.setInputValue(inputValue);
                        documentSsr.setSsrInfo(inputValue);
                        updateList.add(documentSsr);
                        // 如果传入旅客类型是ADT，检查年龄是否变更成儿童，添加CHLD SSR
                        if ("ADT".equals(passenger.getPassengerType()) && changeToChld) {
                            passenger.setPassengerType("CHD");
                        }
                    }
                }

                // 注意旅客类型转变
                switch (passenger.getPassengerType()) {
                    case "ADT":
                        pnrNm.setPsgType("0");
                        // 如果原来是儿童，修改后是成人，需要删除儿童SSR
                        if (ObjectUtil.isNotEmpty(chldSsr)) {
                            deleteList.add(chldSsr);
                            Integer chldPnrIndex = chldSsr.getPnrIndex();
                            if (oldRecordList.stream().anyMatch(r -> r.getPnrIndex().intValue() == chldPnrIndex)) {
                                // 标记记录为删除
                                oldRecordList.stream()
                                        .filter(r -> r.getPnrIndex().intValue() == chldPnrIndex)
                                        .forEach(r -> {
                                            r.setChangeMark("X");
                                            updateList.add(r);
                                        });
                            }
                        }
                        break;
                    case "CHD":
                        pnrNm.setPsgType("1");
                        pnrNm.setInputValue(pnrNm.getName() + " CHD");
                        if (ObjectUtil.isEmpty(chldSsr)) {
                            chldSsr = new MnjxNmSsr();
                            chldSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            chldSsr.setPnrNmId(pnrNm.getPnrNmId());
                            chldSsr.setPnrIndex(0); // 后续重新排序
                            chldSsr.setSsrType("CHLD");
                            chldSsr.setActionCode("HK");
                            chldSsr.setAirlineCode(operatePassengerSegment.getAirline());
                            String inputValue = StrUtil.format("SSR CHLD {} HK1 {}/P{}", operatePassengerSegment.getAirline(), DateUtils.ymd2Com(document.getBirthday()), pnrNm.getPsgIndex());
                            chldSsr.setSsrInfo(inputValue);
                            chldSsr.setInputValue(inputValue);
                            addList.add(chldSsr);
                        } else {
                            chldSsr.setAirlineCode(operatePassengerSegment.getAirline());
                            String inputValue = StrUtil.format("SSR CHLD {} HK1 {}/P{}", operatePassengerSegment.getAirline(), DateUtils.ymd2Com(document.getBirthday()), pnrNm.getPsgIndex());
                            chldSsr.setSsrInfo(inputValue);
                            chldSsr.setInputValue(inputValue);
                            updateList.add(chldSsr);
                        }
                        break;
                    default:
                        break;
                }
                if (StrUtil.isNotEmpty(passenger.getSpecialPassengerType())) {
                    switch (passenger.getSpecialPassengerType()) {
                        case "GM":
                        case "JC":
                            pnrNm.setPsgType("4");
                            break;
                        case "UM":
                            pnrNm.setPsgType("2");
                            break;
                        default:
                            break;
                    }
                }
                updateList.add(pnrNm);

                // 旅客信息变更标识
                UpdatePnrDto.OptFlag optFlag = passenger.getOptFlag();
                if (ObjectUtil.isNotEmpty(optFlag)) {
                    // 变更OSI CTCM
                    if (Boolean.TRUE.equals(optFlag.getUpdateOsiCtcm())) {
                        MnjxNmOsi nmOsi = iMnjxNmOsiService.lambdaQuery()
                                .eq(MnjxNmOsi::getPnrNmId, pnrNm.getPnrNmId())
                                .one();
                        String inputValue = StrUtil.format("OSI {} CTCM {}/P{}", passenger.getSegments().get(0).getAirline(), passenger.getOsiCtcm(), pnrNm.getPsgIndex());
                        if (ObjectUtil.isNotEmpty(nmOsi)) {
                            nmOsi.setInputValue(inputValue);
                            nmOsi.setPnrOsiInfo(inputValue);
                            updateList.add(nmOsi);
                        } else {
                            nmOsi = new MnjxNmOsi();
                            nmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            nmOsi.setPnrNmId(pnrNm.getPnrNmId());
                            nmOsi.setPnrIndex(0); // 后续重新排序
                            nmOsi.setPnrOsiType("CTCM");
                            nmOsi.setInputValue(inputValue);
                            nmOsi.setPnrOsiInfo(inputValue);
                            addList.add(nmOsi);
                        }
                    }
                    // 变更SSR CTCM
                    if (Boolean.TRUE.equals(optFlag.getUpdateSsrCtcm())) {
                        MnjxNmSsr ctcmSsr = iMnjxNmSsrService.lambdaQuery()
                                .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                                .eq(MnjxNmSsr::getSsrType, "CTCM")
                                .one();
                        String inputValue = StrUtil.format("SSR CTCM {} HK1 {}/P{}", passenger.getSegments().get(0).getAirline(), passenger.getSsrCtcm(), pnrNm.getPsgIndex());
                        if (ObjectUtil.isNotEmpty(ctcmSsr)) {
                            ctcmSsr.setSsrInfo(inputValue);
                            ctcmSsr.setInputValue(inputValue);
                            updateList.add(ctcmSsr);
                        } else {
                            ctcmSsr = new MnjxNmSsr();
                            ctcmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            ctcmSsr.setPnrNmId(pnrNm.getPnrNmId());
                            ctcmSsr.setPnrIndex(0); // 后续重新排序
                            ctcmSsr.setSsrType("CTCM");
                            ctcmSsr.setSsrInfo(inputValue);
                            ctcmSsr.setInputValue(inputValue);
                            addList.add(ctcmSsr);
                        }
                    }
                }

                // 处理婴儿信息
                if (passenger.getInfantDetail() != null) {
                    if (passenger.getInfantDetail().getSegments().stream().anyMatch(s -> !"ARNK".equals(s.getFlightNumber()) && !s.getHasInft())) {
                        throw new SguiResultException("PLEASE INPUT SSR INFT AND NEED AIRLINE CONFIRM IT");
                    }

                    pnrNm.setPsgType("3");
                    this.processInfantDetail(passenger.getInfantDetail(), pnrNm, oldRecordList, updateList, addList, deleteList);
                }

                // 处理常旅客卡号
                if (ObjectUtil.isNotEmpty(passenger.getOptFlag()) && ObjectUtil.isNotEmpty(passenger.getOptFlag().getUpdateFrequentNumber()) && passenger.getOptFlag().getUpdateFrequentNumber()) {
                    // 暂不支持修改常旅客卡号
                    throw new SguiResultException("INVLID PROFILE NUMBER");
                }
            }
        }
    }

    /**
     * 处理婴儿详情
     */
    private void processInfantDetail(UpdatePnrDto.InfantDetail infantDetail, MnjxPnrNm pnrNm, List<MnjxPnrRecord> oldRecordList, List<Object> updateList, List<Object> addList, List<Object> deleteList) {
        // 查找现有婴儿记录
        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                .one();

        if (nmXn != null) {
            // 更新婴儿信息
            if (StrUtil.isNotEmpty(infantDetail.getBirthday()) && ObjectUtil.isNotEmpty(infantDetail.getOptFlag()) && Boolean.TRUE.equals(infantDetail.getOptFlag().getUpdateBirthday())) {
                nmXn.setXnBirthday(infantDetail.getBirthday().substring(0, 7));
                String inputValue = nmXn.getInputValue();
                inputValue = inputValue.replace(DateUtils.ym2Com(infantDetail.getDocument().getBirthday().substring(0, 7)), DateUtils.ym2Com(nmXn.getXnBirthday()));
                nmXn.setInputValue(inputValue);
                List<MnjxNmSsr> inftSsrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .list();
                for (MnjxNmSsr ssr : inftSsrList) {
                    String ssrInputValue = ssr.getSsrInfo().replace(DateUtils.ymd2Com(infantDetail.getDocument().getBirthday()), DateUtils.ymd2Com(infantDetail.getBirthday()));
                    ssr.setSsrInfo(ssrInputValue);
                    ssr.setInputValue(ssrInputValue);
                    updateList.add(ssr);
                }
            }
            if (StrUtil.isNotEmpty(infantDetail.getChineseName()) && ObjectUtil.isNotEmpty(infantDetail.getOptFlag()) && Boolean.TRUE.equals(infantDetail.getOptFlag().getUpdateChineseName())) {
                nmXn.setXnCname(infantDetail.getChineseName());
            }

            if (ObjectUtil.isNotEmpty(infantDetail.getOptFlag()) && Boolean.TRUE.equals(infantDetail.getOptFlag().getUpdateSsrInftInfo())) {

                List<UpdatePnrDto.Inft> inftList = infantDetail.getInft();
                List<UpdatePnrDto.OperatePassengerSegment> segments = infantDetail.getSegments().stream()
                        .filter(s -> !"ARNK".equals(s.getFlightNumber()))
                        .collect(Collectors.toList());
                // 原PNR中inft数量大于当前传入的航段组数量，说明删除了航段组，需要删除对应的ssr inft
                if (inftList.size() > segments.size()) {
                    inftList.stream()
                            .filter(i -> segments.stream().noneMatch(s -> StrUtil.equalsAny(s.getOrigin() + "-" + s.getDestination(), i.getSeg())))
                            .forEach(i -> {
                                // 删除SSR INFT
                                MnjxNmSsr inftSsr = iMnjxNmSsrService.lambdaQuery()
                                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                                        .eq(MnjxNmSsr::getSsrType, "INFT")
                                        .eq(MnjxNmSsr::getOrgDst, i.getSeg().replace("-", ""))
                                        .one();
                                if (inftSsr != null) {
                                    deleteList.add(inftSsr);
                                    if (oldRecordList.stream().anyMatch(r -> r.getPnrIndex().intValue() == inftSsr.getPnrIndex())) {
                                        // 标记记录为删除
                                        oldRecordList.stream()
                                                .filter(r -> r.getPnrIndex().intValue() == inftSsr.getPnrIndex())
                                                .forEach(r -> {
                                                    r.setChangeMark("X");
                                                    updateList.add(r);
                                                });
                                    }
                                }
                            });
                }
                // 原PNR的inft数量小于航段组数量，需要根据少的航段hasInft为true还是false来确定需不需要添加ssr inft
                else if (inftList.size() < segments.size()) {
                    segments.stream()
                            .filter(s -> inftList.stream().noneMatch(i -> StrUtil.equalsAny(s.getOrigin() + "-" + s.getDestination(), i.getSeg())))
                            .forEach(s -> {
                                if (s.getHasInft()) {
                                    // 新增SSR INFT
                                    MnjxNmSsr inftSsr = new MnjxNmSsr();
                                    inftSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                    inftSsr.setPnrNmId(pnrNm.getPnrNmId());
                                    inftSsr.setPnrIndex(0); // 后续重新排序
                                    inftSsr.setSsrType("INFT");
                                    inftSsr.setActionCode("KK");
                                    inftSsr.setAirlineCode(s.getAirline());
                                    inftSsr.setOrgDst(StrUtil.format("{}{}", s.getOrigin(), s.getDestination()));
                                    MnjxPnrSeg pnrSeg = this.findPnrSegment(s.getOrigin(), s.getDestination(), s.getAirline(),
                                            s.getFlightNumber(), s.getFlightDate(), pnrNm.getPnrId());
                                    inftSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
                                    String inputValue = StrUtil.format("SSR INFT {} KK1 {}{} {} {}{} {} {}/P{}", s.getAirline(), s.getOrigin(),
                                            s.getDestination(), s.getFlightNumber(), s.getClassId(),
                                            DateUtils.ymd2Com(s.getDepartureDate().split("T")[0]), infantDetail.getFullName(),
                                            DateUtils.ymd2Com(infantDetail.getBirthday()), pnrNm.getPsgIndex());
                                    inftSsr.setSsrInfo(inputValue);
                                    inftSsr.setInputValue(inputValue);
                                    addList.add(inftSsr);
                                }
                            });
                }
                // 数量一样，判断对应segment的hasInft是不是false来确定删除对应的ssr inft
                else {
                    segments.stream()
                            .filter(s -> inftList.stream().anyMatch(i -> StrUtil.equalsAny(s.getOrigin() + "-" + s.getDestination(), i.getSeg())) && !s.getHasInft())
                            .forEach(s -> {
                                // 删除SSR INFT
                                MnjxNmSsr inftSsr = iMnjxNmSsrService.lambdaQuery()
                                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                                        .eq(MnjxNmSsr::getSsrType, "INFT")
                                        .eq(MnjxNmSsr::getOrgDst, s.getOrigin() + s.getDestination())
                                        .one();
                                if (inftSsr != null) {
                                    deleteList.add(inftSsr);
                                    if (oldRecordList.stream().anyMatch(r -> r.getPnrIndex().intValue() == inftSsr.getPnrIndex())) {
                                        // 标记记录为删除
                                        oldRecordList.stream()
                                                .filter(r -> r.getPnrIndex().intValue() == inftSsr.getPnrIndex())
                                                .forEach(r -> {
                                                    r.setChangeMark("X");
                                                    updateList.add(r);
                                                });
                                    }
                                }
                            });
                }
                updateList.add(nmXn);
            }
        } else {
            // 不存在则新增
            // 新增xn
            nmXn = new MnjxNmXn();
            nmXn.setNmXnId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmXn.setPnrNmId(pnrNm.getPnrNmId());
            nmXn.setPnrIndex(0); // 后续重新排序
            nmXn.setXnCname(infantDetail.getFullName());
            nmXn.setXnBirthday(infantDetail.getBirthday().substring(0, 7));
            nmXn.setInputValue(StrUtil.format("XN/IN/{} INF({})/P{}", infantDetail.getFullName(), DateUtils.ym2Com(infantDetail.getBirthday().substring(0, 7)), pnrNm.getPsgIndex()));
            // 根据婴儿航段新增SSR INFT
            List<UpdatePnrDto.OperatePassengerSegment> segments = infantDetail.getSegments();
//            List<UpdatePnrDto.OperatePassengerSegment> selectSegmentList = segments.stream()
//                    .filter(s -> infantDetail.getSelectedSegList().contains(StrUtil.format("{}-{}-{}", s.getOrigin(), s.getDestination(), s.getDepartureDate())))
//                    .collect(Collectors.toList());
            for (UpdatePnrDto.OperatePassengerSegment operatePassengerSegment : segments) {
                if (operatePassengerSegment.getHasInft()) {
                    MnjxPnrSeg pnrSeg = this.findPnrSegment(operatePassengerSegment.getOrigin(), operatePassengerSegment.getDestination(), operatePassengerSegment.getAirline(),
                            operatePassengerSegment.getFlightNumber(), operatePassengerSegment.getFlightDate(), pnrNm.getPnrId());
                    MnjxNmSsr inftSsr = new MnjxNmSsr();
                    inftSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    inftSsr.setPnrNmId(pnrNm.getPnrNmId());
                    inftSsr.setPnrIndex(0); // 后续重新排序
                    inftSsr.setSsrType("INFT");
                    inftSsr.setActionCode("KK");
                    inftSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
                    inftSsr.setOrgDst(StrUtil.format("{}{}", operatePassengerSegment.getOrigin(), operatePassengerSegment.getDestination()));
                    String inputValue = StrUtil.format("SSR INFT {} KK1 {}{} {} {}{} {} {}/P{}", operatePassengerSegment.getAirline(), operatePassengerSegment.getOrigin(),
                            operatePassengerSegment.getDestination(), operatePassengerSegment.getFlightNumber(), operatePassengerSegment.getClassId(),
                            DateUtils.ymd2Com(operatePassengerSegment.getDepartureDate().split("T")[0]), infantDetail.getFullName(),
                            DateUtils.ymd2Com(infantDetail.getBirthday()), pnrNm.getPsgIndex());
                    inftSsr.setSsrInfo(inputValue);
                    inftSsr.setInputValue(inputValue);
                    inftSsr.setAirlineCode(operatePassengerSegment.getAirline());
                    inftSsr.setFltDate(operatePassengerSegment.getDepartureDate().split("T")[0]);
                    addList.add(inftSsr);
                }
            }
            addList.add(nmXn);
        }
    }

    /**
     * 处理联系方式变更
     */
    private void processContacts(List<UpdatePnrDto.Contact> contacts, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                 List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(contacts)) {
            return;
        }

        for (UpdatePnrDto.Contact contact : contacts) {
            switch (contact.getOperateType()) {
                case "A":
                    this.addContact(contact, pnr, addList);
                    break;
                case "M":
                    this.modifyContact(contact, pnr, oldRecordList, pnrNmIdList, updateList, deleteList, addList);
                    break;
                case "D":
                    this.deleteContact(contact, pnr, oldRecordList, pnrNmIdList, updateList, deleteList);
                    break;
            }
        }
    }

    /**
     * 新增联系方式
     */
    private void addContact(UpdatePnrDto.Contact contact, MnjxPnr pnr, List<Object> addList) {
        if ("CT".equals(contact.getType())) {
            MnjxPnrCt pnrCt = new MnjxPnrCt();
            pnrCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrCt.setPnrId(pnr.getPnrId());
            pnrCt.setPnrIndex(0); // 后续重新排序
            pnrCt.setCtText(contact.getText());

            // 构建输入值
            String officeNo = iSguiCommonService.getCurrentUserInfo().getMnjxOffice().getOfficeNo();
            pnrCt.setCityCode(officeNo.substring(0, 3));
            String inputValue = contact.getText();
            if (StrUtil.isNotEmpty(contact.getAirline())) {
                inputValue = StrUtil.format("{} {}", contact.getAirline(), contact.getText());
            }
            pnrCt.setInputValue(inputValue);

            addList.add(pnrCt);
        } else if ("CTCE".equals(contact.getType())) {
            MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
            pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrOsi.setPnrId(pnr.getPnrId());
            pnrOsi.setPnrIndex(0); // 后续重新排序
            pnrOsi.setPnrOsiType("CTCE");
            pnrOsi.setAirlineCode(contact.getAirline());

            String inputValue = StrUtil.format("OSI {} CTCE{}", contact.getAirline(), contact.getText());
            pnrOsi.setInputValue(inputValue);
            pnrOsi.setPnrOsiInfo(inputValue);

            addList.add(pnrOsi);
        }
    }

    /**
     * 修改联系方式
     */
    private void modifyContact(UpdatePnrDto.Contact contact, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                               List<Object> updateList, List<Object> deleteList, List<Object> addList) {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(contact.getDeleteIndexList())) {
            this.deleteContactByIndexes(contact.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, updateList, deleteList);
        }

        // 再新增新记录
        this.addContact(contact, pnr, addList);
    }

    /**
     * 删除联系方式
     */
    private void deleteContact(UpdatePnrDto.Contact contact, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(contact.getDeleteIndexList())) {
            this.deleteContactByIndexes(contact.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, updateList, deleteList);
        }
    }

    /**
     * 根据索引删除联系方式
     */
    private void deleteContactByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> updateList, List<Object> deleteList) {
        for (Integer index : deleteIndexList) {
            // 查找对应的记录类型
            List<MnjxPnrRecord> filterRecord = oldRecordList.stream()
                    .filter(r -> index.intValue() == r.getPnrIndex())
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(filterRecord)) {
                MnjxPnrRecord record = filterRecord.get(0);
                if ("CT".equals(record.getPnrType())) {
                    MnjxPnrCt pnrCt = iMnjxPnrCtService.lambdaQuery()
                            .eq(MnjxPnrCt::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrCt::getPnrIndex, index)
                            .one();
                    if (pnrCt != null) {
                        deleteList.add(pnrCt);
                    }
                } else if ("NM CT".equals(record.getPnrType())) {
                    MnjxNmCt nmCt = iMnjxNmCtService.lambdaQuery()
                            .in(MnjxNmCt::getPnrNmId, pnrNmIdList)
                            .eq(MnjxNmCt::getPnrIndex, index)
                            .one();
                    if (nmCt != null) {
                        deleteList.add(nmCt);
                    }
                }
                // 标记记录为删除
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 处理备注信息变更
     */
    private void processRemarks(List<UpdatePnrDto.Remark> remarks, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(remarks)) {
            return;
        }

        for (UpdatePnrDto.Remark remark : remarks) {
            switch (remark.getOperateType()) {
                case "A":
                    this.addRemark(remark, pnr, addList);
                    break;
                case "M":
                    this.modifyRemark(remark, pnr, oldRecordList, pnrNmIdList, updateList, deleteList);
                    break;
                case "D":
                    this.deleteRemark(remark, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
                    break;
            }
        }
    }

    /**
     * 新增备注
     */
    private void addRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr, List<Object> addList) {
        if ("RMK".equals(remark.getType())) {
            MnjxPnrRmk pnrRmk = new MnjxPnrRmk();
            pnrRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrRmk.setPnrId(pnr.getPnrId());
            pnrRmk.setPnrIndex(0); // 后续重新排序
            pnrRmk.setRmkName("FREE");

            String inputValue = StrUtil.format("RMK {}", remark.getText());
            pnrRmk.setRmkInfo(inputValue);
            pnrRmk.setInputValue(inputValue);

            addList.add(pnrRmk);
        } else if ("OSI".equals(remark.getType())) {
            MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
            pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrOsi.setPnrId(pnr.getPnrId());
            pnrOsi.setPnrIndex(0); // 后续重新排序

            if (remark.getText().startsWith("CTCT")) {
                pnrOsi.setPnrOsiType("CTCT");
            } else if (remark.getText().startsWith("CTCE")) {
                pnrOsi.setPnrOsiType("CTCE");
            }

            String inputValue = StrUtil.format("OSI {} {}", remark.getAirline(), remark.getText());
            pnrOsi.setInputValue(inputValue);
            pnrOsi.setPnrOsiInfo(inputValue);

            addList.add(pnrOsi);
        }
    }

    /**
     * 修改备注
     */
    private void modifyRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                              List<Object> updateList, List<Object> deleteList) {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(remark.getDeleteIndexList())) {
            this.deleteRemarkByIndexes(remark.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }

        // 再新增新记录
        this.addRemark(remark, pnr, updateList);
    }

    /**
     * 删除备注
     */
    private void deleteRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        if (CollUtil.isNotEmpty(remark.getDeleteIndexList())) {
            this.deleteRemarkByIndexes(remark.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }
    }

    /**
     * 根据索引删除备注
     */
    private void deleteRemarkByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        for (Integer index : deleteIndexList) {
            List<MnjxPnrRecord> recordList = oldRecordList.stream()
                    .filter(r -> r.getPnrIndex() == index)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(recordList)) {
                MnjxPnrRecord record = recordList.get(0);
                if ("RMK".equals(record.getPnrType())) {
                    MnjxPnrRmk pnrRmk = iMnjxPnrRmkService.lambdaQuery()
                            .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrRmk::getPnrIndex, index)
                            .one();
                    if (pnrRmk != null) {
                        deleteList.add(pnrRmk);
                    }
                } else if ("OSI".equals(record.getPnrType())) {
                    MnjxPnrOsi pnrOsi = iMnjxPnrOsiService.lambdaQuery()
                            .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrOsi::getPnrIndex, index)
                            .one();
                    if (pnrOsi != null) {
                        deleteList.add(pnrOsi);
                    }
                } else if ("NM RMK".equals(record.getPnrType())) {
                    deleteList.addAll(iMnjxNmRmkService.lambdaQuery()
                            .eq(MnjxNmRmk::getPnrIndex, index)
                            .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                            .list());
                } else if ("NM OSI".equals(record.getPnrType())) {
                    deleteList.addAll(iMnjxNmOsiService.lambdaQuery()
                            .eq(MnjxNmOsi::getPnrIndex, index)
                            .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                            .list());
                }
                // 标记记录为删除
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 处理特殊服务变更
     */
    private void processSpecialServices(List<UpdatePnrDto.SpecialService> specialServices, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                        List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (CollUtil.isEmpty(specialServices)) {
            return;
        }

        for (UpdatePnrDto.SpecialService service : specialServices) {
            switch (service.getOperateType()) {
                case "A":
                    this.addSpecialService(service, pnr, addList);
                    break;
                case "M":
                    this.modifySpecialService(service, pnr, oldRecordList, pnrNmIdList, updateList, deleteList);
                    break;
                case "D":
                    this.deleteSpecialService(service, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
                    break;
            }
        }
    }

    /**
     * 新增特殊服务
     */
    private void addSpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        // 查找对应的航段
        MnjxPnrSeg pnrSeg = this.findPnrSegment(service.getOrigin(), service.getDestination(), service.getAirline(),
                service.getFlightNumber(), service.getFlightDate(), pnr.getPnrId());

        if (pnrSeg == null) {
            throw new SguiResultException("未找到对应的航段信息");
        }

        for (Integer paxId : service.getPaxIds()) {
            // 查找旅客
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, paxId)
                    .one();

            if (pnrNm != null) {
                MnjxNmSsr nmSsr = new MnjxNmSsr();
                nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmSsr.setPnrNmId(pnrNm.getPnrNmId());
                nmSsr.setPnrIndex(0); // 后续重新排序
                nmSsr.setSsrType(service.getSsrCode());
                nmSsr.setActionCode("HK"); // 更新后修改为HK
                nmSsr.setAirlineCode(service.getAirline());
                nmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
                nmSsr.setOrgDst(service.getOrigin() + service.getDestination());
                nmSsr.setFltDate(service.getFlightDate());

                String ssrInfo = StrUtil.format("SSR {} {} HK1 {}{}{}/P{}",
                        service.getSsrCode(), service.getAirline(), service.getOrigin(), service.getDestination(),
                        StrUtil.isNotEmpty(service.getText()) ? " " + service.getText() : "", paxId);
                nmSsr.setSsrInfo(ssrInfo);
                nmSsr.setInputValue(ssrInfo);

                addList.add(nmSsr);
            }
        }
    }

    /**
     * 查找PNR航段
     */
    private MnjxPnrSeg findPnrSegment(String origin, String destination, String airline, String flightNumber,
                                      String flightDate, String pnrId) {
        if (!flightNumber.matches("[0-9A-Z]{2}\\d{3,5}")) {
            flightNumber = airline + flightNumber;
        }
        if (flightDate.contains("T")) {
            flightDate = flightDate.split("T")[0];
        }
        return iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .eq(MnjxPnrSeg::getOrg, origin)
                .eq(MnjxPnrSeg::getDst, destination)
                .eq(MnjxPnrSeg::getFlightNo, flightNumber)
                .eq(MnjxPnrSeg::getFlightDate, flightDate)
                .one();
    }

    /**
     * 修改特殊服务
     */
    private void modifySpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                      List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(service.getDeleteIndexList())) {
            this.deleteSpecialServiceByIndexes(service.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }

        // 再新增新记录
        this.addSpecialService(service, pnr, updateList);
    }

    /**
     * 删除特殊服务
     */
    private void deleteSpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        if (CollUtil.isNotEmpty(service.getDeleteIndexList())) {
            this.deleteSpecialServiceByIndexes(service.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }
    }

    /**
     * 根据索引删除特殊服务
     */
    private void deleteSpecialServiceByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        for (Integer index : deleteIndexList) {
            List<MnjxPnrRecord> pnrRecordList = oldRecordList.stream()
                    .filter(r -> r.getPnrIndex() == index)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrRecordList)) {
                continue;
            }
            MnjxPnrRecord record = pnrRecordList.get(0);
            if (record != null && "SSR".equals(record.getPnrType())) {
                // 查找对应的SSR记录
                List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrIndex, index)
                        .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                        .list();

                deleteList.addAll(ssrList);

                // 标记记录为删除
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 处理出票时限变更
     */
    private void processIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<MnjxPnrSeg> pnrSegList, List<MnjxPnrRecord> oldRecordList,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (issueLimit == null || StrUtil.isEmpty(issueLimit.getOperateType())) {
            return;
        }

        switch (issueLimit.getOperateType()) {
            case "A":
                this.addIssueLimit(issueLimit, pnr, pnrSegList, addList);
                break;
            case "M":
                this.modifyIssueLimit(issueLimit, pnr, pnrSegList, updateList);
                break;
            case "D":
                this.deleteIssueLimit(issueLimit, pnr, oldRecordList, updateList, deleteList);
                break;
        }
    }

    /**
     * 新增出票时限
     */
    private void addIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<MnjxPnrSeg> pnrSegList, List<Object> addList) throws SguiResultException {
        MnjxPnrTk pnrTk = new MnjxPnrTk();
        pnrTk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrTk.setPnrId(pnr.getPnrId());
        pnrTk.setPnrIndex(0); // 后续重新排序
        String issueLimitTime = issueLimit.getIssueLimitTime();
        Date timeLimitDate = DateUtil.parse(issueLimitTime);
        if (timeLimitDate.compareTo(DateUtil.parse(pnrSegList.get(0).getFlightDate() + " " + pnrSegList.get(0).getEstimateOff().substring(0, 2) + ":" + pnrSegList.get(0).getEstimateOff().substring(2) + ":00")) > 0) {
            throw new SguiResultException("请检查出票时限，出票时限不能在航班起飞后");
        }
        String officeNo = iSguiCommonService.getCurrentUserInfo().getMnjxOffice().getOfficeNo();
        if (!officeNo.equals(issueLimit.getIssueLimitOffice())) {
            throw new SguiResultException("NON-CRT OFFICE");
        }
        pnrTk.setPlanEtdzDate(DateUtil.format(timeLimitDate, "yyyy-MM-dd"));
        pnrTk.setPlanEtdzTime(DateUtil.format(timeLimitDate, "HHmm"));
        pnrTk.setEtdzOffice(issueLimit.getIssueLimitOffice());

        String inputValue = StrUtil.format("TL/{}/{}/{}", pnrTk.getPlanEtdzTime(), DateUtils.ymd2Com(pnrTk.getPlanEtdzDate()), pnrTk.getEtdzOffice());
        pnrTk.setInputValue(inputValue);

        addList.add(pnrTk);
    }

    /**
     * 修改出票时限
     */
    private void modifyIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<MnjxPnrSeg> pnrSegList, List<Object> updateList) throws SguiResultException {
        // 查找现有的出票时限记录
        MnjxPnrTk existingTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .one();

        if (existingTk != null) {
            String issueLimitTime = issueLimit.getIssueLimitTime();
            Date timeLimitDate = DateUtil.parse(issueLimitTime);
            if (timeLimitDate.compareTo(DateUtil.parse(pnrSegList.get(0).getFlightDate() + " " + pnrSegList.get(0).getEstimateOff().substring(0, 2) + ":" + pnrSegList.get(0).getEstimateOff().substring(2) + ":00")) >= 0) {
                throw new SguiResultException("请检查出票时限，出票时限不能在航班起飞后");
            }
            String officeNo = iSguiCommonService.getCurrentUserInfo().getMnjxOffice().getOfficeNo();
            if (!officeNo.equals(issueLimit.getIssueLimitOffice())) {
                throw new SguiResultException("NON-CRT OFFICE");
            }
            existingTk.setPlanEtdzDate(DateUtil.format(timeLimitDate, "yyyy-MM-dd"));
            existingTk.setPlanEtdzTime(DateUtil.format(timeLimitDate, "HHmm"));
            existingTk.setEtdzOffice(issueLimit.getIssueLimitOffice());

            String inputValue = StrUtil.format("TL/{}/{}/{}", existingTk.getPlanEtdzTime(), DateUtils.ymd2Com(existingTk.getPlanEtdzDate()), existingTk.getEtdzOffice());
            existingTk.setInputValue(inputValue);

            updateList.add(existingTk);
        }
    }

    /**
     * 删除出票时限
     */
    private void deleteIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<Object> updateList, List<Object> deleteList) {
        MnjxPnrTk existingTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .one();

        if (existingTk != null) {
            deleteList.add(existingTk);
            oldRecordList.stream()
                    .filter(r -> r.getPnrIndex().intValue() == existingTk.getPnrIndex())
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
        }
    }

    /**
     * 处理手工运价
     */
    private void processManualFare(UpdatePnrDto.ManualFare manualFare, List<UpdatePnrDto.PassengerSegment> segments, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (manualFare == null || StrUtil.isEmpty(manualFare.getOperateType())) {
            return;
        }

        switch (manualFare.getOperateType()) {
            case "A":
                this.addManualFare(manualFare, segments, pnr, addList);
                break;
            case "M":
                this.modifyManualFare(manualFare, segments, pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
                break;
            case "D":
                this.deleteManualFare(manualFare, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
                break;
        }
    }

    /**
     * 新增手工运价
     */
    private void addManualFare(UpdatePnrDto.ManualFare manualFare, List<UpdatePnrDto.PassengerSegment> segments, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        UpdatePnrDto.PnrInfo pnrInfo = manualFare.getPnrInfo();
        if (pnrInfo == null) {
            return;
        }

        // 处理FC信息
        if (CollUtil.isNotEmpty(pnrInfo.getFcInfos())) {
            this.processManualFareFcInfos(pnrInfo.getFcInfos(), segments, pnr, addList);
        }

        // 处理FN信息
        if (CollUtil.isNotEmpty(pnrInfo.getFnInfos())) {
            this.processManualFareFnInfos(pnrInfo.getFnInfos(), pnr, addList);
        }

        // 处理FP信息
        if (CollUtil.isNotEmpty(pnrInfo.getFpInfos())) {
            this.processManualFareFpInfos(pnrInfo.getFpInfos(), pnr, addList);
        }

        // 处理EI信息
        if (CollUtil.isNotEmpty(pnrInfo.getEiInfos())) {
            this.processManualFareEiInfos(pnrInfo.getEiInfos(), pnr, addList);
        }

        // 处理TC信息
        if (CollUtil.isNotEmpty(pnrInfo.getTcInfos())) {
            this.processManualFareTcInfos(pnrInfo.getTcInfos(), pnr, addList);
        }
    }

    private void processManualFareTcInfos(List<String> tcInfos, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (String tcInfo : tcInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(tcInfo), StandardCharsets.UTF_8);

            if (decodedText.matches(".+/P\\d+(/\\d+)*")) {
                // 旅客级别TC
                this.createManualFareNmTcRecords(decodedText, pnr, addList);
            } else {
                // PNR级别TC
                this.createManualFarePnrTcRecord(decodedText, pnr, addList);
            }
        }
    }

    private void createManualFarePnrTcRecord(String decodedText, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        boolean isInf = decodedText.contains("IN/");
        // 先查是否已存在EI
        List<MnjxPnrTc> pnrTcList = iMnjxPnrTcService.lambdaQuery()
                .eq(MnjxPnrTc::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrTcList)) {
            if (isInf && pnrTcList.stream().anyMatch(e -> e.getInputValue().contains("/IN/"))) {
                throw new SguiResultException("婴儿TC已存在");
            } else if (!isInf && pnrTcList.stream().anyMatch(e -> !e.getInputValue().contains("/IN/"))) {
                throw new SguiResultException("TC已存在");
            }
        }
        MnjxPnrTc pnrTc = new MnjxPnrTc();
        pnrTc.setPnrTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrTc.setPnrId(pnr.getPnrId());
        pnrTc.setPnrIndex(0); // 后续重新排序
        pnrTc.setInputValue(decodedText.replace("F/", "TC/"));
        pnrTc.setTcInfo(decodedText.replace("F/", "TC/"));

        addList.add(pnrTc);
    }

    private void createManualFareNmTcRecords(String decodedText, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        String pIndex = decodedText.split("/P")[1];
        String[] pSplit = pIndex.split("/");
        for (String passengerId : pSplit) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passengerId)
                    .one();

            if (pnrNm != null) {
//                boolean isInf = decodedText.contains("IN/");
                // 查是否已经有ei了
//                List<MnjxNmTc> nmTcList = iMnjxNmTcService.lambdaQuery()
//                        .eq(MnjxNmTc::getPnrNmId, pnrNm.getPnrNmId())
//                        .list();
//                if (CollUtil.isNotEmpty(nmTcList)) {
//                    if (isInf && nmTcList.stream().anyMatch(e -> e.getInputValue().contains("/IN/"))) {
//                        throw new SguiResultException("婴儿TC已存在");
//                    } else if (!isInf && nmTcList.stream().anyMatch(e -> !e.getInputValue().contains("/IN/"))) {
//                        throw new SguiResultException("TC已存在");
//                    }
//                }

                MnjxNmTc nmTc = new MnjxNmTc();
                nmTc.setNmTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmTc.setPnrNmId(pnrNm.getPnrNmId());
                nmTc.setPnrIndex(0); // 后续重新排序

                String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                nmTc.setInputValue(modifiedText.replace("F/", "TC/"));
                nmTc.setTcInfo(modifiedText.replace("F/", "TC/"));

                addList.add(nmTc);
            }
        }
    }

    /**
     * 处理FC信息
     */
    private void processManualFareFcInfos(List<UpdatePnrDto.FareInfo> fcInfos, List<UpdatePnrDto.PassengerSegment> segments, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (UpdatePnrDto.FareInfo fcInfo : fcInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fcInfo.getText()), StandardCharsets.UTF_8);

            if (decodedText.matches(".+END(\\s[*]{2}([(][A-Z]{2}[)]))?/P\\d+(/\\d+)*")) {
                // 旅客级别FC
                this.createManualFareNmFcRecords(decodedText, segments, fcInfo.getInfSign(), pnr, addList);
            } else {
                // PNR级别FC
                this.createManualFarePnrFcRecord(decodedText, segments, fcInfo.getInfSign(), pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FC记录
     */
    private void createManualFareNmFcRecords(String decodedText, List<UpdatePnrDto.PassengerSegment> segments, Boolean infSign,
                                             MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        String passengerType = Boolean.TRUE.equals(infSign) ? "INF" : decodedText.contains("CH") ? "CHD" : "ADT";

        String pIndex = decodedText.contains("END/P") ? decodedText.split("END/P")[1] : decodedText.split("\\)/P")[1];
        String[] pSplit = pIndex.split("/");
        for (String passengerId : pSplit) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passengerId)
                    .one();

            if (pnrNm != null) {
                MnjxNmFc nmFc = new MnjxNmFc();
                nmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmFc.setPnrNmId(pnrNm.getPnrNmId());
                nmFc.setPnrIndex(0); // 后续重新排序
                String patType = passengerType.substring(0, 2);
                nmFc.setPatType(patType);
                nmFc.setIsBaby(Boolean.TRUE.equals(infSign) ? 1 : 0);

                Map<Integer, String> segTicketAmountMap = new HashMap<>();
                // 修改文本中的旅客引用
                String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId).replace("/A/", "/");
                // FC/PEK CA SHA 2150.00Y // SZX CA PEK 3650.00Y CNY 5800.00 END/P1/2
                // FC/IN/PEK CA SHA 220.00YIN90 // SZX CA PEK 370.00YIN90 CNY 590.00 END/P2
                // FC/PEK CA SHA 1080.00YCH50 // SZX CA PEK 1830.00YCH50 CNY 2910.00 END/P3
                // FC/A/PEK A-22MAY26 CA SHA 1080.00YCH50 //WUH B-0 A-0 MU PKX 700.00V CNY1780.00END **(CH)/P3
                // FC/A/PEK A-22MAY26 CA SHA 1150.00W //WUH B-0 A-0 MU PKX 700.00V CNY1850.00END/P1
                if (modifiedText.contains("//")) {
                    String nmFcRegx = "FC/(A/)?(IN/)?((//\\s*|[A-Z]{3}\\s(B-\\w+)?(A-\\w+)?(\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2}\\d*)?\\s))+)CNY\\s*([0-9.]+)\\sEND(\\s[*]{2}([(][A-Z]+[)]))?(/P([0-9]{1,2}))?";
                    if (ReUtil.isMatch(nmFcRegx, modifiedText)) {
                        List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(nmFcRegx), modifiedText);
                        // 总价
                        nmFc.setTotalPrice(new BigDecimal(allGroups.get(9)));
                        // 每个航段的价格
                        String segPriceArea = allGroups.get(3);
                        List<String> segPriceList = Arrays.asList(segPriceArea.split(" "));
                        int count = 1;
                        for (String segPrice : segPriceList) {
                            if ("//".equals(segPrice)) {
                                count++;
                                continue;
                            }
                            if (segPrice.matches("\\d+\\.00[A-Z]([A-Z]{2}\\d*[A-Z0-9]*)?")) {
                                segTicketAmountMap.put(count, segPrice);
                                count++;
                            }
                        }
                    }
                }
                // FC/SHA MU CKG 1350.00L CNY 1350.00 END
                // FC/IN/CTU 3U PEK 1123.00YIN90 CA SHA 1123.00Y CNY 2246.00 END
                else {
                    String nmFcRegx = "FC/(A/)?(IN/)?([A-Z]{3})\\s((\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2}\\d*)?\\s?)+)\\s*([A-Z]{3})\\s*([0-9.]+)\\s?END(\\s[*]{2}([(][A-Z]+[)]))?(/P([0-9]{1,2}))?";
                    if (ReUtil.isMatch(nmFcRegx, modifiedText)) {
                        List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(nmFcRegx), modifiedText);
                        // 总价
                        nmFc.setTotalPrice(new BigDecimal(allGroups.get(8)));
                        // 每个航段的价格
                        String segPriceArea = allGroups.get(4);
                        // 3U PEK 1123.00Y CA SHA 1123.00Y
                        List<String> segPriceList = Arrays.asList(segPriceArea.split(" "));
                        int count = 1;
                        for (String segPrice : segPriceList) {
                            if (segPrice.matches("\\d+\\.00[A-Z]([A-Z]{2}\\d*[A-Z0-9]*)?")) {
                                segTicketAmountMap.put(count, segPrice);
                                count++;
                            }
                        }
                    }
                }
                nmFc.setCurrency("CNY");

                // 设置每个航段舱位价格
                int segIndex = 1;
                for (int i = 0; i < segments.size(); i++) {
                    UpdatePnrDto.PassengerSegment passengerSegment = segments.get(i);
                    if ("ARNK".equals(passengerSegment.getSegmentType())) {
                        if (i == 0 || i == segments.size() - 1) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        segIndex++;
                        continue;
                    }
                    String ticketAmount = segTicketAmountMap.get(segIndex);
                    ticketAmount = ticketAmount.split("\\.")[0] + ".00";
                    // 设置票价
                    switch (segIndex) {
                        case 1:
                            nmFc.setSeg1Price(new BigDecimal(ticketAmount));
                            nmFc.setSeg1Cabin(passengerSegment.getCabinCode());
                            nmFc.setSeg1PriceType(patType);
                            break;
                        case 2:
                            nmFc.setSeg2Price(new BigDecimal(ticketAmount));
                            nmFc.setSeg2Cabin(passengerSegment.getCabinCode());
                            nmFc.setSeg2PriceType(patType);
                            break;
                        case 3:
                            nmFc.setSeg3Price(new BigDecimal(ticketAmount));
                            nmFc.setSeg3Cabin(passengerSegment.getCabinCode());
                            nmFc.setSeg3PriceType(patType);
                            break;
                        case 4:
                            nmFc.setSeg4Price(new BigDecimal(ticketAmount));
                            nmFc.setSeg4Cabin(passengerSegment.getCabinCode());
                            nmFc.setSeg4PriceType(patType);
                            break;
                        case 5:
                            nmFc.setSeg5Price(new BigDecimal(ticketAmount));
                            nmFc.setSeg5Cabin(passengerSegment.getCabinCode());
                            nmFc.setSeg5PriceType(patType);
                            break;
                        default:
                            break;
                    }
                    segIndex++;
                }
                if ("IN".equals(patType) && !modifiedText.contains("**(IN)")) {
                    modifiedText = modifiedText.split("END/P")[0] + "END **(IN)/P" + modifiedText.split("END/P")[1];
                } else if ("CH".equals(patType) && !modifiedText.contains("**(CH)")) {
                    modifiedText = modifiedText.split("END/P")[0] + "END **(CH)/P" + modifiedText.split("END/P")[1];
                }
                nmFc.setInputValue(modifiedText);

                addList.add(nmFc);
            }
        }
    }

    /**
     * 创建PNR级别FC记录
     */
    private void createManualFarePnrFcRecord(String decodedText, List<UpdatePnrDto.PassengerSegment> segments, Boolean infSign, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        MnjxPnrFc pnrFc = new MnjxPnrFc();
        String passengerType = Boolean.TRUE.equals(infSign) ? "INF" : decodedText.contains("CH") ? "CHD" : "ADT";
        String patType = passengerType.substring(0, 2);
        pnrFc.setPatType(patType);
        pnrFc.setIsBaby(Boolean.TRUE.equals(infSign) ? 1 : 0);
        pnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFc.setPnrId(pnr.getPnrId());
        pnrFc.setPnrIndex(0); // 后续重新排序

        Map<Integer, String> segTicketAmountMap = new HashMap<>();
        // FC/A/IN/PEK A-22MAY26 CA SHA 220.00YIN90 //WUH B-0 A-0 MU PKX 230.00YIN CNY450.00END **(IN)
        if (decodedText.contains("//")) {
            String pnrFcRegx = "FC/(/A/)?(IN/)?((//\\s*|[A-Z]{3}\\s(B-\\w+)?(A-\\w+)?(\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2}\\d*)?\\s))+)CNY\\s*([0-9.]+)\\sEND(\\s[*]{2}([(][A-Z]+[)]))?";
            if (ReUtil.isMatch(pnrFcRegx, decodedText)) {
                List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(pnrFcRegx), decodedText);
                // 总价
                pnrFc.setTotalPrice(new BigDecimal(allGroups.get(9)));
                // 每个航段的价格
                String segPriceArea = allGroups.get(3);
                List<String> segPriceList = Arrays.asList(segPriceArea.split(" "));
                int count = 1;
                for (String segPrice : segPriceList) {
                    if ("//".equals(segPrice)) {
                        count++;
                        continue;
                    }
                    if (segPrice.matches("\\d+\\.00[A-Z]([A-Z]{2}\\d*[A-Z0-9]*)?")) {
                        segTicketAmountMap.put(count, segPrice);
                        count++;
                    }
                }
            }
        }
        // FC/SHA MU CKG 1350.00L CNY 1350.00 END
        // FC/IN/CTU 3U PEK 1123.00YIN90 CA SHA 1123.00Y CNY 2246.00 END
        else {
            String pnrFcRegx = "FC/(A/)?(IN/)?([A-Z]{3})\\s((\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2}\\d*)?\\s)+)([A-Z]{3})\\s*([0-9.]+)\\s?END(\\s[*]{2}([(][A-Z][)]+))?";
            if (ReUtil.isMatch(pnrFcRegx, decodedText)) {
                List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(pnrFcRegx), decodedText);
                // 总价
                pnrFc.setTotalPrice(new BigDecimal(allGroups.get(8)));
                // 每个航段的价格
                String segPriceArea = allGroups.get(4);
                // 3U PEK 1123.00Y CA SHA 1123.00Y
                List<String> segPriceList = Arrays.asList(segPriceArea.split(" "));
                int count = 1;
                for (String segPrice : segPriceList) {
                    if (segPrice.matches("\\d+\\.00[A-Z]([A-Z]{2}\\d*[A-Z0-9]*)?")) {
                        segTicketAmountMap.put(count, segPrice);
                        count++;
                    }
                }
            }
        }
        pnrFc.setCurrency("CNY");

        // 设置每个航段舱位价格
        int segIndex = 1;
        for (int i = 0; i < segments.size(); i++) {
            UpdatePnrDto.PassengerSegment passengerSegment = segments.get(i);
            if ("ARNK".equals(passengerSegment.getSegmentType())) {
                if (i == 0 || i == segments.size() - 1) {
                    throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                }
                segIndex++;
                continue;
            }
            String ticketAmount = segTicketAmountMap.get(segIndex);
            ticketAmount = ticketAmount.split("\\.")[0] + ".00";
            // 设置票价
            switch (segIndex) {
                case 1:
                    pnrFc.setSeg1Price(new BigDecimal(ticketAmount));
                    pnrFc.setSeg1Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg1PriceType(patType);
                    break;
                case 2:
                    pnrFc.setSeg2Price(new BigDecimal(ticketAmount));
                    pnrFc.setSeg2Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg2PriceType(patType);
                    break;
                case 3:
                    pnrFc.setSeg3Price(new BigDecimal(ticketAmount));
                    pnrFc.setSeg3Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg3PriceType(patType);
                    break;
                case 4:
                    pnrFc.setSeg4Price(new BigDecimal(ticketAmount));
                    pnrFc.setSeg4Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg4PriceType(patType);
                    break;
                case 5:
                    pnrFc.setSeg5Price(new BigDecimal(ticketAmount));
                    pnrFc.setSeg5Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg5PriceType(patType);
                    break;
                default:
                    break;
            }
            segIndex++;
        }
        if ("IN".equals(patType) && !decodedText.contains("**(IN)")) {
            decodedText = decodedText + " **(IN)";
        } else if ("CH".equals(patType) && !decodedText.contains("**(CH)")) {
            decodedText = decodedText + " **(CH)";
        }
        pnrFc.setInputValue(decodedText);

        addList.add(pnrFc);
    }

    /**
     * 处理FN信息
     */
    private void processManualFareFnInfos(List<UpdatePnrDto.FareInfo> fnInfos, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (UpdatePnrDto.FareInfo fnInfo : fnInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fnInfo.getText()), StandardCharsets.UTF_8);

            if (decodedText.contains("/P")) {
                // 旅客级别FN
                this.createManualFareNmFnRecords(decodedText, fnInfo.getInfSign(), pnr, addList);
            } else {
                // PNR级别FN
                this.createManualFarePnrFnRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FN记录
     */
    private void createManualFareNmFnRecords(String decodedText, Boolean infSign, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        // FN/FCNY2500.00/SCNY2500.00/C0.00/TCNY100.00CN/TCNY280.00YQ/P1/2
        // FN/IN/FCNY250.00/SCNY250.00/C0.00/TEXEMPTCN/TEXEMPTYQ/P1

        String pIndex = decodedText.split("/P")[1];
        String[] pSplit = pIndex.split("/");
        for (String passengerId : pSplit) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passengerId)
                    .one();

            if (pnrNm != null) {
                MnjxNmFn nmFn = new MnjxNmFn();
                nmFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmFn.setPnrNmId(pnrNm.getPnrNmId());
                nmFn.setPnrIndex(0); // 后续重新排序

                // 修改文本中的旅客引用
                StringBuilder modifiedText = new StringBuilder(decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId));

                BigDecimal fPrice = null;
                BigDecimal sPrice = null;
                BigDecimal cRate = null;
                BigDecimal cnPrice = null;
                BigDecimal yqPrice = null;
                BigDecimal aPrice = null;
                String adtReg = "FN/(A/)?FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TCNY(\\d+\\.00)CN/TCNY(\\d+\\.00)YQ(/ACNY(\\d+\\.00))?/P\\d+";
                String chdReg = "FN/(A/)?FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TEXEMPTCN/TCNY(\\d+\\.00)YQ(/ACNY(\\d+\\.00))?/P\\d+";
                String infReg = "FN/(A/)?IN/FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TEXEMPTCN/TEXEMPTYQ(/ACNY(\\d+\\.00))?/P\\d+";
                if (ReUtil.isMatch(adtReg, modifiedText.toString())) {
                    nmFn.setPatType("AD");
                    nmFn.setIsBaby(0);
                    List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(adtReg), modifiedText.toString());
                    fPrice = new BigDecimal(allGroups.get(2));
                    sPrice = new BigDecimal(allGroups.get(3));
                    cRate = new BigDecimal(allGroups.get(4));
                    cnPrice = new BigDecimal(allGroups.get(7));
                    yqPrice = new BigDecimal(allGroups.get(8));
                    aPrice = fPrice.add(cnPrice).add(yqPrice);
                    if (StrUtil.isNotEmpty(allGroups.get(10)) && aPrice.compareTo(new BigDecimal(allGroups.get(10))) != 0) {
                        throw new SguiResultException("AMOUNT");
                    }
                } else if (ReUtil.isMatch(chdReg, modifiedText.toString())) {
                    nmFn.setPatType("CH");
                    nmFn.setIsBaby(0);
                    List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(chdReg), modifiedText.toString());
                    fPrice = new BigDecimal(allGroups.get(2));
                    sPrice = new BigDecimal(allGroups.get(3));
                    cRate = new BigDecimal(allGroups.get(4));
                    cnPrice = BigDecimal.ZERO;
                    yqPrice = new BigDecimal(allGroups.get(7));
                    aPrice = fPrice.add(cnPrice).add(yqPrice);
                    if (StrUtil.isNotEmpty(allGroups.get(9)) && aPrice.compareTo(new BigDecimal(allGroups.get(9))) != 0) {
                        throw new SguiResultException("AMOUNT");
                    }
                } else if (ReUtil.isMatch(infReg, modifiedText.toString())) {
                    nmFn.setPatType("IN");
                    nmFn.setIsBaby(1);
                    List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(infReg), modifiedText.toString());
                    fPrice = new BigDecimal(allGroups.get(2));
                    sPrice = new BigDecimal(allGroups.get(3));
                    cRate = new BigDecimal(allGroups.get(4));
                    cnPrice = BigDecimal.ZERO;
                    yqPrice = BigDecimal.ZERO;
                    aPrice = fPrice.add(cnPrice).add(yqPrice);
                    if (StrUtil.isNotEmpty(allGroups.get(8)) && aPrice.compareTo(new BigDecimal(allGroups.get(8))) != 0) {
                        throw new SguiResultException("AMOUNT");
                    }
                }
                nmFn.setFCurrency("CNY");
                nmFn.setFPrice(fPrice);
                nmFn.setSCurrency("CNY");
                nmFn.setSPrice(sPrice);
                nmFn.setCRate(cRate);
                nmFn.setTCnCurrency("CNY");
                nmFn.setTCnPrice(cnPrice);
                nmFn.setTYqCurrency("CNY");
                nmFn.setTYqPrice(yqPrice);
                nmFn.setXCurrency("CNY");
                nmFn.setXPrice(cnPrice.add(yqPrice));
                nmFn.setACurrency("CNY");
                nmFn.setAPrice(aPrice);

                if (!modifiedText.toString().contains("XCNY")) {
                    String[] cSplit = modifiedText.toString().split("/C");
                    String beforeC = cSplit[0];
                    String afterC = cSplit[1];
                    String[] split = afterC.split("/");
                    BigDecimal xPrice = nmFn.getXPrice();
                    if (xPrice.compareTo(BigDecimal.ZERO) == 0) {
                        xPrice = new BigDecimal("0.00");
                    }
                    modifiedText = new StringBuilder(beforeC + "/C" + split[0] + "/XCNY" + xPrice);
                    for (int i = 1; i < split.length; i++) {
                        modifiedText.append("/").append(split[i]);
                    }
                }
                if (!modifiedText.toString().contains("ACNY")) {
                    modifiedText = new StringBuilder(modifiedText.toString().split("/P")[0] + "/ACNY" + aPrice + "/P" + modifiedText.toString().split("/P")[1]);
                }
                nmFn.setInputValue(modifiedText.toString());
                addList.add(nmFn);
            }
        }
    }

    /**
     * 创建PNR级别FN记录
     */
    private void createManualFarePnrFnRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrFn pnrFn = new MnjxPnrFn();
        pnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFn.setPnrId(pnr.getPnrId());
        pnrFn.setPnrIndex(0); // 后续重新排序

        BigDecimal fPrice = null;
        BigDecimal sPrice = null;
        BigDecimal cRate = null;
        BigDecimal cnPrice = null;
        BigDecimal yqPrice = null;
        BigDecimal aPrice = null;
        String adtReg = "FN/(A/)?FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TCNY(\\d+\\.00)CN/TCNY(\\d+\\.00)YQ(/ACNY(\\d+\\.00))?";
        String chdReg = "FN/(A/)?FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TEXEMPTCN/TCNY(\\d+\\.00)YQ(/ACNY(\\d+\\.00))?";
        String infReg = "FN/(A/)?IN/FCNY(\\d+\\.00)/SCNY(\\d+\\.00)/C(\\d+\\.00)(/XCNY(\\d+\\.00))?/TEXEMPTCN/TEXEMPTYQ(/ACNY(\\d+\\.00))?";
        if (ReUtil.isMatch(adtReg, decodedText)) {
            pnrFn.setPatType("AD");
            pnrFn.setIsBaby(0);
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(adtReg), decodedText);
            fPrice = new BigDecimal(allGroups.get(2));
            sPrice = new BigDecimal(allGroups.get(3));
            cRate = new BigDecimal(allGroups.get(4));
            cnPrice = new BigDecimal(allGroups.get(7));
            yqPrice = new BigDecimal(allGroups.get(8));
            aPrice = fPrice.add(cnPrice).add(yqPrice);
        } else if (ReUtil.isMatch(chdReg, decodedText)) {
            pnrFn.setPatType("CH");
            pnrFn.setIsBaby(0);
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(chdReg), decodedText);
            fPrice = new BigDecimal(allGroups.get(2));
            sPrice = new BigDecimal(allGroups.get(3));
            cRate = new BigDecimal(allGroups.get(4));
            cnPrice = BigDecimal.ZERO;
            yqPrice = new BigDecimal(allGroups.get(7));
            aPrice = fPrice.add(cnPrice).add(yqPrice);
        } else if (ReUtil.isMatch(infReg, decodedText)) {
            pnrFn.setPatType("IN");
            pnrFn.setIsBaby(1);
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(infReg), decodedText);
            fPrice = new BigDecimal(allGroups.get(2));
            sPrice = new BigDecimal(allGroups.get(3));
            cRate = new BigDecimal(allGroups.get(4));
            cnPrice = BigDecimal.ZERO;
            yqPrice = BigDecimal.ZERO;
            aPrice = fPrice.add(cnPrice).add(yqPrice);
        }
        pnrFn.setFCurrency("CNY");
        pnrFn.setFPrice(fPrice);
        pnrFn.setSCurrency("CNY");
        pnrFn.setSPrice(sPrice);
        pnrFn.setCRate(cRate);
        pnrFn.setTCnCurrency("CNY");
        pnrFn.setTCnPrice(cnPrice);
        pnrFn.setTYqCurrency("CNY");
        pnrFn.setTYqPrice(yqPrice);
        pnrFn.setXCurrency("CNY");
        pnrFn.setXPrice(cnPrice.add(yqPrice));
        pnrFn.setACurrency("CNY");
        pnrFn.setAPrice(aPrice);

        if (!decodedText.contains("XCNY")) {
            String[] cSplit = decodedText.split("/C");
            String beforeC = cSplit[0];
            String afterC = cSplit[1];
            String[] split = afterC.split("/");
            BigDecimal xPrice = pnrFn.getXPrice();
            if (xPrice.compareTo(BigDecimal.ZERO) == 0) {
                xPrice = new BigDecimal("0.00");
            }
            StringBuilder modifiedText = new StringBuilder(beforeC + "/C" + split[0] + "/XCNY" + xPrice);
            for (int i = 1; i < split.length; i++) {
                modifiedText.append("/").append(split[i]);
            }
            decodedText = modifiedText.toString();
        }
        if (!decodedText.contains("ACNY")) {
            decodedText = decodedText + "/ACNY" + aPrice;
        }
        pnrFn.setInputValue(decodedText);

        addList.add(pnrFn);
    }

    /**
     * 处理FP信息
     */
    private void processManualFareFpInfos(List<String> fpInfos, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (String fpInfo : fpInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fpInfo), StandardCharsets.UTF_8);
            if (!decodedText.matches("(FP/)?(IN/)?CASH,CNY(/P\\d+(/\\d+)*)?")) {
                throw new SguiResultException("CODE");
            }

            if (decodedText.contains("/P")) {
                // 旅客级别FP
                this.createManualFareNmFpRecords(decodedText, pnr, addList);
            } else {
                // PNR级别FP
                this.createManualFarePnrFpRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FP记录
     */
    private void createManualFareNmFpRecords(String decodedText, MnjxPnr pnr, List<Object> addList) {
        String pIndex = decodedText.split("/P")[1];
        String[] pSplit = pIndex.split("/");
        for (String passengerId : pSplit) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passengerId)
                    .one();

            if (pnrNm != null) {
                MnjxNmFp nmFp = new MnjxNmFp();
                nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmFp.setPnrNmId(pnrNm.getPnrNmId());
                nmFp.setPnrIndex(0); // 后续重新排序
                nmFp.setCurrencyType("CNY");
                nmFp.setPayType("CASH");
                Integer chldCount = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "CHLD")
                        .count();
                if (chldCount > 0) {
                    nmFp.setPatType("CH");
                    nmFp.setIsBaby(0);
                } else {
                    nmFp.setPatType(decodedText.contains("IN/") ? "IN" : "AD");
                    nmFp.setIsBaby(decodedText.contains("IN/") ? 1 : 0);
                }

                // 修改文本中的旅客引用
                String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                if (!modifiedText.startsWith("FP/")) {
                    modifiedText = "FP/" + modifiedText;
                }
                nmFp.setInputValue(modifiedText);

                addList.add(nmFp);
            }
        }
    }

    /**
     * 创建PNR级别FP记录
     */
    private void createManualFarePnrFpRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrFp pnrFp = new MnjxPnrFp();
        pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFp.setPnrId(pnr.getPnrId());
        pnrFp.setPnrIndex(0); // 后续重新排序
        if (!decodedText.startsWith("FP/")) {
            decodedText = "FP/" + decodedText;
        }
        pnrFp.setInputValue(decodedText);

        pnrFp.setPayType("CASH");
        pnrFp.setCurrencyType("CNY");
        pnrFp.setIsBaby(decodedText.contains("IN/") ? 1 : 0);
        pnrFp.setPatType(decodedText.contains("IN/") ? "IN" : "AD");

        addList.add(pnrFp);
    }

    /**
     * 处理EI信息
     */
    private void processManualFareEiInfos(List<String> eiInfos, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (String eiInfo : eiInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(eiInfo), StandardCharsets.UTF_8);

            if (decodedText.matches(".+/P\\d+(/\\d+)*")) {
                // 旅客级别EI
                this.createManualFareNmEiRecords(decodedText, pnr, addList);
            } else {
                // PNR级别EI
                this.createManualFarePnrEiRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别EI记录
     */
    private void createManualFareNmEiRecords(String decodedText, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        String pIndex = decodedText.split("/P")[1];
        String[] pSplit = pIndex.split("/");
        for (String passengerId : pSplit) {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passengerId)
                    .one();

            if (pnrNm != null) {
//                boolean isInf = decodedText.contains("IN/");
                // 查是否已经有ei了
//                List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
//                        .eq(MnjxNmEi::getPnrNmId, pnrNm.getPnrNmId())
//                        .list();
//                if (CollUtil.isNotEmpty(nmEiList)) {
//                    if (isInf && nmEiList.stream().anyMatch(e -> e.getInputValue().contains("/IN/"))) {
//                        throw new SguiResultException("婴儿EI已存在");
//                    } else if (!isInf && nmEiList.stream().anyMatch(e -> !e.getInputValue().contains("/IN/"))) {
//                        throw new SguiResultException("EI已存在");
//                    }
//                }

                MnjxNmEi nmEi = new MnjxNmEi();
                nmEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmEi.setPnrNmId(pnrNm.getPnrNmId());
                nmEi.setPnrIndex(0); // 后续重新排序

                String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                nmEi.setInputValue("EI/" + modifiedText);
                nmEi.setEiInfo("EI/" + modifiedText);

                addList.add(nmEi);
            }
        }
    }

    /**
     * 创建PNR级别EI记录
     */
    private void createManualFarePnrEiRecord(String decodedText, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
//        boolean isInf = decodedText.contains("IN/");
        // 先查是否已存在EI
//        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
//                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
//                .list();
//        if (CollUtil.isNotEmpty(pnrEiList)) {
//            if (isInf && pnrEiList.stream().anyMatch(e -> e.getInputValue().contains("/IN/"))) {
//                throw new SguiResultException("婴儿EI已存在");
//            } else if (!isInf && pnrEiList.stream().anyMatch(e -> !e.getInputValue().contains("/IN/"))) {
//                throw new SguiResultException("EI已存在");
//            }
//        }
        MnjxPnrEi pnrEi = new MnjxPnrEi();
        pnrEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrEi.setPnrId(pnr.getPnrId());
        pnrEi.setPnrIndex(0); // 后续重新排序
        pnrEi.setInputValue("EI/" + decodedText);
        pnrEi.setEiInfo("EI/" + decodedText);

        addList.add(pnrEi);
    }

    /**
     * 修改手工运价
     */
    private void modifyManualFare(UpdatePnrDto.ManualFare manualFare, List<UpdatePnrDto.PassengerSegment> segments, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> addList,
                                  List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        // 先删除原有记录
        // 有索引按索引删
        if (CollUtil.isNotEmpty(manualFare.getElementNbrList())) {
            this.deleteFareByIndexes(manualFare.getElementNbrList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        } else {
            // 没索引删除所有的FP FC FN EI
            this.deleteManualFareWithoutNbr(pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }

        // 再新增新记录
        this.addManualFare(manualFare, segments, pnr, addList);
    }

    private void deleteManualFareWithoutNbr(MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        String pnrId = pnr.getPnrId();
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnrId)
                .list();
        if (CollUtil.isNotEmpty(pnrFcList)) {
            oldRecordList.stream()
                    .filter(r -> "FC".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(pnrFcList);
        }
        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnrId)
                .list();
        if (CollUtil.isNotEmpty(pnrFpList)) {
            oldRecordList.stream()
                    .filter(r -> "FP".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(pnrFpList);
        }
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnrId)
                .list();
        if (CollUtil.isNotEmpty(pnrFnList)) {
            oldRecordList.stream()
                    .filter(r -> "FN".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(pnrFnList);
        }
        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnrId)
                .list();
        if (CollUtil.isNotEmpty(pnrEiList)) {
            oldRecordList.stream()
                    .filter(r -> "EI".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(pnrEiList);
        }

        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .in(MnjxNmFp::getPnrNmId, pnrNmIdList)
                .list();
        if (CollUtil.isNotEmpty(nmFpList)) {
            oldRecordList.stream()
                    .filter(r -> "NM FP".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(nmFpList);
        }
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, pnrNmIdList)
                .list();
        if (CollUtil.isNotEmpty(nmFcList)) {
            oldRecordList.stream()
                    .filter(r -> "NM FC".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(nmFcList);
        }
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        if (CollUtil.isNotEmpty(nmFnList)) {
            oldRecordList.stream()
                    .filter(r -> "NM FN".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(nmFnList);
        }
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .in(MnjxNmEi::getPnrNmId, pnrNmIdList)
                .list();
        if (CollUtil.isNotEmpty(nmEiList)) {
            oldRecordList.stream()
                    .filter(r -> "NM EI".equals(r.getPnrType()) && StrUtil.isEmpty(r.getChangeMark()))
                    .forEach(r -> {
                        r.setChangeMark("X");
                        updateList.add(r);
                    });
            deleteList.addAll(nmEiList);
        }
    }

    /**
     * 删除手工运价
     */
    private void deleteManualFare(UpdatePnrDto.ManualFare manualFare, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        if (CollUtil.isNotEmpty(manualFare.getElementNbrList())) {
            this.deleteFareByIndexes(manualFare.getElementNbrList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }
    }

    /**
     * 根据索引删除手工/自动运价
     */
    private void deleteFareByIndexes(List<Integer> elementNbrList, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        for (Integer index : elementNbrList) {
            List<MnjxPnrRecord> pnrRecordList = oldRecordList.stream()
                    .filter(r -> r.getPnrIndex() == index)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(pnrRecordList)) {
                MnjxPnrRecord record = pnrRecordList.get(0);
                String pnrType = record.getPnrType();
                switch (pnrType) {
                    case "FC":
                        this.deletePnrFcByIndex(index, pnr, deleteList);
                        break;
                    case "FN":
                        this.deletePnrFnByIndex(index, pnr, deleteList);
                        break;
                    case "FP":
                        this.deletePnrFpByIndex(index, pnr, deleteList);
                        break;
                    case "EI":
                        this.deletePnrEiByIndex(index, pnr, deleteList);
                        break;
                    case "NM FC":
                        this.deleteNmFcByIndex(index, pnrNmIdList, deleteList);
                        break;
                    case "NM FN":
                        this.deleteNmFnByIndex(index, pnrNmIdList, deleteList);
                        break;
                    case "NM FP":
                        this.deleteNmFpByIndex(index, pnrNmIdList, deleteList);
                        break;
                    case "NM EI":
                        this.deleteNmEiByIndex(index, pnrNmIdList, deleteList);
                        break;
                }

                // 标记记录为删除
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 删除PNR FC记录
     */
    private void deletePnrFcByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFc pnrFc = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFc::getPnrIndex, index)
                .one();
        if (pnrFc != null) {
            deleteList.add(pnrFc);
        }
    }

    /**
     * 删除PNR FN记录
     */
    private void deletePnrFnByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFn::getPnrIndex, index)
                .one();
        if (pnrFn != null) {
            deleteList.add(pnrFn);
        }
    }

    /**
     * 删除PNR FP记录
     */
    private void deletePnrFpByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFp pnrFp = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFp::getPnrIndex, index)
                .one();
        if (pnrFp != null) {
            deleteList.add(pnrFp);
        }
    }

    /**
     * 删除PNR EI记录
     */
    private void deletePnrEiByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrEi pnrEi = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrEi::getPnrIndex, index)
                .one();
        if (pnrEi != null) {
            deleteList.add(pnrEi);
        }
    }

    /**
     * 删除旅客FC记录
     */
    private void deleteNmFcByIndex(Integer index, List<String> pnrNmIdList, List<Object> deleteList) {
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrIndex, index)
                .in(MnjxNmFc::getPnrNmId, pnrNmIdList)
                .list();
        deleteList.addAll(nmFcList);
    }

    /**
     * 删除旅客FN记录
     */
    private void deleteNmFnByIndex(Integer index, List<String> pnrNmIdList, List<Object> deleteList) {
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrIndex, index)
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        deleteList.addAll(nmFnList);
    }

    /**
     * 删除旅客FP记录
     */
    private void deleteNmFpByIndex(Integer index, List<String> pnrNmIdList, List<Object> deleteList) {
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrIndex, index)
                .in(MnjxNmFp::getPnrNmId, pnrNmIdList)
                .list();
        deleteList.addAll(nmFpList);
    }

    /**
     * 删除旅客EI记录
     */
    private void deleteNmEiByIndex(Integer index, List<String> pnrNmIdList, List<Object> deleteList) {
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrIndex, index)
                .in(MnjxNmEi::getPnrNmId, pnrNmIdList)
                .list();
        deleteList.addAll(nmEiList);
    }

    /**
     * 处理航段信息变更
     */
    private void processSegments(List<UpdatePnrDto.Segment> segments, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                 List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (CollUtil.isEmpty(segments)) {
            return;
        }

        for (UpdatePnrDto.Segment segment : segments) {
            switch (segment.getOperateType()) {
                case "A":
                    this.addSegment(segment, pnr, addList);
                    break;
                case "M":
                    // 修改航段暂不处理，目前前端修改的方式都是传的删除和新增数据
                    break;
                case "D":
                    this.deleteSegment(segment, pnr, oldRecordList, pnrNmIdList, addList, deleteList, updateList);
                    break;
            }
        }
    }

    /**
     * 新增航段
     */
    private void addSegment(UpdatePnrDto.Segment segment, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
        pnrSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrSeg.setPnrId(pnr.getPnrId());
        pnrSeg.setPnrIndex(0); // 后续重新排序
        pnrSeg.setSellCabin(segment.getCabin());
        pnrSeg.setOrg(segment.getDepartureAirport());
        pnrSeg.setDst(segment.getArrivalAirport());
        pnrSeg.setFlightDate(segment.getDepartureDateTime());
        if ("ARNK".equals(segment.getFlightNo())) {
            pnrSeg.setFlightNo("ARNK");
            pnrSeg.setPnrSegType("SA");
            pnrSeg.setInputValue("    ARNK             " + pnrSeg.getOrg() + pnrSeg.getDst());
            pnrSeg.setPnrSegNo(0);
            addList.add(pnrSeg);
            return;
        }

        pnrSeg.setActionCode("HK");
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, segment.getFlightNo())
                .one();
        if (ObjectUtil.isEmpty(mnjxFlight)) {
            throw new SguiResultException(StrUtil.format("航班{}不存在", segment.getFlightNo()));
        }
        String shareFlightNo = mnjxFlight.getFlightNo();
        String carrierFlight = null;
        if (StrUtil.isNotEmpty(mnjxFlight.getCarrierFlight())) {
            if (Constant.STR_ZERO.equals(mnjxFlight.getShareState())) {
                throw new SguiResultException("共享航班已停用");
            }
            carrierFlight = mnjxFlight.getCarrierFlight();
            mnjxFlight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, mnjxFlight.getCarrierFlight())
                    .one();
        }
        pnrSeg.setFlightNo(shareFlightNo);
        pnrSeg.setCarrierFlight(carrierFlight);

        List<MnjxPlanSection> planSectionList = iSguiCommonService.getPlanSectionListByFlightNo(pnrSeg.getFlightNo(), pnrSeg.getFlightDate());
        if (CollUtil.isEmpty(planSectionList)) {
            throw new SguiResultException("修改的航班不存在");
        }

        List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(pnrSeg.getFlightNo(), pnrSeg.getFlightDate(), pnrSeg.getOrg(), pnrSeg.getDst());
        if (CollUtil.isEmpty(openCabinList)) {
            throw new SguiResultException("修改的航班不存在");
        }
        openCabinList = openCabinList.stream()
                .filter(o -> segment.getCabin().equals(o.getSellCabin()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(openCabinList)) {
            throw new SguiResultException("舱位错误");
        }
        if (openCabinList.stream().map(MnjxOpenCabin::getPlanSectionId).distinct().count() > 1) {
            openCabinList = openCabinList.stream()
                    .collect(Collectors.groupingBy(MnjxOpenCabin::getPlanSectionId))
                    .entrySet()
                    .stream()
                    .map(e -> e.getValue().stream().min(Comparator.comparingInt(MnjxOpenCabin::getSeatAvailable)).get())
                    .collect(Collectors.toList());
        }
        if (openCabinList.get(0).getSeatAvailable() < segment.getSeatNumber()) {
            throw new SguiResultException("所选舱位座位数不足");
        }

        String cabinClass = openCabinList.get(0).getCabinClass();
        pnrSeg.setCabinClass(cabinClass);
        pnrSeg.setSeatNumber(segment.getSeatNumber());
        pnrSeg.setPnrSegType("SS");

        String estimateOff = "";
        String estimateArr = "";
        String planeVersion = "";
        String recreation = "";

        int stop = 0;
        for (MnjxPlanSection planSection : planSectionList) {
            MnjxAirport orgAirport = iMnjxAirportService.getById(planSection.getDepAptId());
            MnjxAirport dstAirport = iMnjxAirportService.getById(planSection.getArrAptId());
            if (orgAirport.getAirportCode().equals(pnrSeg.getOrg()) && dstAirport.getAirportCode().equals(pnrSeg.getDst())) {
                estimateOff = planSection.getEstimateOff();
                estimateArr = planSection.getEstimateArr();
                MnjxPlane plane = iMnjxPlaneService.getById(planSection.getPlaneId());
                planeVersion = iMnjxPlaneModelService.getById(plane.getPlaneModelId()).getPlaneModelVersion();
                recreation = mnjxFlight.getIsE();
                break;
            } else {
                if (orgAirport.getAirportCode().equals(pnrSeg.getOrg())) {
                    estimateOff = planSection.getEstimateOff();
                    MnjxPlane plane = iMnjxPlaneService.getById(planSection.getPlaneId());
                    planeVersion = iMnjxPlaneModelService.getById(plane.getPlaneModelId()).getPlaneModelVersion();
                    recreation = mnjxFlight.getIsE();
                } else if (dstAirport.getAirportCode().equals(pnrSeg.getDst())) {
                    stop++;
                    estimateArr = planSection.getEstimateArr();
                } else {
                    stop++;
                }
            }
        }
        // 验证出发到达
        if (StrUtil.isEmpty(estimateOff) || StrUtil.isEmpty(estimateArr)) {
            throw new SguiResultException("航班信息错误，检查变更航段出发到达机场");
        }
        pnrSeg.setEstimateOff(estimateOff);
        pnrSeg.setEstimateArr(estimateArr);
        pnrSeg.setPlaneVersion(planeVersion);
        pnrSeg.setRecreation(recreation);

        // 获取当前最大航段序号
        MnjxPnrSeg maxPnrSeg = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrSeg::getPnrSegNo)
                .last("LIMIT 1")
                .one();
        Integer maxSegNo = maxPnrSeg != null ? maxPnrSeg.getPnrSegNo() : 0;

        pnrSeg.setPnrSegNo(maxSegNo == null ? 1 : maxSegNo + 1);
        pnrSeg.setPnrSegNo(0);

        // 格式：  FM9303 P   FR30MAY25  SHACAN HK1   0830 1105          737 M 0  R E T2T2
        String weekDay = DateUtils.ymd2WeekEn(pnrSeg.getFlightDate()).substring(0, 2);
        String inputValue = StrUtil.format("  {} {}   {}{}  {}{} HK{}   {} {}          {} M {}  R E T1T2",
                segment.getFlightNo(), segment.getCabin(), weekDay, DateUtils.ymd2Com(pnrSeg.getFlightDate()), segment.getDepartureAirport(),
                segment.getArrivalAirport(), segment.getSeatNumber(), estimateOff, estimateArr, planeVersion, stop);
        pnrSeg.setInputValue(inputValue);

        addList.add(pnrSeg);
    }

    /**
     * 删除航段
     */
    private void deleteSegment(UpdatePnrDto.Segment segment, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                               List<Object> addList, List<Object> deleteList, List<Object> updateList) throws SguiResultException {
        if (CollUtil.isNotEmpty(segment.getDeleteIndexList())) {
            for (Integer index : segment.getDeleteIndexList()) {
                MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrSeg::getPnrIndex, index)
                        .one();
                if (pnrSeg != null) {
                    // 如果删除的是第一段
                    if (pnrSeg.getPnrSegNo() == 1) {
                        // 判断有没有新增和第一段相同航班号的数据，如果没有，则报错不允许删除第一段
                        if (addList.stream().noneMatch(a -> a instanceof MnjxPnrSeg && ((MnjxPnrSeg) a).getFlightNo().equals(pnrSeg.getFlightNo()))) {
                            throw new SguiResultException("不允许单独删除第一航段");
                        }
                    }
                    deleteList.add(pnrSeg);

                    // 标记记录为删除
                    oldRecordList.stream()
                            .filter(r -> r.getPnrIndex().intValue() == index)
                            .forEach(r -> {
                                r.setChangeMark("X");
                                updateList.add(r);
                            });

                    // 删除航段还需要删除其相关的OSI SSR，和运价的FC FN组
                    List<MnjxNmSsr> toDeleteSsrList = iMnjxNmSsrService.lambdaQuery()
                            .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                            .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo())
                            .list();
                    if (CollUtil.isNotEmpty(toDeleteSsrList)) {
                        deleteList.addAll(toDeleteSsrList);
                        for (MnjxNmSsr deleteItem : toDeleteSsrList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxNmOsi> toDeleteNmOsiList = iMnjxNmOsiService.lambdaQuery()
                            .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                            .eq(MnjxNmOsi::getAirlineCode, pnrSeg.getFlightNo().substring(0, 2))
                            .list();
                    if (CollUtil.isNotEmpty(toDeleteNmOsiList)) {
                        deleteList.addAll(toDeleteNmOsiList);
                        for (MnjxNmOsi deleteItem : toDeleteNmOsiList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxPnrOsi> toDeletePnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                            .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrOsi::getAirlineCode, pnrSeg.getFlightNo().substring(0, 2))
                            .list();
                    if (CollUtil.isNotEmpty(toDeletePnrOsiList)) {
                        deleteList.addAll(toDeletePnrOsiList);
                        for (MnjxPnrOsi deleteItem : toDeletePnrOsiList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxPnrFc> toDeletePnrFcList = iMnjxPnrFcService.lambdaQuery()
                            .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                            .list();
                    if (CollUtil.isNotEmpty(toDeletePnrFcList)) {
                        deleteList.addAll(toDeletePnrFcList);
                        for (MnjxPnrFc deleteItem : toDeletePnrFcList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxPnrFn> toDeletePnrFnList = iMnjxPnrFnService.lambdaQuery()
                            .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                            .list();
                    if (CollUtil.isNotEmpty(toDeletePnrFnList)) {
                        deleteList.addAll(toDeletePnrFnList);
                        for (MnjxPnrFn deleteItem : toDeletePnrFnList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxNmFc> toDeleteNmFcList = iMnjxNmFcService.lambdaQuery()
                            .in(MnjxNmFc::getPnrNmId, pnrNmIdList)
                            .list();
                    if (CollUtil.isNotEmpty(toDeleteNmFcList)) {
                        deleteList.addAll(toDeleteNmFcList);
                        for (MnjxNmFc deleteItem : toDeleteNmFcList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                    List<MnjxNmFn> toDeleteNmFnList = iMnjxNmFnService.lambdaQuery()
                            .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                            .list();
                    if (CollUtil.isNotEmpty(toDeleteNmFnList)) {
                        deleteList.addAll(toDeleteNmFnList);
                        for (MnjxNmFn deleteItem : toDeleteNmFnList) {
                            oldRecordList.stream()
                                    .filter(r -> r.getPnrIndex().intValue() == deleteItem.getPnrIndex())
                                    .forEach(r -> {
                                        r.setChangeMark("X");
                                        updateList.add(r);
                                    });
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理自动运价
     */
    private void processAutoFares(UpdatePnrDto dto, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                  List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        List<UpdatePnrDto.AutoFare> autoFares = dto.getAutoFares();
        if (CollUtil.isEmpty(autoFares)) {
            return;
        }

        for (UpdatePnrDto.AutoFare autoFare : autoFares) {
            switch (autoFare.getOperateType()) {
                case "A":
                    this.addAutoFare(autoFare, dto.getPassengers().getSegments(), pnr, pnrNmIdList, oldRecordList, addList, updateList, deleteList);
                    break;
                case "M":
                    this.modifyAutoFare(autoFare, dto.getPassengers().getSegments(), pnr, oldRecordList, pnrNmIdList, addList, updateList, deleteList);
                    break;
                case "D":
                    this.deleteAutoFare(autoFare, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
                    break;
            }
        }
    }

    private void modifyAutoFare(UpdatePnrDto.AutoFare autoFare, List<UpdatePnrDto.PassengerSegment> segments, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList,
                                List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        // 先删除原有记录
        this.deleteAutoFare(autoFare, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);

        // 再新增新记录
        this.addAutoFare(autoFare, segments, pnr, pnrNmIdList, oldRecordList, addList, updateList, deleteList);
    }

    private void deleteAutoFare(UpdatePnrDto.AutoFare autoFare, MnjxPnr pnr, List<MnjxPnrRecord> oldRecordList, List<String> pnrNmIdList, List<Object> deleteList, List<Object> updateList) {
        if (CollUtil.isNotEmpty(autoFare.getDeleteIndexList())) {
            this.deleteFareByIndexes(autoFare.getDeleteIndexList(), pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }
    }

    /**
     * 新增自动运价
     */
    private void addAutoFare(UpdatePnrDto.AutoFare autoFare, List<UpdatePnrDto.PassengerSegment> passengerSegmentList, MnjxPnr pnr, List<String> pnrNmIdList, List<MnjxPnrRecord> oldRecordList, List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {

        UpdatePnrDto.Price price = autoFare.getPrice();
        List<UpdatePnrDto.PriceItem> priceItems = price.getPriceItems();
        if (CollUtil.isEmpty(priceItems)) {
            return;
        }
        List<Integer> deleteIndexList = new ArrayList<>();
        for (UpdatePnrDto.PriceItem priceItem : priceItems) {
            // 设置旅客运价类型
            String patType = "AD";
            int isBaby = 0;
            boolean chUseAdPrice = Boolean.TRUE.equals(price.getChdUsingAdtPrice());
            switch (priceItem.getPassengerType()) {
                case "ADT":
                    patType = "AD";
                    break;
                case "CHD":
                    patType = "CH";
                    // 儿童使用成人价格
                    if (chUseAdPrice) {
                        patType = "AD";
                    }
                    break;
                case "INF":
                    patType = "IN";
                    isBaby = 1;
                    break;
                default:
                    break;
            }
            // 按旅客处理
            if (CollUtil.isNotEmpty(priceItem.getPassengerId()) && priceItem.getPassengerId().stream().noneMatch(p -> p == 0)) {
                for (Integer passengerId : priceItem.getPassengerId()) {
                    MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                            .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrNm::getPsgIndex, passengerId)
                            .one();
                    // 处理nmFc
                    // 先查原来是否有nmFc，如果有需要删除
                    MnjxNmFc dbNmFc = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmFc::getIsBaby, isBaby)
                            .one();
                    if (ObjectUtil.isNotEmpty(dbNmFc)) {
                        deleteIndexList.add(dbNmFc.getPnrIndex());
                    }
                    // 原来有pnrFc也需要删除
                    MnjxPnrFc dbPnrFc = iMnjxPnrFcService.lambdaQuery()
                            .eq(MnjxPnrFc::getPnrId, pnrNm.getPnrId())
                            .eq(MnjxPnrFc::getIsBaby, isBaby)
                            .eq(MnjxPnrFc::getPatType, patType)
                            .one();
                    if (ObjectUtil.isNotEmpty(dbPnrFc)) {
                        deleteIndexList.add(dbPnrFc.getPnrIndex());
                    }
                    this.processAutoFareNmFcInfos(pnrNm, passengerId, patType, isBaby, passengerSegmentList, priceItem, addList, chUseAdPrice);

                    // 处理nmFn
                    // 先查原来是否有nmFn，如果有需要删除
                    MnjxNmFn dbNmFn = iMnjxNmFnService.lambdaQuery()
                            .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmFn::getIsBaby, isBaby)
                            .one();
                    if (ObjectUtil.isNotEmpty(dbNmFn)) {
                        deleteIndexList.add(dbNmFn.getPnrIndex());
                    }
                    // 原来有pnrFn也需要删除
                    MnjxPnrFn dbPnrFn = iMnjxPnrFnService.lambdaQuery()
                            .eq(MnjxPnrFn::getPnrId, pnrNm.getPnrId())
                            .eq(MnjxPnrFn::getIsBaby, isBaby)
                            .eq(MnjxPnrFn::getPatType, patType)
                            .one();
                    if (ObjectUtil.isNotEmpty(dbPnrFn)) {
                        deleteIndexList.add(dbPnrFn.getPnrIndex());
                    }
                    this.processAutoFareNmFnInfos(pnrNm, passengerId, patType, isBaby, priceItem, addList, chUseAdPrice);

                    // 先查询是否有nmFp nmEi记录，因为sgui删除运价只会删除nmFc和nmFn，nmFp和nmEi是保留的，再次添加自动运价时不能重复生成。如果nmFp被删除了就需要重新生成
                    MnjxNmFp dbNmFp = iMnjxNmFpService.lambdaQuery()
                            .eq(MnjxNmFp::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmFp::getIsBaby, isBaby)
                            .one();
                    if (ObjectUtil.isEmpty(dbNmFp) || deleteList.stream().anyMatch(d -> d instanceof MnjxNmFp && ((MnjxNmFp) d).getPnrIndex().intValue() == dbNmFp.getPnrIndex())) {
                        // 处理nmFP
                        this.processAutoFareNmFpInfos(pnrNm, passengerId, patType, isBaby, price, priceItem, addList, chUseAdPrice);
                    }

                    List<MnjxNmEi> dbNmEiList = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, pnrNm.getPnrNmId())
                            .list();
                    // nmEi的判断，/IN/婴儿
                    if (CollUtil.isEmpty(dbNmEiList)
                            || (isBaby == 1 && dbNmEiList.stream().noneMatch(e -> e.getInputValue().contains("/IN/")))
                            || (isBaby == 0 && dbNmEiList.stream().allMatch(e -> e.getInputValue().contains("/IN/")))) {
                        // nmEi需要额外判断该类型旅客pnrEi是不是存在，存在需要删除，该场景为预定成人+儿童，初始只给儿童生成了运价，在pnr上，重新运价时在两个旅客上，这时候需要删除原来的pnrEi
                        MnjxPnrEi dbPnrEi = iMnjxPnrEiService.lambdaQuery()
                                .eq(MnjxPnrEi::getPnrId, pnrNm.getPnrId())
                                .like(isBaby == 1, MnjxPnrEi::getInputValue, "/IN/")
                                .one();
                        if (ObjectUtil.isNotEmpty(dbPnrEi)) {
                            deleteIndexList.add(dbPnrEi.getPnrIndex());
                        }
                        // 处理nmEi
                        this.processAutoFareNmEiInfos(pnrNm, passengerId, addList, isBaby);
                    }
                }
            }
            // 按PNR处理
            else {
                // 处理pnrFc
                // 先查原来是否有pnrFc，如果有需要删除
                MnjxPnrFc pnrFc = iMnjxPnrFcService.lambdaQuery()
                        .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFc::getIsBaby, isBaby)
                        .one();
                if (ObjectUtil.isNotEmpty(pnrFc)) {
                    deleteIndexList.add(pnrFc.getPnrIndex());
                }
                // 再查原来是否有挂在旅客上的
                List<MnjxNmFc> dbNmFcList = iMnjxNmFcService.lambdaQuery()
                        .in(MnjxNmFc::getPnrNmId, pnrNmIdList)
                        .eq(MnjxNmFc::getIsBaby, isBaby)
                        .list();
                if (CollUtil.isNotEmpty(dbNmFcList)) {
                    deleteIndexList.addAll(dbNmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()));
                }
                this.processAutoFarePnrFcInfos(pnr, patType, isBaby, passengerSegmentList, priceItem, addList, chUseAdPrice);

                // 处理pnrFP
                List<MnjxPnrFp> dbPnrFpList = iMnjxPnrFpService.lambdaQuery()
                        .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                        .list();
                boolean canAddFp;
                if (CollUtil.isNotEmpty(dbPnrFpList)) {
                    if (isBaby == 1) {
                        canAddFp = dbPnrFpList.stream().noneMatch(p -> p.getIsBaby() == 1);
                        // 前面判断不能添加fp后，再判断是否已经指定删除了该fp，如果删除了就允许再添加新的
                        if (!canAddFp) {
                            if (deleteList.stream().anyMatch(d -> d instanceof MnjxPnrFp && ((MnjxPnrFp) d).getIsBaby() == 1)) {
                                canAddFp = true;
                            }
                        }
                    } else {
                        canAddFp = dbPnrFpList.stream().noneMatch(p -> p.getIsBaby() == 0);
                        // 前面判断不能添加fp后，再判断是否已经指定删除了该fp，如果删除了就允许再添加新的
                        if (!canAddFp) {
                            if (deleteList.stream().anyMatch(d -> d instanceof MnjxPnrFp && ((MnjxPnrFp) d).getIsBaby() == 0)) {
                                canAddFp = true;
                            }
                        }
                    }
                } else {
                    canAddFp = true;
                }
                if (canAddFp) {
                    this.processAutoFarePnrFpInfos(pnr, patType, isBaby, price, priceItem, addList, chUseAdPrice);
                }

                // 处理pnrFn
                // 先查原来是否有pnrFc，如果有需要删除
                MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFn::getIsBaby, isBaby)
                        .one();
                if (ObjectUtil.isNotEmpty(pnrFn)) {
                    deleteIndexList.add(pnrFn.getPnrIndex());
                }
                // 再查原来是否有挂在旅客上的
                List<MnjxNmFn> dbNmFnList = iMnjxNmFnService.lambdaQuery()
                        .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                        .eq(MnjxNmFn::getIsBaby, isBaby)
                        .list();
                if (CollUtil.isNotEmpty(dbNmFnList)) {
                    deleteIndexList.addAll(dbNmFnList.stream().map(MnjxNmFn::getPnrIndex).collect(Collectors.toList()));
                }
                this.processAutoFarePnrFnInfos(pnr, patType, isBaby, priceItem, addList, chUseAdPrice);

                List<MnjxPnrEi> dbPnrEiList = iMnjxPnrEiService.lambdaQuery()
                        .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                        .list();
                boolean canAddEi;
                if (CollUtil.isNotEmpty(dbPnrEiList)) {
                    if (isBaby == 1) {
                        canAddEi = dbPnrEiList.stream().noneMatch(e -> e.getInputValue().contains("/IN/"));
                    } else {
                        canAddEi = dbPnrEiList.stream().allMatch(e -> e.getInputValue().contains("/IN/"));
                    }
                } else {
                    canAddEi = true;
                }
                if (canAddEi) {
                    // 查原来是否有挂在旅客上的
                    List<MnjxNmEi> dbNmEiList = iMnjxNmEiService.lambdaQuery()
                            .in(MnjxNmEi::getPnrNmId, pnrNmIdList)
                            .like(isBaby == 1, MnjxNmEi::getInputValue, "/IN/")
                            .list();
                    if (CollUtil.isNotEmpty(dbNmEiList)) {
                        deleteIndexList.addAll(dbNmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()));
                    }
                    // 处理pnrEi
                    this.processAutoFarePnrEiInfos(pnr, addList, isBaby);
                }
            }
        }

        // 如果有需要删除的项，调用删除
        if (CollUtil.isNotEmpty(deleteIndexList)) {
            this.deleteFareByIndexes(deleteIndexList, pnr, oldRecordList, pnrNmIdList, deleteList, updateList);
        }
    }

    private void processAutoFarePnrEiInfos(MnjxPnr pnr, List<Object> addList, int isBaby) {
        MnjxPnrEi pnrEi = new MnjxPnrEi();
        pnrEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrEi.setPnrId(pnr.getPnrId());
        pnrEi.setPnrIndex(0); // 后续重新排序
        String inputValue = "EI/Q/GAITUISHOUFEI改退收费";
        if (isBaby == 1) {
            inputValue = "EI/IN/" + inputValue;
        }
        pnrEi.setEiInfo(inputValue);
        pnrEi.setInputValue(inputValue);

        addList.add(pnrEi);
    }

    private void processAutoFarePnrFnInfos(MnjxPnr pnr, String patType, int isBaby, UpdatePnrDto.PriceItem priceItem, List<Object> addList, boolean chUseAdPrice) {
        MnjxPnrFn pnrFn = new MnjxPnrFn();
        pnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFn.setPnrId(pnr.getPnrId());
        pnrFn.setPnrIndex(0); // 后续重新排序
        pnrFn.setIsBaby(isBaby);
        pnrFn.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            pnrFn.setPatType("AD");
        }
        pnrFn.setFCurrency(priceItem.getCurrency());
        pnrFn.setSCurrency(priceItem.getCurrency());
        pnrFn.setACurrency(priceItem.getCurrency());
        pnrFn.setXCurrency(priceItem.getCurrency());
        pnrFn.setTCnCurrency(priceItem.getCurrency());
        pnrFn.setTYqCurrency(priceItem.getCurrency());
        pnrFn.setFPrice(new BigDecimal(priceItem.getTicketAmount()));
        pnrFn.setSPrice(new BigDecimal(priceItem.getTicketAmount()));
        pnrFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
        pnrFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
        pnrFn.setXPrice(pnrFn.getTCnPrice().add(pnrFn.getTYqPrice()));
        pnrFn.setAPrice(new BigDecimal(priceItem.getTotalAmount()));
        String pnrFnInputValue = StrUtil.format("FN/A/FCNY{}/SCNY{}/C{}/XCNY{}/TCNY{}CN/TCNY{}YQ/ACNY{}",
                priceItem.getTicketAmount(), priceItem.getTicketAmount(), StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                pnrFn.getXPrice(), priceItem.getFund(), priceItem.getFuel(), priceItem.getTotalAmount());
        if ("CH".equals(patType) && !chUseAdPrice) {
            pnrFnInputValue = StrUtil.format("FN/A/FCNY{}/SCNY{}/C{}/XCNY{}/TEXEMPTCN/TCNY{}YQ/ACNY{}",
                    priceItem.getTicketAmount(), priceItem.getTicketAmount(), StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                    pnrFn.getXPrice(), priceItem.getFuel(), priceItem.getTotalAmount());
        } else if ("IN".equals(patType)) {
            pnrFnInputValue = StrUtil.format("FN/IN/A/FCNY{}/SCNY{}/C{}/TEXEMPTCN/TEXEMPTYQ/ACNY{}",
                    priceItem.getTicketAmount(), priceItem.getTicketAmount(), StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                    priceItem.getTotalAmount());
        }
        pnrFn.setInputValue(pnrFnInputValue);
        addList.add(pnrFn);
    }

    private void processAutoFarePnrFpInfos(MnjxPnr pnr, String patType, int isBaby, UpdatePnrDto.Price price, UpdatePnrDto.PriceItem priceItem, List<Object> addList, boolean chUseAdPrice) {
        MnjxPnrFp pnrFp = new MnjxPnrFp();
        pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFp.setPnrId(pnr.getPnrId());
        pnrFp.setPnrIndex(0); // 后续重新排序
        pnrFp.setIsBaby(isBaby);
        pnrFp.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            pnrFp.setPatType("AD");
        }
        pnrFp.setPayType(StrUtil.isEmpty(price.getPayMethod()) ? "CASH" : price.getPayMethod());
        pnrFp.setCurrencyType(priceItem.getCurrency());
        StringBuilder pnrFpInputValueSb = new StringBuilder();
        pnrFpInputValueSb.append("FP/");
        if (isBaby == 1) {
            pnrFpInputValueSb.append("IN/");
        }
        pnrFpInputValueSb.append("CASH,CNY");
        pnrFp.setInputValue(pnrFpInputValueSb.toString());

        addList.add(pnrFp);
    }

    private void processAutoFarePnrFcInfos(MnjxPnr pnr, String patType, int isBaby, List<UpdatePnrDto.PassengerSegment> passengerSegmentList, UpdatePnrDto.PriceItem priceItem, List<Object> addList, boolean chUseAdPrice) throws SguiResultException {
        MnjxPnrFc pnrFc = new MnjxPnrFc();
        pnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFc.setPnrId(pnr.getPnrId());
        pnrFc.setPnrIndex(0); // 后续重新排序

        // 拼接input_value
        StringBuilder pnrFcInputValueSb = new StringBuilder();
        pnrFcInputValueSb.append("FC/A/");

        pnrFc.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            pnrFc.setPatType("AD");
        }
        pnrFc.setIsBaby(isBaby);

        if ("IN".equals(patType)) {
            pnrFcInputValueSb.append("IN/");
        }

        // 设置每个航段舱位价格
        int segIndex = 1;
        for (int i = 0; i < passengerSegmentList.size(); i++) {
            UpdatePnrDto.PassengerSegment passengerSegment = passengerSegmentList.get(i);
            if ("ARNK".equals(passengerSegment.getSegmentType())) {
                if (i == 0 || i == passengerSegmentList.size() - 1) {
                    throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                }
                segIndex++;
                pnrFcInputValueSb.append(" // ");
                continue;
            }
            // 设置票价
            switch (segIndex) {
                case 1:
                    pnrFc.setSeg1Price(new BigDecimal(priceItem.getTicketAmount()));
                    pnrFc.setSeg1Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg1PriceType(patType);
                    break;
                case 2:
                    pnrFc.setSeg2Price(new BigDecimal(priceItem.getTicketAmount()));
                    pnrFc.setSeg2Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg2PriceType(patType);
                    break;
                case 3:
                    pnrFc.setSeg3Price(new BigDecimal(priceItem.getTicketAmount()));
                    pnrFc.setSeg3Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg3PriceType(patType);
                    break;
                case 4:
                    pnrFc.setSeg4Price(new BigDecimal(priceItem.getTicketAmount()));
                    pnrFc.setSeg4Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg4PriceType(patType);
                    break;
                case 5:
                    pnrFc.setSeg5Price(new BigDecimal(priceItem.getTicketAmount()));
                    pnrFc.setSeg5Cabin(passengerSegment.getCabinCode());
                    pnrFc.setSeg5PriceType(patType);
                    break;
                default:
                    break;
            }
            if (i == 0) {
                pnrFcInputValueSb.append(passengerSegment.getDepartureAirport()).append(" ");
            }
            pnrFcInputValueSb.append(passengerSegment.getMarketingAirline())
                    .append(" ")
                    .append(passengerSegment.getArrivalAirport())
                    .append(" ")
                    .append(priceItem.getTicketAmount())
                    .append(passengerSegment.getCabinCode());
            if ("CH".equals(patType) && !chUseAdPrice) {
                pnrFcInputValueSb.append("CH").append(" ");
            } else if ("IN".equals(patType)) {
                pnrFcInputValueSb.append("IN").append(" ");
            } else {
                pnrFcInputValueSb.append(" ");
            }
            if (i == passengerSegmentList.size() - 1) {
                pnrFcInputValueSb.append("CNY")
                        .append(priceItem.getTotalAmount())
                        .append("END");
                if ("CH".equals(patType) && !chUseAdPrice) {
                    pnrFcInputValueSb.append(" **(CH)");
                } else if ("IN".equals(patType)) {
                    pnrFcInputValueSb.append(" **(IN)");
                }
            }
            segIndex++;
        }


        // 设置总价、货币
        pnrFc.setCurrency(priceItem.getCurrency());
        pnrFc.setTotalPrice(new BigDecimal(priceItem.getTotalAmount()));
        pnrFc.setInputValue(pnrFcInputValueSb.toString());

        addList.add(pnrFc);
    }

    private void processAutoFareNmEiInfos(MnjxPnrNm pnrNm, Integer passengerId, List<Object> addList, int isBaby) {
        MnjxNmEi nmEi = new MnjxNmEi();
        nmEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmEi.setPnrNmId(pnrNm.getPnrNmId());
        nmEi.setPnrIndex(0); // 后续重新排序
        String inputValue = "EI/Q/GAITUISHOUFEI改退收费/P" + passengerId;
        if (isBaby == 1) {
            inputValue = "EI/IN/Q/GAITUISHOUFEI改退收费/P" + passengerId;
        }
        nmEi.setEiInfo(inputValue);
        nmEi.setInputValue(inputValue);

        addList.add(nmEi);
    }

    private void processAutoFareNmFnInfos(MnjxPnrNm pnrNm, Integer passengerId, String patType, Integer isBaby,
                                          UpdatePnrDto.PriceItem priceItem, List<Object> addList, boolean chUseAdPrice) {
        MnjxNmFn nmFn = new MnjxNmFn();
        nmFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmFn.setPnrNmId(pnrNm.getPnrNmId());
        nmFn.setPnrIndex(0); // 后续重新排序

        nmFn.setIsBaby(isBaby);

        nmFn.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            nmFn.setPatType("AD");
        }

        nmFn.setCRate(StrUtil.isNotEmpty(priceItem.getCommissionRate()) ? new BigDecimal(priceItem.getCommissionRate()) : BigDecimal.ZERO);

        nmFn.setFCurrency(priceItem.getCurrency());
        nmFn.setSCurrency(priceItem.getCurrency());
        nmFn.setACurrency(priceItem.getCurrency());
        nmFn.setXCurrency(priceItem.getCurrency());
        nmFn.setTCnCurrency(priceItem.getCurrency());
        nmFn.setTYqCurrency(priceItem.getCurrency());

        nmFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
        nmFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
        nmFn.setXPrice(nmFn.getTCnPrice().add(nmFn.getTYqPrice()));

        String ticketAmount = NumberUtils.formatBigDecimalStr(priceItem.getTicketAmount());
        nmFn.setFPrice(new BigDecimal(ticketAmount));
        nmFn.setSPrice(new BigDecimal(ticketAmount));
        String totalAmount = NumberUtils.formatBigDecimalStr(priceItem.getTotalAmount());
        nmFn.setAPrice(new BigDecimal(totalAmount));
        String nmFnInputValue = StrUtil.format("FN/A/FCNY{}/SCNY{}/C{}/XCNY{}/TCNY{}CN/TCNY{}YQ/ACNY{}/P{}",
                ticketAmount, ticketAmount, StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                nmFn.getXPrice(), priceItem.getFund(), priceItem.getFuel(), totalAmount, passengerId);
        if ("CH".equals(patType) && !chUseAdPrice) {
            nmFnInputValue = StrUtil.format("FN/A/FCNY{}/SCNY{}/C{}/XCNY{}/TEXEMPTCN/TCNY{}YQ/ACNY{}/P{}",
                    ticketAmount, ticketAmount, StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                    nmFn.getXPrice(), priceItem.getFuel(), totalAmount, passengerId);
        } else if ("IN".equals(patType)) {
            nmFnInputValue = StrUtil.format("FN/IN/A/FCNY{}/SCNY{}/C{}/TEXEMPTCN/TEXEMPTYQ/ACNY{}/P{}",
                    ticketAmount, ticketAmount, StrUtil.isEmpty(priceItem.getCommissionRate()) ? "0.00" : priceItem.getCommissionRate(),
                    totalAmount, passengerId);
        }
        nmFn.setInputValue(nmFnInputValue);
        addList.add(nmFn);
    }

    private void processAutoFareNmFpInfos(MnjxPnrNm pnrNm, Integer passengerId, String patType, Integer isBaby,
                                          UpdatePnrDto.Price price, UpdatePnrDto.PriceItem priceItem,
                                          List<Object> addList, boolean chUseAdPrice) {
        MnjxNmFp nmFp = new MnjxNmFp();
        nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmFp.setPnrNmId(pnrNm.getPnrNmId());
        nmFp.setPnrIndex(0); // 后续重新排序
        nmFp.setIsBaby(isBaby);
        nmFp.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            nmFp.setPatType("AD");
        }
        nmFp.setPayType(StrUtil.isEmpty(price.getPayMethod()) ? "CASH" : price.getPayMethod());
        nmFp.setCurrencyType(priceItem.getCurrency());
        StringBuilder nmFpInputValueSb = new StringBuilder();
        nmFpInputValueSb.append("FP/");
        if (isBaby == 1) {
            nmFpInputValueSb.append("IN/");
        }
        nmFpInputValueSb.append("CASH,CNY/P")
                .append(passengerId);
        nmFp.setInputValue(nmFpInputValueSb.toString());

        addList.add(nmFp);
    }

    private void processAutoFareNmFcInfos(MnjxPnrNm pnrNm, Integer passengerId, String patType, Integer isBaby,
                                          List<UpdatePnrDto.PassengerSegment> passengerSegmentList, UpdatePnrDto.PriceItem priceItem,
                                          List<Object> addList, boolean chUseAdPrice) throws SguiResultException {

        MnjxNmFc nmFc = new MnjxNmFc();
        nmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmFc.setPnrNmId(pnrNm.getPnrNmId());
        nmFc.setPnrIndex(0); // 后续重新排序

        // 拼接input_value
        StringBuilder nmFcInputValueSb = new StringBuilder();
        nmFcInputValueSb.append("FC/A/");

        nmFc.setPatType(patType);
        if ("CH".equals(patType) && chUseAdPrice) {
            nmFc.setPatType("AD");
        }
        nmFc.setIsBaby(isBaby);

        if ("IN".equals(patType)) {
            nmFcInputValueSb.append("IN/");
        }

        // 设置每个航段舱位价格
        int segIndex = 1;
        for (int i = 0; i < passengerSegmentList.size(); i++) {
            UpdatePnrDto.PassengerSegment passengerSegment = passengerSegmentList.get(i);
            if ("ARNK".equals(passengerSegment.getSegmentType())) {
                if (i == passengerSegmentList.size() - 1 || i == 0) {
                    throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                }
                segIndex++;
                nmFcInputValueSb.append(" // ");
                continue;
            }
            // 设置票价
            switch (segIndex) {
                case 1:
                    nmFc.setSeg1Price(new BigDecimal(priceItem.getTicketAmount()));
                    nmFc.setSeg1Cabin(passengerSegment.getCabinCode());
                    nmFc.setSeg1PriceType(patType);
                    break;
                case 2:
                    nmFc.setSeg2Price(new BigDecimal(priceItem.getTicketAmount()));
                    nmFc.setSeg2Cabin(passengerSegment.getCabinCode());
                    nmFc.setSeg2PriceType(patType);
                    break;
                case 3:
                    nmFc.setSeg3Price(new BigDecimal(priceItem.getTicketAmount()));
                    nmFc.setSeg3Cabin(passengerSegment.getCabinCode());
                    nmFc.setSeg3PriceType(patType);
                    break;
                case 4:
                    nmFc.setSeg4Price(new BigDecimal(priceItem.getTicketAmount()));
                    nmFc.setSeg4Cabin(passengerSegment.getCabinCode());
                    nmFc.setSeg4PriceType(patType);
                    break;
                case 5:
                    nmFc.setSeg5Price(new BigDecimal(priceItem.getTicketAmount()));
                    nmFc.setSeg5Cabin(passengerSegment.getCabinCode());
                    nmFc.setSeg5PriceType(patType);
                    break;
                default:
                    break;
            }
            if (i == 0) {
                nmFcInputValueSb.append(passengerSegment.getDepartureAirport()).append(" ");
            }
            nmFcInputValueSb.append(passengerSegment.getMarketingAirline())
                    .append(" ")
                    .append(passengerSegment.getArrivalAirport())
                    .append(" ")
                    .append(priceItem.getTicketAmount())
                    .append(passengerSegment.getCabinCode());
            if ("CH".equals(patType) && !chUseAdPrice) {
                nmFcInputValueSb.append("CH").append(" ");
            } else if ("IN".equals(patType)) {
                nmFcInputValueSb.append("IN90").append(" ");
            } else {
                nmFcInputValueSb.append(" ");
            }
            if (i == passengerSegmentList.size() - 1) {
                nmFcInputValueSb.append("CNY")
                        .append(priceItem.getTotalAmount())
                        .append("END");
                if ("CH".equals(patType) && !chUseAdPrice) {
                    nmFcInputValueSb.append(" **(CH)");
                } else if ("IN".equals(patType)) {
                    nmFcInputValueSb.append(" **(IN)");
                }
                nmFcInputValueSb.append("/P").append(passengerId);
            }
            segIndex++;
        }

        // 设置总价、货币
        nmFc.setCurrency(priceItem.getCurrency());
        nmFc.setTotalPrice(new BigDecimal(priceItem.getTotalAmount()));
        nmFc.setInputValue(nmFcInputValueSb.toString());

        addList.add(nmFc);
    }

    /**
     * 处理婴儿SSR INFT修改
     */
    private void processInfantSsrUpdate(MnjxPnr pnr) {
        // 查找PNR中的婴儿
        List<MnjxNmXn> infantList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId,
                        iMnjxPnrNmService.lambdaQuery()
                                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                                .list()
                                .stream()
                                .map(MnjxPnrNm::getPnrNmId)
                                .collect(Collectors.toList()))
                .list();

        if (CollUtil.isNotEmpty(infantList)) {
            // 查找婴儿的SSR INFT记录
            for (MnjxNmXn infant : infantList) {
                List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, infant.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .ne(MnjxNmSsr::getActionCode, "HK")
                        .list();

                for (MnjxNmSsr ssr : ssrList) {
                    // 修改行动代码为HK
                    ssr.setActionCode("HK");

                    // 更新ssr_info和input_value中的行动代码
                    String ssrInfo = ssr.getSsrInfo();
                    String inputValue = ssr.getInputValue();

                    if (StrUtil.isNotEmpty(ssrInfo)) {
                        ssrInfo = ssrInfo.replaceAll("\\b\\w{2}\\d+", "HK1");
                        ssr.setSsrInfo(ssrInfo);
                    }

                    if (StrUtil.isNotEmpty(inputValue)) {
                        inputValue = inputValue.replaceAll("\\b\\w{2}\\d+", "HK1");
                        ssr.setInputValue(inputValue);
                    }

                    // 更新数据库
                    iMnjxNmSsrService.updateById(ssr);
                }
            }
        }
    }

    /**
     * 重新排序所有项的pnr_index
     */
    @Override
    public void reorderAllPnrIndexesAndUpdate(MnjxPnr pnr, List<MnjxPnrRecord> recordList) {
        // 查询所有PNR相关数据
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrOsi> pnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrRmk> pnrRmkList = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrTk> pnrTkList = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrTc> pnrTcList = iMnjxPnrTcService.lambdaQuery()
                .eq(MnjxPnrTc::getPnrId, pnr.getPnrId())
                .list();

        // 查询旅客级别数据
        List<String> pnrNmIds = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        List<MnjxNmCt> nmCtList = new ArrayList<>();
        List<MnjxNmOi> nmOiList = new ArrayList<>();
        List<MnjxNmTc> nmTcList = new ArrayList<>();
        List<MnjxPnrNmTn> pnrTnList = new ArrayList<>();

        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmSsrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, pnrNmIds)
                    .list();

            nmOsiList = iMnjxNmOsiService.lambdaQuery()
                    .in(MnjxNmOsi::getPnrNmId, pnrNmIds)
                    .list();

            nmRmkList = iMnjxNmRmkService.lambdaQuery()
                    .in(MnjxNmRmk::getPnrNmId, pnrNmIds)
                    .list();

            nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, pnrNmIds)
                    .list();

            nmFnList = iMnjxNmFnService.lambdaQuery()
                    .in(MnjxNmFn::getPnrNmId, pnrNmIds)
                    .list();

            nmFpList = iMnjxNmFpService.lambdaQuery()
                    .in(MnjxNmFp::getPnrNmId, pnrNmIds)
                    .list();

            nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, pnrNmIds)
                    .list();

            nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                    .list();

            nmCtList = iMnjxNmCtService.lambdaQuery()
                    .in(MnjxNmCt::getPnrNmId, pnrNmIds)
                    .list();

            nmOiList = iMnjxNmOiService.lambdaQuery()
                    .in(MnjxNmOi::getPnrNmId, pnrNmIds)
                    .list();

            pnrTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                    .list();

            if (CollUtil.isNotEmpty(nmXnList)) {
                pnrTnList.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                        .list());
            }

            nmTcList = iMnjxNmTcService.lambdaQuery()
                    .in(MnjxNmTc::getPnrNmId, pnrNmIds)
                    .list();

        }

        // 对PNR项进行重新排序
        iBookPnrService.reorderPnrIndexes(pnr, pnrNmList, pnrSegList, pnrCtList,
                nmCtList, pnrTkList, pnrFcList, nmFcList, nmSsrList,
                pnrOsiList, nmOsiList, pnrRmkList, nmRmkList, pnrTcList, nmTcList,
                pnrFnList, nmFnList, pnrEiList, nmEiList, nmOiList,
                pnrTnList, nmXnList, pnrFpList, nmFpList, recordList);

        // 批量更新数据库
        this.batchUpdateIndexes(pnr, pnrNmList, pnrSegList, pnrCtList,
                nmCtList, pnrTkList, pnrFcList, nmFcList, nmSsrList,
                pnrOsiList, nmOsiList, pnrRmkList, nmRmkList, pnrTcList, nmTcList,
                pnrFnList, nmFnList, pnrEiList, nmEiList, nmOiList,
                pnrTnList, nmXnList, pnrFpList, nmFpList);
    }

    /**
     * Title: reorderPnrIndexes
     * Description: 重新排序pnr_index<br>
     *
     * @param pnr
     * @param pnrNmList
     * @param pnrSegList
     * @param pnrCtList
     * @param nmCtList
     * @param pnrTkList
     * @param pnrFcList
     * @param nmFcList
     * @param nmSsrList
     * @param pnrOsiList
     * @param nmOsiList
     * @param pnrRmkList
     * @param nmRmkList
     * @param pnrTcList
     * @param nmTcList
     * @param pnrFnList
     * @param nmFnList
     * @param pnrEiList
     * @param nmEiList
     * @param nmOiList
     * @param pnrTnList
     * @param nmXnList
     * @param pnrFpList
     * @param nmFpList
     * @return void
     * <AUTHOR>
     * @date 2025/5/27 14:13
     */
    private void reorderPnrIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                   List<MnjxPnrCt> pnrCtList, List<MnjxNmCt> nmCtList, List<MnjxPnrTk> pnrTkList,
                                   List<MnjxPnrFc> pnrFcList, List<MnjxNmFc> nmFcList, List<MnjxNmSsr> nmSsrList,
                                   List<MnjxPnrOsi> pnrOsiList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrRmk> pnrRmkList,
                                   List<MnjxNmRmk> nmRmkList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList, List<MnjxPnrFn> pnrFnList,
                                   List<MnjxNmFn> nmFnList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                                   List<MnjxNmOi> nmOiList, List<MnjxPnrNmTn> pnrTnList, List<MnjxNmXn> nmXnList,
                                   List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList) {
        int newIndex = 1;

        // 1. pnr nm组（按psgIndex排序）
        if (CollUtil.isNotEmpty(pnrNmList)) {
            // 按psgIndex排序
            pnrNmList.sort(Comparator.comparing(MnjxPnrNm::getPsgIndex));
            for (MnjxPnrNm pnrNm : pnrNmList) {
                pnrNm.setPnrIndex(newIndex++);
            }
        }

        // 2. pnr seg组
        if (CollUtil.isNotEmpty(pnrSegList)) {
            for (MnjxPnrSeg pnrSeg : pnrSegList) {
                pnrSeg.setPnrIndex(newIndex++);
            }
        }

        // 3. pnr ct组
        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                pnrCt.setPnrIndex(newIndex++);
            }
        }

        // 4. nm ct组
        if (CollUtil.isNotEmpty(nmCtList)) {
            for (MnjxNmCt nmCt : nmCtList) {
                nmCt.setPnrIndex(newIndex++);
            }
        }

        // 5. pnr tk组
        if (CollUtil.isNotEmpty(pnrTkList)) {
            for (MnjxPnrTk pnrTk : pnrTkList) {
                pnrTk.setPnrIndex(newIndex++);
            }
        }

        // 6. pnr fc组
        if (CollUtil.isNotEmpty(pnrFcList)) {
            for (MnjxPnrFc pnrFc : pnrFcList) {
                pnrFc.setPnrIndex(newIndex++);
            }
        }

        // 7. nm fc组
        if (CollUtil.isNotEmpty(nmFcList)) {
            for (MnjxNmFc nmFc : nmFcList) {
                nmFc.setPnrIndex(newIndex++);
            }
        }

        // 8. nm ssr组
        if (CollUtil.isNotEmpty(nmSsrList)) {
            for (MnjxNmSsr nmSsr : nmSsrList) {
                nmSsr.setPnrIndex(newIndex++);
            }
        }

        // 9. pnr osi组
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi pnrOsi : pnrOsiList) {
                pnrOsi.setPnrIndex(newIndex++);
            }
        }

        // 10. nm osi组
        if (CollUtil.isNotEmpty(nmOsiList)) {
            for (MnjxNmOsi nmOsi : nmOsiList) {
                nmOsi.setPnrIndex(newIndex++);
            }
        }

        // 11. pnr rmk组
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk pnrRmk : pnrRmkList) {
                pnrRmk.setPnrIndex(newIndex++);
            }
        }

        // 12. nm rmk组
        if (CollUtil.isNotEmpty(nmRmkList)) {
            for (MnjxNmRmk nmRmk : nmRmkList) {
                nmRmk.setPnrIndex(newIndex++);
            }
        }

        // 13. pnr tc组
        if (CollUtil.isNotEmpty(pnrTcList)) {
            for (MnjxPnrTc pnrTc : pnrTcList) {
                pnrTc.setPnrIndex(newIndex++);
            }
        }

        // 13. nm tc组
        if (CollUtil.isNotEmpty(nmTcList)) {
            for (MnjxNmTc nmTc : nmTcList) {
                nmTc.setPnrIndex(newIndex++);
            }
        }

        // 14. pnr fn组
        if (CollUtil.isNotEmpty(pnrFnList)) {
            for (MnjxPnrFn pnrFn : pnrFnList) {
                pnrFn.setPnrIndex(newIndex++);
            }
        }

        // 15. nm fn组
        if (CollUtil.isNotEmpty(nmFnList)) {
            for (MnjxNmFn nmFn : nmFnList) {
                nmFn.setPnrIndex(newIndex++);
            }
        }

        // 16. pnr ei组
        if (CollUtil.isNotEmpty(pnrEiList)) {
            for (MnjxPnrEi pnrEi : pnrEiList) {
                pnrEi.setPnrIndex(newIndex++);
            }
        }

        // 17. nm ei组
        if (CollUtil.isNotEmpty(nmEiList)) {
            for (MnjxNmEi nmEi : nmEiList) {
                nmEi.setPnrIndex(newIndex++);
            }
        }

        // 18. nm oi组
        if (CollUtil.isNotEmpty(nmOiList)) {
            for (MnjxNmOi nmOi : nmOiList) {
                nmOi.setPnrIndex(newIndex++);
            }
        }

        // 19. pnr tn组
        if (CollUtil.isNotEmpty(pnrTnList)) {
            for (MnjxPnrNmTn pnrTn : pnrTnList) {
                pnrTn.setPnrIndex(newIndex++);
            }
        }

        // 20. nm xn组
        if (CollUtil.isNotEmpty(nmXnList)) {
            for (MnjxNmXn nmXn : nmXnList) {
                nmXn.setPnrIndex(newIndex++);
            }
        }

        // 21. pnr fp组
        if (CollUtil.isNotEmpty(pnrFpList)) {
            for (MnjxPnrFp pnrFp : pnrFpList) {
                pnrFp.setPnrIndex(newIndex++);
            }
        }

        // 22. nm fp组
        if (CollUtil.isNotEmpty(nmFpList)) {
            for (MnjxNmFp nmFp : nmFpList) {
                nmFp.setPnrIndex(newIndex++);
            }
        }

        // 更新PNR最大索引值
        pnr.setMaxIndex(newIndex - 1);
    }

    /**
     * 批量更新索引
     */
    private void batchUpdateIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                    List<MnjxPnrCt> pnrCtList, List<MnjxNmCt> nmCtList, List<MnjxPnrTk> pnrTkList,
                                    List<MnjxPnrFc> pnrFcList, List<MnjxNmFc> nmFcList, List<MnjxNmSsr> nmSsrList,
                                    List<MnjxPnrOsi> pnrOsiList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrRmk> pnrRmkList,
                                    List<MnjxNmRmk> nmRmkList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList, List<MnjxPnrFn> pnrFnList,
                                    List<MnjxNmFn> nmFnList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                                    List<MnjxNmOi> nmOiList, List<MnjxPnrNmTn> pnrTnList, List<MnjxNmXn> nmXnList,
                                    List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList) {
        // 更新PNR
        iMnjxPnrService.updateById(pnr);

        // 批量更新各项索引
        if (CollUtil.isNotEmpty(pnrNmList)) {
            iMnjxPnrNmService.updateBatchById(pnrNmList);
        }
        if (CollUtil.isNotEmpty(pnrSegList)) {
            iMnjxPnrSegService.updateBatchById(pnrSegList);
        }
        if (CollUtil.isNotEmpty(pnrCtList)) {
            iMnjxPnrCtService.updateBatchById(pnrCtList);
        }
        if (CollUtil.isNotEmpty(nmCtList)) {
            iMnjxNmCtService.updateBatchById(nmCtList);
        }
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            iMnjxPnrOsiService.updateBatchById(pnrOsiList);
        }
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            iMnjxPnrRmkService.updateBatchById(pnrRmkList);
        }
        if (CollUtil.isNotEmpty(pnrTkList)) {
            iMnjxPnrTkService.updateBatchById(pnrTkList);
        }
        if (CollUtil.isNotEmpty(pnrFcList)) {
            iMnjxPnrFcService.updateBatchById(pnrFcList);
        }
        if (CollUtil.isNotEmpty(pnrFnList)) {
            iMnjxPnrFnService.updateBatchById(pnrFnList);
        }
        if (CollUtil.isNotEmpty(pnrFpList)) {
            iMnjxPnrFpService.updateBatchById(pnrFpList);
        }
        if (CollUtil.isNotEmpty(pnrEiList)) {
            iMnjxPnrEiService.updateBatchById(pnrEiList);
        }
        if (CollUtil.isNotEmpty(pnrTcList)) {
            iMnjxPnrTcService.updateBatchById(pnrTcList);
        }
        if (CollUtil.isNotEmpty(nmTcList)) {
            iMnjxNmTcService.updateBatchById(nmTcList);
        }
        if (CollUtil.isNotEmpty(pnrTnList)) {
            iMnjxPnrNmTnService.updateBatchById(pnrTnList);
        }
        if (CollUtil.isNotEmpty(nmSsrList)) {
            iMnjxNmSsrService.updateBatchById(nmSsrList);
        }
        if (CollUtil.isNotEmpty(nmOsiList)) {
            iMnjxNmOsiService.updateBatchById(nmOsiList);
        }
        if (CollUtil.isNotEmpty(nmRmkList)) {
            iMnjxNmRmkService.updateBatchById(nmRmkList);
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            iMnjxNmFcService.updateBatchById(nmFcList);
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            iMnjxNmFnService.updateBatchById(nmFnList);
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            iMnjxNmFpService.updateBatchById(nmFpList);
        }
        if (CollUtil.isNotEmpty(nmEiList)) {
            iMnjxNmEiService.updateBatchById(nmEiList);
        }
        if (CollUtil.isNotEmpty(nmXnList)) {
            iMnjxNmXnService.updateBatchById(nmXnList);
        }
        if (CollUtil.isNotEmpty(nmOiList)) {
            iMnjxNmOiService.updateBatchById(nmOiList);
        }
    }

    /**
     * 批量执行数据库操作
     */
    private void batchExecuteOperations(List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        // 批量新增
        if (CollUtil.isNotEmpty(addList)) {
            this.batchSave(addList);
        }

        // 批量更新
        if (CollUtil.isNotEmpty(updateList)) {
            this.batchUpdate(updateList);
        }

        // 批量删除
        if (CollUtil.isNotEmpty(deleteList)) {
            this.batchDelete(deleteList);
        }
    }

    /**
     * 批量保存
     */
    private void batchSave(List<Object> addList) {
        // 按类型分组批量保存
        Map<Class<?>, List<Object>> groupedMap = addList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                List<MnjxPnrNm> list = objects.stream()
                        .map(obj -> (MnjxPnrNm) obj)
                        .collect(Collectors.toList());
                iMnjxPnrNmService.saveBatch(list);
            } else if (clazz == MnjxPnrSeg.class) {
                List<MnjxPnrSeg> list = objects.stream()
                        .map(obj -> (MnjxPnrSeg) obj)
                        .collect(Collectors.toList());
                iMnjxPnrSegService.saveBatch(list);
            } else if (clazz == MnjxPnrCt.class) {
                List<MnjxPnrCt> list = objects.stream()
                        .map(obj -> (MnjxPnrCt) obj)
                        .collect(Collectors.toList());
                iMnjxPnrCtService.saveBatch(list);
            } else if (clazz == MnjxPnrOsi.class) {
                List<MnjxPnrOsi> list = objects.stream()
                        .map(obj -> (MnjxPnrOsi) obj)
                        .collect(Collectors.toList());
                iMnjxPnrOsiService.saveBatch(list);
            } else if (clazz == MnjxPnrRmk.class) {
                List<MnjxPnrRmk> list = objects.stream()
                        .map(obj -> (MnjxPnrRmk) obj)
                        .collect(Collectors.toList());
                iMnjxPnrRmkService.saveBatch(list);
            } else if (clazz == MnjxPnrTk.class) {
                List<MnjxPnrTk> list = objects.stream()
                        .map(obj -> (MnjxPnrTk) obj)
                        .collect(Collectors.toList());
                iMnjxPnrTkService.saveBatch(list);
            } else if (clazz == MnjxPnrFc.class) {
                List<MnjxPnrFc> list = objects.stream()
                        .map(obj -> (MnjxPnrFc) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFcService.saveBatch(list);
            } else if (clazz == MnjxPnrFn.class) {
                List<MnjxPnrFn> list = objects.stream()
                        .map(obj -> (MnjxPnrFn) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFnService.saveBatch(list);
            } else if (clazz == MnjxPnrFp.class) {
                List<MnjxPnrFp> list = objects.stream()
                        .map(obj -> (MnjxPnrFp) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFpService.saveBatch(list);
            } else if (clazz == MnjxPnrEi.class) {
                List<MnjxPnrEi> list = objects.stream()
                        .map(obj -> (MnjxPnrEi) obj)
                        .collect(Collectors.toList());
                iMnjxPnrEiService.saveBatch(list);
            } else if (clazz == MnjxPnrTc.class) {
                List<MnjxPnrTc> list = objects.stream()
                        .map(obj -> (MnjxPnrTc) obj)
                        .collect(Collectors.toList());
                iMnjxPnrTcService.saveBatch(list);
            } else if (clazz == MnjxNmSsr.class) {
                List<MnjxNmSsr> list = objects.stream()
                        .map(obj -> (MnjxNmSsr) obj)
                        .collect(Collectors.toList());
                iMnjxNmSsrService.saveBatch(list);
            } else if (clazz == MnjxNmOsi.class) {
                List<MnjxNmOsi> list = objects.stream()
                        .map(obj -> (MnjxNmOsi) obj)
                        .collect(Collectors.toList());
                iMnjxNmOsiService.saveBatch(list);
            } else if (clazz == MnjxNmRmk.class) {
                List<MnjxNmRmk> list = objects.stream()
                        .map(obj -> (MnjxNmRmk) obj)
                        .collect(Collectors.toList());
                iMnjxNmRmkService.saveBatch(list);
            } else if (clazz == MnjxNmFc.class) {
                List<MnjxNmFc> list = objects.stream()
                        .map(obj -> (MnjxNmFc) obj)
                        .collect(Collectors.toList());
                iMnjxNmFcService.saveBatch(list);
            } else if (clazz == MnjxNmFn.class) {
                List<MnjxNmFn> list = objects.stream()
                        .map(obj -> (MnjxNmFn) obj)
                        .collect(Collectors.toList());
                iMnjxNmFnService.saveBatch(list);
            } else if (clazz == MnjxNmFp.class) {
                List<MnjxNmFp> list = objects.stream()
                        .map(obj -> (MnjxNmFp) obj)
                        .collect(Collectors.toList());
                iMnjxNmFpService.saveBatch(list);
            } else if (clazz == MnjxNmEi.class) {
                List<MnjxNmEi> list = objects.stream()
                        .map(obj -> (MnjxNmEi) obj)
                        .collect(Collectors.toList());
                iMnjxNmEiService.saveBatch(list);
            } else if (clazz == MnjxNmTc.class) {
                List<MnjxNmTc> list = objects.stream()
                        .map(obj -> (MnjxNmTc) obj)
                        .collect(Collectors.toList());
                iMnjxNmTcService.saveBatch(list);
            } else if (clazz == MnjxNmXn.class) {
                List<MnjxNmXn> list = objects.stream()
                        .map(obj -> (MnjxNmXn) obj)
                        .collect(Collectors.toList());
                iMnjxNmXnService.saveBatch(list);
            }
        }
    }

    /**
     * 批量更新
     */
    private void batchUpdate(List<Object> updateList) {
        // 按类型分组批量更新
        Map<Class<?>, List<Object>> groupedMap = updateList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                List<MnjxPnrNm> list = objects.stream()
                        .map(obj -> (MnjxPnrNm) obj)
                        .collect(Collectors.toList());
                iMnjxPnrNmService.updateBatchById(list);
            } else if (clazz == MnjxPnrSeg.class) {
                List<MnjxPnrSeg> list = objects.stream()
                        .map(obj -> (MnjxPnrSeg) obj)
                        .collect(Collectors.toList());
                iMnjxPnrSegService.updateBatchById(list);
            } else if (clazz == MnjxPnrCt.class) {
                List<MnjxPnrCt> list = objects.stream()
                        .map(obj -> (MnjxPnrCt) obj)
                        .collect(Collectors.toList());
                iMnjxPnrCtService.updateBatchById(list);
            } else if (clazz == MnjxPnrOsi.class) {
                List<MnjxPnrOsi> list = objects.stream()
                        .map(obj -> (MnjxPnrOsi) obj)
                        .collect(Collectors.toList());
                iMnjxPnrOsiService.updateBatchById(list);
            } else if (clazz == MnjxPnrRmk.class) {
                List<MnjxPnrRmk> list = objects.stream()
                        .map(obj -> (MnjxPnrRmk) obj)
                        .collect(Collectors.toList());
                iMnjxPnrRmkService.updateBatchById(list);
            } else if (clazz == MnjxPnrTk.class) {
                List<MnjxPnrTk> list = objects.stream()
                        .map(obj -> (MnjxPnrTk) obj)
                        .collect(Collectors.toList());
                iMnjxPnrTkService.updateBatchById(list);
            } else if (clazz == MnjxPnrFc.class) {
                List<MnjxPnrFc> list = objects.stream()
                        .map(obj -> (MnjxPnrFc) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFcService.updateBatchById(list);
            } else if (clazz == MnjxPnrFn.class) {
                List<MnjxPnrFn> list = objects.stream()
                        .map(obj -> (MnjxPnrFn) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFnService.updateBatchById(list);
            } else if (clazz == MnjxPnrFp.class) {
                List<MnjxPnrFp> list = objects.stream()
                        .map(obj -> (MnjxPnrFp) obj)
                        .collect(Collectors.toList());
                iMnjxPnrFpService.updateBatchById(list);
            } else if (clazz == MnjxPnrEi.class) {
                List<MnjxPnrEi> list = objects.stream()
                        .map(obj -> (MnjxPnrEi) obj)
                        .collect(Collectors.toList());
                iMnjxPnrEiService.updateBatchById(list);
            } else if (clazz == MnjxPnrTc.class) {
                List<MnjxPnrTc> list = objects.stream()
                        .map(obj -> (MnjxPnrTc) obj)
                        .collect(Collectors.toList());
                iMnjxPnrTcService.updateBatchById(list);
            } else if (clazz == MnjxNmSsr.class) {
                List<MnjxNmSsr> list = objects.stream()
                        .map(obj -> (MnjxNmSsr) obj)
                        .collect(Collectors.toList());
                iMnjxNmSsrService.updateBatchById(list);
            } else if (clazz == MnjxNmOsi.class) {
                List<MnjxNmOsi> list = objects.stream()
                        .map(obj -> (MnjxNmOsi) obj)
                        .collect(Collectors.toList());
                iMnjxNmOsiService.updateBatchById(list);
            } else if (clazz == MnjxNmRmk.class) {
                List<MnjxNmRmk> list = objects.stream()
                        .map(obj -> (MnjxNmRmk) obj)
                        .collect(Collectors.toList());
                iMnjxNmRmkService.updateBatchById(list);
            } else if (clazz == MnjxNmFc.class) {
                List<MnjxNmFc> list = objects.stream()
                        .map(obj -> (MnjxNmFc) obj)
                        .collect(Collectors.toList());
                iMnjxNmFcService.updateBatchById(list);
            } else if (clazz == MnjxNmFn.class) {
                List<MnjxNmFn> list = objects.stream()
                        .map(obj -> (MnjxNmFn) obj)
                        .collect(Collectors.toList());
                iMnjxNmFnService.updateBatchById(list);
            } else if (clazz == MnjxNmFp.class) {
                List<MnjxNmFp> list = objects.stream()
                        .map(obj -> (MnjxNmFp) obj)
                        .collect(Collectors.toList());
                iMnjxNmFpService.updateBatchById(list);
            } else if (clazz == MnjxNmEi.class) {
                List<MnjxNmEi> list = objects.stream()
                        .map(obj -> (MnjxNmEi) obj)
                        .collect(Collectors.toList());
                iMnjxNmEiService.updateBatchById(list);
            } else if (clazz == MnjxNmTc.class) {
                List<MnjxNmTc> list = objects.stream()
                        .map(obj -> (MnjxNmTc) obj)
                        .collect(Collectors.toList());
                iMnjxNmTcService.updateBatchById(list);
            } else if (clazz == MnjxNmXn.class) {
                List<MnjxNmXn> list = objects.stream()
                        .map(obj -> (MnjxNmXn) obj)
                        .collect(Collectors.toList());
                iMnjxNmXnService.updateBatchById(list);
            } else if (clazz == MnjxPnrRecord.class) {
                List<MnjxPnrRecord> list = objects.stream()
                        .map(obj -> (MnjxPnrRecord) obj)
                        .collect(Collectors.toList());
                iMnjxPnrRecordService.updateBatchById(list);
            }
        }
    }

    /**
     * 批量删除
     */
    private void batchDelete(List<Object> deleteList) {
        // 按类型分组批量删除
        Map<Class<?>, List<Object>> groupedMap = deleteList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrNm) obj).getPnrNmId())
                        .collect(Collectors.toList());
                iMnjxPnrNmService.removeByIds(ids);
            } else if (clazz == MnjxPnrSeg.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrSeg) obj).getPnrSegId())
                        .collect(Collectors.toList());
                iMnjxPnrSegService.removeByIds(ids);
            } else if (clazz == MnjxPnrCt.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrCt) obj).getPnrCtId())
                        .collect(Collectors.toList());
                iMnjxPnrCtService.removeByIds(ids);
            } else if (clazz == MnjxPnrOsi.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrOsi) obj).getPnrOsiId())
                        .collect(Collectors.toList());
                iMnjxPnrOsiService.removeByIds(ids);
            } else if (clazz == MnjxPnrRmk.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrRmk) obj).getPnrRmkId())
                        .collect(Collectors.toList());
                iMnjxPnrRmkService.removeByIds(ids);
            } else if (clazz == MnjxPnrTk.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrTk) obj).getPnrTkId())
                        .collect(Collectors.toList());
                iMnjxPnrTkService.removeByIds(ids);
            } else if (clazz == MnjxPnrFc.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrFc) obj).getPnrFcId())
                        .collect(Collectors.toList());
                iMnjxPnrFcService.removeByIds(ids);
            } else if (clazz == MnjxPnrFn.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrFn) obj).getPnrFnId())
                        .collect(Collectors.toList());
                iMnjxPnrFnService.removeByIds(ids);
            } else if (clazz == MnjxPnrFp.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrFp) obj).getPnrFpId())
                        .collect(Collectors.toList());
                iMnjxPnrFpService.removeByIds(ids);
            } else if (clazz == MnjxPnrEi.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrEi) obj).getPnrEiId())
                        .collect(Collectors.toList());
                iMnjxPnrEiService.removeByIds(ids);
            } else if (clazz == MnjxPnrTc.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxPnrTc) obj).getPnrTcId())
                        .collect(Collectors.toList());
                iMnjxPnrTcService.removeByIds(ids);
            } else if (clazz == MnjxNmSsr.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmSsr) obj).getNmSsrId())
                        .collect(Collectors.toList());
                iMnjxNmSsrService.removeByIds(ids);
            } else if (clazz == MnjxNmOsi.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmOsi) obj).getPnrOsiId())
                        .collect(Collectors.toList());
                iMnjxNmOsiService.removeByIds(ids);
            } else if (clazz == MnjxNmRmk.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmRmk) obj).getNmRmkId())
                        .collect(Collectors.toList());
                iMnjxNmRmkService.removeByIds(ids);
            } else if (clazz == MnjxNmFc.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmFc) obj).getNmFcId())
                        .collect(Collectors.toList());
                iMnjxNmFcService.removeByIds(ids);
            } else if (clazz == MnjxNmFn.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmFn) obj).getNmFnId())
                        .collect(Collectors.toList());
                iMnjxNmFnService.removeByIds(ids);
            } else if (clazz == MnjxNmFp.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmFp) obj).getNmFpId())
                        .collect(Collectors.toList());
                iMnjxNmFpService.removeByIds(ids);
            } else if (clazz == MnjxNmEi.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmEi) obj).getNmEiId())
                        .collect(Collectors.toList());
                iMnjxNmEiService.removeByIds(ids);
            } else if (clazz == MnjxNmTc.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmTc) obj).getNmTcId())
                        .collect(Collectors.toList());
                iMnjxNmTcService.removeByIds(ids);
            } else if (clazz == MnjxNmXn.class) {
                List<String> ids = objects.stream()
                        .map(obj -> ((MnjxNmXn) obj).getNmXnId())
                        .collect(Collectors.toList());
                iMnjxNmXnService.removeByIds(ids);
            }
        }
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, List<MnjxPnrRecord> recordList, String envelopType, String newAtNo) throws SguiResultException {
        // 创建封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);

        // 设置封口类型
        if (StrUtil.isNotEmpty(envelopType)) {
            if ("I".equals(envelopType)) {
                pnrAt.setAtType("I");
            } else if ("KI".equals(envelopType)) {
                pnrAt.setAtType("KI");
            }
        }

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        iMnjxPnrRecordService.saveBatch(recordList);
    }
}