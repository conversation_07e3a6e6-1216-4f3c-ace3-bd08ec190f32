package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.vo.CheckCodeVo;
import com.swcares.obj.vo.UserInfoDetailVo;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/4/18 10:58
 */
public interface IAuthService {

    CheckCodeVo checkCode(HttpServletRequest request, String checkCodeInput, String iamToken) throws SguiResultException;

    UserInfoDetailVo getCurrentUser(HttpServletRequest request);

}
