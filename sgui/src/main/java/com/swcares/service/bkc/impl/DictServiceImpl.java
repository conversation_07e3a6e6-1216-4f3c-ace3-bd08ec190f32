package com.swcares.service.bkc.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.entity.SguiData;
import com.swcares.obj.dto.QueryDictEntryDto;
import com.swcares.obj.vo.QueryDictEntryVo;
import com.swcares.service.ISguiDataService;
import com.swcares.service.bkc.IDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Slf4j
@Service
public class DictServiceImpl implements IDictService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public QueryDictEntryVo queryDictEntryList(QueryDictEntryDto dto) {
        // 查询sgui_data表，查询参数为name=dict_entry
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "dict_entry")
                .one();
        
        // 将查询回来的value（json格式的数据）转vo返回
        if (data != null && data.getValue() != null) {
            return JSONUtil.parseObj(data.getValue()).toBean(QueryDictEntryVo.class);
        }
        
        // 如果没有数据，返回空对象
        return new QueryDictEntryVo();
    }
}
