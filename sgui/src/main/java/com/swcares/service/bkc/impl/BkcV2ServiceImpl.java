package com.swcares.service.bkc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.CustomUserDetails;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.bkc.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:34
 */
@Service
public class BkcV2ServiceImpl implements IBkcV2Service {

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IPnrDetailService iPnrDetailService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Resource
    private IIssueService iIssueService;

    @Resource
    private IRemoveNameService iRemoveNameService;

    @Resource
    private ISplitPnrService iSplitPnrService;

    @Resource
    private IXePnrService iXePnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Override
    public CheckVersionVo checkVersion(CheckVersionDto dto) {
        CheckVersionVo vo = new CheckVersionVo();
        vo.setApp(dto.getApp());
        vo.setModule(dto.getModule());
        vo.setVersion(dto.getVersion());
        vo.setVersionAttr(dto.getVersionAttr());
        vo.setUserInfo(BeanUtil.copyProperties(dto.getUserInfo(), CheckVersionVo.UserInfo.class));
        vo.setUpgradeStrategy(1);
        vo.setOnlineUpgradeFlag(false);
        vo.setDaysRemaining(0);
        return vo;
    }

    @Override
    public List<String> domesticairline() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
            UserInfo userInfo = customUserDetails.getUserInfo();
            MnjxOffice office = userInfo.getMnjxOffice();
            if ("0".equals(office.getOfficeType())) {
                MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
                List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                        .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                        .list();
                List<String> airlineIdList = agentAirlineList.stream()
                        .map(MnjxAgentAirline::getAirlineId)
                        .collect(Collectors.toList());
                List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(airlineIdList);
                return airlineList.stream()
                        .map(MnjxAirline::getAirlineCode)
                        .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public QueryPnrDetailVo queryPnrDetail(QueryPnrDetailDto dto) throws SguiResultException {
        // 调用PnrCommonService的queryPnrDetail方法
        return iPnrDetailService.queryPnrDetail(dto);
    }

    @Override
    public PnrHistoryVo queryPnrHistory(String pnrNo) throws SguiResultException {
        if (StrUtil.isEmpty(pnrNo)) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnrNo)
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 查询PNR历史记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRecord::getPnrIndex)
                .list();

        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        // 筛选pnrRecords中changeAtNo不为空的数据
        List<MnjxPnrRecord> changeRecordList = pnrRecords.stream()
                .filter(r -> StrUtil.isNotEmpty(r.getChangeAtNo()))
                .collect(Collectors.toList());

        // 查询PNR封口记录
        List<MnjxPnrAt> pnrAts = iMnjxPnrAtService.lambdaQuery()
                .eq(MnjxPnrAt::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrAt::getAtNo)
                .orderByAsc(MnjxPnrAt::getAtDateTime)
                .list();

        // 构建返回对象
        PnrHistoryVo vo = new PnrHistoryVo();
        vo.setPnrNo(pnrNo);

        // 构建历史行内容
        List<PnrHistoryVo.LineContent> historyLineContents = new ArrayList<>();

        // 处理历史记录
        for (MnjxPnrRecord record : pnrRecords) {
            if (StrUtil.isNotEmpty(record.getChangeMark())) {
                continue;
            }
            PnrHistoryVo.LineContent lineContent = new PnrHistoryVo.LineContent();
            lineContent.setIndex(record.getAtNo());
            lineContent.setContent(StrUtil.format("    {}.{}", record.getPnrIndex(), record.getInputValue()));
            if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                lineContent.setContent(StrUtil.format("    {}.EI/{}", record.getPnrIndex(), record.getInputValue()));
            }
            lineContent.setCanDelete(false);
            lineContent.setCode(null);

            lineContent.setInvalidSeg(null);

            historyLineContents.add(lineContent);
        }

        // 处理封口记录
        MnjxSi mnjxSi = iMnjxSiService.getById(pnrAts.get(0).getAtSiId());
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        String siNo = mnjxSi.getSiNo();
        String officeNo = mnjxOffice.getOfficeNo();
        for (MnjxPnrAt at : pnrAts) {
            PnrHistoryVo.LineContent lineContent = new PnrHistoryVo.LineContent();
            lineContent.setIndex(at.getAtNo());

            // 格式化封口时间
            String atDate = DateUtils.ymd2Com(DateUtil.format(at.getAtDateTime(), "yyyy-MM-dd")).substring(0, 5);
            String atTime = DateUtil.format(at.getAtDateTime(), "HHmm");

            // 构建封口行内容
            String content;
            if (StrUtil.isEmpty(at.getAtType())) {
                content = StrUtil.format("    {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate);
            } else {
                if (StrUtil.equalsAny(at.getAtType(), "I", "KI")) {
                    content = StrUtil.format("    {} {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate, at.getAtType());
                } else {
                    if (Integer.parseInt(at.getAtNo()) > 1) {
                        content = StrUtil.format("    HDQ{} {} {} {} /RLC{}", at.getAtType(), siNo, atTime, atDate, Integer.parseInt(at.getAtNo()) - 1);
                    } else {
                        content = StrUtil.format("    HDQ{} {} {} {}", at.getAtType(), siNo, atTime, atDate);
                    }
                }
            }

            lineContent.setContent(content);
            lineContent.setCanDelete(false);
            lineContent.setCode(null);
            lineContent.setInvalidSeg(false);

            historyLineContents.add(lineContent);

            for (MnjxPnrRecord record : changeRecordList) {
                if ((StrUtil.isNotEmpty(at.getAtType()) && !StrUtil.equalsAny(at.getAtType(), "K", "I", "KI")) || !at.getAtNo().equals(record.getAtNo())) {
                    continue;
                }
                PnrHistoryVo.LineContent changelineContent = new PnrHistoryVo.LineContent();
                changelineContent.setIndex(record.getAtNo() + "/" + record.getChangeAtNo());
                changelineContent.setContent(record.getInputValue());
                if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                    changelineContent.setContent("EI/" + record.getInputValue());
                }
                changelineContent.setCanDelete(false);
                changelineContent.setCode(null);

                changelineContent.setInvalidSeg(null);

                historyLineContents.add(changelineContent);
            }
        }

        vo.setHistoryLineContents(historyLineContents);
        return vo;
    }

    @Override
    public QueryPnrRtlVo queryPnrRtl(QueryPnrRtlDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 查询PNR记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRecord::getPnrIndex)
                .list();

        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        pnrRecords = pnrRecords.stream()
                .filter(p -> StrUtil.isEmpty(p.getChangeAtNo()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrRecords)) {
            return null;
        }

        // 构建返回对象
        QueryPnrRtlVo vo = new QueryPnrRtlVo();
        List<QueryPnrRtlVo.LineContent> rtlLineContents = new ArrayList<>();

        // 处理PNR记录
        for (int i = 0; i < pnrRecords.size(); i++) {
            MnjxPnrRecord record = pnrRecords.get(i);
            QueryPnrRtlVo.LineContent lineContent = new QueryPnrRtlVo.LineContent();
            lineContent.setIndex(record.getPnrIndex() + ".");
            lineContent.setContent(record.getInputValue());
            if (i + 1 < pnrRecords.size() && "NM".equals(record.getPnrType()) && !"NM".equals(pnrRecords.get(i + 1).getPnrType())) {
                lineContent.setContent(record.getInputValue() + " " + dto.getPnrNo());
            }
            if ("SEG".equals(record.getPnrType())) {
                MnjxPnrSeg pnrSeg = pnrSegList.stream()
                        .filter(p -> p.getPnrIndex().intValue() == record.getPnrIndex())
                        .collect(Collectors.toList())
                        .get(0);
                if (!"SA".equals(pnrSeg.getPnrSegType())) {
                    lineContent.setContent(record.getInputValue() + " --" + pnrSegList.get(0).getFlightNo().substring(0, 2) + "/" + pnr.getPnrIcs());
                } else {
                    lineContent.setContent(record.getInputValue() + " INFORM");
                }
            }
            lineContent.setCanDelete(false);
            lineContent.setCode(null);
            lineContent.setInvalidSeg(false);

            rtlLineContents.add(lineContent);
        }

        vo.setRtlLineContent(rtlLineContents);
        return vo;
    }

    @Override
    public void strongRefresh(StrongRefreshDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = this.iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 查询PNR中的旅客信息
        List<MnjxPnrNm> pnrNmList = this.iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isEmpty(pnrNmList)) {
            return; // 没有旅客信息，直接返回
        }

        // 获取当前登录用户的工作号信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo == null) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        MnjxSi currentSi = iMnjxSiService.getById(userInfo.getSiId());
        if (currentSi == null) {
            throw new SguiResultException("获取当前用户工作号信息失败");
        }

        // 查询PNR记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .list();

        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());

        // 遍历旅客，查找婴儿信息
        boolean hasUpdates = false;
        for (MnjxPnrNm pnrNm : pnrNmList) {
            // 查询旅客是否有婴儿
            MnjxNmXn nmXn = this.iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                    .one();

            if (nmXn != null) {
                // 查询婴儿的SSR INFT信息
                List<MnjxNmSsr> infantSsrList = this.iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .list();

                if (CollUtil.isNotEmpty(infantSsrList)) {
                    // 遍历婴儿的SSR INFT信息，检查行动代码
                    List<MnjxNmSsr> updateSsrList = new ArrayList<>();
                    List<MnjxPnrRecord> updateRecordList = new ArrayList<>();

                    for (MnjxNmSsr infantSsr : infantSsrList) {
                        if (!"HK".equals(infantSsr.getActionCode())) {
                            hasUpdates = true;
                            // 修改行动代码为HK
                            String oldActionCode = infantSsr.getActionCode();
                            infantSsr.setActionCode("HK");

                            // 修改ssr_info和input_value中的行动代码
                            if (StrUtil.isNotEmpty(infantSsr.getSsrInfo())) {
                                infantSsr.setSsrInfo(infantSsr.getSsrInfo().replace(oldActionCode, "HK"));
                            }

                            if (StrUtil.isNotEmpty(infantSsr.getInputValue())) {
                                infantSsr.setInputValue(infantSsr.getInputValue().replace(oldActionCode, "HK"));
                            }

                            updateSsrList.add(infantSsr);

                            // 查找对应的PNR记录
                            List<MnjxPnrRecord> ssrRecords = pnrRecords.stream()
                                    .filter(record -> "SSR".equals(record.getPnrType()) &&
                                            record.getPnrNmId() != null &&
                                            record.getPnrNmId().equals(pnrNm.getPnrNmId()) &&
//                                            StrUtil.isEmpty(record.getChangeAtNo()) &&
                                            record.getInputValue() != null &&
                                            record.getInputValue().contains("INFT") &&
                                            record.getInputValue().contains(oldActionCode))
                                    .collect(Collectors.toList());

                            for (MnjxPnrRecord ssrRecord : ssrRecords) {
                                // 更新现有记录
                                ssrRecord.setInputValue(ssrRecord.getInputValue().replace(oldActionCode, "HK"));
                                ssrRecord.setChangeMark("U"); // 设置为修改
                                ssrRecord.setChangeAtNo(newAtNo);

                                updateRecordList.add(ssrRecord);
                            }
                        }
                    }

                    // 批量更新SSR信息
                    if (CollUtil.isNotEmpty(updateSsrList)) {
                        this.iMnjxNmSsrService.updateBatchById(updateSsrList);
                    }

                    // 批量更新PNR记录
                    if (CollUtil.isNotEmpty(updateRecordList)) {
                        this.iMnjxPnrRecordService.updateBatchById(updateRecordList);
                    }
                }
            }
        }

        // 如果有更新，创建新的封口记录
        if (hasUpdates) {

            // 创建新的封口记录
            MnjxPnrAt pnrAt = new MnjxPnrAt();
            pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrAt.setPnrId(pnr.getPnrId());
            pnrAt.setAtNo(newAtNo);
            pnrAt.setAtSiId(currentSi.getSiId());
            pnrAt.setAtDateTime(new Date());
            pnrAt.setAtType("KI"); // 设置封口类型为KI

            iMnjxPnrAtService.save(pnrAt);
        }
    }

    @Override
    public UpdatePnrVo updatePnr(UpdatePnrReqDto reqDto) throws SguiResultException {
        // 参数校验
        if (reqDto == null || StrUtil.isEmpty(reqDto.getRequest())) {
            throw new SguiResultException("请求参数不能为空");
        }

        // Base64解码
        String decodedJson = new String(java.util.Base64.getDecoder().decode(reqDto.getRequest()),
                java.nio.charset.StandardCharsets.UTF_8);

        // JSON转换为DTO
        UpdatePnrDto dto = JSONUtil.toBean(decodedJson, UpdatePnrDto.class);

        // 调用业务服务
        return iUpdatePnrService.updatePnr(dto);
    }

    @Override
    public Object querySeatMap(QuerySeatMapDto dto) throws SguiResultException {
        // 暂时不处理业务逻辑，返回null
        return null;
    }

    @Override
    public IssueTicketVo issueTicket(IssueTicketDto dto) throws SguiResultException {
        // 调用IIssueService的issueTicket方法
        return iIssueService.issueTicket(dto);
    }

    @Override
    public void removeName(RemoveNameDto dto) throws SguiResultException {
        // 调用IRemoveNameService的removeName方法
        iRemoveNameService.removeName(dto);
    }

    @Override
    public SplitPnrByPassengerVo splitPnrByPassenger(SplitPnrByPassengerDto dto) throws SguiResultException {
        // 调用ISplitPnrService的splitPnrByPassenger方法
        return iSplitPnrService.splitPnrByPassenger(dto);
    }

    @Override
    public String xePnr(XePnrDto dto) throws SguiResultException {
        // 调用IXePnrService的xePnr方法
        return iXePnrService.xePnr(dto);
    }

    @Override
    public List<StructuredPreviewVo> structuredPreview(StructuredPreviewDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnr())) {
            throw new SguiResultException("PNR编号不能为空");
        }
        if (StrUtil.isEmpty(dto.getPassengerId())) {
            throw new SguiResultException("旅客ID不能为空");
        }
        if (StrUtil.isEmpty(dto.getPassengerType())) {
            throw new SguiResultException("旅客类型不能为空");
        }

        // 1. 如果请求参数airline不为空，查询mnjx_airline表，判断该航司是否存在
        if (StrUtil.isNotEmpty(dto.getAirline())) {
            MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                    .eq(MnjxAirline::getAirlineCode, dto.getAirline())
                    .one();
            if (airline == null) {
                throw new SguiResultException("USAS ISSU/EXCH PREVIEW ERROR, BECAUSE: 需要授权 (出票/换开预览失败)");
            }
        }

        // 2. 根据请求参数pnr查询pnr信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnr())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 根据pnrId和请求参数的passengerId查询旅客信息
        int passengerIndex = Integer.parseInt(dto.getPassengerId().replace("P", ""));
        MnjxPnrNm passenger = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrNm::getPsgIndex, passengerIndex)
                .one();
        if (passenger == null) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 如果passengerType为INF，则需要查询婴儿信息
        MnjxNmXn infant = null;
        if ("INF".equals(dto.getPassengerType())) {
            infant = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .one();
            if (infant == null) {
                throw new SguiResultException("未找到婴儿信息");
            }
        }

        // 3. 根据当前登录的账号查询agent表，获取agentIata和agentContactName
        UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
        if (currentUser == null) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        MnjxSi currentSi = iMnjxSiService.getById(currentUser.getSiId());
        MnjxOffice office = iMnjxOfficeService.getById(currentSi.getOfficeId());
        if (office == null) {
            throw new SguiResultException("获取Office信息失败");
        }

        String deviceIataNumber = "";
        String airlineName = "";
        if ("0".equals(office.getOfficeType())) {
            // 代理人Office
            MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
            if (agent != null) {
                deviceIataNumber = agent.getAgentIata();
                airlineName = agent.getAgentContactCname();
            }
        }

        // 4. 根据pnrId查询航班信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 5. 查询运价信息和背书信息
        return this.buildStructuredPreviewResult(dto, pnr, passenger, infant, office, deviceIataNumber, airlineName, segments);
    }

    /**
     * 构建预览票面结果
     */
    private List<StructuredPreviewVo> buildStructuredPreviewResult(StructuredPreviewDto dto, MnjxPnr pnr,
                                                                   MnjxPnrNm passenger, MnjxNmXn infant,
                                                                   MnjxOffice office, String deviceIataNumber,
                                                                   String airlineName, List<MnjxPnrSeg> segments) throws SguiResultException {

        List<StructuredPreviewVo> voList = new ArrayList<>();

        // 构建航班信息列表
        StructuredPreviewVo vo = new StructuredPreviewVo();
        List<StructuredPreviewVo.FlightInfo> flightInfos = new ArrayList<>();
        boolean hasStopover = false;

        for (int i = 0; i < segments.size(); i++) {
            MnjxPnrSeg segment = segments.get(i);
            if (i % 2 == 0) {
                vo = new StructuredPreviewVo();
                flightInfos = new ArrayList<>();

                // 基本信息
                vo.setChannelPnr(dto.getPnr());
                vo.setChannelCode("1E");
                vo.setDeviceIataNumber(deviceIataNumber);
                vo.setAirlineName(airlineName);
                vo.setOffice(office.getOfficeNo());
                vo.setDevice("DEV-" + dto.getDeviceNum());

                // 旅客姓名
                if (infant != null) {
                    vo.setFullName(infant.getXnCname());
                } else {
                    vo.setFullName(passenger.getName());
                }

                // 预订代理
                MnjxSi mnjxSi = iMnjxSiService.getById(pnr.getCreateSiId());
                vo.setBookingAgent(mnjxSi.getSiNo());

                // 设置出发地和目的地
                MnjxCity orgCity = iSguiCommonService.getCityByAirportCode(segments.get(0).getOrg());
                vo.setOrigin(orgCity.getCityCode());

                MnjxCity dstCity = iSguiCommonService.getCityByAirportCode(segments.get(segments.size() - 1).getDst());
                vo.setDestination(dstCity.getCityCode());

                // 设置验证航司（第一个航段的航司）
                if (StrUtil.isNotEmpty(segments.get(0).getFlightNo()) && segments.get(0).getFlightNo().length() >= 2) {
                    vo.setVcAirline(segments.get(0).getFlightNo().substring(0, 2));
                    MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                            .eq(MnjxAirline::getAirlineCode, vo.getVcAirline())
                            .one();
                    vo.setAirlineBookingReference(airline.getAirlineSettlementCode());
                }

                // 查询运价和背书信息
                this.buildFareAndEndorsementInfo(vo, dto, pnr, passenger, infant, segments);

                // 设置航司预订参考
                vo.setOriginalIssueText(null);

                vo.setFlights(flightInfos);
                vo.setHasStopover(hasStopover);

                voList.add(vo);
            }

            StructuredPreviewVo.FlightInfo flightInfo = new StructuredPreviewVo.FlightInfo();

            // 出发和到达城市/机场
            flightInfo.setDepartureAirport(segment.getOrg());
            flightInfo.setArrivalAirport(segment.getDst());

            // 从缓存或数据库获取城市信息
            MnjxCity depCity = iSguiCommonService.getCityByAirportCode(segment.getOrg());
            MnjxCity arrCity = iSguiCommonService.getCityByAirportCode(segment.getDst());
            flightInfo.setDepartureCity(depCity != null ? depCity.getCityCname() : segment.getOrg());
            flightInfo.setArrivalCity(arrCity != null ? arrCity.getCityCname() : segment.getDst());
            flightInfo.setIsStopover(false);
            flightInfo.setOpenSegment(false);

            if (!"SA".equals(segment.getPnrSegType())) { // 非地面段
                // 航司代码
                if (StrUtil.isNotEmpty(segment.getFlightNo()) && segment.getFlightNo().length() >= 2) {
                    flightInfo.setAirline(segment.getFlightNo().substring(0, 2));
                }

                // 航班号
                flightInfo.setFlightNumber(segment.getFlightNo().substring(2));

                // 舱位信息
                flightInfo.setFareBasis(segment.getSellCabin());
                flightInfo.setMcRbd(segment.getSellCabin());

                // 日期时间
                flightInfo.setEtermDate(DateUtils.ymd2Com(segment.getFlightDate()).substring(0, 5));
                flightInfo.setEtermTime(segment.getEstimateOff());

                // 预订状态
                flightInfo.setReservationStatusCode("OK");

                // 其他信息
                flightInfo.setVoidSegment(false);
                flightInfo.setFreeBaggageAllowance("20K"); // 默认值
                flightInfo.setNotValidAfterEtermDate("");
                flightInfo.setIsStopover(false);

                flightInfos.add(flightInfo);
            } else {
                flightInfo.setVoidSegment(true);
                flightInfos.add(flightInfo);
            }
        }

        return voList;
    }

    /**
     * 构建运价和背书信息
     */
    private void buildFareAndEndorsementInfo(StructuredPreviewVo vo, StructuredPreviewDto dto,
                                             MnjxPnr pnr, MnjxPnrNm passenger, MnjxNmXn infant, List<MnjxPnrSeg> segments) throws SguiResultException {

        // 确定是否为婴儿
        int isBaby = "INF".equals(dto.getPassengerType()) ? 1 : 0;

        // 查询运价信息：先查nmfn，再查pnrfn

        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, passenger.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isBaby)
                .one();

        MnjxPnrFn pnrFn = null;
        if (nmFn == null) {
            // 没有找到nmfn，查询pnrfn
            pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isBaby)
                    .one();
        }

        // 查询背书信息：先查nmei，再查pnrei
        MnjxNmEi nmEi = null;
        MnjxPnrEi pnrEi = null;

        if (infant != null) {
            // 婴儿情况
            nmEi = iMnjxNmEiService.lambdaQuery()
                    .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                    .like(MnjxNmEi::getEiInfo, "IN/")
                    .one();
        } else {
            // 成人/儿童情况
            nmEi = iMnjxNmEiService.lambdaQuery()
                    .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                    .one();
        }

        if (nmEi == null) {
            // 没有找到nmei，查询pnrei
            if (infant != null) {
                pnrEi = iMnjxPnrEiService.lambdaQuery()
                        .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                        .like(MnjxPnrEi::getEiInfo, "IN/")
                        .one();
            } else {
                pnrEi = iMnjxPnrEiService.lambdaQuery()
                        .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                        .one();
            }
        }

        if (ObjectUtil.isAllEmpty(nmEi, pnrEi)) {
            throw new SguiResultException("USAS ISSU/EXCH PREVIEW ERROR, BECAUSE: EI ELEMENT MISSING   (出票/换开预览失败)");
        }

        MnjxNmFp nmFp = null;
        MnjxPnrFp pnrFp = null;
        if (infant != null) {
            // 婴儿情况
            nmFp = iMnjxNmFpService.lambdaQuery()
                    .eq(MnjxNmFp::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmFp::getIsBaby, 1)
                    .one();
        } else {
            // 成人/儿童情况
            nmFp = iMnjxNmFpService.lambdaQuery()
                    .eq(MnjxNmFp::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmFp::getIsBaby, 0)
                    .one();
        }
        if (nmFp == null) {
            // 没有找到nmfp，查询pnrfp
            if (infant != null) {
                pnrFp = iMnjxPnrFpService.lambdaQuery()
                        .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFp::getIsBaby, 1)
                        .one();
            } else {
                pnrFp = iMnjxPnrFpService.lambdaQuery()
                        .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFp::getIsBaby, 0)
                        .one();
            }
        }
        if (ObjectUtil.isAllEmpty(nmFp, pnrFp)) {
            throw new SguiResultException("USAS ISSU/EXCH PREVIEW ERROR, BECAUSE: NO FORM OF PAYMENT   (出票/换开预览失败)");
        }

        MnjxNmFc nmFc = null;
        MnjxPnrFc pnrFc = null;
        if (infant != null) {
            // 婴儿情况
            nmFc = iMnjxNmFcService.lambdaQuery()
                    .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmFc::getIsBaby, 1)
                    .one();
        } else {
            // 成人/儿童情况
            nmFc = iMnjxNmFcService.lambdaQuery()
                    .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmFc::getIsBaby, 0)
                    .one();
        }
        if (nmFc == null) {
            // 没有找到nmfc，查询pnrfc
            if (infant != null) {
                pnrFc = iMnjxPnrFcService.lambdaQuery()
                        .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFc::getIsBaby, 1)
                        .one();
            } else {
                pnrFc = iMnjxPnrFcService.lambdaQuery()
                        .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrFc::getIsBaby, 0)
                        .one();
            }
        }
        if (ObjectUtil.isAllEmpty(nmFc, pnrFc)) {
            throw new SguiResultException("USAS ISSU/EXCH PREVIEW ERROR, BECAUSE: FC ELEMENT MISSING   (出票/换开预览失败)");
        }

        // 构建票价信息
        StructuredPreviewVo.FareInfo fareInfo = new StructuredPreviewVo.FareInfo();
        this.buildFareInfo(fareInfo, nmFn, nmFc, pnrFn, pnrFc, segments);

        // 设置背书信息
        if (nmEi != null) {
            vo.setEndorsementInformation(nmEi.getEiInfo());
        } else {
            vo.setEndorsementInformation(pnrEi.getEiInfo());
        }

        vo.setFare(fareInfo);
    }

    /**
     * 从NmFn构建票价信息
     */
    private void buildFareInfo(StructuredPreviewVo.FareInfo fareInfo, MnjxNmFn nmFn, MnjxNmFc nmFc,
                               MnjxPnrFn pnrFn, MnjxPnrFc pnrFc, List<MnjxPnrSeg> segments) {
        fareInfo.setAutoFareType("A");
        fareInfo.setItFare(false);
        fareInfo.setFormOfPaymentText("CASH(CNY)");
        fareInfo.setCommissionAmount(StrUtil.toString(segments.stream().filter(s -> !"SA".equals(s.getPnrSegType())).count() * 5) + ".00");
        fareInfo.setTourCode("");

        // 总金额
        StructuredPreviewVo.AmountInfo totalAmount = new StructuredPreviewVo.AmountInfo();
        totalAmount.setCurrency(nmFn != null ? nmFn.getACurrency() : pnrFn.getACurrency());
        totalAmount.setAmount(nmFn != null ? nmFn.getAPrice().toString() : pnrFn.getAPrice().toString());
        fareInfo.setTotalAmount(totalAmount);

        // 票面金额
        StructuredPreviewVo.AmountInfo ticketAmount = new StructuredPreviewVo.AmountInfo();
        ticketAmount.setCurrency(nmFn != null ? nmFn.getFCurrency() : pnrFn.getFCurrency());
        ticketAmount.setAmount(nmFn != null ? nmFn.getFPrice().toString() : pnrFn.getFPrice().toString());
        fareInfo.setTicketAmount(ticketAmount);
        fareInfo.setTicketSpreadPrice(ticketAmount);

        // 税费金额
        StructuredPreviewVo.AmountInfo taxAmount = new StructuredPreviewVo.AmountInfo();
        taxAmount.setCurrency("CNY");
        taxAmount.setAmount(nmFn != null ? nmFn.getXPrice().toString() : pnrFn.getXPrice().toString());
        fareInfo.setTaxAmount(taxAmount);

        // 税费列表
        List<StructuredPreviewVo.TaxInfo> taxes = new ArrayList<>();

        // CN税
        BigDecimal cnPrice = nmFn != null ? nmFn.getTCnPrice() : pnrFn.getTCnPrice();
        if (cnPrice.compareTo(BigDecimal.ZERO) > 0) {
            StructuredPreviewVo.TaxInfo cnTax = new StructuredPreviewVo.TaxInfo();
            cnTax.setTaxCode("CN");
            cnTax.setTaxAmount(cnPrice.toString());
            taxes.add(cnTax);
        } else {
            StructuredPreviewVo.TaxInfo cnTax = new StructuredPreviewVo.TaxInfo();
            cnTax.setTaxCode("CN");
            cnTax.setTaxAmount("EXEMPT");
            taxes.add(cnTax);
        }

        // YQ税
        BigDecimal yqPrice = nmFn != null ? nmFn.getTYqPrice() : pnrFn.getTYqPrice();
        if (yqPrice.compareTo(BigDecimal.ZERO) > 0) {
            StructuredPreviewVo.TaxInfo yqTax = new StructuredPreviewVo.TaxInfo();
            yqTax.setTaxCode("YQ");
            yqTax.setTaxAmount(yqPrice.toString());
            taxes.add(yqTax);
        } else {
            StructuredPreviewVo.TaxInfo yqTax = new StructuredPreviewVo.TaxInfo();
            yqTax.setTaxCode("YQ");
            yqTax.setTaxAmount("EXEMPT");
            taxes.add(yqTax);
        }

        fareInfo.setTaxes(taxes);

        // 简化运价计算
        fareInfo.setSimplifiedFareCalculation(this.buildSimplifiedFareCalculation(nmFc, pnrFc, segments));
    }

    /**
     * 从NmFn构建简化运价计算
     */
    private String buildSimplifiedFareCalculation(MnjxNmFc nmFc, MnjxPnrFc pnrFc, List<MnjxPnrSeg> segments) {
        // 04JUL25PKX MU SHA800.00//CTU CA PEK2950.00CNY3750.00END
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < segments.size(); i++) {
            MnjxPnrSeg pnrSeg = segments.get(i);
            // 获取第一个航班的日期和航班信息
            if (i == 0) {
                sb.append(DateUtils.ymd2Com(pnrSeg.getFlightDate()));
            }
            if ("SA".equals(pnrSeg.getPnrSegType())) {
                sb.append("//");
                continue;
            }
            sb.append(pnrSeg.getOrg());
            sb.append(" ");
            sb.append(pnrSeg.getFlightNo().substring(0, 2));
            sb.append(" ");
            sb.append(pnrSeg.getDst());
            switch (pnrSeg.getPnrSegNo()) {
                case 1:
                    sb.append(nmFc != null ? nmFc.getSeg1Price() : pnrFc.getSeg1Price());
                    break;
                case 2:
                    sb.append(nmFc != null ? nmFc.getSeg2Price() : pnrFc.getSeg2Price());
                    break;
                case 3:
                    sb.append(nmFc != null ? nmFc.getSeg3Price() : pnrFc.getSeg3Price());
                    break;
                case 4:
                    sb.append(nmFc != null ? nmFc.getSeg4Price() : pnrFc.getSeg4Price());
                    break;
                case 5:
                    sb.append(nmFc != null ? nmFc.getSeg5Price() : pnrFc.getSeg5Price());
                    break;
                default:
                    break;
            }
        }
        sb.append("CNY");
        sb.append(nmFc != null ? nmFc.getTotalPrice() : pnrFc.getTotalPrice());
        sb.append("END");

        return sb.toString();
    }
}
